#!/usr/bin/env node
/**
 * 完整的构建和运行脚本
 * 1. 生成 OData 客户端
 * 2. 后处理修复
 * 3. 启动开发服务器
 */

const { spawn } = require('child_process')
const path = require('path')

const SCRIPTS_DIR = path.resolve(__dirname)
const PROJECT_ROOT = path.resolve(__dirname, '..')

/**
 * 执行命令并返回 Promise
 */
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 执行命令: ${command} ${args.join(' ')}`)
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      cwd: options.cwd || PROJECT_ROOT,
      ...options
    })
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ 命令执行成功: ${command}`)
        resolve(code)
      } else {
        console.error(`❌ 命令执行失败: ${command} (退出码: ${code})`)
        reject(new Error(`Command failed with exit code ${code}`))
      }
    })
    
    child.on('error', (error) => {
      console.error(`❌ 命令执行错误: ${command}`)
      console.error(error)
      reject(error)
    })
  })
}

/**
 * 步骤 1: 生成 OData 客户端
 */
async function generateODataClient() {
  console.log('\n📋 步骤 1: 生成 OData 客户端')
  console.log('=' * 50)
  
  try {
    await runCommand('node', [path.join(SCRIPTS_DIR, 'gen-odata.js')])
    console.log('✅ OData 客户端生成完成')
  } catch (error) {
    console.error('❌ OData 客户端生成失败')
    throw error
  }
}

/**
 * 步骤 2: 后处理修复
 */
async function postProcessGenerated() {
  console.log('\n📋 步骤 2: 后处理修复')
  console.log('=' * 50)
  
  try {
    await runCommand('node', [path.join(SCRIPTS_DIR, 'post-process-odata.js')])
    console.log('✅ 后处理修复完成')
  } catch (error) {
    console.error('❌ 后处理修复失败')
    throw error
  }
}

/**
 * 步骤 3: 验证生成的代码
 */
async function validateGenerated() {
  console.log('\n📋 步骤 3: 验证生成的代码')
  console.log('=' * 50)
  
  try {
    await runCommand('node', [path.join(SCRIPTS_DIR, 'check-missing-types.js')])
    console.log('✅ 代码验证完成')
  } catch (error) {
    console.error('❌ 代码验证失败')
    throw error
  }
}

/**
 * 步骤 4: TypeScript 类型检查
 */
async function typeCheck() {
  console.log('\n📋 步骤 4: TypeScript 类型检查')
  console.log('=' * 50)
  
  try {
    await runCommand('npx', ['tsc', '--noEmit'], {
      cwd: path.join(PROJECT_ROOT, 'play')
    })
    console.log('✅ TypeScript 类型检查通过')
  } catch (error) {
    console.error('❌ TypeScript 类型检查失败')
    // 类型检查失败不阻止后续步骤
    console.log('⚠️  继续执行后续步骤...')
  }
}

/**
 * 步骤 5: 启动开发服务器（可选）
 */
async function startDevServer() {
  console.log('\n📋 步骤 5: 启动开发服务器')
  console.log('=' * 50)
  
  const shouldStart = process.argv.includes('--start-dev')
  
  if (!shouldStart) {
    console.log('ℹ️  跳过开发服务器启动（使用 --start-dev 参数来启动）')
    return
  }
  
  try {
    console.log('🚀 启动开发服务器...')
    console.log('📝 注意: 开发服务器将在前台运行，按 Ctrl+C 停止')
    
    // 这里不使用 await，因为开发服务器会一直运行
    runCommand('npm', ['run', 'dev'], {
      cwd: path.join(PROJECT_ROOT, 'play')
    }).catch(error => {
      if (error.message.includes('SIGINT') || error.message.includes('SIGTERM')) {
        console.log('\n👋 开发服务器已停止')
      } else {
        console.error('❌ 开发服务器启动失败:', error)
      }
    })
    
  } catch (error) {
    console.error('❌ 开发服务器启动失败')
    throw error
  }
}

/**
 * 主执行函数
 */
async function main() {
  console.log('🚀 开始完整的构建和运行流程')
  console.log('🕐 开始时间:', new Date().toLocaleString())
  console.log('=' * 60)
  
  const startTime = Date.now()
  
  try {
    // 执行所有步骤
    await generateODataClient()
    await postProcessGenerated()
    await validateGenerated()
    await typeCheck()
    
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    console.log('\n🎉 构建流程完成!')
    console.log(`⏱️  总耗时: ${duration} 秒`)
    console.log('🕐 完成时间:', new Date().toLocaleString())
    
    // 显示下一步操作提示
    console.log('\n📋 下一步操作:')
    console.log('   1. 进入 play 目录: cd play')
    console.log('   2. 启动开发服务器: npm run dev')
    console.log('   3. 打开浏览器访问: http://localhost:5173')
    console.log('   4. 查看 OData 客户端演示')
    
    // 可选启动开发服务器
    await startDevServer()
    
  } catch (error) {
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    console.error('\n💥 构建流程失败!')
    console.error(`⏱️  失败前耗时: ${duration} 秒`)
    console.error('❌ 错误信息:', error.message)
    
    process.exit(1)
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
SAP Cloud SDK 构建和运行脚本

用法:
  node scripts/build-and-run.js [选项]

选项:
  --start-dev    构建完成后自动启动开发服务器
  --help         显示此帮助信息

示例:
  node scripts/build-and-run.js                # 只构建，不启动服务器
  node scripts/build-and-run.js --start-dev    # 构建并启动开发服务器

步骤说明:
  1. 生成 OData 客户端代码
  2. 后处理修复常见问题
  3. 验证生成的代码
  4. TypeScript 类型检查
  5. 可选启动开发服务器
`)
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp()
  process.exit(0)
}

// 执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('💥 未处理的错误:', error)
    process.exit(1)
  })
}
