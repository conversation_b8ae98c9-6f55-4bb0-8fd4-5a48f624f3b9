import { descriptionItemProps, descriptionProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

// 选项配置接口
export const neDescriptionItemProps = buildProps({
  ...descriptionItemProps,
  value: { type: String, default: () => '' },
} as const)

export type NeDescriptionItemProps = Partial<
  ExtractPropTypes<typeof neDescriptionItemProps> & {
    labelRender?: () => any
    valueRender?: () => any
  }
>
// 继承 Element Plus Description 的所有属性，并添加自定义属性
export const neDescriptionProps = buildProps({
  // 继承 Element Plus Description 的所有属性
  ...descriptionProps,
  // 自定义属性：选项配置数组
  options: {
    type: Array as () => Array<NeDescriptionItemProps>,
    default: () => [],
  },
} as const)

export type NeDescriptionProps = ExtractPropTypes<typeof neDescriptionProps>
