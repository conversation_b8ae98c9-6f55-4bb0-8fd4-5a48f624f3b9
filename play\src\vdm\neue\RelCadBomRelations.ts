/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers
} from '@sap-cloud-sdk/odata-v4';
import type { RelCadBomRelationsApi } from './RelCadBomRelationsApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "RelCadBomRelations" of service "neue".
 */
export class RelCadBomRelations<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements RelCadBomRelationsType<T>
{
  /**
   * Technical entity name for RelCadBomRelations.
   */
  static override _entityName = 'RelCadBomRelations';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the RelCadBomRelations entity.
   */
  static _keys = ['ncid'];
  /**
   * Instance Name.
   */
  declare instanceName: DeserializedType<T, 'Edm.String'>;
  /**
   * Bom Excluded.
   * Maximum length: 64.
   */
  declare bomExcluded: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Suppressed.
   * Maximum length: 64.
   */
  declare suppressed: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Quantity.
   * Maximum length: 64.
   * @nullable
   */
  declare quantity?: DeserializedType<T, 'Edm.Int32'> | null;
  /**
   * Configuration.
   * Maximum length: 64.
   */
  declare configuration: DeserializedType<T, 'Edm.String'>;
  /**
   * Transformation Matrix.
   * Maximum length: 1000.
   */
  declare transformationMatrix: DeserializedType<T, 'Edm.String'>;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: RelCadBomRelationsApi<T>) {
    super(_entityApi);
  }
}

export interface RelCadBomRelationsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  instanceName: DeserializedType<T, 'Edm.String'>;
  bomExcluded: DeserializedType<T, 'Edm.Boolean'>;
  suppressed: DeserializedType<T, 'Edm.Boolean'>;
  quantity?: DeserializedType<T, 'Edm.Int32'> | null;
  configuration: DeserializedType<T, 'Edm.String'>;
  transformationMatrix: DeserializedType<T, 'Edm.String'>;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
