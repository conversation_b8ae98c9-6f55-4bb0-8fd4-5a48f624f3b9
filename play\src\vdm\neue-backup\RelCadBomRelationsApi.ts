/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { RelCadBomRelations } from './RelCadBomRelations';
import { RelCadBomRelationsRequestBuilder } from './RelCadBomRelationsRequestBuilder';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class RelCadBomRelationsApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<RelCadBomRelations<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): RelCadBomRelationsApi<DeSerializersT> {
    return new RelCadBomRelationsApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = RelCadBomRelations;

  requestBuilder(): RelCadBomRelationsRequestBuilder<DeSerializersT> {
    return new RelCadBomRelationsRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    RelCadBomRelations<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>(
      this
    );
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<
    RelCadBomRelations<DeSerializersT>,
    DeSerializersT,
    NullableT
  > {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<
    typeof RelCadBomRelations,
    DeSerializersT
  >;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(
        RelCadBomRelations,
        this.deSerializers
      );
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    INSTANCE_NAME: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    BOM_EXCLUDED: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      false,
      true
    >;
    SUPPRESSED: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      false,
      true
    >;
    QUANTITY: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.Int32',
      true,
      true
    >;
    CONFIGURATION: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    TRANSFORMATION_MATRIX: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    NCID: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      RelCadBomRelations<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<RelCadBomRelations<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link instanceName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        INSTANCE_NAME: fieldBuilder.buildEdmTypeField(
          'instanceName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link bomExcluded} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        BOM_EXCLUDED: fieldBuilder.buildEdmTypeField(
          'bomExcluded',
          'Edm.Boolean',
          false
        ),
        /**
         * Static representation of the {@link suppressed} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SUPPRESSED: fieldBuilder.buildEdmTypeField(
          'suppressed',
          'Edm.Boolean',
          false
        ),
        /**
         * Static representation of the {@link quantity} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        QUANTITY: fieldBuilder.buildEdmTypeField('quantity', 'Edm.Int32', true),
        /**
         * Static representation of the {@link configuration} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CONFIGURATION: fieldBuilder.buildEdmTypeField(
          'configuration',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link transformationMatrix} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        TRANSFORMATION_MATRIX: fieldBuilder.buildEdmTypeField(
          'transformationMatrix',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', RelCadBomRelations)
      };
    }

    return this._schema;
  }
}
