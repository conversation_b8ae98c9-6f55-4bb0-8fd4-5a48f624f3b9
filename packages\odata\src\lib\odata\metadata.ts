// odataMetadata.js
// 浏览器端 ES6 版本

import * as utils from '../utils.js'
import * as oDSxml from '../xml.js'
import * as odataHandler from './handler.js'

// Utils
const { contains } = utils
const {
  xmlAttributes,
  xmlChildElements,
  xmlFirstChildElement,
  xmlInnerText,
  xmlLocalName,
  xmlNamespaceURI,
  xmlnsNS,
  xmlParse,
  http: odataHttp,
} = oDSxml

const ado = `${odataHttp}docs.oasis-open.org/odata/`
const adoDs = `${ado}ns`
const edmxNs = `${adoDs}/edmx`
const edmNs1 = `${adoDs}/edm`
const odataMetaXmlNs = `${adoDs}/metadata`
const MAX_DATA_SERVICE_VERSION = odataHandler.MAX_DATA_SERVICE_VERSION

const xmlMediaType = 'application/xml'

function schemaElement(attributes, elements, text = false, ns = null) {
  return { attributes, elements, text, ns }
}

const schema = {
  elements: {
    Action: schemaElement(
      ['Name', 'IsBound', 'EntitySetPath'],
      ['ReturnType', 'Parameter*', 'Annotation*']
    ),
    ActionImport: schemaElement(['Name', 'Action', 'EntitySet', 'Annotation*']),
    Annotation: schemaElement(
      [
        'Term',
        'Qualifier',
        'Binary',
        'Bool',
        'Date',
        'DateTimeOffset',
        'Decimal',
        'Duration',
        'EnumMember',
        'Float',
        'Guid',
        'Int',
        'String',
        'TimeOfDay',
        'AnnotationPath',
        'NavigationPropertyPath',
        'Path',
        'PropertyPath',
        'UrlRef',
      ],
      [
        'Binary*',
        'Bool*',
        'Date*',
        'DateTimeOffset*',
        'Decimal*',
        'Duration*',
        'EnumMember*',
        'Float*',
        'Guid*',
        'Int*',
        'String*',
        'TimeOfDay*',
        'And*',
        'Or*',
        'Not*',
        'Eq*',
        'Ne*',
        'Gt*',
        'Ge*',
        'Lt*',
        'Le*',
        'AnnotationPath*',
        'Apply*',
        'Cast*',
        'Collection*',
        'If*',
        'IsOf*',
        'LabeledElement*',
        'LabeledElementReference*',
        'Null*',
        'NavigationPropertyPath*',
        'Path*',
        'PropertyPath*',
        'Record*',
        'UrlRef*',
        'Annotation*',
      ]
    ),
    AnnotationPath: schemaElement(null, null, true),
    Annotations: schemaElement(['Target', 'Qualifier'], ['Annotation*']),
    Apply: schemaElement(
      ['Function'],
      ['String*', 'Path*', 'LabeledElement*', 'Annotation*']
    ),
    And: schemaElement(null, null, true),
    Or: schemaElement(null, null, true),
    Not: schemaElement(null, null, true),
    Eq: schemaElement(null, null, true),
    Ne: schemaElement(null, null, true),
    Gt: schemaElement(null, null, true),
    Ge: schemaElement(null, null, true),
    Lt: schemaElement(null, null, true),
    Le: schemaElement(null, null, true),
    Binary: schemaElement(null, null, true),
    Bool: schemaElement(null, null, true),
    Cast: schemaElement(['Type'], ['Path*', 'Annotation*']),
    Collection: schemaElement(null, [
      'Binary*',
      'Bool*',
      'Date*',
      'DateTimeOffset*',
      'Decimal*',
      'Duration*',
      'EnumMember*',
      'Float*',
      'Guid*',
      'Int*',
      'String*',
      'TimeOfDay*',
      'And*',
      'Or*',
      'Not*',
      'Eq*',
      'Ne*',
      'Gt*',
      'Ge*',
      'Lt*',
      'Le*',
      'AnnotationPath*',
      'Apply*',
      'Cast*',
      'Collection*',
      'If*',
      'IsOf*',
      'LabeledElement*',
      'LabeledElementReference*',
      'Null*',
      'NavigationPropertyPath*',
      'Path*',
      'PropertyPath*',
      'Record*',
      'UrlRef*',
    ]),
    ComplexType: schemaElement(
      ['Name', 'BaseType', 'Abstract', 'OpenType'],
      ['Property*', 'NavigationProperty*', 'Annotation*']
    ),
    Date: schemaElement(null, null, true),
    DateTimeOffset: schemaElement(null, null, true),
    Decimal: schemaElement(null, null, true),
    Duration: schemaElement(null, null, true),
    EntityContainer: schemaElement(
      ['Name', 'Extends'],
      [
        'EntitySet*',
        'Singleton*',
        'ActionImport*',
        'FunctionImport*',
        'Annotation*',
      ]
    ),
    EntitySet: schemaElement(
      ['Name', 'EntityType', 'IncludeInServiceDocument'],
      ['NavigationPropertyBinding*', 'Annotation*']
    ),
    EntityType: schemaElement(
      ['Name', 'BaseType', 'Abstract', 'OpenType', 'HasStream'],
      ['Key*', 'Property*', 'NavigationProperty*', 'Annotation*']
    ),
    EnumMember: schemaElement(null, null, true),
    EnumType: schemaElement(['Name', 'UnderlyingType', 'IsFlags'], ['Member*']),
    Float: schemaElement(null, null, true),
    Function: schemaElement(
      ['Name', 'IsBound', 'IsComposable', 'EntitySetPath'],
      ['ReturnType', 'Parameter*', 'Annotation*']
    ),
    FunctionImport: schemaElement([
      'Name',
      'Function',
      'EntitySet',
      'IncludeInServiceDocument',
      'Annotation*',
    ]),
    Guid: schemaElement(null, null, true),
    If: schemaElement(null, ['Path*', 'String*', 'Annotation*']),
    Int: schemaElement(null, null, true),
    IsOf: schemaElement(
      [
        'Type',
        'MaxLength',
        'Precision',
        'Scale',
        'Unicode',
        'SRID',
        'DefaultValue',
        'Annotation*',
      ],
      ['Path*']
    ),
    Key: schemaElement(null, ['PropertyRef*']),
    LabeledElement: schemaElement(
      ['Name'],
      [
        'Binary*',
        'Bool*',
        'Date*',
        'DateTimeOffset*',
        'Decimal*',
        'Duration*',
        'EnumMember*',
        'Float*',
        'Guid*',
        'Int*',
        'String*',
        'TimeOfDay*',
        'And*',
        'Or*',
        'Not*',
        'Eq*',
        'Ne*',
        'Gt*',
        'Ge*',
        'Lt*',
        'Le*',
        'AnnotationPath*',
        'Apply*',
        'Cast*',
        'Collection*',
        'If*',
        'IsOf*',
        'LabeledElement*',
        'LabeledElementReference*',
        'Null*',
        'NavigationPropertyPath*',
        'Path*',
        'PropertyPath*',
        'Record*',
        'UrlRef*',
        'Annotation*',
      ]
    ),
    LabeledElementReference: schemaElement(
      ['Term'],
      [
        'Binary*',
        'Bool*',
        'Date*',
        'DateTimeOffset*',
        'Decimal*',
        'Duration*',
        'EnumMember*',
        'Float*',
        'Guid*',
        'Int*',
        'String*',
        'TimeOfDay*',
        'And*',
        'Or*',
        'Not*',
        'Eq*',
        'Ne*',
        'Gt*',
        'Ge*',
        'Lt*',
        'Le*',
        'AnnotationPath*',
        'Apply*',
        'Cast*',
        'Collection*',
        'If*',
        'IsOf*',
        'LabeledElement*',
        'LabeledElementReference*',
        'Null*',
        'NavigationPropertyPath*',
        'Path*',
        'PropertyPath*',
        'Record*',
        'UrlRef*',
      ]
    ),
    Member: schemaElement(['Name', 'Value'], ['Annotation*']),
    NavigationProperty: schemaElement(
      ['Name', 'Type', 'Nullable', 'Partner', 'ContainsTarget'],
      ['ReferentialConstraint*', 'OnDelete*', 'Annotation*']
    ),
    NavigationPropertyBinding: schemaElement(['Path', 'Target']),
    NavigationPropertyPath: schemaElement(null, null, true),
    Null: schemaElement(null, ['Annotation*']),
    OnDelete: schemaElement(['Action'], ['Annotation*']),
    Path: schemaElement(null, null, true),
    Parameter: schemaElement(
      ['Name', 'Type', 'Nullable', 'MaxLength', 'Precision', 'Scale', 'SRID'],
      ['Annotation*']
    ),
    Property: schemaElement(
      [
        'Name',
        'Type',
        'Nullable',
        'MaxLength',
        'Precision',
        'Scale',
        'Unicode',
        'SRID',
        'DefaultValue',
      ],
      ['Annotation*']
    ),
    PropertyPath: schemaElement(null, null, true),
    PropertyRef: schemaElement(['Name', 'Alias']),
    PropertyValue: schemaElement(
      ['Property', 'Path'],
      [
        'Binary*',
        'Bool*',
        'Date*',
        'DateTimeOffset*',
        'Decimal*',
        'Duration*',
        'EnumMember*',
        'Float*',
        'Guid*',
        'Int*',
        'String*',
        'TimeOfDay*',
        'And*',
        'Or*',
        'Not*',
        'Eq*',
        'Ne*',
        'Gt*',
        'Ge*',
        'Lt*',
        'Le*',
        'AnnotationPath*',
        'Apply*',
        'Cast*',
        'Collection*',
        'If*',
        'IsOf*',
        'LabeledElement*',
        'LabeledElementReference*',
        'Null*',
        'NavigationPropertyPath*',
        'Path*',
        'PropertyPath*',
        'Record*',
        'UrlRef*',
        'Annotation*',
      ]
    ),
    Record: schemaElement(null, ['PropertyValue*', 'Property*', 'Annotation*']),
    ReferentialConstraint: schemaElement([
      'Property',
      'ReferencedProperty',
      'Annotation*',
    ]),
    ReturnType: schemaElement([
      'Type',
      'Nullable',
      'MaxLength',
      'Precision',
      'Scale',
      'SRID',
    ]),
    String: schemaElement(null, null, true),
    Schema: schemaElement(
      ['Namespace', 'Alias'],
      [
        'Action*',
        'Annotations*',
        'Annotation*',
        'ComplexType*',
        'EntityContainer',
        'EntityType*',
        'EnumType*',
        'Function*',
        'Term*',
        'TypeDefinition*',
        'Annotation*',
      ]
    ),
    Singleton: schemaElement(
      ['Name', 'Type'],
      ['NavigationPropertyBinding*', 'Annotation*']
    ),
    Term: schemaElement(
      [
        'Name',
        'Type',
        'BaseTerm',
        'DefaultValue ',
        'AppliesTo',
        'Nullable',
        'MaxLength',
        'Precision',
        'Scale',
        'SRID',
      ],
      ['Annotation*']
    ),
    TimeOfDay: schemaElement(null, null, true),
    TypeDefinition: schemaElement(
      [
        'Name',
        'UnderlyingType',
        'MaxLength',
        'Unicode',
        'Precision',
        'Scale',
        'SRID',
      ],
      ['Annotation*']
    ),
    UrlRef: schemaElement(null, [
      'Binary*',
      'Bool*',
      'Date*',
      'DateTimeOffset*',
      'Decimal*',
      'Duration*',
      'EnumMember*',
      'Float*',
      'Guid*',
      'Int*',
      'String*',
      'TimeOfDay*',
      'And*',
      'Or*',
      'Not*',
      'Eq*',
      'Ne*',
      'Gt*',
      'Ge*',
      'Lt*',
      'Le*',
      'AnnotationPath*',
      'Apply*',
      'Cast*',
      'Collection*',
      'If*',
      'IsOf*',
      'LabeledElement*',
      'LabeledElementReference*',
      'Null*',
      'NavigationPropertyPath*',
      'Path*',
      'PropertyPath*',
      'Record*',
      'UrlRef*',
      'Annotation*',
    ]),
    Edmx: schemaElement(
      ['Version'],
      ['DataServices', 'Reference*'],
      false,
      edmxNs
    ),
    DataServices: schemaElement(
      ['m:MaxDataServiceVersion', 'm:DataServiceVersion'],
      ['Schema*'],
      false,
      edmxNs
    ),
    Reference: schemaElement(
      ['Uri'],
      ['Include*', 'IncludeAnnotations*', 'Annotation*']
    ),
    Include: schemaElement(['Namespace', 'Alias']),
    IncludeAnnotations: schemaElement([
      'TermNamespace',
      'Qualifier',
      'TargetNamespace',
    ]),
  },
}

/** 将 PascalCase 转为 camelCase */
function scriptCase(text) {
  if (!text) return text
  if (text.length > 1) {
    const firstTwo = text.slice(0, 2)
    if (firstTwo === firstTwo.toUpperCase()) return text
    return text.charAt(0).toLowerCase() + text.slice(1)
  }
  return text.charAt(0).toLowerCase()
}

/** 获取子 schema */
function getChildSchema(parentSchema, candidateName) {
  const elements = parentSchema.elements
  if (!elements) return null
  for (let elementName of elements) {
    let multipleElements = false
    if (elementName.endsWith('*')) {
      multipleElements = true
      elementName = elementName.slice(0, -1)
    }
    if (candidateName === elementName) {
      return {
        isArray: multipleElements,
        propertyName: scriptCase(elementName),
      }
    }
  }
  return null
}

/** 判断是否 EDM namespace */
function isEdmNamespace(nsURI) {
  return nsURI === edmNs1
}

/** 解析 CSDL 元素 */
function parseConceptualModelElement(element) {
  const localName = xmlLocalName(element)
  const nsURI = xmlNamespaceURI(element)
  const elementSchema = schema.elements[localName]
  if (!elementSchema) return null
  if (elementSchema.ns) {
    if (nsURI !== elementSchema.ns) return null
  } else if (!isEdmNamespace(nsURI)) return null

  const item = {}
  const attributes = elementSchema.attributes || []

  xmlAttributes(element, (attribute) => {
    const localName = xmlLocalName(attribute)
    const nsURI = xmlNamespaceURI(attribute)
    const value = attribute.value
    if (nsURI === xmlnsNS) return

    let schemaName = null
    if (isEdmNamespace(nsURI) || nsURI === null) schemaName = ''
    else if (nsURI === odataMetaXmlNs) schemaName = 'm:'

    if (schemaName !== null) {
      schemaName += localName
      if (contains(attributes, schemaName)) item[scriptCase(localName)] = value
    }
  })

  xmlChildElements(element, (child) => {
    const localName = xmlLocalName(child)
    const childSchema = getChildSchema(elementSchema, localName)
    if (childSchema) {
      if (childSchema.isArray) {
        let arr = item[childSchema.propertyName]
        if (!arr) arr = item[childSchema.propertyName] = []
        arr.push(parseConceptualModelElement(child))
      } else {
        item[childSchema.propertyName] = parseConceptualModelElement(child)
      }
    }
  })

  if (elementSchema.text) item.text = xmlInnerText(element)
  return item
}

/** 解析元数据文本 */
function metadataParser(handler, text) {
  const doc = xmlParse(text)
  const root = xmlFirstChildElement(doc)
  return parseConceptualModelElement(root) || undefined
}

export const metadataHandler = odataHandler.handler(
  metadataParser,
  null,
  xmlMediaType,
  MAX_DATA_SERVICE_VERSION
)

export {
  schema,
  scriptCase,
  getChildSchema,
  parseConceptualModelElement,
  metadataParser,
}
