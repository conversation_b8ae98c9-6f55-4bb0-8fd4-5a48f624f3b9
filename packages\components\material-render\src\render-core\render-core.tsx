import { computed, defineComponent, resolveDynamicComponent } from 'vue'
import { getComponentByName } from '../utils'
import { ElementEvent } from '../../types'
import { useApisRef, useEventContext } from '../context/event-flow'
import { useEventFlow } from '../hooks/useEventFlow'
import { neRenderCoreProps } from './types'

export default defineComponent({
  name: 'NeRenderCore',
  props: neRenderCoreProps,
  setup(props) {
    useApisRef(props.apis)
    const eventFlow = useEventFlow()
    const { refs } = useEventContext()
    const elProps = computed(() => props.props || {})
    const elements = computed(() => props.elements || [])
    const resolvedComponent = computed(() => {
      return getComponentByName(props.type)
    })
    const dynamicEvents = computed(() => {
      const result: Record<string, (...args: any[]) => void> = {}
      props.events.forEach((evt: ElementEvent) => {
        if (evt.eventName && Array.isArray(evt.actions)) {
          result[evt.eventName] = (arg0, arg1, arg2) => {
            eventFlow?.run(evt.actions, {
              id: props.id,
              clickParams: [arg0, arg1, arg2],
            })
          }
        }
      })
      return result
    })

    const setRef = (el: any) => {
      if (props.id && el) {
        refs[props.id] = el
      }
    }

    return () => {
      const DynamicTag = resolveDynamicComponent(resolvedComponent.value) as any
      const NeRenderCore = resolveDynamicComponent('NeRenderCore') as any
      const defaultSlot = elements.value?.length
        ? () =>
            elements.value.map((child, index) => (
              <NeRenderCore {...child} key={index} />
            ))
        : undefined
      const dynamicSlots = props.slots
        ? Object.fromEntries(
            Object.entries(props.slots).map(([slotName, slotValue]) => [
              slotName,
              () =>
                Array.isArray(slotValue)
                  ? slotValue.map((child: any, index: number) => (
                      <NeRenderCore {...child} key={index} />
                    ))
                  : slotValue,
            ])
          )
        : {}
      return (
        <DynamicTag ref={setRef} {...elProps.value} {...dynamicEvents.value}>
          {{
            default: defaultSlot,
            ...dynamicSlots,
          }}
        </DynamicTag>
      )
    }
  },
})
