/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EnumTypes } from './EnumTypes';

/**
 * Request builder class for operations supported on the {@link EnumTypes} entity.
 */
export class EnumTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EnumTypes<T>, T> {
  /**
   * Returns a request builder for querying all `EnumTypes` entities.
   * @returns A request builder for creating requests to retrieve all `EnumTypes` entities.
   */
  getAll(): GetAllRequestBuilder<EnumTypes<T>, T> {
    return new GetAllRequestBuilder<EnumTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `EnumTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EnumTypes`.
   */
  create(entity: EnumTypes<T>): CreateRequestBuilder<EnumTypes<T>, T> {
    return new CreateRequestBuilder<EnumTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `EnumTypes` entity based on its keys.
   * @param ncid Key property. See {@link EnumTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `EnumTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EnumTypes<T>, T> {
    return new GetByKeyRequestBuilder<EnumTypes<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `EnumTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EnumTypes`.
   */
  update(entity: EnumTypes<T>): UpdateRequestBuilder<EnumTypes<T>, T> {
    return new UpdateRequestBuilder<EnumTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `EnumTypes`.
   * @param ncid Key property. See {@link EnumTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EnumTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<EnumTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EnumTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EnumTypes` by taking the entity as a parameter.
   */
  delete(entity: EnumTypes<T>): DeleteRequestBuilder<EnumTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<EnumTypes<T>, T> {
    return new DeleteRequestBuilder<EnumTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EnumTypes ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
