{"config": {"apiBaseConfig": {"baseURL": "https://api-cn-hangzhou-2.cloud.neuetech.cn"}}, "apis": {"submit": {"url": "/cdp/202512/OdataService/CadFile", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision)"}}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}, "panelFormEditRequestDraw": {"url": "/modeling/202512/OdataService/NeueTwoDimensionDrawings({{ncid}})", "method": "get", "params": {"$expand": "version($expand=revision($expand=lockedBy)),owner,thumbnail,lifecycleStatus"}}}, "elements": [{"id": "div123123", "type": "div", "name": "OData表格", "props": {"style": "padding:16px;background:#ECEEF3;box-sizing:border-box;height:100%;"}, "elements": [{"id": "pro-tab", "type": "pro-tab", "name": "OData表格", "props": {"tabProps": {"tabPosition": "left"}, "tabs": [{"name": "base-info", "label": "基本信息", "elements": [{"id": "PanelForm_er4mn6jbtk1234562", "type": "wg-panel-form", "name": "面板表单", "props": {"config": {"labelWidth": "100px", "title": "属性信息", "span": 12, "gutter": 12, "odataType": "drawing"}, "formItems": [{"name": "基本属性", "key": "base", "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "disabled": false, "required": true, "minLength": 6, "maxLength": 18}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "placeholder": ""}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true}, {"prop": "owner.name", "fieldName": "责任人", "fieldType": "text", "disabled": false, "minLength": 1, "maxLength": 32}, {"prop": "lifecycleStatus.name", "fieldName": "状态", "fieldType": "text", "disabled": true, "valueEnum": {"IN_WORK": "工作中"}}, {"prop": "version.revision.lockedBy.name", "fieldName": "锁定人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "version.revision.lockedAt", "fieldName": "锁定时间", "fieldType": "dateShow", "disabled": true, "dateFormat": {"format": "YYYY-MM-DD HH:mm:ss"}, "minLength": 1, "maxLength": 32}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "rows": 4, "maxLength": 255}]}, {"name": "自身属性", "key": "design", "formItems": []}]}, "elements": [], "events": []}]}]}, "elements": []}]}]}