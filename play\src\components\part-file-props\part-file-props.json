{"config": {"apiBaseConfig": {"baseUrl": ""}}, "apis": {"metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}}, "elements": [{"id": "pro-tab", "type": "pro-tab", "name": "OData表格", "props": {"tabProps": {"tabPosition": "left", "style": "width:100%;padding:16px;box-sizing:border-box"}, "toolbar": {"actions": {}}, "tabs": [{"name": "base-info", "label": "基本信息", "elements": [{"id": "PanelForm_er4mn6jbtk1234562", "type": "wg-panel-form", "name": "面板表单", "props": {"config": {"span": 12, "gutter": 12, "labelWidth": "100px", "title": "零部件属性信息", "showSubmit": true}, "formItems": [{"name": "基本属性", "key": "base", "viewImg": "https://example.com/image1.png", "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101"}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "disabled": false, "minLength": 6, "maxLength": 18, "showWordLimit": true, "ruleId": "RULE_101"}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "ruleId": "RULE_EMAIL"}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "ruleId": "RULE_EMAIL"}, {"prop": "owner", "fieldName": "责任人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入"}, {"prop": "lifecycleStatus.name", "fieldName": "状态", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入状态"}, {"prop": "locked<PERSON>y", "fieldName": "锁定人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入锁定人", "value": "张无珊"}, {"prop": "lockedAt", "fieldName": "锁定时间", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入"}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, {"name": "设计属性", "key": "design", "formItems": [{"prop": "solidSurfaceArea", "fieldName": "实体曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入", "disabled": true}, {"prop": "openSurfaceArea", "fieldName": "开放曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入开放曲面面积", "disabled": true}, {"prop": "volume", "fieldName": "体积", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入体积", "disabled": true, "value": "84464.4"}, {"label": "质量", "prop": "mass", "fieldName": "质量", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入质量", "disabled": true, "value": "0.660512"}, {"prop": "material", "fieldName": "材料", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入材料", "disabled": true}, {"prop": "centerOfGravity", "fieldName": "重心", "fieldType": "text", "minLength": 1, "maxLength": 32, "ruleId": "RULE_101", "placeholder": "请输入重心", "disabled": true}]}]}, "elements": [], "events": []}]}]}, "elements": []}]}