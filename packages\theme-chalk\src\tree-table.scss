@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(tree-table) {
  @include set-component-css-var('tree-table', $tree-table);
}

@include b(tree-table) {
  .el-table {
    .el-table__row {
      .el-table__indent {
        padding-left: getCssVar('tree-table', 'indent');
      }
      
      .el-table__expand-icon {
        color: getCssVar('tree-table', 'expand-icon-color');
        transition: transform getCssVar('transition-duration');
        
        &.el-table__expand-icon--expanded {
          transform: rotate(90deg);
        }
      }
    }
    
    // 树形表格行的悬停效果
    .el-table__row:hover {
      background-color: getCssVar('tree-table', 'row-hover-bg-color');
    }
    
    // 选中行的样式
    .el-table__row.current-row {
      background-color: getCssVar('tree-table', 'row-current-bg-color');
    }
  }
  
  // 加载状态
  &.is-loading {
    .el-table__body-wrapper {
      opacity: 0.6;
    }
  }
}
