/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { BusinessObjectRevisionNumberCodes } from './BusinessObjectRevisionNumberCodes';
import { BusinessObjectRevisionNumberCodesRequestBuilder } from './BusinessObjectRevisionNumberCodesRequestBuilder';
import { BusinessObjectRevisionNumberRulesApi } from './BusinessObjectRevisionNumberRulesApi';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField,
  OneToOneLink
} from '@sap-cloud-sdk/odata-v4';
export class BusinessObjectRevisionNumberCodesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements
    EntityApi<BusinessObjectRevisionNumberCodes<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): BusinessObjectRevisionNumberCodesApi<DeSerializersT> {
    return new BusinessObjectRevisionNumberCodesApi(deSerializers);
  }

  private navigationPropertyFields!: {
    /**
     * Static representation of the one-to-one navigation property {@link ruleCodeRef} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    RULE_CODE_REF: OneToOneLink<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT,
      BusinessObjectRevisionNumberRulesApi<DeSerializersT>
    >;
  };

  _addNavigationProperties(
    linkedApis: [BusinessObjectRevisionNumberRulesApi<DeSerializersT>]
  ): this {
    this.navigationPropertyFields = {
      RULE_CODE_REF: new OneToOneLink('ruleCodeRef', this, linkedApis[0])
    };
    return this;
  }

  entityConstructor = BusinessObjectRevisionNumberCodes;

  requestBuilder(): BusinessObjectRevisionNumberCodesRequestBuilder<DeSerializersT> {
    return new BusinessObjectRevisionNumberCodesRequestBuilder<DeSerializersT>(
      this
    );
  }

  entityBuilder(): EntityBuilderType<
    BusinessObjectRevisionNumberCodes<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<
    BusinessObjectRevisionNumberCodes<DeSerializersT>,
    DeSerializersT,
    NullableT
  > {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<
    typeof BusinessObjectRevisionNumberCodes,
    DeSerializersT
  >;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(
        BusinessObjectRevisionNumberCodes,
        this.deSerializers
      );
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    REVISION_ORDER: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.Int32',
      false,
      true
    >;
    REVISION_CODE: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    NCID: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      BusinessObjectRevisionNumberCodes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    /**
     * Static representation of the one-to-one navigation property {@link ruleCodeRef} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    RULE_CODE_REF: OneToOneLink<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT,
      BusinessObjectRevisionNumberRulesApi<DeSerializersT>
    >;
    ALL_FIELDS: AllFields<BusinessObjectRevisionNumberCodes<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link revisionOrder} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        REVISION_ORDER: fieldBuilder.buildEdmTypeField(
          'revisionOrder',
          'Edm.Int32',
          false
        ),
        /**
         * Static representation of the {@link revisionCode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        REVISION_CODE: fieldBuilder.buildEdmTypeField(
          'revisionCode',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', BusinessObjectRevisionNumberCodes)
      };
    }

    return this._schema;
  }
}
