<template>
  <widget-wrapper>
    <el-dialog
      v-model="dialogVisible"
      :title="props.config.title || '业务类型'"
      :width="props.config.width || '500'"
      :height="props.config.height || 'auto'"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="ne-dialog-form"
    >
      <widget-basic-form
        ref="formRef"
        :form-items="props.formItems"
        :config="props.config"
        :dialog-form-edit-request-asm="props.dialogFormEditRequestAsm"
        :dialog-form-edit-request-part="props.dialogFormEditRequestPart"
        :dialog-form-submit-asm="props.dialogFormSubmitAsm"
        :dialog-form-submit-part="props.dialogFormSubmitPart"
        :dialog-form-update-asm="props.dialogFormUpdateAsm"
        :dialog-form-update-part="props.dialogFormUpdatePart"
        :business-type-request="props.businessTypeRequest"
        :dialog-form-submit-relation="props.dialogFormSubmitRelation"
        :dialog-form-edit-request-relation="props.dialogFormEditRequestRelation"
        :dialog-form-update-relation="props.dialogFormUpdateRelationNcid"
        :dialog-form-edit-request="dialogFormEditRequest"
        @form-submit="handleSubmit"
        @form-reset="handleReset"
        @form-cancel="handleCancel"
      />
    </el-dialog>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { type PropType, nextTick, provide, ref, watch } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import { useSysParamsRef } from '@neue-plus/components/material-render/src/context/event-flow'
import WidgetBasicForm from '../../basic-form/src/form.vue'

defineOptions({
  name: 'DialogForm',
  inheritAttrs: false,
})

const emit = defineEmits(['refresh', 'formSubmit', 'formReset', 'formCancel'])

const props = defineProps({
  config: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  formItems: {
    type: Array as PropType<any>,
    default: () => [],
  },
  dialogFormEditRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormEditRequestAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormEditRequestPart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormSubmitAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormSubmitPart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormUpdateAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormUpdatePart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  // 属性关系编辑
  dialogFormSubmitRelation: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormEditRequestRelation: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormUpdateRelationNcid: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  businessTypeRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
})

const dialogVisible = ref(false)
const formRef = ref()
const editData = ref<any>({})
const inited = ref(false)
provide('dialogVisible', dialogVisible)
// const editData = ref<any>({})

const handleClose = () => {
  dialogVisible.value = false
  close()
}

// const apiRefs = useApisRef()
const sysParamsRef = useSysParamsRef()
const handleSubmit = (res: any) => {
  // await apiRefs['dialogFormSubmit']?.({ data: res })
  emit('formSubmit', res)
  emit('refresh')
  console.log('dialog-submit-refresh', res)
  close()
}

const handleReset = (res: any) => {
  emit('formReset', res)
}
const handleCancel = (res: any) => {
  close()
  dialogVisible.value = false
  emit('formCancel', res)
}

const open = (eData: any) => {
  console.log('open==>', eData)
  dialogVisible.value = true
  inited.value = true
  const target = eData?.ncid
    ? eData
    : sysParamsRef.value?.ncid
    ? sysParamsRef.value
    : {}
  if (target) editData.value = { ...target }
  console.log('open==>', editData.value, target)
}

const close = () => {
  dialogVisible.value = false
  inited.value = false
}

// watchEffect(() => {
//   if (
//     dialogVisible.value &&
//     editData.value?.ncid &&
//     formRef.value &&
//     inited.value
//   ) {
//     console.log('initEdit==>', editData.value)
//     formRef.value.initEdit({ ...editData.value })
//   }
// })

watch(
  () => dialogVisible.value,
  (visible: boolean) => {
    if (visible) {
      // 强制换引用
      editData.value = { ...editData.value }
      nextTick(() => {
        if (formRef.value && editData.value?.ncid && inited.value) {
          formRef.value.initEdit(editData.value)
        }
      })
    }
  },
  { immediate: true }
)

defineExpose({
  open,
  close,
})
</script>

<style scoped></style>
