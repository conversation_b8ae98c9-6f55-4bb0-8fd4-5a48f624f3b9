<template>
  <widget-wrapper>
    <el-dialog
      v-model="dialogVisible"
      :title="props.config.title || '业务类型'"
      :width="props.config.width || '500'"
      :height="props.config.height || 'auto'"
      :before-close="handleClose"
    >
      <widget-basic-form
        :form-items="props.formItems"
        :config="props.config"
        :init-request="props.initRequest"
        @form-submit="handleSubmit"
        @form-reset="handleReset"
        @form-cancel="handleCancel"
      />
    </el-dialog>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { type PropType, ref } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import { useApisRef } from '@neue-plus/components/material-render/src/context/event-flow'
import WidgetBasicForm from '../../basic-form/src/form.vue'
import type { dialogConfig } from './dialog-form'
import type { FormConfig } from '../../basic-form/src/form'

interface DialogFormWidgetConfig extends FormConfig, dialogConfig {}

defineOptions({
  name: 'DialogForm',
  inheritAttrs: false,
})

const emit = defineEmits(['refresh', 'formSubmit', 'formReset', 'formCancel'])

const props = defineProps({
  config: {
    type: Object as PropType<DialogFormWidgetConfig>,
    default: () => ({}),
  },
  formItems: {
    type: Array as PropType<any>,
    default: () => [],
  },
  initRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  submit: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
})

const dialogVisible = ref(false)
// const editData = ref<any>({})

const handleClose = () => {
  dialogVisible.value = false
  close()
}

const apiRefs = useApisRef()
const handleSubmit = async (res: any) => {
  await apiRefs['submit']?.({ data: res })
  emit('refresh')
  emit('formSubmit', res)
  close()
}

const handleReset = (res: any) => {
  emit('formReset', res)
}
const handleCancel = (res: any) => {
  close()
  dialogVisible.value = false
  emit('formCancel', res)
}

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

defineExpose({
  open,
  close,
})
</script>

<style scoped></style>
