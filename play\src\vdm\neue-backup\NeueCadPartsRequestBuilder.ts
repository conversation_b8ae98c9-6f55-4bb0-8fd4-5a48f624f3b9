/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { NeueCadParts } from './NeueCadParts';

/**
 * Request builder class for operations supported on the {@link NeueCadParts} entity.
 */
export class NeueCadPartsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<NeueCadParts<T>, T> {
  /**
   * Returns a request builder for querying all `NeueCadParts` entities.
   * @returns A request builder for creating requests to retrieve all `NeueCadParts` entities.
   */
  getAll(): GetAllRequestBuilder<NeueCadParts<T>, T> {
    return new GetAllRequestBuilder<NeueCadParts<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `NeueCadParts` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `NeueCadParts`.
   */
  create(entity: NeueCadParts<T>): CreateRequestBuilder<NeueCadParts<T>, T> {
    return new CreateRequestBuilder<NeueCadParts<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `NeueCadParts` entity based on its keys.
   * @param ncid Key property. See {@link NeueCadParts.ncid}.
   * @returns A request builder for creating requests to retrieve one `NeueCadParts` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<NeueCadParts<T>, T> {
    return new GetByKeyRequestBuilder<NeueCadParts<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `NeueCadParts`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `NeueCadParts`.
   */
  update(entity: NeueCadParts<T>): UpdateRequestBuilder<NeueCadParts<T>, T> {
    return new UpdateRequestBuilder<NeueCadParts<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `NeueCadParts`.
   * @param ncid Key property. See {@link NeueCadParts.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `NeueCadParts`.
   */
  delete(ncid: string): DeleteRequestBuilder<NeueCadParts<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `NeueCadParts`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `NeueCadParts` by taking the entity as a parameter.
   */
  delete(entity: NeueCadParts<T>): DeleteRequestBuilder<NeueCadParts<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<NeueCadParts<T>, T> {
    return new DeleteRequestBuilder<NeueCadParts<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof NeueCadParts
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
