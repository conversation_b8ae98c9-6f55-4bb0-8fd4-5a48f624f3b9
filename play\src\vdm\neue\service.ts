/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { SchemasApi } from './SchemasApi'
import { EntityTypesApi } from './EntityTypesApi'
import { ComplexTypesApi } from './ComplexTypesApi'
import { EnumTypesApi } from './EnumTypesApi'
import { TypeDefinitionsApi } from './TypeDefinitionsApi'
import { StructuralPropertiesApi } from './StructuralPropertiesApi'
import { NavigationPropertiesApi } from './NavigationPropertiesApi'
import { ActionsApi } from './ActionsApi'
import { FunctionsApi } from './FunctionsApi'
import { EntityContainersApi } from './EntityContainersApi'
import { EntitySetsApi } from './EntitySetsApi'
import { SingletonsApi } from './SingletonsApi'
import { NavigationPropertyBindingsApi } from './NavigationPropertyBindingsApi'
import { ActionImportsApi } from './ActionImportsApi'
import { FunctionImportsApi } from './FunctionImportsApi'
import { BusinessObjectRevisionNumberRulesApi } from './BusinessObjectRevisionNumberRulesApi'
import { BusinessObjectRevisionNumberCodesApi } from './BusinessObjectRevisionNumberCodesApi'
import { LifecycleStatusesApi } from './LifecycleStatusesApi'
import { LifecycleStatusStrategiesApi } from './LifecycleStatusStrategiesApi'
import { FromOrToTypesApi } from './FromOrToTypesApi'
import { RootRelationTypesApi } from './RootRelationTypesApi'
import { EntityTypeUnionUniqsApi } from './EntityTypeUnionUniqsApi'
import { EntityTypeUnionUniqPropertiesApi } from './EntityTypeUnionUniqPropertiesApi'
import { FilesApi } from './FilesApi'
import { IdiFilesApi } from './IdiFilesApi'
import { EntityTypePropertyOverridesApi } from './EntityTypePropertyOverridesApi'
import { MappingConfigsApi } from './MappingConfigsApi'
import { RelCadBomWithTypesApi } from './RelCadBomWithTypesApi'
import { RelCadPartTwoDimensionDrawingWithTypesApi } from './RelCadPartTwoDimensionDrawingWithTypesApi'
import { RelCadFileFileWithTypesApi } from './RelCadFileFileWithTypesApi'
import { RelCadFileFileRelationsApi } from './RelCadFileFileRelationsApi'
import { RelCadPartTwoDimensionDrawingRelationsApi } from './RelCadPartTwoDimensionDrawingRelationsApi'
import { RelCadBomRelationsApi } from './RelCadBomRelationsApi'
import { CadPartsApi } from './CadPartsApi'
import { NeueCadAsmsApi } from './NeueCadAsmsApi'
import { NeueCadPartsApi } from './NeueCadPartsApi'
import { NeueTwoDimensionDrawingsApi } from './NeueTwoDimensionDrawingsApi'
import { BigNumber } from 'bignumber.js'
import { Moment, Duration } from 'moment'
import {
  defaultDeSerializers,
  DeSerializers,
  DefaultDeSerializers,
  mergeDefaultDeSerializersWith,
  Time,
} from '@sap-cloud-sdk/odata-v4'
import { batch, changeset } from './BatchRequest'

export function neue<
  BinaryT = string,
  BooleanT = boolean,
  ByteT = number,
  DecimalT = BigNumber,
  DoubleT = number,
  FloatT = number,
  Int16T = number,
  Int32T = number,
  Int64T = BigNumber,
  GuidT = string,
  SByteT = number,
  SingleT = number,
  StringT = string,
  AnyT = any,
  DateTimeOffsetT = Moment,
  DateT = Moment,
  DurationT = Duration,
  TimeOfDayT = Time,
  EnumT = any
>(
  deSerializers: Partial<
    DeSerializers<
      BinaryT,
      BooleanT,
      ByteT,
      DecimalT,
      DoubleT,
      FloatT,
      Int16T,
      Int32T,
      Int64T,
      GuidT,
      SByteT,
      SingleT,
      StringT,
      AnyT,
      DateTimeOffsetT,
      DateT,
      DurationT,
      TimeOfDayT,
      EnumT
    >
  > = defaultDeSerializers as any
): Neue<
  DeSerializers<
    BinaryT,
    BooleanT,
    ByteT,
    DecimalT,
    DoubleT,
    FloatT,
    Int16T,
    Int32T,
    Int64T,
    GuidT,
    SByteT,
    SingleT,
    StringT,
    AnyT,
    DateTimeOffsetT,
    DateT,
    DurationT,
    TimeOfDayT,
    EnumT
  >
> {
  return new Neue(mergeDefaultDeSerializersWith(deSerializers))
}
class Neue<DeSerializersT extends DeSerializers = DefaultDeSerializers> {
  private apis: Record<string, any> = {}
  private deSerializers: DeSerializersT

  constructor(deSerializers: DeSerializersT) {
    this.deSerializers = deSerializers
  }

  private initApi(key: string, entityApi: any): any {
    if (!this.apis[key]) {
      this.apis[key] = entityApi._privateFactory(this.deSerializers)
    }
    return this.apis[key]
  }

  get schemasApi(): SchemasApi<DeSerializersT> {
    return this.initApi('schemasApi', SchemasApi)
  }

  get entityTypesApi(): EntityTypesApi<DeSerializersT> {
    return this.initApi('entityTypesApi', EntityTypesApi)
  }

  get complexTypesApi(): ComplexTypesApi<DeSerializersT> {
    return this.initApi('complexTypesApi', ComplexTypesApi)
  }

  get enumTypesApi(): EnumTypesApi<DeSerializersT> {
    return this.initApi('enumTypesApi', EnumTypesApi)
  }

  get typeDefinitionsApi(): TypeDefinitionsApi<DeSerializersT> {
    return this.initApi('typeDefinitionsApi', TypeDefinitionsApi)
  }

  get structuralPropertiesApi(): StructuralPropertiesApi<DeSerializersT> {
    return this.initApi('structuralPropertiesApi', StructuralPropertiesApi)
  }

  get navigationPropertiesApi(): NavigationPropertiesApi<DeSerializersT> {
    return this.initApi('navigationPropertiesApi', NavigationPropertiesApi)
  }

  get actionsApi(): ActionsApi<DeSerializersT> {
    return this.initApi('actionsApi', ActionsApi)
  }

  get functionsApi(): FunctionsApi<DeSerializersT> {
    return this.initApi('functionsApi', FunctionsApi)
  }

  get entityContainersApi(): EntityContainersApi<DeSerializersT> {
    return this.initApi('entityContainersApi', EntityContainersApi)
  }

  get entitySetsApi(): EntitySetsApi<DeSerializersT> {
    return this.initApi('entitySetsApi', EntitySetsApi)
  }

  get singletonsApi(): SingletonsApi<DeSerializersT> {
    return this.initApi('singletonsApi', SingletonsApi)
  }

  get navigationPropertyBindingsApi(): NavigationPropertyBindingsApi<DeSerializersT> {
    return this.initApi(
      'navigationPropertyBindingsApi',
      NavigationPropertyBindingsApi
    )
  }

  get actionImportsApi(): ActionImportsApi<DeSerializersT> {
    return this.initApi('actionImportsApi', ActionImportsApi)
  }

  get functionImportsApi(): FunctionImportsApi<DeSerializersT> {
    return this.initApi('functionImportsApi', FunctionImportsApi)
  }

  get businessObjectRevisionNumberRulesApi(): BusinessObjectRevisionNumberRulesApi<DeSerializersT> {
    return this.initApi(
      'businessObjectRevisionNumberRulesApi',
      BusinessObjectRevisionNumberRulesApi
    )
  }

  get businessObjectRevisionNumberCodesApi(): BusinessObjectRevisionNumberCodesApi<DeSerializersT> {
    const api = this.initApi(
      'businessObjectRevisionNumberCodesApi',
      BusinessObjectRevisionNumberCodesApi
    )
    const linkedApis = [
      this.initApi(
        'businessObjectRevisionNumberRulesApi',
        BusinessObjectRevisionNumberRulesApi
      ),
    ]
    api._addNavigationProperties(linkedApis)
    return api
  }

  get lifecycleStatusesApi(): LifecycleStatusesApi<DeSerializersT> {
    return this.initApi('lifecycleStatusesApi', LifecycleStatusesApi)
  }

  get lifecycleStatusStrategiesApi(): LifecycleStatusStrategiesApi<DeSerializersT> {
    return this.initApi(
      'lifecycleStatusStrategiesApi',
      LifecycleStatusStrategiesApi
    )
  }

  get fromOrToTypesApi(): FromOrToTypesApi<DeSerializersT> {
    return this.initApi('fromOrToTypesApi', FromOrToTypesApi)
  }

  get rootRelationTypesApi(): RootRelationTypesApi<DeSerializersT> {
    return this.initApi('rootRelationTypesApi', RootRelationTypesApi)
  }

  get entityTypeUnionUniqsApi(): EntityTypeUnionUniqsApi<DeSerializersT> {
    return this.initApi('entityTypeUnionUniqsApi', EntityTypeUnionUniqsApi)
  }

  get entityTypeUnionUniqPropertiesApi(): EntityTypeUnionUniqPropertiesApi<DeSerializersT> {
    return this.initApi(
      'entityTypeUnionUniqPropertiesApi',
      EntityTypeUnionUniqPropertiesApi
    )
  }

  get filesApi(): FilesApi<DeSerializersT> {
    return this.initApi('filesApi', FilesApi)
  }

  get idiFilesApi(): IdiFilesApi<DeSerializersT> {
    return this.initApi('idiFilesApi', IdiFilesApi)
  }

  get entityTypePropertyOverridesApi(): EntityTypePropertyOverridesApi<DeSerializersT> {
    return this.initApi(
      'entityTypePropertyOverridesApi',
      EntityTypePropertyOverridesApi
    )
  }

  get mappingConfigsApi(): MappingConfigsApi<DeSerializersT> {
    return this.initApi('mappingConfigsApi', MappingConfigsApi)
  }

  get relCadBomWithTypesApi(): RelCadBomWithTypesApi<DeSerializersT> {
    return this.initApi('relCadBomWithTypesApi', RelCadBomWithTypesApi)
  }

  get relCadPartTwoDimensionDrawingWithTypesApi(): RelCadPartTwoDimensionDrawingWithTypesApi<DeSerializersT> {
    return this.initApi(
      'relCadPartTwoDimensionDrawingWithTypesApi',
      RelCadPartTwoDimensionDrawingWithTypesApi
    )
  }

  get relCadFileFileWithTypesApi(): RelCadFileFileWithTypesApi<DeSerializersT> {
    return this.initApi(
      'relCadFileFileWithTypesApi',
      RelCadFileFileWithTypesApi
    )
  }

  get relCadFileFileRelationsApi(): RelCadFileFileRelationsApi<DeSerializersT> {
    return this.initApi(
      'relCadFileFileRelationsApi',
      RelCadFileFileRelationsApi
    )
  }

  get relCadPartTwoDimensionDrawingRelationsApi(): RelCadPartTwoDimensionDrawingRelationsApi<DeSerializersT> {
    return this.initApi(
      'relCadPartTwoDimensionDrawingRelationsApi',
      RelCadPartTwoDimensionDrawingRelationsApi
    )
  }

  get relCadBomRelationsApi(): RelCadBomRelationsApi<DeSerializersT> {
    return this.initApi('relCadBomRelationsApi', RelCadBomRelationsApi)
  }

  get cadPartsApi(): CadPartsApi<DeSerializersT> {
    return this.initApi('cadPartsApi', CadPartsApi)
  }

  get neueCadAsmsApi(): NeueCadAsmsApi<DeSerializersT> {
    return this.initApi('neueCadAsmsApi', NeueCadAsmsApi)
  }

  get neueCadPartsApi(): NeueCadPartsApi<DeSerializersT> {
    return this.initApi('neueCadPartsApi', NeueCadPartsApi)
  }

  get neueTwoDimensionDrawingsApi(): NeueTwoDimensionDrawingsApi<DeSerializersT> {
    return this.initApi(
      'neueTwoDimensionDrawingsApi',
      NeueTwoDimensionDrawingsApi
    )
  }

  get batch(): typeof batch {
    return batch
  }

  get changeset(): typeof changeset {
    return changeset
  }
}
