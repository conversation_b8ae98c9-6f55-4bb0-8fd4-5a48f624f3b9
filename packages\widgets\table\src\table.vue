<template>
  <widget-wrapper>
    <NeProTable :columns="columns" :data="data" @row-click="rowClick" />
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NeProTable from '@neue-plus/components/pro-table'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import { widgetTableProps } from './type'

defineOptions({
  name: 'WidgetTable',
  inheritAttrs: false,
})
defineProps({ ...widgetTableProps })
const columns = ref([])
const data = ref([])
const emit = defineEmits(['rowClick'])

function rowClick(row: any) {
  emit('rowClick', row)
}

defineExpose({
  describe: 'describe', //保留字段
})
</script>
