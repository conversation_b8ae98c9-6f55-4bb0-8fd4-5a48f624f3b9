/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { CadParts } from './CadParts';
import { CadPartsRequestBuilder } from './CadPartsRequestBuilder';
import { PartType } from './PartType';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class CadPartsApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<CadParts<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): CadPartsApi<DeSerializersT> {
    return new CadPartsApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = CadParts;

  requestBuilder(): CadPartsRequestBuilder<DeSerializersT> {
    return new CadPartsRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<CadParts<DeSerializersT>, DeSerializersT> {
    return entityBuilder<CadParts<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<CadParts<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof CadParts, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(CadParts, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    GRAVITY_CENTER: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    MASS: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.Decimal',
      true,
      true
    >;
    MATERIAL: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    OPEN_SURFACE_AREA: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.Decimal',
      true,
      true
    >;
    PART_TYPE: EnumField<
      CadParts<DeSerializers>,
      DeSerializersT,
      PartType,
      false,
      true
    >;
    SOLID_SURFACE_AREA: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.Decimal',
      true,
      true
    >;
    VOLUME: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.Decimal',
      true,
      true
    >;
    SUBMIT_DESCRIPTION: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    CODE: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    NAME: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      CadParts<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      CadParts<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<CadParts<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link gravityCenter} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        GRAVITY_CENTER: fieldBuilder.buildEdmTypeField(
          'gravityCenter',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link mass} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MASS: fieldBuilder.buildEdmTypeField('mass', 'Edm.Decimal', true),
        /**
         * Static representation of the {@link material} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MATERIAL: fieldBuilder.buildEdmTypeField(
          'material',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link openSurfaceArea} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        OPEN_SURFACE_AREA: fieldBuilder.buildEdmTypeField(
          'openSurfaceArea',
          'Edm.Decimal',
          true
        ),
        /**
         * Static representation of the {@link partType} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        PART_TYPE: fieldBuilder.buildEnumField('partType', PartType, false),
        /**
         * Static representation of the {@link solidSurfaceArea} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SOLID_SURFACE_AREA: fieldBuilder.buildEdmTypeField(
          'solidSurfaceArea',
          'Edm.Decimal',
          true
        ),
        /**
         * Static representation of the {@link volume} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        VOLUME: fieldBuilder.buildEdmTypeField('volume', 'Edm.Decimal', true),
        /**
         * Static representation of the {@link submitDescription} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SUBMIT_DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'submitDescription',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link code} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CODE: fieldBuilder.buildEdmTypeField('code', 'Edm.String', false),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', true),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', CadParts)
      };
    }

    return this._schema;
  }
}
