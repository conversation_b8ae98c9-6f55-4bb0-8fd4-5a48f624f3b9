# 常量数据文档

本文档描述了 `play/src/constant/index.ts` 文件中定义的各种常量数据结构。

## 概述

该文件包含了用于测试和演示的各种数据结构，主要用于 Vue 组件的开发和测试。

## 导出的常量

### 1. `initialData`

**类型**: `Array<Object>`  
**用途**: 简单的表格测试数据

```typescript
interface InitialDataItem {
  name: string // 姓名
  age: number // 年龄
  address: string // 地址
}
```

**示例数据**:

- Alice, 24, New York
- Bob, 30, Los Angeles
- Charlie, 28, Chicago

### 2. `tableData`

**类型**: `Array<CADPartObject>`  
**用途**: CAD 零部件数据，用于 BOM 表格展示

#### 主要字段结构

| 字段名            | 类型   | 描述                           |
| ----------------- | ------ | ------------------------------ |
| `ncid`            | string | 零部件唯一标识符               |
| `partNo`          | string | 零部件编号                     |
| `partName`        | string | 零部件名称                     |
| `partDescription` | string | 零部件描述                     |
| `partType`        | string | 零部件类型 (NEUE_ASM/NEUE_PRT) |
| `version`         | string | 版本号                         |
| `revision`        | string | 修订版本                       |
| `status`          | string | 状态 (INWORK/ACTIVE)           |
| `mass`            | number | 质量                           |
| `volume`          | number | 体积                           |
| `material`        | string | 材料                           |

#### 嵌套对象结构

- **`createdBy`/`modifiedBy`/`owner`**: 用户信息对象

  - `ncid`: 用户 ID
  - `name`: 用户名
  - `email`: 邮箱
  - `phone`: 电话
  - `lifecycleState`: 生命周期状态

- **`master`**: 主版本信息

  - `ncid`: 主版本 ID
  - `createdAt`: 创建时间
  - `modifiedAt`: 修改时间
  - `schemaVersion`: 模式版本

- **`cadboms`**: CAD BOM 关系数组
  - `source`: 源零部件
  - `target`: 目标零部件
  - `instanceName`: 实例名称
  - `quantity`: 数量
  - `transformationMatrix`: 变换矩阵

### 3. `pageData`

**类型**: `PageConfiguration`  
**用途**: 页面配置数据，用于动态页面渲染

#### 配置结构

```typescript
interface PageConfiguration {
  config: {
    configProvider: object // 全局配置
  }
  events: {
    // 事件定义
    [eventName: string]: {
      name: string // 事件名称
      params: string[] // 参数列表
      body: string // 事件处理代码
    }
  }
  apis: {
    // API 配置
    [apiName: string]: {
      url: string // 请求URL
      method: string // 请求方法
      params?: object // 请求参数
      body?: object // 请求体
      map?: object // 数据映射
    }
  }
  elements: ElementConfig[] // 页面元素配置
}
```

#### 元素配置 (`elements`)

每个元素包含以下基本结构：

| 字段       | 类型   | 描述         |
| ---------- | ------ | ------------ |
| `id`       | string | 元素唯一标识 |
| `type`     | string | 元素类型     |
| `name`     | string | 元素名称     |
| `props`    | object | 元素属性     |
| `events`   | array  | 事件配置     |
| `elements` | array  | 子元素       |

#### 支持的元素类型

- **`widget-container`**: 容器组件
- **`bom-table`**: BOM 表格组件
- **`widget-table`**: 通用表格组件
- **`drawer`**: 抽屉组件
- **`tabs`**: 标签页组件
- **`tab-pane`**: 标签页面板

### 4. `bomTableConfig`

**类型**: `PageConfiguration`  
**用途**: BOM 表格专用配置，结构与 `pageData` 类似

#### 特殊配置

- **表格列配置**: 包含零部件编号、名称、实例名称、版本号、状态等列
- **数据源**: 使用内联数据或 API 请求
- **值类型映射**: 支持 avatar、dateTime 等特殊显示类型

## 数据特点

### 时间格式

所有时间字段使用 ISO 8601 格式：`YYYY-MM-DDTHH:mm:ss.sssZ`

### 标识符格式

NCID 格式：`ncid1.{type}.{domain}..{uuid}`

### 状态枚举

- **生命周期状态**: `ACTIVE`, `INACTIVE`
- **工作状态**: `INWORK`, `RELEASED`
- **锁定状态**: `LOCKED`, `UNLOCKED`
- **检入状态**: `CHECKED_IN`, `CHECKED_OUT`

## 使用示例

```typescript
import { initialData, tableData, pageData, bomTableConfig } from './constant'

// 使用简单表格数据
const simpleTable = initialData

// 使用 CAD 零部件数据
const cadParts = tableData

// 使用页面配置
const pageConfig = pageData

// 使用 BOM 表格配置
const bomConfig = bomTableConfig
```

## 注意事项

1. **数据完整性**: 所有嵌套对象都包含完整的字段信息
2. **类型安全**: 建议配合 TypeScript 接口使用
3. **测试用途**: 这些数据主要用于开发和测试，生产环境应使用实际 API 数据
4. **版本兼容**: 注意 `schemaVersion` 字段，确保数据结构兼容性

## 相关文件

- 组件定义: `packages/components/`
- 类型定义: `packages/types/`
- API 接口: `packages/api/`
