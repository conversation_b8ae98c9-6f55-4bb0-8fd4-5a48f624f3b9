import { keys } from 'lodash-unified'
import { ElDatePicker, ElInputNumber } from 'element-plus'
import { ValueType } from '@neue-plus/components/pro-table'
import NeSelect from '../../select'
import NeCheckboxGroup from '../../checkbox-group'
import NeDateRange from '../../date-range'
import NeInput from '../../input'
import { ProFormItemProps } from './types'

export function useComponentMap() {
  const getComponentMap = (formItem: ProFormItemProps) => {
    const { valueType, valueEnum } = formItem
    if (valueEnum) {
      return NeSelect
    }
    let component: any = NeInput
    switch (valueType) {
      case 'text':
      case 'input':
        component = NeInput
        break
      case 'select':
        component = NeSelect
        break
      case 'checkbox':
        component = NeCheckboxGroup
        break
      case 'dateRange':
      case 'time':
        component = NeDateRange
        break
      case 'date':
        component = ElDatePicker
        break
      case 'switch':
        component = NeInput
        break
      case 'digit':
        component = ElInputNumber
        break
      default:
        component = NeInput
        break
    }
    return component
  }

  // 获取默认值
  const getDefaultValue = (type?: ValueType) => {
    switch (type) {
      case 'radio':
      case 'checkbox':
      case 'dateRange':
        return []
      case 'switch':
        return false
      case 'digit':
        return 0
      default:
        return ''
    }
  }
  const getFormItemProps = (formItem: ProFormItemProps) => {
    const { valueType, placeholder, valueEnum = {}, fieldProps = {} } = formItem
    let props: any = { placeholder, ...fieldProps }
    const type = valueEnum ? 'select' : valueType
    switch (type) {
      case 'select':
        props = {
          ...props,
          placeholder,
          options: keys(valueEnum).map((key) => ({
            label: valueEnum[key].text,
            value: key,
          })),
        }
        break
      case 'text':
      case 'input':
      case 'switch':
      case 'digit':
      case 'checkbox':
        props = { ...props }
        break
      case 'time':
      case 'dateRange':
      case 'date':
        props = {
          ...props,
          placeholder,
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        }
        break
    }
    return props
  }
  return {
    getComponentMap,
    getDefaultValue,
    getFormItemProps,
  }
}
