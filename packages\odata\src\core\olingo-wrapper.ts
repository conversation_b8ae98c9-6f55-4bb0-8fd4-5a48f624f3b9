/**
 * Olingo OData4 JS 库的 TypeScript 包装器
 */

// 导入 Olingo 库（从 utils 包中）
// @ts-ignore - Olingo 库没有 TypeScript 类型定义
const odatajs = require('@neue-plus/utils/olingo-odata4-js')

import type {
  RequestConfig,
  Response,
  ODataQueryOptions,
  ODataResponse,
} from '../types'

/**
 * Olingo 请求选项
 */
interface OlingoRequestOptions {
  requestUri: string
  method?: string
  headers?: Record<string, string>
  data?: any
  user?: string
  password?: string
  enableJsonpCallback?: boolean
  callbackParameterName?: string
  formatQueryString?: string
  recognizeDates?: boolean
  maxDataServiceVersion?: string
  dataServiceVersion?: string
}

/**
 * Olingo 响应对象
 */
interface OlingoResponse {
  data: any
  statusCode: number
  statusText: string
  headers: Record<string, string>
  requestUri: string
}

/**
 * Olingo 客户端包装器类
 */
export class OlingoWrapper {
  private baseUrl: string
  private defaultHeaders: Record<string, string>

  constructor(baseUrl: string, defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾斜杠
    this.defaultHeaders = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'OData-MaxVersion': '4.0',
      'OData-Version': '4.0',
      ...defaultHeaders,
    }
  }

  /**
   * 构建完整的请求 URL
   */
  private buildUrl(path: string, queryOptions?: ODataQueryOptions): string {
    const url = `${this.baseUrl}/${path.replace(/^\//, '')}`

    if (!queryOptions) {
      return url
    }

    const params = new URLSearchParams()

    // 处理 OData 查询选项
    if (queryOptions.$select?.length) {
      params.append('$select', queryOptions.$select.join(','))
    }

    if (queryOptions.$expand?.length) {
      params.append('$expand', queryOptions.$expand.join(','))
    }

    if (queryOptions.$filter) {
      params.append('$filter', queryOptions.$filter)
    }

    if (queryOptions.$orderby) {
      params.append('$orderby', queryOptions.$orderby)
    }

    if (typeof queryOptions.$top === 'number') {
      params.append('$top', queryOptions.$top.toString())
    }

    if (typeof queryOptions.$skip === 'number') {
      params.append('$skip', queryOptions.$skip.toString())
    }

    if (queryOptions.$count) {
      params.append('$count', 'true')
    }

    if (queryOptions.$search) {
      params.append('$search', queryOptions.$search)
    }

    if (queryOptions.$format) {
      params.append('$format', queryOptions.$format)
    }

    const queryString = params.toString()
    return queryString ? `${url}?${queryString}` : url
  }

  /**
   * 转换请求配置为 Olingo 格式
   */
  private toOlingoRequest(config: RequestConfig): OlingoRequestOptions {
    const url = config.url.startsWith('http')
      ? config.url
      : this.buildUrl(config.url)

    return {
      requestUri: url,
      method: config.method || 'GET',
      headers: {
        ...this.defaultHeaders,
        ...config.headers,
      },
      data: config.data,
      recognizeDates: true,
      maxDataServiceVersion: '4.0',
      dataServiceVersion: '4.0',
    }
  }

  /**
   * 转换 Olingo 响应为标准格式
   */
  private fromOlingoResponse(
    olingoResponse: OlingoResponse,
    originalConfig: RequestConfig
  ): Response {
    return {
      data: olingoResponse.data,
      status: olingoResponse.statusCode,
      statusText: olingoResponse.statusText,
      headers: olingoResponse.headers,
      config: originalConfig,
    }
  }

  /**
   * 执行 HTTP 请求
   */
  async request<T = any>(config: RequestConfig): Promise<Response<T>> {
    return new Promise((resolve, reject) => {
      const olingoRequest = this.toOlingoRequest(config)

      // 使用 Olingo 的 oData.request 方法
      odatajs.oData.request(
        olingoRequest,
        (data: any, response: OlingoResponse) => {
          // 成功回调
          const standardResponse = this.fromOlingoResponse(response, config)
          resolve(standardResponse as Response<T>)
        },
        (error: any) => {
          // 错误回调
          const errorResponse = this.createErrorResponse(error, config)
          reject(errorResponse)
        }
      )
    })
  }

  /**
   * GET 请求
   */
  async get<T = any>(
    url: string,
    queryOptions?: ODataQueryOptions,
    headers?: Record<string, string>
  ): Promise<Response<ODataResponse<T>>> {
    const fullUrl = this.buildUrl(url, queryOptions)
    return this.request<ODataResponse<T>>({
      url: fullUrl,
      method: 'GET',
      headers,
    })
  }

  /**
   * POST 请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      headers,
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      headers,
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      headers,
    })
  }

  /**
   * DELETE 请求
   */
  async delete(
    url: string,
    headers?: Record<string, string>
  ): Promise<Response<void>> {
    return this.request<void>({
      url,
      method: 'DELETE',
      headers,
    })
  }

  /**
   * 获取服务元数据
   */
  async getMetadata(): Promise<any> {
    const response = await this.get('$metadata', undefined, {
      Accept: 'application/xml',
    })
    return response.data
  }

  /**
   * 创建错误响应对象
   */
  private createErrorResponse(error: any, config: RequestConfig): any {
    const errorObj = new Error(error.message || 'OData request failed')

    // 添加额外的错误信息
    Object.assign(errorObj, {
      config,
      response: error.response
        ? {
            data: error.response.data,
            status: error.response.statusCode,
            statusText: error.response.statusText,
            headers: error.response.headers,
          }
        : undefined,
      isODataError: true,
    })

    return errorObj
  }
}
