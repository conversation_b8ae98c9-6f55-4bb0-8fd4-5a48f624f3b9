/**
 * OData HTTP 客户端包装器
 */

import type {
  ODataQueryOptions,
  ODataResponse,
  RequestConfig,
  Response,
} from '../types'

/**
 * HTTP 客户端包装器类
 */
export class OlingoWrapper {
  private baseUrl: string
  private defaultHeaders: Record<string, string>

  constructor(baseUrl: string, defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾斜杠
    this.defaultHeaders = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'OData-MaxVersion': '4.0',
      'OData-Version': '4.0',
      ...defaultHeaders,
    }
  }

  /**
   * 构建完整的请求 URL
   */
  private buildUrl(path: string, queryOptions?: ODataQueryOptions): string {
    const url = `${this.baseUrl}/${path.replace(/^\//, '')}`

    if (!queryOptions) {
      return url
    }

    const params = new URLSearchParams()

    // 处理 OData 查询选项
    if (queryOptions.$select?.length) {
      params.append('$select', queryOptions.$select.join(','))
    }

    if (queryOptions.$expand?.length) {
      params.append('$expand', queryOptions.$expand.join(','))
    }

    if (queryOptions.$filter) {
      params.append('$filter', queryOptions.$filter)
    }

    if (queryOptions.$orderby) {
      params.append('$orderby', queryOptions.$orderby)
    }

    if (typeof queryOptions.$top === 'number') {
      params.append('$top', queryOptions.$top.toString())
    }

    if (typeof queryOptions.$skip === 'number') {
      params.append('$skip', queryOptions.$skip.toString())
    }

    if (queryOptions.$count) {
      params.append('$count', 'true')
    }

    if (queryOptions.$search) {
      params.append('$search', queryOptions.$search)
    }

    if (queryOptions.$format) {
      params.append('$format', queryOptions.$format)
    }

    const queryString = params.toString()
    return queryString ? `${url}?${queryString}` : url
  }

  /**
   * 执行 HTTP 请求
   */
  async request<T = any>(config: RequestConfig): Promise<Response<T>> {
    const url = config.url.startsWith('http')
      ? config.url
      : this.buildUrl(config.url)

    const headers = {
      ...this.defaultHeaders,
      ...config.headers,
    }

    const fetchOptions: RequestInit = {
      method: config.method || 'GET',
      headers,
    }

    if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method || '')) {
      fetchOptions.body = JSON.stringify(config.data)
    }

    try {
      const response = await fetch(url, fetchOptions)

      let data: any
      const contentType = response.headers.get('content-type')

      if (contentType?.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      const responseHeaders: Record<string, string> = {}
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value
      })

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        config,
      }
    } catch (error: any) {
      throw this.createErrorResponse(error, config)
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(
    url: string,
    queryOptions?: ODataQueryOptions,
    headers?: Record<string, string>
  ): Promise<Response<ODataResponse<T>>> {
    const fullUrl = this.buildUrl(url, queryOptions)
    return this.request<ODataResponse<T>>({
      url: fullUrl,
      method: 'GET',
      headers,
    })
  }

  /**
   * POST 请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      headers,
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      headers,
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>> {
    return this.request<T>({
      url,
      method: 'PATCH',
      data,
      headers,
    })
  }

  /**
   * DELETE 请求
   */
  async delete(
    url: string,
    headers?: Record<string, string>
  ): Promise<Response<void>> {
    return this.request<void>({
      url,
      method: 'DELETE',
      headers,
    })
  }

  /**
   * 获取服务元数据
   */
  async getMetadata(): Promise<any> {
    const response = await this.get('$metadata', undefined, {
      Accept: 'application/xml',
    })
    return response.data
  }

  /**
   * 创建错误响应对象
   */
  private createErrorResponse(error: any, config: RequestConfig): any {
    const errorObj = new Error(error.message || 'OData request failed')

    // 添加额外的错误信息
    Object.assign(errorObj, {
      config,
      response: error.response
        ? {
            data: error.response.data,
            status: error.response.statusCode,
            statusText: error.response.statusText,
            headers: error.response.headers,
          }
        : undefined,
      isODataError: true,
    })

    return errorObj
  }
}
