/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { Singletons } from './Singletons';

/**
 * Request builder class for operations supported on the {@link Singletons} entity.
 */
export class SingletonsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<Singletons<T>, T> {
  /**
   * Returns a request builder for querying all `Singletons` entities.
   * @returns A request builder for creating requests to retrieve all `Singletons` entities.
   */
  getAll(): GetAllRequestBuilder<Singletons<T>, T> {
    return new GetAllRequestBuilder<Singletons<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `Singletons` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `Singletons`.
   */
  create(entity: Singletons<T>): CreateRequestBuilder<Singletons<T>, T> {
    return new CreateRequestBuilder<Singletons<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `Singletons` entity based on its keys.
   * @param ncid Key property. See {@link Singletons.ncid}.
   * @returns A request builder for creating requests to retrieve one `Singletons` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<Singletons<T>, T> {
    return new GetByKeyRequestBuilder<Singletons<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `Singletons`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `Singletons`.
   */
  update(entity: Singletons<T>): UpdateRequestBuilder<Singletons<T>, T> {
    return new UpdateRequestBuilder<Singletons<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `Singletons`.
   * @param ncid Key property. See {@link Singletons.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `Singletons`.
   */
  delete(ncid: string): DeleteRequestBuilder<Singletons<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `Singletons`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `Singletons` by taking the entity as a parameter.
   */
  delete(entity: Singletons<T>): DeleteRequestBuilder<Singletons<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<Singletons<T>, T> {
    return new DeleteRequestBuilder<Singletons<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof Singletons
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
