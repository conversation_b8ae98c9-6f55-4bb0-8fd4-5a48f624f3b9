interface BusinessTypeConfig {
  title?: string // 业务类型标题
}

interface TreeItemSource {
  ncid?: string | number
  displayName?: string
  isAbstract?: boolean
  isVirtual?: boolean
  name?: string
  [key: string]: any
}

interface TreeNode {
  id: string
  label: string
  parentId?: string
  disabled?: boolean
  children?: TreeNode[]
  raw?: TreeItemSource
}

export type { BusinessTypeConfig, TreeItemSource, TreeNode }
