/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { StructuralProperties } from './StructuralProperties';
import { StructuralPropertiesRequestBuilder } from './StructuralPropertiesRequestBuilder';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class StructuralPropertiesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<StructuralProperties<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): StructuralPropertiesApi<DeSerializersT> {
    return new StructuralPropertiesApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = StructuralProperties;

  requestBuilder(): StructuralPropertiesRequestBuilder<DeSerializersT> {
    return new StructuralPropertiesRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    StructuralProperties<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>(
      this
    );
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<
    StructuralProperties<DeSerializersT>,
    DeSerializersT,
    NullableT
  > {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<
    typeof StructuralProperties,
    DeSerializersT
  >;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(
        StructuralProperties,
        this.deSerializers
      );
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    NAME: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DISPLAY_NAME: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    TYPE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    IS_COLLECTION: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_NULLABLE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    MAX_LENGTH: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Int16',
      true,
      true
    >;
    IS_UNICODE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    SRID: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    PRECISION: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Int16',
      true,
      true
    >;
    SCALE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Int16',
      true,
      true
    >;
    DEFAULT_VALUE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    IS_AUTO_GENERATE_CODE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_READ_ONLY: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_UNIQ: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      StructuralProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<StructuralProperties<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', false),
        /**
         * Static representation of the {@link displayName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DISPLAY_NAME: fieldBuilder.buildEdmTypeField(
          'displayName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link type} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        TYPE: fieldBuilder.buildEdmTypeField('type', 'Edm.String', false),
        /**
         * Static representation of the {@link isCollection} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_COLLECTION: fieldBuilder.buildEdmTypeField(
          'isCollection',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isNullable} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_NULLABLE: fieldBuilder.buildEdmTypeField(
          'isNullable',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link maxLength} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MAX_LENGTH: fieldBuilder.buildEdmTypeField(
          'maxLength',
          'Edm.Int16',
          true
        ),
        /**
         * Static representation of the {@link isUnicode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_UNICODE: fieldBuilder.buildEdmTypeField(
          'isUnicode',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link srid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SRID: fieldBuilder.buildEdmTypeField('srid', 'Edm.String', true),
        /**
         * Static representation of the {@link precision} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        PRECISION: fieldBuilder.buildEdmTypeField(
          'precision',
          'Edm.Int16',
          true
        ),
        /**
         * Static representation of the {@link scale} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCALE: fieldBuilder.buildEdmTypeField('scale', 'Edm.Int16', true),
        /**
         * Static representation of the {@link defaultValue} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DEFAULT_VALUE: fieldBuilder.buildEdmTypeField(
          'defaultValue',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link isAutoGenerateCode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_AUTO_GENERATE_CODE: fieldBuilder.buildEdmTypeField(
          'isAutoGenerateCode',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isReadOnly} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_READ_ONLY: fieldBuilder.buildEdmTypeField(
          'isReadOnly',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isUniq} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_UNIQ: fieldBuilder.buildEdmTypeField('isUniq', 'Edm.Boolean', true),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', StructuralProperties)
      };
    }

    return this._schema;
  }
}
