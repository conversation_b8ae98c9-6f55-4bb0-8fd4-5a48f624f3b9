"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.qPlt0ComplexType = exports.QPlt0ComplexType = exports.qPlt0EntityTypePropertyOverride = exports.QPlt0EntityTypePropertyOverride = exports.qPlt0EntityTypeUnionUniqProperty = exports.QPlt0EntityTypeUnionUniqProperty = exports.qPlt0EntityTypeUnionUniq = exports.QPlt0EntityTypeUnionUniq = exports.qPlt0EntityType = exports.QPlt0EntityType = exports.qPlt0BaseType = exports.QPlt0BaseType = exports.QPlt0BaseTypeBaseType = exports.qPlt0Schema = exports.QPlt0Schema = exports.qPlt0RelationFromOrToType = exports.QPlt0RelationFromOrToType = exports.qPlt0RootRelationTypeConfig = exports.QPlt0RootRelationTypeConfig = exports.qPlt0RelationQuery = exports.QPlt0RelationQuery = exports.qPlt0RootRelationType = exports.QPlt0RootRelationType = exports.QPlt0RootRelationTypeBaseType = exports.qPlt0IdiFile = exports.QPlt0IdiFile = exports.qPlt0File = exports.QPlt0File = exports.QPlt0FileBaseType = exports.Plt0MrvObject_QUnlock = exports.Plt0MrvObject_QLock = exports.Plt0MrvObject_QNextRevision = exports.qPlt0MrvObject = exports.QPlt0MrvObject = exports.QPlt0MrvObjectBaseType = exports.qPlt0LifecycleStatusStrategy = exports.QPlt0LifecycleStatusStrategy = exports.qPlt0LifecycleStatus = exports.QPlt0LifecycleStatus = exports.qPlt0VersionObject = exports.QPlt0VersionObject = exports.qPlt0RevisionObject = exports.QPlt0RevisionObject = exports.qPlt0MasterObject = exports.QPlt0MasterObject = exports.Plt0ApplicationObject_QGenerateCode = exports.QPlt0ApplicationObjectId = exports.qPlt0ApplicationObject = exports.QPlt0ApplicationObject = exports.QPlt0ApplicationObjectBaseType = void 0;
exports.qRelCadBom = exports.QRelCadBom = exports.qPlt0IdiFile2 = exports.QPlt0IdiFile2 = exports.qPlt0File2 = exports.QPlt0File2 = exports.QPlt0File2BaseType = exports.qNeueTwoDimensionDrawing = exports.QNeueTwoDimensionDrawing = exports.qTwoDimensionDrawing = exports.QTwoDimensionDrawing = exports.QTwoDimensionDrawingBaseType = exports.qNeueCadAsm = exports.QNeueCadAsm = exports.qNeueCadPart = exports.QNeueCadPart = exports.qCadPart = exports.QCadPart = exports.QCadPartBaseType = exports.qCadFile = exports.QCadFile = exports.QCadFileBaseType = exports.qPlt0BusinessObjectRevisionNumberCode = exports.QPlt0BusinessObjectRevisionNumberCode = exports.qPlt0BusinessObjectRevisionNumberRule = exports.QPlt0BusinessObjectRevisionNumberRule = exports.qPlt0FunctionImport = exports.QPlt0FunctionImport = exports.qPlt0ActionImport = exports.QPlt0ActionImport = exports.qPlt0NavigationPropertyBinding = exports.QPlt0NavigationPropertyBinding = exports.qPlt0Singleton = exports.QPlt0Singleton = exports.qPlt0EntitySet = exports.QPlt0EntitySet = exports.qPlt0EntityContainer = exports.QPlt0EntityContainer = exports.qPlt0Function = exports.QPlt0Function = exports.qPlt0Action = exports.QPlt0Action = exports.qPlt0NavigationProperty = exports.QPlt0NavigationProperty = exports.qPlt0StructuralProperty = exports.QPlt0StructuralProperty = exports.qPlt0TypeDefinition = exports.QPlt0TypeDefinition = exports.qPlt0EnumType = exports.QPlt0EnumType = void 0;
exports.QBatchGetFileSignatureUrl = exports.QDeploy = exports.QGetRelationTreeByNcid = exports.qFileSignatureUrl = exports.QFileSignatureUrl = exports.qTest_ProductModel444 = exports.QTest_ProductModel444 = exports.qTest_ProductModel333 = exports.QTest_ProductModel333 = exports.qTest_ProductModel222 = exports.QTest_ProductModel222 = exports.qTest_ProductModel111 = exports.QTest_ProductModel111 = exports.qTest_ProductModel = exports.QTest_ProductModel = exports.qtest_product001 = exports.Qtest_product001 = exports.qtest_product = exports.Qtest_product = exports.qMappingConfig = exports.QMappingConfig = exports.qRelCadFileFile = exports.QRelCadFileFile = exports.qRelCadPartTwoDimensionDrawing = exports.QRelCadPartTwoDimensionDrawing = void 0;
// @ts-nocheck
var odata_query_objects_1 = require("@odata2ts/odata-query-objects");
var PaaSModel_1 = require("./PaaSModel");
var QPlt0ApplicationObjectBaseType = /** @class */ (function (_super) {
    __extends(QPlt0ApplicationObjectBaseType, _super);
    function QPlt0ApplicationObjectBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ncid = new odata_query_objects_1.QStringPath(_this.withPrefix("ncid"));
        _this.createdAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("createdAt"));
        _this.modifiedAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("modifiedAt"));
        _this.schemaVersion = new odata_query_objects_1.QStringPath(_this.withPrefix("schemaVersion"));
        _this.lifecycleState = new odata_query_objects_1.QEnumPath(_this.withPrefix("lifecycleState"), PaaSModel_1.LifecycleState);
        _this.lifecycleNote = new odata_query_objects_1.QStringPath(_this.withPrefix("lifecycleNote"));
        _this.createdBy = new odata_query_objects_1.QEntityPath(_this.withPrefix("createdBy"), function () { return QPlt0ApplicationObject; });
        _this.modifiedBy = new odata_query_objects_1.QEntityPath(_this.withPrefix("modifiedBy"), function () { return QPlt0ApplicationObject; });
        return _this;
    }
    return QPlt0ApplicationObjectBaseType;
}(odata_query_objects_1.QueryObject));
exports.QPlt0ApplicationObjectBaseType = QPlt0ApplicationObjectBaseType;
var QPlt0ApplicationObject = /** @class */ (function (_super) {
    __extends(QPlt0ApplicationObject, _super);
    function QPlt0ApplicationObject() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "BuiltInModel.Plt0MasterObject": "QPlt0MasterObject", "BuiltInModel.Plt0RevisionObject": "QPlt0RevisionObject", "BuiltInModel.Plt0VersionObject": "QPlt0VersionObject", "BuiltInModel.Plt0LifecycleStatus": "QPlt0LifecycleStatus", "BuiltInModel.Plt0LifecycleStatusStrategy": "QPlt0LifecycleStatusStrategy", "BuiltInModel.Plt0MrvObject": "QPlt0MrvObject", "BuiltInModel.Plt0File": "QPlt0File", "BuiltInModel.Plt0IdiFile": "QPlt0IdiFile", "BuiltInModel.Plt0RootRelationType": "QPlt0RootRelationType", "BuiltInModel.Plt0RelationQuery": "QPlt0RelationQuery", "BuiltInModel.Plt0RootRelationTypeConfig": "QPlt0RootRelationTypeConfig", "BuiltInModel.Plt0RelationFromOrToType": "QPlt0RelationFromOrToType", "BuiltInModel.Plt0Schema": "QPlt0Schema", "BuiltInModel.Plt0BaseType": "QPlt0BaseType", "BuiltInModel.Plt0EntityType": "QPlt0EntityType", "BuiltInModel.Plt0EntityTypeUnionUniq": "QPlt0EntityTypeUnionUniq", "BuiltInModel.Plt0EntityTypeUnionUniqProperty": "QPlt0EntityTypeUnionUniqProperty", "BuiltInModel.Plt0EntityTypePropertyOverride": "QPlt0EntityTypePropertyOverride", "BuiltInModel.Plt0ComplexType": "QPlt0ComplexType", "BuiltInModel.Plt0EnumType": "QPlt0EnumType", "BuiltInModel.Plt0TypeDefinition": "QPlt0TypeDefinition", "BuiltInModel.Plt0StructuralProperty": "QPlt0StructuralProperty", "BuiltInModel.Plt0NavigationProperty": "QPlt0NavigationProperty", "BuiltInModel.Plt0Action": "QPlt0Action", "BuiltInModel.Plt0Function": "QPlt0Function", "BuiltInModel.Plt0EntityContainer": "QPlt0EntityContainer", "BuiltInModel.Plt0EntitySet": "QPlt0EntitySet", "BuiltInModel.Plt0Singleton": "QPlt0Singleton", "BuiltInModel.Plt0NavigationPropertyBinding": "QPlt0NavigationPropertyBinding", "BuiltInModel.Plt0ActionImport": "QPlt0ActionImport", "BuiltInModel.Plt0FunctionImport": "QPlt0FunctionImport", "BuiltInModel.Plt0BusinessObjectRevisionNumberRule": "QPlt0BusinessObjectRevisionNumberRule", "BuiltInModel.Plt0BusinessObjectRevisionNumberCode": "QPlt0BusinessObjectRevisionNumberCode", "neue.CadFile": "QCadFile", "neue.CadPart": "QCadPart", "neue.NeueCadPart": "QNeueCadPart", "neue.NeueCadAsm": "QNeueCadAsm", "neue.TwoDimensionDrawing": "QTwoDimensionDrawing", "neue.NeueTwoDimensionDrawing": "QNeueTwoDimensionDrawing", "neue.Plt0File": "QPlt0File2", "neue.Plt0IdiFile": "QPlt0IdiFile2", "neue.RelCadBom": "QRelCadBom", "neue.RelCadPartTwoDimensionDrawing": "QRelCadPartTwoDimensionDrawing", "neue.RelCadFileFile": "QRelCadFileFile", "neue.MappingConfig": "QMappingConfig", "neue.test_product": "Qtest_product", "neue.test_product001": "Qtest_product001", "neue.Test_ProductModel": "QTest_ProductModel", "neue.Test_ProductModel111": "QTest_ProductModel111", "neue.Test_ProductModel222": "QTest_ProductModel222", "neue.Test_ProductModel333": "QTest_ProductModel333", "neue.Test_ProductModel444": "QTest_ProductModel444" };
        return _this;
    }
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_revisionCode", {
        get: function () {
            return this.__asQPlt0RevisionObject().revisionCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_revisionOrder", {
        get: function () {
            return this.__asQPlt0RevisionObject().revisionOrder;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_isLatestRevision", {
        get: function () {
            return this.__asQPlt0RevisionObject().isLatestRevision;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_isLocked", {
        get: function () {
            return this.__asQPlt0RevisionObject().isLocked;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_lockedAt", {
        get: function () {
            return this.__asQPlt0RevisionObject().lockedAt;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_preRevision", {
        get: function () {
            return this.__asQPlt0RevisionObject().preRevision;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_master", {
        get: function () {
            return this.__asQPlt0RevisionObject().master;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RevisionObject_lockedBy", {
        get: function () {
            return this.__asQPlt0RevisionObject().lockedBy;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0VersionObject_versionNumber", {
        get: function () {
            return this.__asQPlt0VersionObject().versionNumber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0VersionObject_isLatestVersion", {
        get: function () {
            return this.__asQPlt0VersionObject().isLatestVersion;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0VersionObject_revision", {
        get: function () {
            return this.__asQPlt0VersionObject().revision;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatus_statusCode", {
        get: function () {
            return this.__asQPlt0LifecycleStatus().statusCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatus_name", {
        get: function () {
            return this.__asQPlt0LifecycleStatus().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatus_description", {
        get: function () {
            return this.__asQPlt0LifecycleStatus().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatusStrategy_strategyCode", {
        get: function () {
            return this.__asQPlt0LifecycleStatusStrategy().strategyCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatusStrategy_name", {
        get: function () {
            return this.__asQPlt0LifecycleStatusStrategy().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0LifecycleStatusStrategy_description", {
        get: function () {
            return this.__asQPlt0LifecycleStatusStrategy().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_code", {
        get: function () {
            return this.__asQPlt0MrvObject().code;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_name", {
        get: function () {
            return this.__asQPlt0MrvObject().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_description", {
        get: function () {
            return this.__asQPlt0MrvObject().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_version", {
        get: function () {
            return this.__asQPlt0MrvObject().version;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_owner", {
        get: function () {
            return this.__asQPlt0MrvObject().owner;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0MrvObject_lifecycleStatus", {
        get: function () {
            return this.__asQPlt0MrvObject().lifecycleStatus;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File_name", {
        get: function () {
            return this.__asQPlt0File().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File_description", {
        get: function () {
            return this.__asQPlt0File().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File_softwareRev", {
        get: function () {
            return this.__asQPlt0File().softwareRev;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File_storageItem", {
        get: function () {
            return this.__asQPlt0File().storageItem;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile_idiLightModelId", {
        get: function () {
            return this.__asQPlt0IdiFile().idiLightModelId;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile_convertState", {
        get: function () {
            return this.__asQPlt0IdiFile().convertState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile_triggerTime", {
        get: function () {
            return this.__asQPlt0IdiFile().triggerTime;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationType_from", {
        get: function () {
            return this.__asQPlt0RootRelationType().from;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationType_to", {
        get: function () {
            return this.__asQPlt0RootRelationType().to;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RelationQuery_toRelations", {
        get: function () {
            return this.__asQPlt0RelationQuery().toRelations;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_relationClassify", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().relationClassify;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_sourceRelationConstrict", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().sourceRelationConstrict;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_targetRelationConstrict", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().targetRelationConstrict;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_sourceRelationVersionUpgradeAction", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().sourceRelationVersionUpgradeAction;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_targetRelationVersionUpgradeAction", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().targetRelationVersionUpgradeAction;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_relationStatus", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().relationStatus;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_description", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RootRelationTypeConfig_applyRelationType", {
        get: function () {
            return this.__asQPlt0RootRelationTypeConfig().applyRelationType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RelationFromOrToType_sideType", {
        get: function () {
            return this.__asQPlt0RelationFromOrToType().sideType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RelationFromOrToType_relationType", {
        get: function () {
            return this.__asQPlt0RelationFromOrToType().relationType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0RelationFromOrToType_constrictType", {
        get: function () {
            return this.__asQPlt0RelationFromOrToType().constrictType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Schema_namespace", {
        get: function () {
            return this.__asQPlt0Schema().namespace;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Schema_alias", {
        get: function () {
            return this.__asQPlt0Schema().alias;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_name", {
        get: function () {
            return this.__asQPlt0BaseType().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_displayName", {
        get: function () {
            return this.__asQPlt0BaseType().displayName;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_description", {
        get: function () {
            return this.__asQPlt0BaseType().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_isAbstract", {
        get: function () {
            return this.__asQPlt0BaseType().isAbstract;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_isOpen", {
        get: function () {
            return this.__asQPlt0BaseType().isOpen;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BaseType_schema", {
        get: function () {
            return this.__asQPlt0BaseType().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_entityTypeCode", {
        get: function () {
            return this.__asQPlt0EntityType().entityTypeCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_isVirtual", {
        get: function () {
            return this.__asQPlt0EntityType().isVirtual;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_hasStream", {
        get: function () {
            return this.__asQPlt0EntityType().hasStream;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_mrvStrategy", {
        get: function () {
            return this.__asQPlt0EntityType().mrvStrategy;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_isReadOnly", {
        get: function () {
            return this.__asQPlt0EntityType().isReadOnly;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_icon", {
        get: function () {
            return this.__asQPlt0EntityType().icon;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_baseType", {
        get: function () {
            return this.__asQPlt0EntityType().baseType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_lifecycleStatusStrategy", {
        get: function () {
            return this.__asQPlt0EntityType().lifecycleStatusStrategy;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityType_revisionNumberRule", {
        get: function () {
            return this.__asQPlt0EntityType().revisionNumberRule;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniq_name", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniq().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniq_description", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniq().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniq_ownerType", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniq().ownerType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniqProperty_ownerUnionUniq", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniqProperty().ownerUnionUniq;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniqProperty_structuralProperty", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniqProperty().structuralProperty;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypeUnionUniqProperty_navigationProperty", {
        get: function () {
            return this.__asQPlt0EntityTypeUnionUniqProperty().navigationProperty;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypePropertyOverride_isAutoGenerateCode", {
        get: function () {
            return this.__asQPlt0EntityTypePropertyOverride().isAutoGenerateCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypePropertyOverride_ownerType", {
        get: function () {
            return this.__asQPlt0EntityTypePropertyOverride().ownerType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityTypePropertyOverride_structuralProperty", {
        get: function () {
            return this.__asQPlt0EntityTypePropertyOverride().structuralProperty;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ComplexType_complexTypeCode", {
        get: function () {
            return this.__asQPlt0ComplexType().complexTypeCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ComplexType_baseType", {
        get: function () {
            return this.__asQPlt0ComplexType().baseType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_enumTypeCode", {
        get: function () {
            return this.__asQPlt0EnumType().enumTypeCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_name", {
        get: function () {
            return this.__asQPlt0EnumType().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_description", {
        get: function () {
            return this.__asQPlt0EnumType().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_underlyingType", {
        get: function () {
            return this.__asQPlt0EnumType().underlyingType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_isFlags", {
        get: function () {
            return this.__asQPlt0EnumType().isFlags;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_members", {
        get: function () {
            return this.__asQPlt0EnumType().members;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EnumType_schema", {
        get: function () {
            return this.__asQPlt0EnumType().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_typeDefinitionCode", {
        get: function () {
            return this.__asQPlt0TypeDefinition().typeDefinitionCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_name", {
        get: function () {
            return this.__asQPlt0TypeDefinition().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_description", {
        get: function () {
            return this.__asQPlt0TypeDefinition().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_underlyingType", {
        get: function () {
            return this.__asQPlt0TypeDefinition().underlyingType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_maxLength", {
        get: function () {
            return this.__asQPlt0TypeDefinition().maxLength;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_isUnicode", {
        get: function () {
            return this.__asQPlt0TypeDefinition().isUnicode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_srid", {
        get: function () {
            return this.__asQPlt0TypeDefinition().srid;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_precision", {
        get: function () {
            return this.__asQPlt0TypeDefinition().precision;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_scale", {
        get: function () {
            return this.__asQPlt0TypeDefinition().scale;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0TypeDefinition_schema", {
        get: function () {
            return this.__asQPlt0TypeDefinition().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_name", {
        get: function () {
            return this.__asQPlt0StructuralProperty().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_displayName", {
        get: function () {
            return this.__asQPlt0StructuralProperty().displayName;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_description", {
        get: function () {
            return this.__asQPlt0StructuralProperty().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_type", {
        get: function () {
            return this.__asQPlt0StructuralProperty().type;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isCollection", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isCollection;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isNullable", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isNullable;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_maxLength", {
        get: function () {
            return this.__asQPlt0StructuralProperty().maxLength;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isUnicode", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isUnicode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_srid", {
        get: function () {
            return this.__asQPlt0StructuralProperty().srid;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_precision", {
        get: function () {
            return this.__asQPlt0StructuralProperty().precision;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_scale", {
        get: function () {
            return this.__asQPlt0StructuralProperty().scale;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_defaultValue", {
        get: function () {
            return this.__asQPlt0StructuralProperty().defaultValue;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isAutoGenerateCode", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isAutoGenerateCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isReadOnly", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isReadOnly;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_isUniq", {
        get: function () {
            return this.__asQPlt0StructuralProperty().isUniq;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0StructuralProperty_ownerType", {
        get: function () {
            return this.__asQPlt0StructuralProperty().ownerType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_name", {
        get: function () {
            return this.__asQPlt0NavigationProperty().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_displayName", {
        get: function () {
            return this.__asQPlt0NavigationProperty().displayName;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_description", {
        get: function () {
            return this.__asQPlt0NavigationProperty().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_isCollection", {
        get: function () {
            return this.__asQPlt0NavigationProperty().isCollection;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_isNullable", {
        get: function () {
            return this.__asQPlt0NavigationProperty().isNullable;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_isContainsTarget", {
        get: function () {
            return this.__asQPlt0NavigationProperty().isContainsTarget;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_referentialConstraint", {
        get: function () {
            return this.__asQPlt0NavigationProperty().referentialConstraint;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_onDelete", {
        get: function () {
            return this.__asQPlt0NavigationProperty().onDelete;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_isReadOnly", {
        get: function () {
            return this.__asQPlt0NavigationProperty().isReadOnly;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_isUniq", {
        get: function () {
            return this.__asQPlt0NavigationProperty().isUniq;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_ownerType", {
        get: function () {
            return this.__asQPlt0NavigationProperty().ownerType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_type", {
        get: function () {
            return this.__asQPlt0NavigationProperty().type;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationProperty_partner", {
        get: function () {
            return this.__asQPlt0NavigationProperty().partner;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_name", {
        get: function () {
            return this.__asQPlt0Action().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_description", {
        get: function () {
            return this.__asQPlt0Action().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_schema", {
        get: function () {
            return this.__asQPlt0Action().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_isBound", {
        get: function () {
            return this.__asQPlt0Action().isBound;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_parameter", {
        get: function () {
            return this.__asQPlt0Action().parameter;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_returnType", {
        get: function () {
            return this.__asQPlt0Action().returnType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Action_entitySetPath", {
        get: function () {
            return this.__asQPlt0Action().entitySetPath;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_name", {
        get: function () {
            return this.__asQPlt0Function().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_description", {
        get: function () {
            return this.__asQPlt0Function().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_schema", {
        get: function () {
            return this.__asQPlt0Function().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_isBound", {
        get: function () {
            return this.__asQPlt0Function().isBound;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_isComposable", {
        get: function () {
            return this.__asQPlt0Function().isComposable;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_parameter", {
        get: function () {
            return this.__asQPlt0Function().parameter;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_returnType", {
        get: function () {
            return this.__asQPlt0Function().returnType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Function_entitySetPath", {
        get: function () {
            return this.__asQPlt0Function().entitySetPath;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityContainer_name", {
        get: function () {
            return this.__asQPlt0EntityContainer().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityContainer_extend", {
        get: function () {
            return this.__asQPlt0EntityContainer().extend;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntityContainer_schema", {
        get: function () {
            return this.__asQPlt0EntityContainer().schema;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntitySet_name", {
        get: function () {
            return this.__asQPlt0EntitySet().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntitySet_description", {
        get: function () {
            return this.__asQPlt0EntitySet().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntitySet_isIncludeInServiceDocument", {
        get: function () {
            return this.__asQPlt0EntitySet().isIncludeInServiceDocument;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntitySet_entityContainer", {
        get: function () {
            return this.__asQPlt0EntitySet().entityContainer;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0EntitySet_entityType", {
        get: function () {
            return this.__asQPlt0EntitySet().entityType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Singleton_name", {
        get: function () {
            return this.__asQPlt0Singleton().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Singleton_description", {
        get: function () {
            return this.__asQPlt0Singleton().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Singleton_entityContainer", {
        get: function () {
            return this.__asQPlt0Singleton().entityContainer;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0Singleton_entityType", {
        get: function () {
            return this.__asQPlt0Singleton().entityType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationPropertyBinding_entitySet", {
        get: function () {
            return this.__asQPlt0NavigationPropertyBinding().entitySet;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationPropertyBinding_path", {
        get: function () {
            return this.__asQPlt0NavigationPropertyBinding().path;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0NavigationPropertyBinding_target", {
        get: function () {
            return this.__asQPlt0NavigationPropertyBinding().target;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ActionImport_name", {
        get: function () {
            return this.__asQPlt0ActionImport().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ActionImport_description", {
        get: function () {
            return this.__asQPlt0ActionImport().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ActionImport_entityContainer", {
        get: function () {
            return this.__asQPlt0ActionImport().entityContainer;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ActionImport_action", {
        get: function () {
            return this.__asQPlt0ActionImport().action;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0ActionImport_entitySet", {
        get: function () {
            return this.__asQPlt0ActionImport().entitySet;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_name", {
        get: function () {
            return this.__asQPlt0FunctionImport().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_description", {
        get: function () {
            return this.__asQPlt0FunctionImport().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_isIncludeInServiceDocument", {
        get: function () {
            return this.__asQPlt0FunctionImport().isIncludeInServiceDocument;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_entityContainer", {
        get: function () {
            return this.__asQPlt0FunctionImport().entityContainer;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_function", {
        get: function () {
            return this.__asQPlt0FunctionImport().function;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0FunctionImport_entitySet", {
        get: function () {
            return this.__asQPlt0FunctionImport().entitySet;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_ruleCode", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().ruleCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_name", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_isActive", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().isActive;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_isEmployed", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().isEmployed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_isLocked", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().isLocked;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberRule_description", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberRule().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberCode_revisionOrder", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberCode().revisionOrder;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberCode_revisionCode", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberCode().revisionCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0BusinessObjectRevisionNumberCode_ruleCodeRef", {
        get: function () {
            return this.__asQPlt0BusinessObjectRevisionNumberCode().ruleCodeRef;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadFile_submitDescription", {
        get: function () {
            return this.__asQCadFile().submitDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_partType", {
        get: function () {
            return this.__asQCadPart().partType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_volume", {
        get: function () {
            return this.__asQCadPart().volume;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_mass", {
        get: function () {
            return this.__asQCadPart().mass;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_material", {
        get: function () {
            return this.__asQCadPart().material;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_openSurfaceArea", {
        get: function () {
            return this.__asQCadPart().openSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_solidSurfaceArea", {
        get: function () {
            return this.__asQCadPart().solidSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_gravityCenter", {
        get: function () {
            return this.__asQCadPart().gravityCenter;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QCadPart_thumbnail", {
        get: function () {
            return this.__asQCadPart().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QTwoDimensionDrawing_thumbnail", {
        get: function () {
            return this.__asQTwoDimensionDrawing().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File2_description", {
        get: function () {
            return this.__asQPlt0File2().description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File2_softwareRev", {
        get: function () {
            return this.__asQPlt0File2().softwareRev;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File2_name", {
        get: function () {
            return this.__asQPlt0File2().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0File2_storageItem", {
        get: function () {
            return this.__asQPlt0File2().storageItem;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile2_convertState", {
        get: function () {
            return this.__asQPlt0IdiFile2().convertState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile2_triggerTime", {
        get: function () {
            return this.__asQPlt0IdiFile2().triggerTime;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QPlt0IdiFile2_idiLightModelId", {
        get: function () {
            return this.__asQPlt0IdiFile2().idiLightModelId;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_transformationMatrix", {
        get: function () {
            return this.__asQRelCadBom().transformationMatrix;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_configuration", {
        get: function () {
            return this.__asQRelCadBom().configuration;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_quantity", {
        get: function () {
            return this.__asQRelCadBom().quantity;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_suppressed", {
        get: function () {
            return this.__asQRelCadBom().suppressed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_bomExcluded", {
        get: function () {
            return this.__asQRelCadBom().bomExcluded;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadBom_instanceName", {
        get: function () {
            return this.__asQRelCadBom().instanceName;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadFileFile_linkType", {
        get: function () {
            return this.__asQRelCadFileFile().linkType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QRelCadFileFile_usageType", {
        get: function () {
            return this.__asQRelCadFileFile().usageType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_tool", {
        get: function () {
            return this.__asQMappingConfig().tool;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_caxProperty", {
        get: function () {
            return this.__asQMappingConfig().caxProperty;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_name", {
        get: function () {
            return this.__asQMappingConfig().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_drawingSheetArea", {
        get: function () {
            return this.__asQMappingConfig().drawingSheetArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_partType", {
        get: function () {
            return this.__asQMappingConfig().partType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_cdpProperty", {
        get: function () {
            return this.__asQMappingConfig().cdpProperty;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_onCax", {
        get: function () {
            return this.__asQMappingConfig().onCax;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QMappingConfig_direction", {
        get: function () {
            return this.__asQMappingConfig().direction;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QTest_ProductModel111_Productname", {
        get: function () {
            return this.__asQTest_ProductModel111().Productname;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QTest_ProductModel444_name", {
        get: function () {
            return this.__asQTest_ProductModel444().name;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0ApplicationObject.prototype, "QTest_ProductModel444_prince", {
        get: function () {
            return this.__asQTest_ProductModel444().prince;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0ApplicationObject.prototype.__asQPlt0MasterObject = function () {
        return new QPlt0MasterObject(this.withPrefix("BuiltInModel.Plt0MasterObject"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0RevisionObject = function () {
        return new QPlt0RevisionObject(this.withPrefix("BuiltInModel.Plt0RevisionObject"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0VersionObject = function () {
        return new QPlt0VersionObject(this.withPrefix("BuiltInModel.Plt0VersionObject"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0LifecycleStatus = function () {
        return new QPlt0LifecycleStatus(this.withPrefix("BuiltInModel.Plt0LifecycleStatus"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0LifecycleStatusStrategy = function () {
        return new QPlt0LifecycleStatusStrategy(this.withPrefix("BuiltInModel.Plt0LifecycleStatusStrategy"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0MrvObject = function () {
        return new QPlt0MrvObject(this.withPrefix("BuiltInModel.Plt0MrvObject"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0File = function () {
        return new QPlt0File(this.withPrefix("BuiltInModel.Plt0File"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0IdiFile = function () {
        return new QPlt0IdiFile(this.withPrefix("BuiltInModel.Plt0IdiFile"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0RootRelationType = function () {
        return new QPlt0RootRelationType(this.withPrefix("BuiltInModel.Plt0RootRelationType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0RelationQuery = function () {
        return new QPlt0RelationQuery(this.withPrefix("BuiltInModel.Plt0RelationQuery"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0RootRelationTypeConfig = function () {
        return new QPlt0RootRelationTypeConfig(this.withPrefix("BuiltInModel.Plt0RootRelationTypeConfig"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0RelationFromOrToType = function () {
        return new QPlt0RelationFromOrToType(this.withPrefix("BuiltInModel.Plt0RelationFromOrToType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0Schema = function () {
        return new QPlt0Schema(this.withPrefix("BuiltInModel.Plt0Schema"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0BaseType = function () {
        return new QPlt0BaseType(this.withPrefix("BuiltInModel.Plt0BaseType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntityType = function () {
        return new QPlt0EntityType(this.withPrefix("BuiltInModel.Plt0EntityType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntityTypeUnionUniq = function () {
        return new QPlt0EntityTypeUnionUniq(this.withPrefix("BuiltInModel.Plt0EntityTypeUnionUniq"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntityTypeUnionUniqProperty = function () {
        return new QPlt0EntityTypeUnionUniqProperty(this.withPrefix("BuiltInModel.Plt0EntityTypeUnionUniqProperty"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntityTypePropertyOverride = function () {
        return new QPlt0EntityTypePropertyOverride(this.withPrefix("BuiltInModel.Plt0EntityTypePropertyOverride"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0ComplexType = function () {
        return new QPlt0ComplexType(this.withPrefix("BuiltInModel.Plt0ComplexType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EnumType = function () {
        return new QPlt0EnumType(this.withPrefix("BuiltInModel.Plt0EnumType"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0TypeDefinition = function () {
        return new QPlt0TypeDefinition(this.withPrefix("BuiltInModel.Plt0TypeDefinition"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0StructuralProperty = function () {
        return new QPlt0StructuralProperty(this.withPrefix("BuiltInModel.Plt0StructuralProperty"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0NavigationProperty = function () {
        return new QPlt0NavigationProperty(this.withPrefix("BuiltInModel.Plt0NavigationProperty"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0Action = function () {
        return new QPlt0Action(this.withPrefix("BuiltInModel.Plt0Action"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0Function = function () {
        return new QPlt0Function(this.withPrefix("BuiltInModel.Plt0Function"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntityContainer = function () {
        return new QPlt0EntityContainer(this.withPrefix("BuiltInModel.Plt0EntityContainer"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0EntitySet = function () {
        return new QPlt0EntitySet(this.withPrefix("BuiltInModel.Plt0EntitySet"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0Singleton = function () {
        return new QPlt0Singleton(this.withPrefix("BuiltInModel.Plt0Singleton"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0NavigationPropertyBinding = function () {
        return new QPlt0NavigationPropertyBinding(this.withPrefix("BuiltInModel.Plt0NavigationPropertyBinding"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0ActionImport = function () {
        return new QPlt0ActionImport(this.withPrefix("BuiltInModel.Plt0ActionImport"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0FunctionImport = function () {
        return new QPlt0FunctionImport(this.withPrefix("BuiltInModel.Plt0FunctionImport"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0BusinessObjectRevisionNumberRule = function () {
        return new QPlt0BusinessObjectRevisionNumberRule(this.withPrefix("BuiltInModel.Plt0BusinessObjectRevisionNumberRule"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0BusinessObjectRevisionNumberCode = function () {
        return new QPlt0BusinessObjectRevisionNumberCode(this.withPrefix("BuiltInModel.Plt0BusinessObjectRevisionNumberCode"));
    };
    QPlt0ApplicationObject.prototype.__asQCadFile = function () {
        return new QCadFile(this.withPrefix("neue.CadFile"));
    };
    QPlt0ApplicationObject.prototype.__asQCadPart = function () {
        return new QCadPart(this.withPrefix("neue.CadPart"));
    };
    QPlt0ApplicationObject.prototype.__asQNeueCadPart = function () {
        return new QNeueCadPart(this.withPrefix("neue.NeueCadPart"));
    };
    QPlt0ApplicationObject.prototype.__asQNeueCadAsm = function () {
        return new QNeueCadAsm(this.withPrefix("neue.NeueCadAsm"));
    };
    QPlt0ApplicationObject.prototype.__asQTwoDimensionDrawing = function () {
        return new QTwoDimensionDrawing(this.withPrefix("neue.TwoDimensionDrawing"));
    };
    QPlt0ApplicationObject.prototype.__asQNeueTwoDimensionDrawing = function () {
        return new QNeueTwoDimensionDrawing(this.withPrefix("neue.NeueTwoDimensionDrawing"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0File2 = function () {
        return new QPlt0File2(this.withPrefix("neue.Plt0File"));
    };
    QPlt0ApplicationObject.prototype.__asQPlt0IdiFile2 = function () {
        return new QPlt0IdiFile2(this.withPrefix("neue.Plt0IdiFile"));
    };
    QPlt0ApplicationObject.prototype.__asQRelCadBom = function () {
        return new QRelCadBom(this.withPrefix("neue.RelCadBom"));
    };
    QPlt0ApplicationObject.prototype.__asQRelCadPartTwoDimensionDrawing = function () {
        return new QRelCadPartTwoDimensionDrawing(this.withPrefix("neue.RelCadPartTwoDimensionDrawing"));
    };
    QPlt0ApplicationObject.prototype.__asQRelCadFileFile = function () {
        return new QRelCadFileFile(this.withPrefix("neue.RelCadFileFile"));
    };
    QPlt0ApplicationObject.prototype.__asQMappingConfig = function () {
        return new QMappingConfig(this.withPrefix("neue.MappingConfig"));
    };
    QPlt0ApplicationObject.prototype.__asQtest_product = function () {
        return new Qtest_product(this.withPrefix("neue.test_product"));
    };
    QPlt0ApplicationObject.prototype.__asQtest_product001 = function () {
        return new Qtest_product001(this.withPrefix("neue.test_product001"));
    };
    QPlt0ApplicationObject.prototype.__asQTest_ProductModel = function () {
        return new QTest_ProductModel(this.withPrefix("neue.Test_ProductModel"));
    };
    QPlt0ApplicationObject.prototype.__asQTest_ProductModel111 = function () {
        return new QTest_ProductModel111(this.withPrefix("neue.Test_ProductModel111"));
    };
    QPlt0ApplicationObject.prototype.__asQTest_ProductModel222 = function () {
        return new QTest_ProductModel222(this.withPrefix("neue.Test_ProductModel222"));
    };
    QPlt0ApplicationObject.prototype.__asQTest_ProductModel333 = function () {
        return new QTest_ProductModel333(this.withPrefix("neue.Test_ProductModel333"));
    };
    QPlt0ApplicationObject.prototype.__asQTest_ProductModel444 = function () {
        return new QTest_ProductModel444(this.withPrefix("neue.Test_ProductModel444"));
    };
    return QPlt0ApplicationObject;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0ApplicationObject = QPlt0ApplicationObject;
Object.defineProperties(QPlt0ApplicationObject.prototype, { QPlt0RevisionObject_revisionCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_revisionOrder: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_isLatestRevision: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_isLocked: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_lockedAt: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_preRevision: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_master: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RevisionObject_lockedBy: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0VersionObject_versionNumber: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0VersionObject_isLatestVersion: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0VersionObject_revision: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatus_statusCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatus_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatus_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatusStrategy_strategyCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatusStrategy_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0LifecycleStatusStrategy_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_code: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_version: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_owner: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0MrvObject_lifecycleStatus: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File_softwareRev: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File_storageItem: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile_idiLightModelId: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile_convertState: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile_triggerTime: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationType_from: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationType_to: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RelationQuery_toRelations: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_relationClassify: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_sourceRelationConstrict: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_targetRelationConstrict: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_sourceRelationVersionUpgradeAction: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_targetRelationVersionUpgradeAction: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_relationStatus: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RootRelationTypeConfig_applyRelationType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RelationFromOrToType_sideType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RelationFromOrToType_relationType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0RelationFromOrToType_constrictType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Schema_namespace: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Schema_alias: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_displayName: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_isAbstract: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_isOpen: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BaseType_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_entityTypeCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_isVirtual: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_hasStream: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_mrvStrategy: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_isReadOnly: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_icon: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_baseType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_lifecycleStatusStrategy: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_revisionNumberRule: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniq_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniq_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniq_ownerType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniqProperty_ownerUnionUniq: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniqProperty_structuralProperty: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypeUnionUniqProperty_navigationProperty: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypePropertyOverride_isAutoGenerateCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypePropertyOverride_ownerType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityTypePropertyOverride_structuralProperty: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ComplexType_complexTypeCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ComplexType_baseType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_enumTypeCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_underlyingType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_isFlags: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_members: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EnumType_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_typeDefinitionCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_underlyingType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_maxLength: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_isUnicode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_srid: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_precision: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_scale: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0TypeDefinition_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_displayName: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_type: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isCollection: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isNullable: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_maxLength: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isUnicode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_srid: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_precision: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_scale: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_defaultValue: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isAutoGenerateCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isReadOnly: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_isUniq: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0StructuralProperty_ownerType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_displayName: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_isCollection: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_isNullable: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_isContainsTarget: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_referentialConstraint: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_onDelete: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_isReadOnly: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_isUniq: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_ownerType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_type: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationProperty_partner: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_isBound: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_parameter: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_returnType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Action_entitySetPath: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_isBound: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_isComposable: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_parameter: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_returnType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Function_entitySetPath: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityContainer_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityContainer_extend: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityContainer_schema: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntitySet_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntitySet_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntitySet_isIncludeInServiceDocument: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntitySet_entityContainer: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntitySet_entityType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Singleton_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Singleton_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Singleton_entityContainer: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0Singleton_entityType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationPropertyBinding_entitySet: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationPropertyBinding_path: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0NavigationPropertyBinding_target: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ActionImport_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ActionImport_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ActionImport_entityContainer: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ActionImport_action: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ActionImport_entitySet: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_isIncludeInServiceDocument: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_entityContainer: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_function: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0FunctionImport_entitySet: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_ruleCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_isActive: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_isEmployed: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_isLocked: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberRule_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberCode_revisionOrder: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberCode_revisionCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0BusinessObjectRevisionNumberCode_ruleCodeRef: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadFile_submitDescription: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_partType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_volume: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_mass: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_material: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_openSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_solidSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_gravityCenter: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTwoDimensionDrawing_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File2_description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File2_softwareRev: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File2_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0File2_storageItem: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile2_convertState: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile2_triggerTime: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile2_idiLightModelId: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_transformationMatrix: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_configuration: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_quantity: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_suppressed: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_bomExcluded: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_instanceName: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadFileFile_linkType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadFileFile_usageType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_tool: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_caxProperty: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_drawingSheetArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_partType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_cdpProperty: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_onCax: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QMappingConfig_direction: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTest_ProductModel111_Productname: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTest_ProductModel444_name: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTest_ProductModel444_prince: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0ApplicationObject = new QPlt0ApplicationObject();
var QPlt0ApplicationObjectId = /** @class */ (function (_super) {
    __extends(QPlt0ApplicationObjectId, _super);
    function QPlt0ApplicationObjectId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QStringParam("ncid")];
        return _this;
    }
    QPlt0ApplicationObjectId.prototype.getParams = function () {
        return this.params;
    };
    return QPlt0ApplicationObjectId;
}(odata_query_objects_1.QId));
exports.QPlt0ApplicationObjectId = QPlt0ApplicationObjectId;
var Plt0ApplicationObject_QGenerateCode = /** @class */ (function (_super) {
    __extends(Plt0ApplicationObject_QGenerateCode, _super);
    function Plt0ApplicationObject_QGenerateCode() {
        var _this = _super.call(this, "BuiltInModel.GenerateCode") || this;
        _this.params = [new odata_query_objects_1.QStringParam("propertyName")];
        return _this;
    }
    Plt0ApplicationObject_QGenerateCode.prototype.getParams = function () {
        return this.params;
    };
    return Plt0ApplicationObject_QGenerateCode;
}(odata_query_objects_1.QAction));
exports.Plt0ApplicationObject_QGenerateCode = Plt0ApplicationObject_QGenerateCode;
var QPlt0MasterObject = /** @class */ (function (_super) {
    __extends(QPlt0MasterObject, _super);
    function QPlt0MasterObject() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QPlt0MasterObject;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0MasterObject = QPlt0MasterObject;
exports.qPlt0MasterObject = new QPlt0MasterObject();
var QPlt0RevisionObject = /** @class */ (function (_super) {
    __extends(QPlt0RevisionObject, _super);
    function QPlt0RevisionObject() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.revisionCode = new odata_query_objects_1.QStringPath(_this.withPrefix("revisionCode"));
        _this.revisionOrder = new odata_query_objects_1.QNumberPath(_this.withPrefix("revisionOrder"));
        _this.isLatestRevision = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isLatestRevision"));
        _this.isLocked = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isLocked"));
        _this.lockedAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("lockedAt"));
        _this.preRevision = new odata_query_objects_1.QEntityPath(_this.withPrefix("preRevision"), function () { return QPlt0RevisionObject; });
        _this.master = new odata_query_objects_1.QEntityPath(_this.withPrefix("master"), function () { return QPlt0MasterObject; });
        _this.lockedBy = new odata_query_objects_1.QEntityPath(_this.withPrefix("lockedBy"), function () { return QPlt0ApplicationObject; });
        return _this;
    }
    return QPlt0RevisionObject;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0RevisionObject = QPlt0RevisionObject;
exports.qPlt0RevisionObject = new QPlt0RevisionObject();
var QPlt0VersionObject = /** @class */ (function (_super) {
    __extends(QPlt0VersionObject, _super);
    function QPlt0VersionObject() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.versionNumber = new odata_query_objects_1.QNumberPath(_this.withPrefix("versionNumber"));
        _this.isLatestVersion = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isLatestVersion"));
        _this.revision = new odata_query_objects_1.QEntityPath(_this.withPrefix("revision"), function () { return QPlt0RevisionObject; });
        return _this;
    }
    return QPlt0VersionObject;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0VersionObject = QPlt0VersionObject;
exports.qPlt0VersionObject = new QPlt0VersionObject();
var QPlt0LifecycleStatus = /** @class */ (function (_super) {
    __extends(QPlt0LifecycleStatus, _super);
    function QPlt0LifecycleStatus() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.statusCode = new odata_query_objects_1.QStringPath(_this.withPrefix("statusCode"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        return _this;
    }
    return QPlt0LifecycleStatus;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0LifecycleStatus = QPlt0LifecycleStatus;
exports.qPlt0LifecycleStatus = new QPlt0LifecycleStatus();
var QPlt0LifecycleStatusStrategy = /** @class */ (function (_super) {
    __extends(QPlt0LifecycleStatusStrategy, _super);
    function QPlt0LifecycleStatusStrategy() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.strategyCode = new odata_query_objects_1.QStringPath(_this.withPrefix("strategyCode"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        return _this;
    }
    return QPlt0LifecycleStatusStrategy;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0LifecycleStatusStrategy = QPlt0LifecycleStatusStrategy;
exports.qPlt0LifecycleStatusStrategy = new QPlt0LifecycleStatusStrategy();
var QPlt0MrvObjectBaseType = /** @class */ (function (_super) {
    __extends(QPlt0MrvObjectBaseType, _super);
    function QPlt0MrvObjectBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.code = new odata_query_objects_1.QStringPath(_this.withPrefix("code"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.version = new odata_query_objects_1.QEntityPath(_this.withPrefix("version"), function () { return QPlt0VersionObject; });
        _this.owner = new odata_query_objects_1.QEntityPath(_this.withPrefix("owner"), function () { return QPlt0ApplicationObject; });
        _this.lifecycleStatus = new odata_query_objects_1.QEntityPath(_this.withPrefix("lifecycleStatus"), function () { return QPlt0LifecycleStatus; });
        return _this;
    }
    return QPlt0MrvObjectBaseType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0MrvObjectBaseType = QPlt0MrvObjectBaseType;
var QPlt0MrvObject = /** @class */ (function (_super) {
    __extends(QPlt0MrvObject, _super);
    function QPlt0MrvObject() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.CadFile": "QCadFile", "neue.CadPart": "QCadPart", "neue.NeueCadPart": "QNeueCadPart", "neue.NeueCadAsm": "QNeueCadAsm", "neue.TwoDimensionDrawing": "QTwoDimensionDrawing", "neue.NeueTwoDimensionDrawing": "QNeueTwoDimensionDrawing" };
        return _this;
    }
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadFile_submitDescription", {
        get: function () {
            return this.__asQCadFile().submitDescription;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_partType", {
        get: function () {
            return this.__asQCadPart().partType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_volume", {
        get: function () {
            return this.__asQCadPart().volume;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_mass", {
        get: function () {
            return this.__asQCadPart().mass;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_material", {
        get: function () {
            return this.__asQCadPart().material;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_openSurfaceArea", {
        get: function () {
            return this.__asQCadPart().openSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_solidSurfaceArea", {
        get: function () {
            return this.__asQCadPart().solidSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_gravityCenter", {
        get: function () {
            return this.__asQCadPart().gravityCenter;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QCadPart_thumbnail", {
        get: function () {
            return this.__asQCadPart().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0MrvObject.prototype, "QTwoDimensionDrawing_thumbnail", {
        get: function () {
            return this.__asQTwoDimensionDrawing().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0MrvObject.prototype.__asQCadFile = function () {
        return new QCadFile(this.withPrefix("neue.CadFile"));
    };
    QPlt0MrvObject.prototype.__asQCadPart = function () {
        return new QCadPart(this.withPrefix("neue.CadPart"));
    };
    QPlt0MrvObject.prototype.__asQNeueCadPart = function () {
        return new QNeueCadPart(this.withPrefix("neue.NeueCadPart"));
    };
    QPlt0MrvObject.prototype.__asQNeueCadAsm = function () {
        return new QNeueCadAsm(this.withPrefix("neue.NeueCadAsm"));
    };
    QPlt0MrvObject.prototype.__asQTwoDimensionDrawing = function () {
        return new QTwoDimensionDrawing(this.withPrefix("neue.TwoDimensionDrawing"));
    };
    QPlt0MrvObject.prototype.__asQNeueTwoDimensionDrawing = function () {
        return new QNeueTwoDimensionDrawing(this.withPrefix("neue.NeueTwoDimensionDrawing"));
    };
    return QPlt0MrvObject;
}(QPlt0MrvObjectBaseType));
exports.QPlt0MrvObject = QPlt0MrvObject;
Object.defineProperties(QPlt0MrvObject.prototype, { QCadFile_submitDescription: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_partType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_volume: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_mass: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_material: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_openSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_solidSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_gravityCenter: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTwoDimensionDrawing_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0MrvObject = new QPlt0MrvObject();
var Plt0MrvObject_QNextRevision = /** @class */ (function (_super) {
    __extends(Plt0MrvObject_QNextRevision, _super);
    function Plt0MrvObject_QNextRevision() {
        var _this = _super.call(this, "BuiltInModel.NextRevision") || this;
        _this.params = [];
        return _this;
    }
    Plt0MrvObject_QNextRevision.prototype.getParams = function () {
        return this.params;
    };
    return Plt0MrvObject_QNextRevision;
}(odata_query_objects_1.QAction));
exports.Plt0MrvObject_QNextRevision = Plt0MrvObject_QNextRevision;
var Plt0MrvObject_QLock = /** @class */ (function (_super) {
    __extends(Plt0MrvObject_QLock, _super);
    function Plt0MrvObject_QLock() {
        var _this = _super.call(this, "BuiltInModel.Lock") || this;
        _this.params = [];
        return _this;
    }
    Plt0MrvObject_QLock.prototype.getParams = function () {
        return this.params;
    };
    return Plt0MrvObject_QLock;
}(odata_query_objects_1.QAction));
exports.Plt0MrvObject_QLock = Plt0MrvObject_QLock;
var Plt0MrvObject_QUnlock = /** @class */ (function (_super) {
    __extends(Plt0MrvObject_QUnlock, _super);
    function Plt0MrvObject_QUnlock() {
        var _this = _super.call(this, "BuiltInModel.Unlock") || this;
        _this.params = [];
        return _this;
    }
    Plt0MrvObject_QUnlock.prototype.getParams = function () {
        return this.params;
    };
    return Plt0MrvObject_QUnlock;
}(odata_query_objects_1.QAction));
exports.Plt0MrvObject_QUnlock = Plt0MrvObject_QUnlock;
var QPlt0FileBaseType = /** @class */ (function (_super) {
    __extends(QPlt0FileBaseType, _super);
    function QPlt0FileBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.softwareRev = new odata_query_objects_1.QStringPath(_this.withPrefix("softwareRev"));
        _this.storageItem = new odata_query_objects_1.QEntityPath(_this.withPrefix("storageItem"), function () { return QPlt0ApplicationObject; });
        return _this;
    }
    return QPlt0FileBaseType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0FileBaseType = QPlt0FileBaseType;
var QPlt0File = /** @class */ (function (_super) {
    __extends(QPlt0File, _super);
    function QPlt0File() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "BuiltInModel.Plt0IdiFile": "QPlt0IdiFile" };
        return _this;
    }
    Object.defineProperty(QPlt0File.prototype, "QPlt0IdiFile_idiLightModelId", {
        get: function () {
            return this.__asQPlt0IdiFile().idiLightModelId;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0File.prototype, "QPlt0IdiFile_convertState", {
        get: function () {
            return this.__asQPlt0IdiFile().convertState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0File.prototype, "QPlt0IdiFile_triggerTime", {
        get: function () {
            return this.__asQPlt0IdiFile().triggerTime;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0File.prototype.__asQPlt0IdiFile = function () {
        return new QPlt0IdiFile(this.withPrefix("BuiltInModel.Plt0IdiFile"));
    };
    return QPlt0File;
}(QPlt0FileBaseType));
exports.QPlt0File = QPlt0File;
Object.defineProperties(QPlt0File.prototype, { QPlt0IdiFile_idiLightModelId: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile_convertState: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile_triggerTime: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0File = new QPlt0File();
var QPlt0IdiFile = /** @class */ (function (_super) {
    __extends(QPlt0IdiFile, _super);
    function QPlt0IdiFile() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.idiLightModelId = new odata_query_objects_1.QStringPath(_this.withPrefix("idiLightModelId"));
        _this.convertState = new odata_query_objects_1.QEnumPath(_this.withPrefix("convertState"), PaaSModel_1.IdiConvertState);
        _this.triggerTime = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("triggerTime"));
        return _this;
    }
    return QPlt0IdiFile;
}(QPlt0FileBaseType));
exports.QPlt0IdiFile = QPlt0IdiFile;
exports.qPlt0IdiFile = new QPlt0IdiFile();
var QPlt0RootRelationTypeBaseType = /** @class */ (function (_super) {
    __extends(QPlt0RootRelationTypeBaseType, _super);
    function QPlt0RootRelationTypeBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.from = new odata_query_objects_1.QEntityPath(_this.withPrefix("from"), function () { return QPlt0ApplicationObject; });
        _this.to = new odata_query_objects_1.QEntityPath(_this.withPrefix("to"), function () { return QPlt0ApplicationObject; });
        return _this;
    }
    return QPlt0RootRelationTypeBaseType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0RootRelationTypeBaseType = QPlt0RootRelationTypeBaseType;
var QPlt0RootRelationType = /** @class */ (function (_super) {
    __extends(QPlt0RootRelationType, _super);
    function QPlt0RootRelationType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.RelCadBom": "QRelCadBom", "neue.RelCadPartTwoDimensionDrawing": "QRelCadPartTwoDimensionDrawing", "neue.RelCadFileFile": "QRelCadFileFile" };
        return _this;
    }
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_transformationMatrix", {
        get: function () {
            return this.__asQRelCadBom().transformationMatrix;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_configuration", {
        get: function () {
            return this.__asQRelCadBom().configuration;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_quantity", {
        get: function () {
            return this.__asQRelCadBom().quantity;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_suppressed", {
        get: function () {
            return this.__asQRelCadBom().suppressed;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_bomExcluded", {
        get: function () {
            return this.__asQRelCadBom().bomExcluded;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadBom_instanceName", {
        get: function () {
            return this.__asQRelCadBom().instanceName;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadFileFile_linkType", {
        get: function () {
            return this.__asQRelCadFileFile().linkType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0RootRelationType.prototype, "QRelCadFileFile_usageType", {
        get: function () {
            return this.__asQRelCadFileFile().usageType;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0RootRelationType.prototype.__asQRelCadBom = function () {
        return new QRelCadBom(this.withPrefix("neue.RelCadBom"));
    };
    QPlt0RootRelationType.prototype.__asQRelCadPartTwoDimensionDrawing = function () {
        return new QRelCadPartTwoDimensionDrawing(this.withPrefix("neue.RelCadPartTwoDimensionDrawing"));
    };
    QPlt0RootRelationType.prototype.__asQRelCadFileFile = function () {
        return new QRelCadFileFile(this.withPrefix("neue.RelCadFileFile"));
    };
    return QPlt0RootRelationType;
}(QPlt0RootRelationTypeBaseType));
exports.QPlt0RootRelationType = QPlt0RootRelationType;
Object.defineProperties(QPlt0RootRelationType.prototype, { QRelCadBom_transformationMatrix: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_configuration: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_quantity: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_suppressed: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_bomExcluded: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadBom_instanceName: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadFileFile_linkType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QRelCadFileFile_usageType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0RootRelationType = new QPlt0RootRelationType();
var QPlt0RelationQuery = /** @class */ (function (_super) {
    __extends(QPlt0RelationQuery, _super);
    function QPlt0RelationQuery() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.toRelations = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("toRelations"), function () { return QPlt0RootRelationType; });
        return _this;
    }
    return QPlt0RelationQuery;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0RelationQuery = QPlt0RelationQuery;
exports.qPlt0RelationQuery = new QPlt0RelationQuery();
var QPlt0RootRelationTypeConfig = /** @class */ (function (_super) {
    __extends(QPlt0RootRelationTypeConfig, _super);
    function QPlt0RootRelationTypeConfig() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.relationClassify = new odata_query_objects_1.QEnumPath(_this.withPrefix("relationClassify"), PaaSModel_1.RelationClassify);
        _this.sourceRelationConstrict = new odata_query_objects_1.QEnumPath(_this.withPrefix("sourceRelationConstrict"), PaaSModel_1.RelationConstrictEnum);
        _this.targetRelationConstrict = new odata_query_objects_1.QEnumPath(_this.withPrefix("targetRelationConstrict"), PaaSModel_1.RelationConstrictEnum);
        _this.sourceRelationVersionUpgradeAction = new odata_query_objects_1.QEnumPath(_this.withPrefix("sourceRelationVersionUpgradeAction"), PaaSModel_1.RelationVersionUpgradeActionEnum);
        _this.targetRelationVersionUpgradeAction = new odata_query_objects_1.QEnumPath(_this.withPrefix("targetRelationVersionUpgradeAction"), PaaSModel_1.RelationVersionUpgradeActionEnum);
        _this.relationStatus = new odata_query_objects_1.QEnumPath(_this.withPrefix("relationStatus"), PaaSModel_1.RelationStatus);
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.applyRelationType = new odata_query_objects_1.QEntityPath(_this.withPrefix("applyRelationType"), function () { return QPlt0RootRelationType; });
        return _this;
    }
    return QPlt0RootRelationTypeConfig;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0RootRelationTypeConfig = QPlt0RootRelationTypeConfig;
exports.qPlt0RootRelationTypeConfig = new QPlt0RootRelationTypeConfig();
var QPlt0RelationFromOrToType = /** @class */ (function (_super) {
    __extends(QPlt0RelationFromOrToType, _super);
    function QPlt0RelationFromOrToType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sideType = new odata_query_objects_1.QEnumPath(_this.withPrefix("sideType"), PaaSModel_1.RelationSourceTypeEnum);
        _this.relationType = new odata_query_objects_1.QEntityPath(_this.withPrefix("relationType"), function () { return QPlt0EntityType; });
        _this.constrictType = new odata_query_objects_1.QEntityPath(_this.withPrefix("constrictType"), function () { return QPlt0EntityType; });
        return _this;
    }
    return QPlt0RelationFromOrToType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0RelationFromOrToType = QPlt0RelationFromOrToType;
exports.qPlt0RelationFromOrToType = new QPlt0RelationFromOrToType();
var QPlt0Schema = /** @class */ (function (_super) {
    __extends(QPlt0Schema, _super);
    function QPlt0Schema() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.namespace = new odata_query_objects_1.QStringPath(_this.withPrefix("namespace"));
        _this.alias = new odata_query_objects_1.QStringPath(_this.withPrefix("alias"));
        return _this;
    }
    return QPlt0Schema;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0Schema = QPlt0Schema;
exports.qPlt0Schema = new QPlt0Schema();
var QPlt0BaseTypeBaseType = /** @class */ (function (_super) {
    __extends(QPlt0BaseTypeBaseType, _super);
    function QPlt0BaseTypeBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.displayName = new odata_query_objects_1.QStringPath(_this.withPrefix("displayName"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.isAbstract = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isAbstract"));
        _this.isOpen = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isOpen"));
        _this.schema = new odata_query_objects_1.QEntityPath(_this.withPrefix("schema"), function () { return QPlt0Schema; });
        return _this;
    }
    return QPlt0BaseTypeBaseType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0BaseTypeBaseType = QPlt0BaseTypeBaseType;
var QPlt0BaseType = /** @class */ (function (_super) {
    __extends(QPlt0BaseType, _super);
    function QPlt0BaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "BuiltInModel.Plt0EntityType": "QPlt0EntityType", "BuiltInModel.Plt0ComplexType": "QPlt0ComplexType" };
        return _this;
    }
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_entityTypeCode", {
        get: function () {
            return this.__asQPlt0EntityType().entityTypeCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_isVirtual", {
        get: function () {
            return this.__asQPlt0EntityType().isVirtual;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_hasStream", {
        get: function () {
            return this.__asQPlt0EntityType().hasStream;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_mrvStrategy", {
        get: function () {
            return this.__asQPlt0EntityType().mrvStrategy;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_isReadOnly", {
        get: function () {
            return this.__asQPlt0EntityType().isReadOnly;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_icon", {
        get: function () {
            return this.__asQPlt0EntityType().icon;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_baseType", {
        get: function () {
            return this.__asQPlt0EntityType().baseType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_lifecycleStatusStrategy", {
        get: function () {
            return this.__asQPlt0EntityType().lifecycleStatusStrategy;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0EntityType_revisionNumberRule", {
        get: function () {
            return this.__asQPlt0EntityType().revisionNumberRule;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0ComplexType_complexTypeCode", {
        get: function () {
            return this.__asQPlt0ComplexType().complexTypeCode;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0BaseType.prototype, "QPlt0ComplexType_baseType", {
        get: function () {
            return this.__asQPlt0ComplexType().baseType;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0BaseType.prototype.__asQPlt0EntityType = function () {
        return new QPlt0EntityType(this.withPrefix("BuiltInModel.Plt0EntityType"));
    };
    QPlt0BaseType.prototype.__asQPlt0ComplexType = function () {
        return new QPlt0ComplexType(this.withPrefix("BuiltInModel.Plt0ComplexType"));
    };
    return QPlt0BaseType;
}(QPlt0BaseTypeBaseType));
exports.QPlt0BaseType = QPlt0BaseType;
Object.defineProperties(QPlt0BaseType.prototype, { QPlt0EntityType_entityTypeCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_isVirtual: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_hasStream: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_mrvStrategy: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_isReadOnly: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_icon: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_baseType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_lifecycleStatusStrategy: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0EntityType_revisionNumberRule: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ComplexType_complexTypeCode: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0ComplexType_baseType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0BaseType = new QPlt0BaseType();
var QPlt0EntityType = /** @class */ (function (_super) {
    __extends(QPlt0EntityType, _super);
    function QPlt0EntityType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.entityTypeCode = new odata_query_objects_1.QStringPath(_this.withPrefix("entityTypeCode"));
        _this.isVirtual = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isVirtual"));
        _this.hasStream = new odata_query_objects_1.QBooleanPath(_this.withPrefix("hasStream"));
        _this.mrvStrategy = new odata_query_objects_1.QEnumPath(_this.withPrefix("mrvStrategy"), PaaSModel_1.MrvStrategyType);
        _this.isReadOnly = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isReadOnly"));
        _this.icon = new odata_query_objects_1.QEntityPath(_this.withPrefix("icon"), function () { return QPlt0File; });
        _this.baseType = new odata_query_objects_1.QEntityPath(_this.withPrefix("baseType"), function () { return QPlt0EntityType; });
        _this.lifecycleStatusStrategy = new odata_query_objects_1.QEntityPath(_this.withPrefix("lifecycleStatusStrategy"), function () { return QPlt0LifecycleStatusStrategy; });
        _this.revisionNumberRule = new odata_query_objects_1.QEntityPath(_this.withPrefix("revisionNumberRule"), function () { return QPlt0BusinessObjectRevisionNumberRule; });
        return _this;
    }
    return QPlt0EntityType;
}(QPlt0BaseTypeBaseType));
exports.QPlt0EntityType = QPlt0EntityType;
exports.qPlt0EntityType = new QPlt0EntityType();
var QPlt0EntityTypeUnionUniq = /** @class */ (function (_super) {
    __extends(QPlt0EntityTypeUnionUniq, _super);
    function QPlt0EntityTypeUnionUniq() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.ownerType = new odata_query_objects_1.QEntityPath(_this.withPrefix("ownerType"), function () { return QPlt0EntityType; });
        return _this;
    }
    return QPlt0EntityTypeUnionUniq;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EntityTypeUnionUniq = QPlt0EntityTypeUnionUniq;
exports.qPlt0EntityTypeUnionUniq = new QPlt0EntityTypeUnionUniq();
var QPlt0EntityTypeUnionUniqProperty = /** @class */ (function (_super) {
    __extends(QPlt0EntityTypeUnionUniqProperty, _super);
    function QPlt0EntityTypeUnionUniqProperty() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ownerUnionUniq = new odata_query_objects_1.QEntityPath(_this.withPrefix("ownerUnionUniq"), function () { return QPlt0EntityTypeUnionUniq; });
        _this.structuralProperty = new odata_query_objects_1.QEntityPath(_this.withPrefix("structuralProperty"), function () { return QPlt0StructuralProperty; });
        _this.navigationProperty = new odata_query_objects_1.QEntityPath(_this.withPrefix("navigationProperty"), function () { return QPlt0NavigationProperty; });
        return _this;
    }
    return QPlt0EntityTypeUnionUniqProperty;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EntityTypeUnionUniqProperty = QPlt0EntityTypeUnionUniqProperty;
exports.qPlt0EntityTypeUnionUniqProperty = new QPlt0EntityTypeUnionUniqProperty();
var QPlt0EntityTypePropertyOverride = /** @class */ (function (_super) {
    __extends(QPlt0EntityTypePropertyOverride, _super);
    function QPlt0EntityTypePropertyOverride() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isAutoGenerateCode = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isAutoGenerateCode"));
        _this.ownerType = new odata_query_objects_1.QEntityPath(_this.withPrefix("ownerType"), function () { return QPlt0EntityType; });
        _this.structuralProperty = new odata_query_objects_1.QEntityPath(_this.withPrefix("structuralProperty"), function () { return QPlt0StructuralProperty; });
        return _this;
    }
    return QPlt0EntityTypePropertyOverride;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EntityTypePropertyOverride = QPlt0EntityTypePropertyOverride;
exports.qPlt0EntityTypePropertyOverride = new QPlt0EntityTypePropertyOverride();
var QPlt0ComplexType = /** @class */ (function (_super) {
    __extends(QPlt0ComplexType, _super);
    function QPlt0ComplexType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.complexTypeCode = new odata_query_objects_1.QStringPath(_this.withPrefix("complexTypeCode"));
        _this.baseType = new odata_query_objects_1.QEntityPath(_this.withPrefix("baseType"), function () { return QPlt0ComplexType; });
        return _this;
    }
    return QPlt0ComplexType;
}(QPlt0BaseTypeBaseType));
exports.QPlt0ComplexType = QPlt0ComplexType;
exports.qPlt0ComplexType = new QPlt0ComplexType();
var QPlt0EnumType = /** @class */ (function (_super) {
    __extends(QPlt0EnumType, _super);
    function QPlt0EnumType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.enumTypeCode = new odata_query_objects_1.QStringPath(_this.withPrefix("enumTypeCode"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.underlyingType = new odata_query_objects_1.QStringPath(_this.withPrefix("underlyingType"));
        _this.isFlags = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isFlags"));
        _this.members = new odata_query_objects_1.QCollectionPath(_this.withPrefix("members"), function () { return odata_query_objects_1.QStringCollection; });
        _this.schema = new odata_query_objects_1.QEntityPath(_this.withPrefix("schema"), function () { return QPlt0Schema; });
        return _this;
    }
    return QPlt0EnumType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EnumType = QPlt0EnumType;
exports.qPlt0EnumType = new QPlt0EnumType();
var QPlt0TypeDefinition = /** @class */ (function (_super) {
    __extends(QPlt0TypeDefinition, _super);
    function QPlt0TypeDefinition() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.typeDefinitionCode = new odata_query_objects_1.QStringPath(_this.withPrefix("typeDefinitionCode"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.underlyingType = new odata_query_objects_1.QStringPath(_this.withPrefix("underlyingType"));
        _this.maxLength = new odata_query_objects_1.QNumberPath(_this.withPrefix("maxLength"));
        _this.isUnicode = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isUnicode"));
        _this.srid = new odata_query_objects_1.QStringPath(_this.withPrefix("srid"));
        _this.precision = new odata_query_objects_1.QNumberPath(_this.withPrefix("precision"));
        _this.scale = new odata_query_objects_1.QNumberPath(_this.withPrefix("scale"));
        _this.schema = new odata_query_objects_1.QEntityPath(_this.withPrefix("schema"), function () { return QPlt0Schema; });
        return _this;
    }
    return QPlt0TypeDefinition;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0TypeDefinition = QPlt0TypeDefinition;
exports.qPlt0TypeDefinition = new QPlt0TypeDefinition();
var QPlt0StructuralProperty = /** @class */ (function (_super) {
    __extends(QPlt0StructuralProperty, _super);
    function QPlt0StructuralProperty() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.displayName = new odata_query_objects_1.QStringPath(_this.withPrefix("displayName"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.type = new odata_query_objects_1.QStringPath(_this.withPrefix("type"));
        _this.isCollection = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isCollection"));
        _this.isNullable = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isNullable"));
        _this.maxLength = new odata_query_objects_1.QNumberPath(_this.withPrefix("maxLength"));
        _this.isUnicode = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isUnicode"));
        _this.srid = new odata_query_objects_1.QStringPath(_this.withPrefix("srid"));
        _this.precision = new odata_query_objects_1.QNumberPath(_this.withPrefix("precision"));
        _this.scale = new odata_query_objects_1.QNumberPath(_this.withPrefix("scale"));
        _this.defaultValue = new odata_query_objects_1.QStringPath(_this.withPrefix("defaultValue"));
        _this.isAutoGenerateCode = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isAutoGenerateCode"));
        _this.isReadOnly = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isReadOnly"));
        _this.isUniq = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isUniq"));
        _this.ownerType = new odata_query_objects_1.QEntityPath(_this.withPrefix("ownerType"), function () { return QPlt0BaseType; });
        return _this;
    }
    return QPlt0StructuralProperty;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0StructuralProperty = QPlt0StructuralProperty;
exports.qPlt0StructuralProperty = new QPlt0StructuralProperty();
var QPlt0NavigationProperty = /** @class */ (function (_super) {
    __extends(QPlt0NavigationProperty, _super);
    function QPlt0NavigationProperty() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.displayName = new odata_query_objects_1.QStringPath(_this.withPrefix("displayName"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.isCollection = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isCollection"));
        _this.isNullable = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isNullable"));
        _this.isContainsTarget = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isContainsTarget"));
        _this.referentialConstraint = new odata_query_objects_1.QStringPath(_this.withPrefix("referentialConstraint"));
        _this.onDelete = new odata_query_objects_1.QEnumPath(_this.withPrefix("onDelete"), PaaSModel_1.OnDelete);
        _this.isReadOnly = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isReadOnly"));
        _this.isUniq = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isUniq"));
        _this.ownerType = new odata_query_objects_1.QEntityPath(_this.withPrefix("ownerType"), function () { return QPlt0BaseType; });
        _this.type = new odata_query_objects_1.QEntityPath(_this.withPrefix("type"), function () { return QPlt0EntityType; });
        _this.partner = new odata_query_objects_1.QEntityPath(_this.withPrefix("partner"), function () { return QPlt0NavigationProperty; });
        return _this;
    }
    return QPlt0NavigationProperty;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0NavigationProperty = QPlt0NavigationProperty;
exports.qPlt0NavigationProperty = new QPlt0NavigationProperty();
var QPlt0Action = /** @class */ (function (_super) {
    __extends(QPlt0Action, _super);
    function QPlt0Action() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.schema = new odata_query_objects_1.QStringPath(_this.withPrefix("schema"));
        _this.isBound = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isBound"));
        _this.parameter = new odata_query_objects_1.QStringPath(_this.withPrefix("parameter"));
        _this.returnType = new odata_query_objects_1.QStringPath(_this.withPrefix("returnType"));
        _this.entitySetPath = new odata_query_objects_1.QStringPath(_this.withPrefix("entitySetPath"));
        return _this;
    }
    return QPlt0Action;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0Action = QPlt0Action;
exports.qPlt0Action = new QPlt0Action();
var QPlt0Function = /** @class */ (function (_super) {
    __extends(QPlt0Function, _super);
    function QPlt0Function() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.schema = new odata_query_objects_1.QStringPath(_this.withPrefix("schema"));
        _this.isBound = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isBound"));
        _this.isComposable = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isComposable"));
        _this.parameter = new odata_query_objects_1.QStringPath(_this.withPrefix("parameter"));
        _this.returnType = new odata_query_objects_1.QStringPath(_this.withPrefix("returnType"));
        _this.entitySetPath = new odata_query_objects_1.QStringPath(_this.withPrefix("entitySetPath"));
        return _this;
    }
    return QPlt0Function;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0Function = QPlt0Function;
exports.qPlt0Function = new QPlt0Function();
var QPlt0EntityContainer = /** @class */ (function (_super) {
    __extends(QPlt0EntityContainer, _super);
    function QPlt0EntityContainer() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.extend = new odata_query_objects_1.QStringPath(_this.withPrefix("extend"));
        _this.schema = new odata_query_objects_1.QEntityPath(_this.withPrefix("schema"), function () { return QPlt0Schema; });
        return _this;
    }
    return QPlt0EntityContainer;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EntityContainer = QPlt0EntityContainer;
exports.qPlt0EntityContainer = new QPlt0EntityContainer();
var QPlt0EntitySet = /** @class */ (function (_super) {
    __extends(QPlt0EntitySet, _super);
    function QPlt0EntitySet() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.isIncludeInServiceDocument = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isIncludeInServiceDocument"));
        _this.entityContainer = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityContainer"), function () { return QPlt0EntityContainer; });
        _this.entityType = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityType"), function () { return QPlt0EntityType; });
        return _this;
    }
    return QPlt0EntitySet;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0EntitySet = QPlt0EntitySet;
exports.qPlt0EntitySet = new QPlt0EntitySet();
var QPlt0Singleton = /** @class */ (function (_super) {
    __extends(QPlt0Singleton, _super);
    function QPlt0Singleton() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.entityContainer = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityContainer"), function () { return QPlt0EntityContainer; });
        _this.entityType = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityType"), function () { return QPlt0EntityType; });
        return _this;
    }
    return QPlt0Singleton;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0Singleton = QPlt0Singleton;
exports.qPlt0Singleton = new QPlt0Singleton();
var QPlt0NavigationPropertyBinding = /** @class */ (function (_super) {
    __extends(QPlt0NavigationPropertyBinding, _super);
    function QPlt0NavigationPropertyBinding() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.entitySet = new odata_query_objects_1.QEntityPath(_this.withPrefix("entitySet"), function () { return QPlt0EntitySet; });
        _this.path = new odata_query_objects_1.QEntityPath(_this.withPrefix("path"), function () { return QPlt0NavigationProperty; });
        _this.target = new odata_query_objects_1.QEntityPath(_this.withPrefix("target"), function () { return QPlt0EntitySet; });
        return _this;
    }
    return QPlt0NavigationPropertyBinding;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0NavigationPropertyBinding = QPlt0NavigationPropertyBinding;
exports.qPlt0NavigationPropertyBinding = new QPlt0NavigationPropertyBinding();
var QPlt0ActionImport = /** @class */ (function (_super) {
    __extends(QPlt0ActionImport, _super);
    function QPlt0ActionImport() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.entityContainer = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityContainer"), function () { return QPlt0EntityContainer; });
        _this.action = new odata_query_objects_1.QEntityPath(_this.withPrefix("action"), function () { return QPlt0Action; });
        _this.entitySet = new odata_query_objects_1.QEntityPath(_this.withPrefix("entitySet"), function () { return QPlt0EntitySet; });
        return _this;
    }
    return QPlt0ActionImport;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0ActionImport = QPlt0ActionImport;
exports.qPlt0ActionImport = new QPlt0ActionImport();
var QPlt0FunctionImport = /** @class */ (function (_super) {
    __extends(QPlt0FunctionImport, _super);
    function QPlt0FunctionImport() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.isIncludeInServiceDocument = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isIncludeInServiceDocument"));
        _this.entityContainer = new odata_query_objects_1.QEntityPath(_this.withPrefix("entityContainer"), function () { return QPlt0EntityContainer; });
        _this.function = new odata_query_objects_1.QEntityPath(_this.withPrefix("function"), function () { return QPlt0Function; });
        _this.entitySet = new odata_query_objects_1.QEntityPath(_this.withPrefix("entitySet"), function () { return QPlt0EntitySet; });
        return _this;
    }
    return QPlt0FunctionImport;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0FunctionImport = QPlt0FunctionImport;
exports.qPlt0FunctionImport = new QPlt0FunctionImport();
var QPlt0BusinessObjectRevisionNumberRule = /** @class */ (function (_super) {
    __extends(QPlt0BusinessObjectRevisionNumberRule, _super);
    function QPlt0BusinessObjectRevisionNumberRule() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ruleCode = new odata_query_objects_1.QStringPath(_this.withPrefix("ruleCode"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.isActive = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isActive"));
        _this.isEmployed = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isEmployed"));
        _this.isLocked = new odata_query_objects_1.QBooleanPath(_this.withPrefix("isLocked"));
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        return _this;
    }
    return QPlt0BusinessObjectRevisionNumberRule;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0BusinessObjectRevisionNumberRule = QPlt0BusinessObjectRevisionNumberRule;
exports.qPlt0BusinessObjectRevisionNumberRule = new QPlt0BusinessObjectRevisionNumberRule();
var QPlt0BusinessObjectRevisionNumberCode = /** @class */ (function (_super) {
    __extends(QPlt0BusinessObjectRevisionNumberCode, _super);
    function QPlt0BusinessObjectRevisionNumberCode() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.revisionOrder = new odata_query_objects_1.QNumberPath(_this.withPrefix("revisionOrder"));
        _this.revisionCode = new odata_query_objects_1.QStringPath(_this.withPrefix("revisionCode"));
        _this.ruleCodeRef = new odata_query_objects_1.QEntityPath(_this.withPrefix("ruleCodeRef"), function () { return QPlt0BusinessObjectRevisionNumberRule; });
        return _this;
    }
    return QPlt0BusinessObjectRevisionNumberCode;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0BusinessObjectRevisionNumberCode = QPlt0BusinessObjectRevisionNumberCode;
exports.qPlt0BusinessObjectRevisionNumberCode = new QPlt0BusinessObjectRevisionNumberCode();
var QCadFileBaseType = /** @class */ (function (_super) {
    __extends(QCadFileBaseType, _super);
    function QCadFileBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.submitDescription = new odata_query_objects_1.QStringPath(_this.withPrefix("submitDescription"));
        return _this;
    }
    return QCadFileBaseType;
}(QPlt0MrvObjectBaseType));
exports.QCadFileBaseType = QCadFileBaseType;
var QCadFile = /** @class */ (function (_super) {
    __extends(QCadFile, _super);
    function QCadFile() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.CadPart": "QCadPart", "neue.NeueCadPart": "QNeueCadPart", "neue.NeueCadAsm": "QNeueCadAsm", "neue.TwoDimensionDrawing": "QTwoDimensionDrawing", "neue.NeueTwoDimensionDrawing": "QNeueTwoDimensionDrawing" };
        return _this;
    }
    Object.defineProperty(QCadFile.prototype, "QCadPart_partType", {
        get: function () {
            return this.__asQCadPart().partType;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_volume", {
        get: function () {
            return this.__asQCadPart().volume;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_mass", {
        get: function () {
            return this.__asQCadPart().mass;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_material", {
        get: function () {
            return this.__asQCadPart().material;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_openSurfaceArea", {
        get: function () {
            return this.__asQCadPart().openSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_solidSurfaceArea", {
        get: function () {
            return this.__asQCadPart().solidSurfaceArea;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_gravityCenter", {
        get: function () {
            return this.__asQCadPart().gravityCenter;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QCadPart_thumbnail", {
        get: function () {
            return this.__asQCadPart().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QCadFile.prototype, "QTwoDimensionDrawing_thumbnail", {
        get: function () {
            return this.__asQTwoDimensionDrawing().thumbnail;
        },
        enumerable: false,
        configurable: true
    });
    QCadFile.prototype.__asQCadPart = function () {
        return new QCadPart(this.withPrefix("neue.CadPart"));
    };
    QCadFile.prototype.__asQNeueCadPart = function () {
        return new QNeueCadPart(this.withPrefix("neue.NeueCadPart"));
    };
    QCadFile.prototype.__asQNeueCadAsm = function () {
        return new QNeueCadAsm(this.withPrefix("neue.NeueCadAsm"));
    };
    QCadFile.prototype.__asQTwoDimensionDrawing = function () {
        return new QTwoDimensionDrawing(this.withPrefix("neue.TwoDimensionDrawing"));
    };
    QCadFile.prototype.__asQNeueTwoDimensionDrawing = function () {
        return new QNeueTwoDimensionDrawing(this.withPrefix("neue.NeueTwoDimensionDrawing"));
    };
    return QCadFile;
}(QCadFileBaseType));
exports.QCadFile = QCadFile;
Object.defineProperties(QCadFile.prototype, { QCadPart_partType: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_volume: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_mass: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_material: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_openSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_solidSurfaceArea: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_gravityCenter: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QCadPart_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QTwoDimensionDrawing_thumbnail: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qCadFile = new QCadFile();
var QCadPartBaseType = /** @class */ (function (_super) {
    __extends(QCadPartBaseType, _super);
    function QCadPartBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.partType = new odata_query_objects_1.QEnumPath(_this.withPrefix("partType"), PaaSModel_1.PartType);
        _this.volume = new odata_query_objects_1.QNumberPath(_this.withPrefix("volume"));
        _this.mass = new odata_query_objects_1.QNumberPath(_this.withPrefix("mass"));
        _this.material = new odata_query_objects_1.QStringPath(_this.withPrefix("material"));
        _this.openSurfaceArea = new odata_query_objects_1.QNumberPath(_this.withPrefix("openSurfaceArea"));
        _this.solidSurfaceArea = new odata_query_objects_1.QNumberPath(_this.withPrefix("solidSurfaceArea"));
        _this.gravityCenter = new odata_query_objects_1.QStringPath(_this.withPrefix("gravityCenter"));
        _this.thumbnail = new odata_query_objects_1.QEntityPath(_this.withPrefix("thumbnail"), function () { return QPlt0File; });
        return _this;
    }
    return QCadPartBaseType;
}(QCadFileBaseType));
exports.QCadPartBaseType = QCadPartBaseType;
var QCadPart = /** @class */ (function (_super) {
    __extends(QCadPart, _super);
    function QCadPart() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.NeueCadPart": "QNeueCadPart", "neue.NeueCadAsm": "QNeueCadAsm" };
        return _this;
    }
    QCadPart.prototype.__asQNeueCadPart = function () {
        return new QNeueCadPart(this.withPrefix("neue.NeueCadPart"));
    };
    QCadPart.prototype.__asQNeueCadAsm = function () {
        return new QNeueCadAsm(this.withPrefix("neue.NeueCadAsm"));
    };
    return QCadPart;
}(QCadPartBaseType));
exports.QCadPart = QCadPart;
Object.defineProperties(QCadPart.prototype, {});
exports.qCadPart = new QCadPart();
var QNeueCadPart = /** @class */ (function (_super) {
    __extends(QNeueCadPart, _super);
    function QNeueCadPart() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QNeueCadPart;
}(QCadPartBaseType));
exports.QNeueCadPart = QNeueCadPart;
exports.qNeueCadPart = new QNeueCadPart();
var QNeueCadAsm = /** @class */ (function (_super) {
    __extends(QNeueCadAsm, _super);
    function QNeueCadAsm() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QNeueCadAsm;
}(QCadPartBaseType));
exports.QNeueCadAsm = QNeueCadAsm;
exports.qNeueCadAsm = new QNeueCadAsm();
var QTwoDimensionDrawingBaseType = /** @class */ (function (_super) {
    __extends(QTwoDimensionDrawingBaseType, _super);
    function QTwoDimensionDrawingBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.thumbnail = new odata_query_objects_1.QEntityPath(_this.withPrefix("thumbnail"), function () { return QPlt0File; });
        return _this;
    }
    return QTwoDimensionDrawingBaseType;
}(QCadFileBaseType));
exports.QTwoDimensionDrawingBaseType = QTwoDimensionDrawingBaseType;
var QTwoDimensionDrawing = /** @class */ (function (_super) {
    __extends(QTwoDimensionDrawing, _super);
    function QTwoDimensionDrawing() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.NeueTwoDimensionDrawing": "QNeueTwoDimensionDrawing" };
        return _this;
    }
    QTwoDimensionDrawing.prototype.__asQNeueTwoDimensionDrawing = function () {
        return new QNeueTwoDimensionDrawing(this.withPrefix("neue.NeueTwoDimensionDrawing"));
    };
    return QTwoDimensionDrawing;
}(QTwoDimensionDrawingBaseType));
exports.QTwoDimensionDrawing = QTwoDimensionDrawing;
Object.defineProperties(QTwoDimensionDrawing.prototype, {});
exports.qTwoDimensionDrawing = new QTwoDimensionDrawing();
var QNeueTwoDimensionDrawing = /** @class */ (function (_super) {
    __extends(QNeueTwoDimensionDrawing, _super);
    function QNeueTwoDimensionDrawing() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QNeueTwoDimensionDrawing;
}(QTwoDimensionDrawingBaseType));
exports.QNeueTwoDimensionDrawing = QNeueTwoDimensionDrawing;
exports.qNeueTwoDimensionDrawing = new QNeueTwoDimensionDrawing();
var QPlt0File2BaseType = /** @class */ (function (_super) {
    __extends(QPlt0File2BaseType, _super);
    function QPlt0File2BaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.description = new odata_query_objects_1.QStringPath(_this.withPrefix("description"));
        _this.softwareRev = new odata_query_objects_1.QStringPath(_this.withPrefix("softwareRev"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.storageItem = new odata_query_objects_1.QEntityPath(_this.withPrefix("storageItem"), function () { return QPlt0ApplicationObject; });
        return _this;
    }
    return QPlt0File2BaseType;
}(QPlt0ApplicationObjectBaseType));
exports.QPlt0File2BaseType = QPlt0File2BaseType;
var QPlt0File2 = /** @class */ (function (_super) {
    __extends(QPlt0File2, _super);
    function QPlt0File2() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "neue.Plt0IdiFile": "QPlt0IdiFile2" };
        return _this;
    }
    Object.defineProperty(QPlt0File2.prototype, "QPlt0IdiFile2_convertState", {
        get: function () {
            return this.__asQPlt0IdiFile2().convertState;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0File2.prototype, "QPlt0IdiFile2_triggerTime", {
        get: function () {
            return this.__asQPlt0IdiFile2().triggerTime;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlt0File2.prototype, "QPlt0IdiFile2_idiLightModelId", {
        get: function () {
            return this.__asQPlt0IdiFile2().idiLightModelId;
        },
        enumerable: false,
        configurable: true
    });
    QPlt0File2.prototype.__asQPlt0IdiFile2 = function () {
        return new QPlt0IdiFile2(this.withPrefix("neue.Plt0IdiFile"));
    };
    return QPlt0File2;
}(QPlt0File2BaseType));
exports.QPlt0File2 = QPlt0File2;
Object.defineProperties(QPlt0File2.prototype, { QPlt0IdiFile2_convertState: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile2_triggerTime: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPlt0IdiFile2_idiLightModelId: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlt0File2 = new QPlt0File2();
var QPlt0IdiFile2 = /** @class */ (function (_super) {
    __extends(QPlt0IdiFile2, _super);
    function QPlt0IdiFile2() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.convertState = new odata_query_objects_1.QEnumPath(_this.withPrefix("convertState"), PaaSModel_1.IdiConvertState);
        _this.triggerTime = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("triggerTime"));
        _this.idiLightModelId = new odata_query_objects_1.QStringPath(_this.withPrefix("idiLightModelId"));
        return _this;
    }
    return QPlt0IdiFile2;
}(QPlt0File2BaseType));
exports.QPlt0IdiFile2 = QPlt0IdiFile2;
exports.qPlt0IdiFile2 = new QPlt0IdiFile2();
var QRelCadBom = /** @class */ (function (_super) {
    __extends(QRelCadBom, _super);
    function QRelCadBom() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.transformationMatrix = new odata_query_objects_1.QStringPath(_this.withPrefix("transformationMatrix"));
        _this.configuration = new odata_query_objects_1.QStringPath(_this.withPrefix("configuration"));
        _this.quantity = new odata_query_objects_1.QNumberPath(_this.withPrefix("quantity"));
        _this.suppressed = new odata_query_objects_1.QBooleanPath(_this.withPrefix("suppressed"));
        _this.bomExcluded = new odata_query_objects_1.QBooleanPath(_this.withPrefix("bomExcluded"));
        _this.instanceName = new odata_query_objects_1.QStringPath(_this.withPrefix("instanceName"));
        return _this;
    }
    return QRelCadBom;
}(QPlt0RootRelationTypeBaseType));
exports.QRelCadBom = QRelCadBom;
exports.qRelCadBom = new QRelCadBom();
var QRelCadPartTwoDimensionDrawing = /** @class */ (function (_super) {
    __extends(QRelCadPartTwoDimensionDrawing, _super);
    function QRelCadPartTwoDimensionDrawing() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QRelCadPartTwoDimensionDrawing;
}(QPlt0RootRelationTypeBaseType));
exports.QRelCadPartTwoDimensionDrawing = QRelCadPartTwoDimensionDrawing;
exports.qRelCadPartTwoDimensionDrawing = new QRelCadPartTwoDimensionDrawing();
var QRelCadFileFile = /** @class */ (function (_super) {
    __extends(QRelCadFileFile, _super);
    function QRelCadFileFile() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.linkType = new odata_query_objects_1.QEnumPath(_this.withPrefix("linkType"), PaaSModel_1.RelCadFileFileLinkTypeEnum);
        _this.usageType = new odata_query_objects_1.QEnumPath(_this.withPrefix("usageType"), PaaSModel_1.RelCadFileFileUsageTypeEnum);
        return _this;
    }
    return QRelCadFileFile;
}(QPlt0RootRelationTypeBaseType));
exports.QRelCadFileFile = QRelCadFileFile;
exports.qRelCadFileFile = new QRelCadFileFile();
var QMappingConfig = /** @class */ (function (_super) {
    __extends(QMappingConfig, _super);
    function QMappingConfig() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.tool = new odata_query_objects_1.QEnumPath(_this.withPrefix("tool"), PaaSModel_1.MappingConfigToolEnum);
        _this.caxProperty = new odata_query_objects_1.QStringPath(_this.withPrefix("caxProperty"));
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.drawingSheetArea = new odata_query_objects_1.QStringPath(_this.withPrefix("drawingSheetArea"));
        _this.partType = new odata_query_objects_1.QEnumPath(_this.withPrefix("partType"), PaaSModel_1.MappingConfigPartTypeEnum);
        _this.cdpProperty = new odata_query_objects_1.QStringPath(_this.withPrefix("cdpProperty"));
        _this.onCax = new odata_query_objects_1.QBooleanPath(_this.withPrefix("onCax"));
        _this.direction = new odata_query_objects_1.QEnumPath(_this.withPrefix("direction"), PaaSModel_1.MappingConfigDirectionEnum);
        return _this;
    }
    return QMappingConfig;
}(QPlt0ApplicationObjectBaseType));
exports.QMappingConfig = QMappingConfig;
exports.qMappingConfig = new QMappingConfig();
var Qtest_product = /** @class */ (function (_super) {
    __extends(Qtest_product, _super);
    function Qtest_product() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return Qtest_product;
}(QPlt0ApplicationObjectBaseType));
exports.Qtest_product = Qtest_product;
exports.qtest_product = new Qtest_product();
var Qtest_product001 = /** @class */ (function (_super) {
    __extends(Qtest_product001, _super);
    function Qtest_product001() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return Qtest_product001;
}(QPlt0ApplicationObjectBaseType));
exports.Qtest_product001 = Qtest_product001;
exports.qtest_product001 = new Qtest_product001();
var QTest_ProductModel = /** @class */ (function (_super) {
    __extends(QTest_ProductModel, _super);
    function QTest_ProductModel() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QTest_ProductModel;
}(QPlt0ApplicationObjectBaseType));
exports.QTest_ProductModel = QTest_ProductModel;
exports.qTest_ProductModel = new QTest_ProductModel();
var QTest_ProductModel111 = /** @class */ (function (_super) {
    __extends(QTest_ProductModel111, _super);
    function QTest_ProductModel111() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Productname = new odata_query_objects_1.QStringPath(_this.withPrefix("Productname"));
        return _this;
    }
    return QTest_ProductModel111;
}(QPlt0ApplicationObjectBaseType));
exports.QTest_ProductModel111 = QTest_ProductModel111;
exports.qTest_ProductModel111 = new QTest_ProductModel111();
var QTest_ProductModel222 = /** @class */ (function (_super) {
    __extends(QTest_ProductModel222, _super);
    function QTest_ProductModel222() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QTest_ProductModel222;
}(QPlt0ApplicationObjectBaseType));
exports.QTest_ProductModel222 = QTest_ProductModel222;
exports.qTest_ProductModel222 = new QTest_ProductModel222();
var QTest_ProductModel333 = /** @class */ (function (_super) {
    __extends(QTest_ProductModel333, _super);
    function QTest_ProductModel333() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return QTest_ProductModel333;
}(QPlt0ApplicationObjectBaseType));
exports.QTest_ProductModel333 = QTest_ProductModel333;
exports.qTest_ProductModel333 = new QTest_ProductModel333();
var QTest_ProductModel444 = /** @class */ (function (_super) {
    __extends(QTest_ProductModel444, _super);
    function QTest_ProductModel444() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.name = new odata_query_objects_1.QStringPath(_this.withPrefix("name"));
        _this.prince = new odata_query_objects_1.QNumberPath(_this.withPrefix("prince"));
        return _this;
    }
    return QTest_ProductModel444;
}(QPlt0ApplicationObjectBaseType));
exports.QTest_ProductModel444 = QTest_ProductModel444;
exports.qTest_ProductModel444 = new QTest_ProductModel444();
var QFileSignatureUrl = /** @class */ (function (_super) {
    __extends(QFileSignatureUrl, _super);
    function QFileSignatureUrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.storageItemId = new odata_query_objects_1.QStringPath(_this.withPrefix("storageItemId"));
        _this.fileId = new odata_query_objects_1.QStringPath(_this.withPrefix("fileId"));
        _this.signatureUrl = new odata_query_objects_1.QStringPath(_this.withPrefix("signatureUrl"));
        return _this;
    }
    return QFileSignatureUrl;
}(odata_query_objects_1.QueryObject));
exports.QFileSignatureUrl = QFileSignatureUrl;
exports.qFileSignatureUrl = new QFileSignatureUrl();
var QGetRelationTreeByNcid = /** @class */ (function (_super) {
    __extends(QGetRelationTreeByNcid, _super);
    function QGetRelationTreeByNcid() {
        var _this = _super.call(this, "GetRelationTreeByNcid", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX, new odata_query_objects_1.QComplexParam("NONE", new QPlt0RelationQuery))) || this;
        _this.params = [new odata_query_objects_1.QStringParam("ncid"), new odata_query_objects_1.QStringParam("typeNcid"), new odata_query_objects_1.QBooleanParam("isTree")];
        return _this;
    }
    QGetRelationTreeByNcid.prototype.getParams = function () {
        return this.params;
    };
    return QGetRelationTreeByNcid;
}(odata_query_objects_1.QFunction));
exports.QGetRelationTreeByNcid = QGetRelationTreeByNcid;
var QDeploy = /** @class */ (function (_super) {
    __extends(QDeploy, _super);
    function QDeploy() {
        var _this = _super.call(this, "Deploy") || this;
        _this.params = [new odata_query_objects_1.QStringParam("entityTypeIds")];
        return _this;
    }
    QDeploy.prototype.getParams = function () {
        return this.params;
    };
    return QDeploy;
}(odata_query_objects_1.QAction));
exports.QDeploy = QDeploy;
var QBatchGetFileSignatureUrl = /** @class */ (function (_super) {
    __extends(QBatchGetFileSignatureUrl, _super);
    function QBatchGetFileSignatureUrl() {
        var _this = _super.call(this, "BatchGetFileSignatureUrl", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX_COLLECTION, new odata_query_objects_1.QComplexParam("NONE", new QFileSignatureUrl))) || this;
        _this.params = [new odata_query_objects_1.QStringParam("fileIds"), new odata_query_objects_1.QEnumParam("actionType")];
        return _this;
    }
    QBatchGetFileSignatureUrl.prototype.getParams = function () {
        return this.params;
    };
    return QBatchGetFileSignatureUrl;
}(odata_query_objects_1.QAction));
exports.QBatchGetFileSignatureUrl = QBatchGetFileSignatureUrl;
