 .custom-collapse {
  margin-top: 16px;
  .el-collapse-item {
    border: 0 none;
    &:nth-child(n+2) {
      margin-top: 4px;
    }

  }
  .el-collapse-item__wrap {
    border-bottom: 0 none;
  }
  .title-wrapper {
    order: 1;
  }
  .el-collapse-item__arrow {
    order: -1;
    margin-left: 0px;
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: rgba(0, 0, 0, 0.85);
    &:before {
      content: "\e724";
    }
    .collapse-content {
    }
  }
 }
  .custom-collapse .el-collapse-item__header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 0 none;
    height: 24px;
    /* TODO: 变量替换 */
    background-color: #F0F3FA;
    padding: 0 2px;
    border-top: 0 none;
    border-bottom: 0 none;
  }
