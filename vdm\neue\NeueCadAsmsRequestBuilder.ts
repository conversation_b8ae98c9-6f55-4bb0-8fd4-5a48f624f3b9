/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { NeueCadAsms } from './NeueCadAsms';

/**
 * Request builder class for operations supported on the {@link NeueCadAsms} entity.
 */
export class NeueCadAsmsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<NeueCadAsms<T>, T> {
  /**
   * Returns a request builder for querying all `NeueCadAsms` entities.
   * @returns A request builder for creating requests to retrieve all `NeueCadAsms` entities.
   */
  getAll(): GetAllRequestBuilder<NeueCadAsms<T>, T> {
    return new GetAllRequestBuilder<NeueCadAsms<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `NeueCadAsms` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `NeueCadAsms`.
   */
  create(entity: NeueCadAsms<T>): CreateRequestBuilder<NeueCadAsms<T>, T> {
    return new CreateRequestBuilder<NeueCadAsms<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `NeueCadAsms` entity based on its keys.
   * @param ncid Key property. See {@link NeueCadAsms.ncid}.
   * @returns A request builder for creating requests to retrieve one `NeueCadAsms` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<NeueCadAsms<T>, T> {
    return new GetByKeyRequestBuilder<NeueCadAsms<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `NeueCadAsms`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `NeueCadAsms`.
   */
  update(entity: NeueCadAsms<T>): UpdateRequestBuilder<NeueCadAsms<T>, T> {
    return new UpdateRequestBuilder<NeueCadAsms<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `NeueCadAsms`.
   * @param ncid Key property. See {@link NeueCadAsms.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `NeueCadAsms`.
   */
  delete(ncid: string): DeleteRequestBuilder<NeueCadAsms<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `NeueCadAsms`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `NeueCadAsms` by taking the entity as a parameter.
   */
  delete(entity: NeueCadAsms<T>): DeleteRequestBuilder<NeueCadAsms<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<NeueCadAsms<T>, T> {
    return new DeleteRequestBuilder<NeueCadAsms<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof NeueCadAsms
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
