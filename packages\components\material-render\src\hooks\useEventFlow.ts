import { get } from 'lodash-unified'
import { strReplaceTemplate } from '@neue-plus/utils'
import { ActionNode } from '../../types'
import { useEventContext } from '../context/event-flow'
import { request as requestService } from '../handlers/handleRequest'

export interface EventNode {
  action: ActionNode
  next?: EventNode | null // 顺序执行节点
  branches?: Record<string, EventNode | null> // 条件分支节点
}

/**
 * useEventFlow - 支持顺序 + 条件分支的事件执行器
 */
export function useEventFlow() {
  const { refs, handlers } = useEventContext()

  function run(actions: ActionNode[] = [], params: Record<string, any> = {}) {
    const map: Record<string, EventNode> = {}
    let head: EventNode | null = null

    // 创建所有节点
    for (const action of actions) {
      map[action.id] = { action, next: null, branches: {} }
    }
    // 链接节点顺序和分支
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i]
      const node = map[action.id]

      // 顺序 next
      if (i + 1 < actions.length) node.next = map[actions[i + 1].id]

      // 条件分支
      const { branches = {} } = action.config || {}
      for (const key in branches) {
        const targetId = branches[key]
        if (targetId && map[targetId]) node.branches![key] = map[targetId]
      }

      if (i === 0) head = node
    }

    if (head) exec(head, params)
  }

  async function exec(node: EventNode | null, params: Record<string, any>) {
    if (!node || !node.action) return

    const {
      actionType = '',
      delay = 0,
      target = '',
      request,
      href,
    } = node.action.config || {}
    if (href) {
      const newHref = strReplaceTemplate(href, params.clickParams[0])
      window.open(newHref, '_blank')
      return
    }
    if (delay > 0) await new Promise((resolve) => setTimeout(resolve, delay))

    let branchResult: string | undefined
    let resData = {}
    try {
      // axios 异步请求
      if (request) {
        branchResult = 'fail'
        const response = await requestService({
          ...request,
          replaceData: params,
        })
        branchResult = 'success'
        resData = request.responsePath
          ? get(response.data, request.responsePath)
          : response.data
      } else {
        // 常规 handler
        const handler = refs[target]?.[actionType] || handlers[actionType]
        if (typeof handler === 'function') {
          branchResult = await handler(node.action, params, refs)
        }
      }
    } catch {}
    // 条件分支优先
    if (branchResult && node.branches?.[branchResult]) {
      await exec(node.branches[branchResult], { ...params, ...resData })
      return
    }

    // 顺序执行 next
    if (node.next) {
      await exec(node.next, { ...params, ...resData })
    }
  }

  return { run }
}
