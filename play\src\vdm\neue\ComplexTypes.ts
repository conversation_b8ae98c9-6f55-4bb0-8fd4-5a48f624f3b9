/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers
} from '@sap-cloud-sdk/odata-v4';
import type { ComplexTypesApi } from './ComplexTypesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "ComplexTypes" of service "neue".
 */
export class ComplexTypes<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements ComplexTypesType<T>
{
  /**
   * Technical entity name for ComplexTypes.
   */
  static override _entityName = 'ComplexTypes';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the ComplexTypes entity.
   */
  static _keys = ['ncid'];
  /**
   * Complex Type Code.
   */
  declare complexTypeCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Display Name.
   */
  declare displayName: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Abstract.
   * @nullable
   */
  declare isAbstract?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Open.
   * @nullable
   */
  declare isOpen?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: ComplexTypesApi<T>) {
    super(_entityApi);
  }
}

export interface ComplexTypesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  complexTypeCode: DeserializedType<T, 'Edm.String'>;
  name: DeserializedType<T, 'Edm.String'>;
  displayName: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  isAbstract?: DeserializedType<T, 'Edm.Boolean'> | null;
  isOpen?: DeserializedType<T, 'Edm.Boolean'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
