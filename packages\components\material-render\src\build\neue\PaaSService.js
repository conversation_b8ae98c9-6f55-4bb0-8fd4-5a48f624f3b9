"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Plt0ActionService = exports.Plt0NavigationPropertyCollectionService = exports.Plt0NavigationPropertyService = exports.Plt0StructuralPropertyCollectionService = exports.Plt0StructuralPropertyService = exports.Plt0TypeDefinitionCollectionService = exports.Plt0TypeDefinitionService = exports.Plt0EnumTypeCollectionService = exports.Plt0EnumTypeService = exports.Plt0ComplexTypeCollectionService = exports.Plt0ComplexTypeService = exports.Plt0EntityTypePropertyOverrideCollectionService = exports.Plt0EntityTypePropertyOverrideService = exports.Plt0EntityTypeUnionUniqPropertyCollectionService = exports.Plt0EntityTypeUnionUniqPropertyService = exports.Plt0EntityTypeUnionUniqCollectionService = exports.Plt0EntityTypeUnionUniqService = exports.Plt0EntityTypeCollectionService = exports.Plt0EntityTypeService = exports.Plt0BaseTypeCollectionService = exports.Plt0BaseTypeService = exports.Plt0SchemaCollectionService = exports.Plt0SchemaService = exports.Plt0RelationFromOrToTypeCollectionService = exports.Plt0RelationFromOrToTypeService = exports.Plt0RootRelationTypeConfigCollectionService = exports.Plt0RootRelationTypeConfigService = exports.Plt0RelationQueryCollectionService = exports.Plt0RelationQueryService = exports.Plt0RootRelationTypeCollectionService = exports.Plt0RootRelationTypeService = exports.Plt0IdiFileCollectionService = exports.Plt0IdiFileService = exports.Plt0FileCollectionService = exports.Plt0FileService = exports.Plt0MrvObjectCollectionService = exports.Plt0MrvObjectService = exports.Plt0LifecycleStatusStrategyCollectionService = exports.Plt0LifecycleStatusStrategyService = exports.Plt0LifecycleStatusCollectionService = exports.Plt0LifecycleStatusService = exports.Plt0VersionObjectCollectionService = exports.Plt0VersionObjectService = exports.Plt0RevisionObjectCollectionService = exports.Plt0RevisionObjectService = exports.Plt0MasterObjectCollectionService = exports.Plt0MasterObjectService = exports.Plt0ApplicationObjectCollectionService = exports.Plt0ApplicationObjectService = exports.PaaSService = void 0;
exports.Test_ProductModel111Service = exports.Test_ProductModelCollectionService = exports.Test_ProductModelService = exports.test_product001CollectionService = exports.test_product001Service = exports.test_productCollectionService = exports.test_productService = exports.MappingConfigCollectionService = exports.MappingConfigService = exports.RelCadFileFileCollectionService = exports.RelCadFileFileService = exports.RelCadPartTwoDimensionDrawingCollectionService = exports.RelCadPartTwoDimensionDrawingService = exports.RelCadBomCollectionService = exports.RelCadBomService = exports.Plt0IdiFile2CollectionService = exports.Plt0IdiFile2Service = exports.Plt0File2CollectionService = exports.Plt0File2Service = exports.NeueTwoDimensionDrawingCollectionService = exports.NeueTwoDimensionDrawingService = exports.TwoDimensionDrawingCollectionService = exports.TwoDimensionDrawingService = exports.NeueCadAsmCollectionService = exports.NeueCadAsmService = exports.NeueCadPartCollectionService = exports.NeueCadPartService = exports.CadPartCollectionService = exports.CadPartService = exports.CadFileCollectionService = exports.CadFileService = exports.Plt0BusinessObjectRevisionNumberCodeCollectionService = exports.Plt0BusinessObjectRevisionNumberCodeService = exports.Plt0BusinessObjectRevisionNumberRuleCollectionService = exports.Plt0BusinessObjectRevisionNumberRuleService = exports.Plt0FunctionImportCollectionService = exports.Plt0FunctionImportService = exports.Plt0ActionImportCollectionService = exports.Plt0ActionImportService = exports.Plt0NavigationPropertyBindingCollectionService = exports.Plt0NavigationPropertyBindingService = exports.Plt0SingletonCollectionService = exports.Plt0SingletonService = exports.Plt0EntitySetCollectionService = exports.Plt0EntitySetService = exports.Plt0EntityContainerCollectionService = exports.Plt0EntityContainerService = exports.Plt0FunctionCollectionService = exports.Plt0FunctionService = exports.Plt0ActionCollectionService = void 0;
exports.Test_ProductModel444CollectionService = exports.Test_ProductModel444Service = exports.Test_ProductModel333CollectionService = exports.Test_ProductModel333Service = exports.Test_ProductModel222CollectionService = exports.Test_ProductModel222Service = exports.Test_ProductModel111CollectionService = void 0;
var odata_query_objects_1 = require("@odata2ts/odata-query-objects");
var odata_service_1 = require("@odata2ts/odata-service");
var QPaaS_1 = require("./QPaaS");
var PaaSService = /** @class */ (function (_super) {
    __extends(PaaSService, _super);
    function PaaSService() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PaaSService.prototype.Schemas = function (id) {
        var fieldName = "Schemas";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0SchemaCollectionService(client, path, fieldName, options)
            : new Plt0SchemaService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntityTypes = function (id) {
        var fieldName = "EntityTypes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntityTypeCollectionService(client, path, fieldName, options)
            : new Plt0EntityTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.ComplexTypes = function (id) {
        var fieldName = "ComplexTypes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0ComplexTypeCollectionService(client, path, fieldName, options)
            : new Plt0ComplexTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EnumTypes = function (id) {
        var fieldName = "EnumTypes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EnumTypeCollectionService(client, path, fieldName, options)
            : new Plt0EnumTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.TypeDefinitions = function (id) {
        var fieldName = "TypeDefinitions";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0TypeDefinitionCollectionService(client, path, fieldName, options)
            : new Plt0TypeDefinitionService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.StructuralProperties = function (id) {
        var fieldName = "StructuralProperties";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0StructuralPropertyCollectionService(client, path, fieldName, options)
            : new Plt0StructuralPropertyService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.NavigationProperties = function (id) {
        var fieldName = "NavigationProperties";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0NavigationPropertyCollectionService(client, path, fieldName, options)
            : new Plt0NavigationPropertyService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Actions = function (id) {
        var fieldName = "Actions";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0ActionCollectionService(client, path, fieldName, options)
            : new Plt0ActionService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Functions = function (id) {
        var fieldName = "Functions";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0FunctionCollectionService(client, path, fieldName, options)
            : new Plt0FunctionService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntityContainers = function (id) {
        var fieldName = "EntityContainers";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntityContainerCollectionService(client, path, fieldName, options)
            : new Plt0EntityContainerService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntitySets = function (id) {
        var fieldName = "EntitySets";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntitySetCollectionService(client, path, fieldName, options)
            : new Plt0EntitySetService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Singletons = function (id) {
        var fieldName = "Singletons";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0SingletonCollectionService(client, path, fieldName, options)
            : new Plt0SingletonService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.NavigationPropertyBindings = function (id) {
        var fieldName = "NavigationPropertyBindings";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0NavigationPropertyBindingCollectionService(client, path, fieldName, options)
            : new Plt0NavigationPropertyBindingService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.ActionImports = function (id) {
        var fieldName = "ActionImports";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0ActionImportCollectionService(client, path, fieldName, options)
            : new Plt0ActionImportService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.FunctionImports = function (id) {
        var fieldName = "FunctionImports";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0FunctionImportCollectionService(client, path, fieldName, options)
            : new Plt0FunctionImportService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.BusinessObjectRevisionNumberRules = function (id) {
        var fieldName = "BusinessObjectRevisionNumberRules";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0BusinessObjectRevisionNumberRuleCollectionService(client, path, fieldName, options)
            : new Plt0BusinessObjectRevisionNumberRuleService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.BusinessObjectRevisionNumberCodes = function (id) {
        var fieldName = "BusinessObjectRevisionNumberCodes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0BusinessObjectRevisionNumberCodeCollectionService(client, path, fieldName, options)
            : new Plt0BusinessObjectRevisionNumberCodeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.LifecycleStatuses = function (id) {
        var fieldName = "LifecycleStatuses";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0LifecycleStatusCollectionService(client, path, fieldName, options)
            : new Plt0LifecycleStatusService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.LifecycleStatusStrategies = function (id) {
        var fieldName = "LifecycleStatusStrategies";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0LifecycleStatusStrategyCollectionService(client, path, fieldName, options)
            : new Plt0LifecycleStatusStrategyService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.FromOrToTypes = function (id) {
        var fieldName = "FromOrToTypes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0RelationFromOrToTypeCollectionService(client, path, fieldName, options)
            : new Plt0RelationFromOrToTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.RootRelationTypes = function (id) {
        var fieldName = "RootRelationTypes";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0RootRelationTypeCollectionService(client, path, fieldName, options)
            : new Plt0RootRelationTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntityTypeUnionUniqs = function (id) {
        var fieldName = "EntityTypeUnionUniqs";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntityTypeUnionUniqCollectionService(client, path, fieldName, options)
            : new Plt0EntityTypeUnionUniqService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntityTypeUnionUniqProperties = function (id) {
        var fieldName = "EntityTypeUnionUniqProperties";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntityTypeUnionUniqPropertyCollectionService(client, path, fieldName, options)
            : new Plt0EntityTypeUnionUniqPropertyService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Files = function (id) {
        var fieldName = "Files";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0FileCollectionService(client, path, fieldName, options)
            : new Plt0FileService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.IdiFiles = function (id) {
        var fieldName = "IdiFiles";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0IdiFileCollectionService(client, path, fieldName, options)
            : new Plt0IdiFileService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.EntityTypePropertyOverrides = function (id) {
        var fieldName = "EntityTypePropertyOverrides";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0EntityTypePropertyOverrideCollectionService(client, path, fieldName, options)
            : new Plt0EntityTypePropertyOverrideService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.CadParts = function (id) {
        var fieldName = "CadParts";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new CadPartCollectionService(client, path, fieldName, options)
            : new CadPartService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.NeueCadParts = function (id) {
        var fieldName = "NeueCadParts";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new NeueCadPartCollectionService(client, path, fieldName, options)
            : new NeueCadPartService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.NeueCadAsms = function (id) {
        var fieldName = "NeueCadAsms";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new NeueCadAsmCollectionService(client, path, fieldName, options)
            : new NeueCadAsmService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.NeueTwoDimensionDrawings = function (id) {
        var fieldName = "NeueTwoDimensionDrawings";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new NeueTwoDimensionDrawingCollectionService(client, path, fieldName, options)
            : new NeueTwoDimensionDrawingService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.RelCadBomRelations = function (id) {
        var fieldName = "RelCadBomRelations";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new RelCadBomCollectionService(client, path, fieldName, options)
            : new RelCadBomService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.RelCadPartTwoDimensionDrawingRelations = function (id) {
        var fieldName = "RelCadPartTwoDimensionDrawingRelations";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new RelCadPartTwoDimensionDrawingCollectionService(client, path, fieldName, options)
            : new RelCadPartTwoDimensionDrawingService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.RelCadFileFileRelations = function (id) {
        var fieldName = "RelCadFileFileRelations";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new RelCadFileFileCollectionService(client, path, fieldName, options)
            : new RelCadFileFileService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.MappingConfigs = function (id) {
        var fieldName = "MappingConfigs";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new MappingConfigCollectionService(client, path, fieldName, options)
            : new MappingConfigService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Test_CollectionProducts = function (id) {
        var fieldName = "Test_CollectionProducts";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Test_ProductModelCollectionService(client, path, fieldName, options)
            : new Test_ProductModelService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Test_CollectionProduct1113 = function (id) {
        var fieldName = "Test_CollectionProduct111";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Test_ProductModel222CollectionService(client, path, fieldName, options)
            : new Test_ProductModel222Service(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Test_CollectionProduct222 = function (id) {
        var fieldName = "Test_CollectionProduct222";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Test_ProductModel222CollectionService(client, path, fieldName, options)
            : new Test_ProductModel222Service(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Test_CollectionProduct333 = function (id) {
        var fieldName = "Test_CollectionProduct333";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Test_ProductModel333CollectionService(client, path, fieldName, options)
            : new Test_ProductModel333Service(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.Test_CollectionProduct444 = function (id) {
        var fieldName = "Test_CollectionProduct444";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Test_ProductModel444CollectionService(client, path, fieldName, options)
            : new Test_ProductModel444Service(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PaaSService.prototype.GetRelationTreeByNcid = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._QGetRelationTreeByNcid) {
                            this._QGetRelationTreeByNcid = new QPaaS_1.QGetRelationTreeByNcid();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._QGetRelationTreeByNcid.buildUrl(params, isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._QGetRelationTreeByNcid.convertResponse(response)];
                }
            });
        });
    };
    PaaSService.prototype.Deploy = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._QDeploy) {
                            this._QDeploy = new QPaaS_1.QDeploy();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._QDeploy.buildUrl());
                        return [4 /*yield*/, client.post(url, this._QDeploy.convertUserParams(params), requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._QDeploy.convertResponse(response)];
                }
            });
        });
    };
    PaaSService.prototype.BatchGetFileSignatureUrl = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._QBatchGetFileSignatureUrl) {
                            this._QBatchGetFileSignatureUrl = new QPaaS_1.QBatchGetFileSignatureUrl();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._QBatchGetFileSignatureUrl.buildUrl());
                        return [4 /*yield*/, client.post(url, this._QBatchGetFileSignatureUrl.convertUserParams(params), requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._QBatchGetFileSignatureUrl.convertResponse(response)];
                }
            });
        });
    };
    return PaaSService;
}(odata_service_1.ODataService));
exports.PaaSService = PaaSService;
var Plt0ApplicationObjectService = /** @class */ (function (_super) {
    __extends(Plt0ApplicationObjectService, _super);
    function Plt0ApplicationObjectService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ApplicationObject, options) || this;
    }
    Plt0ApplicationObjectService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0ApplicationObjectService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0ApplicationObjectService.prototype.GenerateCode = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Plt0ApplicationObject_QGenerateCode) {
                            this._Plt0ApplicationObject_QGenerateCode = new QPaaS_1.Plt0ApplicationObject_QGenerateCode();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Plt0ApplicationObject_QGenerateCode.buildUrl());
                        return [4 /*yield*/, client.post(url, this._Plt0ApplicationObject_QGenerateCode.convertUserParams(params), requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Plt0ApplicationObject_QGenerateCode.convertResponse(response)];
                }
            });
        });
    };
    Plt0ApplicationObjectService.prototype.asPlt0MasterObjectService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0MasterObjectService(client, path, "BuiltInModel.Plt0MasterObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0RevisionObjectService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RevisionObjectService(client, path, "BuiltInModel.Plt0RevisionObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0VersionObjectService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0VersionObjectService(client, path, "BuiltInModel.Plt0VersionObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0LifecycleStatusService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0LifecycleStatusService(client, path, "BuiltInModel.Plt0LifecycleStatus", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0LifecycleStatusStrategyService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0LifecycleStatusStrategyService(client, path, "BuiltInModel.Plt0LifecycleStatusStrategy", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0MrvObjectService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0MrvObjectService(client, path, "BuiltInModel.Plt0MrvObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0FileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FileService(client, path, "BuiltInModel.Plt0File", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0IdiFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFileService(client, path, "BuiltInModel.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0RootRelationTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RootRelationTypeService(client, path, "BuiltInModel.Plt0RootRelationType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0RelationQueryService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RelationQueryService(client, path, "BuiltInModel.Plt0RelationQuery", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0RootRelationTypeConfigService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RootRelationTypeConfigService(client, path, "BuiltInModel.Plt0RootRelationTypeConfig", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0RelationFromOrToTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RelationFromOrToTypeService(client, path, "BuiltInModel.Plt0RelationFromOrToType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0SchemaService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0SchemaService(client, path, "BuiltInModel.Plt0Schema", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0BaseTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BaseTypeService(client, path, "BuiltInModel.Plt0BaseType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntityTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeService(client, path, "BuiltInModel.Plt0EntityType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntityTypeUnionUniqService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeUnionUniqService(client, path, "BuiltInModel.Plt0EntityTypeUnionUniq", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntityTypeUnionUniqPropertyService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeUnionUniqPropertyService(client, path, "BuiltInModel.Plt0EntityTypeUnionUniqProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntityTypePropertyOverrideService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypePropertyOverrideService(client, path, "BuiltInModel.Plt0EntityTypePropertyOverride", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0ComplexTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ComplexTypeService(client, path, "BuiltInModel.Plt0ComplexType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EnumTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EnumTypeService(client, path, "BuiltInModel.Plt0EnumType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0TypeDefinitionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0TypeDefinitionService(client, path, "BuiltInModel.Plt0TypeDefinition", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0StructuralPropertyService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0StructuralPropertyService(client, path, "BuiltInModel.Plt0StructuralProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0NavigationPropertyService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0NavigationPropertyService(client, path, "BuiltInModel.Plt0NavigationProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0ActionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ActionService(client, path, "BuiltInModel.Plt0Action", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0FunctionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FunctionService(client, path, "BuiltInModel.Plt0Function", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntityContainerService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityContainerService(client, path, "BuiltInModel.Plt0EntityContainer", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0EntitySetService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntitySetService(client, path, "BuiltInModel.Plt0EntitySet", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0SingletonService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0SingletonService(client, path, "BuiltInModel.Plt0Singleton", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0NavigationPropertyBindingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0NavigationPropertyBindingService(client, path, "BuiltInModel.Plt0NavigationPropertyBinding", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0ActionImportService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ActionImportService(client, path, "BuiltInModel.Plt0ActionImport", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0FunctionImportService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FunctionImportService(client, path, "BuiltInModel.Plt0FunctionImport", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0BusinessObjectRevisionNumberRuleService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BusinessObjectRevisionNumberRuleService(client, path, "BuiltInModel.Plt0BusinessObjectRevisionNumberRule", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0BusinessObjectRevisionNumberCodeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BusinessObjectRevisionNumberCodeService(client, path, "BuiltInModel.Plt0BusinessObjectRevisionNumberCode", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asCadFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadFileService(client, path, "neue.CadFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asNeueCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asNeueCadAsmService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asNeueTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0File2Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0File2Service(client, path, "neue.Plt0File", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asPlt0IdiFile2Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFile2Service(client, path, "neue.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asRelCadBomService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadBomService(client, path, "neue.RelCadBom", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asRelCadPartTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadPartTwoDimensionDrawingService(client, path, "neue.RelCadPartTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asRelCadFileFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadFileFileService(client, path, "neue.RelCadFileFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asMappingConfigService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new MappingConfigService(client, path, "neue.MappingConfig", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_productService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new test_productService(client, path, "neue.test_product", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_product001Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new test_product001Service(client, path, "neue.test_product001", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_ProductModelService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModelService(client, path, "neue.Test_ProductModel", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_ProductModel111Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel111Service(client, path, "neue.Test_ProductModel111", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_ProductModel222Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel222Service(client, path, "neue.Test_ProductModel222", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_ProductModel333Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel333Service(client, path, "neue.Test_ProductModel333", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectService.prototype.asTest_ProductModel444Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel444Service(client, path, "neue.Test_ProductModel444", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0ApplicationObjectService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0ApplicationObjectService = Plt0ApplicationObjectService;
var Plt0ApplicationObjectCollectionService = /** @class */ (function (_super) {
    __extends(Plt0ApplicationObjectCollectionService, _super);
    function Plt0ApplicationObjectCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ApplicationObject, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0ApplicationObjectCollectionService.prototype.asPlt0MasterObjectCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0MasterObjectCollectionService(client, path, "BuiltInModel.Plt0MasterObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0RevisionObjectCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RevisionObjectCollectionService(client, path, "BuiltInModel.Plt0RevisionObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0VersionObjectCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0VersionObjectCollectionService(client, path, "BuiltInModel.Plt0VersionObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0LifecycleStatusCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0LifecycleStatusCollectionService(client, path, "BuiltInModel.Plt0LifecycleStatus", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0LifecycleStatusStrategyCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0LifecycleStatusStrategyCollectionService(client, path, "BuiltInModel.Plt0LifecycleStatusStrategy", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0MrvObjectCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0MrvObjectCollectionService(client, path, "BuiltInModel.Plt0MrvObject", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0FileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FileCollectionService(client, path, "BuiltInModel.Plt0File", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0IdiFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFileCollectionService(client, path, "BuiltInModel.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0RootRelationTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RootRelationTypeCollectionService(client, path, "BuiltInModel.Plt0RootRelationType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0RelationQueryCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RelationQueryCollectionService(client, path, "BuiltInModel.Plt0RelationQuery", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0RootRelationTypeConfigCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RootRelationTypeConfigCollectionService(client, path, "BuiltInModel.Plt0RootRelationTypeConfig", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0RelationFromOrToTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0RelationFromOrToTypeCollectionService(client, path, "BuiltInModel.Plt0RelationFromOrToType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0SchemaCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0SchemaCollectionService(client, path, "BuiltInModel.Plt0Schema", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0BaseTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BaseTypeCollectionService(client, path, "BuiltInModel.Plt0BaseType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntityTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeCollectionService(client, path, "BuiltInModel.Plt0EntityType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntityTypeUnionUniqCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeUnionUniqCollectionService(client, path, "BuiltInModel.Plt0EntityTypeUnionUniq", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntityTypeUnionUniqPropertyCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeUnionUniqPropertyCollectionService(client, path, "BuiltInModel.Plt0EntityTypeUnionUniqProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntityTypePropertyOverrideCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypePropertyOverrideCollectionService(client, path, "BuiltInModel.Plt0EntityTypePropertyOverride", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0ComplexTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ComplexTypeCollectionService(client, path, "BuiltInModel.Plt0ComplexType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EnumTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EnumTypeCollectionService(client, path, "BuiltInModel.Plt0EnumType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0TypeDefinitionCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0TypeDefinitionCollectionService(client, path, "BuiltInModel.Plt0TypeDefinition", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0StructuralPropertyCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0StructuralPropertyCollectionService(client, path, "BuiltInModel.Plt0StructuralProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0NavigationPropertyCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0NavigationPropertyCollectionService(client, path, "BuiltInModel.Plt0NavigationProperty", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0ActionCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ActionCollectionService(client, path, "BuiltInModel.Plt0Action", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0FunctionCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FunctionCollectionService(client, path, "BuiltInModel.Plt0Function", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntityContainerCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityContainerCollectionService(client, path, "BuiltInModel.Plt0EntityContainer", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0EntitySetCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntitySetCollectionService(client, path, "BuiltInModel.Plt0EntitySet", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0SingletonCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0SingletonCollectionService(client, path, "BuiltInModel.Plt0Singleton", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0NavigationPropertyBindingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0NavigationPropertyBindingCollectionService(client, path, "BuiltInModel.Plt0NavigationPropertyBinding", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0ActionImportCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ActionImportCollectionService(client, path, "BuiltInModel.Plt0ActionImport", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0FunctionImportCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0FunctionImportCollectionService(client, path, "BuiltInModel.Plt0FunctionImport", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0BusinessObjectRevisionNumberRuleCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BusinessObjectRevisionNumberRuleCollectionService(client, path, "BuiltInModel.Plt0BusinessObjectRevisionNumberRule", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0BusinessObjectRevisionNumberCodeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0BusinessObjectRevisionNumberCodeCollectionService(client, path, "BuiltInModel.Plt0BusinessObjectRevisionNumberCode", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asCadFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadFileCollectionService(client, path, "neue.CadFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartCollectionService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asNeueCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartCollectionService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asNeueCadAsmCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmCollectionService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingCollectionService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asNeueTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingCollectionService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0File2CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0File2CollectionService(client, path, "neue.Plt0File", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asPlt0IdiFile2CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFile2CollectionService(client, path, "neue.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asRelCadBomCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadBomCollectionService(client, path, "neue.RelCadBom", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asRelCadPartTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadPartTwoDimensionDrawingCollectionService(client, path, "neue.RelCadPartTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asRelCadFileFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadFileFileCollectionService(client, path, "neue.RelCadFileFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asMappingConfigCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new MappingConfigCollectionService(client, path, "neue.MappingConfig", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_productCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new test_productCollectionService(client, path, "neue.test_product", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_product001CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new test_product001CollectionService(client, path, "neue.test_product001", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_ProductModelCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModelCollectionService(client, path, "neue.Test_ProductModel", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_ProductModel111CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel111CollectionService(client, path, "neue.Test_ProductModel111", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_ProductModel222CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel222CollectionService(client, path, "neue.Test_ProductModel222", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_ProductModel333CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel333CollectionService(client, path, "neue.Test_ProductModel333", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0ApplicationObjectCollectionService.prototype.asTest_ProductModel444CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Test_ProductModel444CollectionService(client, path, "neue.Test_ProductModel444", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0ApplicationObjectCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0ApplicationObjectCollectionService = Plt0ApplicationObjectCollectionService;
var Plt0MasterObjectService = /** @class */ (function (_super) {
    __extends(Plt0MasterObjectService, _super);
    function Plt0MasterObjectService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0MasterObject, options) || this;
    }
    Plt0MasterObjectService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0MasterObjectService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0MasterObjectService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0MasterObjectService = Plt0MasterObjectService;
var Plt0MasterObjectCollectionService = /** @class */ (function (_super) {
    __extends(Plt0MasterObjectCollectionService, _super);
    function Plt0MasterObjectCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0MasterObject, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0MasterObjectCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0MasterObjectCollectionService = Plt0MasterObjectCollectionService;
var Plt0RevisionObjectService = /** @class */ (function (_super) {
    __extends(Plt0RevisionObjectService, _super);
    function Plt0RevisionObjectService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RevisionObject, options) || this;
    }
    Plt0RevisionObjectService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0RevisionObjectService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0RevisionObjectService.prototype.preRevision = function () {
        if (!this._preRevision) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._preRevision = new Plt0RevisionObjectService(client, path, "preRevision", options);
        }
        return this._preRevision;
    };
    Plt0RevisionObjectService.prototype.master = function () {
        if (!this._master) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._master = new Plt0MasterObjectService(client, path, "master", options);
        }
        return this._master;
    };
    Plt0RevisionObjectService.prototype.lockedBy = function () {
        if (!this._lockedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lockedBy = new Plt0ApplicationObjectService(client, path, "lockedBy", options);
        }
        return this._lockedBy;
    };
    return Plt0RevisionObjectService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0RevisionObjectService = Plt0RevisionObjectService;
var Plt0RevisionObjectCollectionService = /** @class */ (function (_super) {
    __extends(Plt0RevisionObjectCollectionService, _super);
    function Plt0RevisionObjectCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RevisionObject, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0RevisionObjectCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0RevisionObjectCollectionService = Plt0RevisionObjectCollectionService;
var Plt0VersionObjectService = /** @class */ (function (_super) {
    __extends(Plt0VersionObjectService, _super);
    function Plt0VersionObjectService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0VersionObject, options) || this;
    }
    Plt0VersionObjectService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0VersionObjectService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0VersionObjectService.prototype.revision = function () {
        if (!this._revision) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._revision = new Plt0RevisionObjectService(client, path, "revision", options);
        }
        return this._revision;
    };
    return Plt0VersionObjectService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0VersionObjectService = Plt0VersionObjectService;
var Plt0VersionObjectCollectionService = /** @class */ (function (_super) {
    __extends(Plt0VersionObjectCollectionService, _super);
    function Plt0VersionObjectCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0VersionObject, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0VersionObjectCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0VersionObjectCollectionService = Plt0VersionObjectCollectionService;
var Plt0LifecycleStatusService = /** @class */ (function (_super) {
    __extends(Plt0LifecycleStatusService, _super);
    function Plt0LifecycleStatusService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0LifecycleStatus, options) || this;
    }
    Plt0LifecycleStatusService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0LifecycleStatusService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0LifecycleStatusService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0LifecycleStatusService = Plt0LifecycleStatusService;
var Plt0LifecycleStatusCollectionService = /** @class */ (function (_super) {
    __extends(Plt0LifecycleStatusCollectionService, _super);
    function Plt0LifecycleStatusCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0LifecycleStatus, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0LifecycleStatusCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0LifecycleStatusCollectionService = Plt0LifecycleStatusCollectionService;
var Plt0LifecycleStatusStrategyService = /** @class */ (function (_super) {
    __extends(Plt0LifecycleStatusStrategyService, _super);
    function Plt0LifecycleStatusStrategyService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0LifecycleStatusStrategy, options) || this;
    }
    Plt0LifecycleStatusStrategyService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0LifecycleStatusStrategyService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0LifecycleStatusStrategyService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0LifecycleStatusStrategyService = Plt0LifecycleStatusStrategyService;
var Plt0LifecycleStatusStrategyCollectionService = /** @class */ (function (_super) {
    __extends(Plt0LifecycleStatusStrategyCollectionService, _super);
    function Plt0LifecycleStatusStrategyCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0LifecycleStatusStrategy, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0LifecycleStatusStrategyCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0LifecycleStatusStrategyCollectionService = Plt0LifecycleStatusStrategyCollectionService;
var Plt0MrvObjectService = /** @class */ (function (_super) {
    __extends(Plt0MrvObjectService, _super);
    function Plt0MrvObjectService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0MrvObject, options) || this;
    }
    Plt0MrvObjectService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0MrvObjectService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0MrvObjectService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    Plt0MrvObjectService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    Plt0MrvObjectService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    Plt0MrvObjectService.prototype.NextRevision = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Plt0MrvObject_QNextRevision) {
                            this._Plt0MrvObject_QNextRevision = new QPaaS_1.Plt0MrvObject_QNextRevision();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Plt0MrvObject_QNextRevision.buildUrl());
                        return [4 /*yield*/, client.post(url, {}, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Plt0MrvObject_QNextRevision.convertResponse(response)];
                }
            });
        });
    };
    Plt0MrvObjectService.prototype.Lock = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Plt0MrvObject_QLock) {
                            this._Plt0MrvObject_QLock = new QPaaS_1.Plt0MrvObject_QLock();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Plt0MrvObject_QLock.buildUrl());
                        return [4 /*yield*/, client.post(url, {}, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Plt0MrvObject_QLock.convertResponse(response)];
                }
            });
        });
    };
    Plt0MrvObjectService.prototype.Unlock = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Plt0MrvObject_QUnlock) {
                            this._Plt0MrvObject_QUnlock = new QPaaS_1.Plt0MrvObject_QUnlock();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Plt0MrvObject_QUnlock.buildUrl());
                        return [4 /*yield*/, client.post(url, {}, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Plt0MrvObject_QUnlock.convertResponse(response)];
                }
            });
        });
    };
    Plt0MrvObjectService.prototype.asCadFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadFileService(client, path, "neue.CadFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectService.prototype.asCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectService.prototype.asNeueCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectService.prototype.asNeueCadAsmService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectService.prototype.asTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectService.prototype.asNeueTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0MrvObjectService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0MrvObjectService = Plt0MrvObjectService;
var Plt0MrvObjectCollectionService = /** @class */ (function (_super) {
    __extends(Plt0MrvObjectCollectionService, _super);
    function Plt0MrvObjectCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0MrvObject, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0MrvObjectCollectionService.prototype.asCadFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadFileCollectionService(client, path, "neue.CadFile", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectCollectionService.prototype.asCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartCollectionService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectCollectionService.prototype.asNeueCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartCollectionService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectCollectionService.prototype.asNeueCadAsmCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmCollectionService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectCollectionService.prototype.asTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingCollectionService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0MrvObjectCollectionService.prototype.asNeueTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingCollectionService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0MrvObjectCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0MrvObjectCollectionService = Plt0MrvObjectCollectionService;
var Plt0FileService = /** @class */ (function (_super) {
    __extends(Plt0FileService, _super);
    function Plt0FileService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0File, options) || this;
    }
    Plt0FileService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0FileService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0FileService.prototype.storageItem = function () {
        if (!this._storageItem) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._storageItem = new Plt0ApplicationObjectService(client, path, "storageItem", options);
        }
        return this._storageItem;
    };
    Plt0FileService.prototype.asPlt0IdiFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFileService(client, path, "BuiltInModel.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0FileService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0FileService = Plt0FileService;
var Plt0FileCollectionService = /** @class */ (function (_super) {
    __extends(Plt0FileCollectionService, _super);
    function Plt0FileCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0File, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0FileCollectionService.prototype.asPlt0IdiFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFileCollectionService(client, path, "BuiltInModel.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0FileCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0FileCollectionService = Plt0FileCollectionService;
var Plt0IdiFileService = /** @class */ (function (_super) {
    __extends(Plt0IdiFileService, _super);
    function Plt0IdiFileService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0IdiFile, options) || this;
    }
    Plt0IdiFileService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0IdiFileService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0IdiFileService.prototype.storageItem = function () {
        if (!this._storageItem) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._storageItem = new Plt0ApplicationObjectService(client, path, "storageItem", options);
        }
        return this._storageItem;
    };
    return Plt0IdiFileService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0IdiFileService = Plt0IdiFileService;
var Plt0IdiFileCollectionService = /** @class */ (function (_super) {
    __extends(Plt0IdiFileCollectionService, _super);
    function Plt0IdiFileCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0IdiFile, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0IdiFileCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0IdiFileCollectionService = Plt0IdiFileCollectionService;
var Plt0RootRelationTypeService = /** @class */ (function (_super) {
    __extends(Plt0RootRelationTypeService, _super);
    function Plt0RootRelationTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RootRelationType, options) || this;
    }
    Plt0RootRelationTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0RootRelationTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0RootRelationTypeService.prototype.from = function () {
        if (!this._from) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._from = new Plt0ApplicationObjectService(client, path, "from", options);
        }
        return this._from;
    };
    Plt0RootRelationTypeService.prototype.to = function () {
        if (!this._to) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._to = new Plt0ApplicationObjectService(client, path, "to", options);
        }
        return this._to;
    };
    Plt0RootRelationTypeService.prototype.asRelCadBomService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadBomService(client, path, "neue.RelCadBom", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0RootRelationTypeService.prototype.asRelCadPartTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadPartTwoDimensionDrawingService(client, path, "neue.RelCadPartTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0RootRelationTypeService.prototype.asRelCadFileFileService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadFileFileService(client, path, "neue.RelCadFileFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0RootRelationTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0RootRelationTypeService = Plt0RootRelationTypeService;
var Plt0RootRelationTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0RootRelationTypeCollectionService, _super);
    function Plt0RootRelationTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RootRelationType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0RootRelationTypeCollectionService.prototype.asRelCadBomCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadBomCollectionService(client, path, "neue.RelCadBom", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0RootRelationTypeCollectionService.prototype.asRelCadPartTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadPartTwoDimensionDrawingCollectionService(client, path, "neue.RelCadPartTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0RootRelationTypeCollectionService.prototype.asRelCadFileFileCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new RelCadFileFileCollectionService(client, path, "neue.RelCadFileFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0RootRelationTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0RootRelationTypeCollectionService = Plt0RootRelationTypeCollectionService;
var Plt0RelationQueryService = /** @class */ (function (_super) {
    __extends(Plt0RelationQueryService, _super);
    function Plt0RelationQueryService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RelationQuery, options) || this;
    }
    Plt0RelationQueryService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0RelationQueryService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0RelationQueryService.prototype.toRelations = function (id) {
        var fieldName = "toRelations";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new Plt0RootRelationTypeCollectionService(client, path, fieldName, options)
            : new Plt0RootRelationTypeService(client, path, new QPaaS_1.QPlt0ApplicationObjectId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    return Plt0RelationQueryService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0RelationQueryService = Plt0RelationQueryService;
var Plt0RelationQueryCollectionService = /** @class */ (function (_super) {
    __extends(Plt0RelationQueryCollectionService, _super);
    function Plt0RelationQueryCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RelationQuery, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0RelationQueryCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0RelationQueryCollectionService = Plt0RelationQueryCollectionService;
var Plt0RootRelationTypeConfigService = /** @class */ (function (_super) {
    __extends(Plt0RootRelationTypeConfigService, _super);
    function Plt0RootRelationTypeConfigService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RootRelationTypeConfig, options) || this;
    }
    Plt0RootRelationTypeConfigService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0RootRelationTypeConfigService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0RootRelationTypeConfigService.prototype.applyRelationType = function () {
        if (!this._applyRelationType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._applyRelationType = new Plt0RootRelationTypeService(client, path, "applyRelationType", options);
        }
        return this._applyRelationType;
    };
    return Plt0RootRelationTypeConfigService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0RootRelationTypeConfigService = Plt0RootRelationTypeConfigService;
var Plt0RootRelationTypeConfigCollectionService = /** @class */ (function (_super) {
    __extends(Plt0RootRelationTypeConfigCollectionService, _super);
    function Plt0RootRelationTypeConfigCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RootRelationTypeConfig, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0RootRelationTypeConfigCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0RootRelationTypeConfigCollectionService = Plt0RootRelationTypeConfigCollectionService;
var Plt0RelationFromOrToTypeService = /** @class */ (function (_super) {
    __extends(Plt0RelationFromOrToTypeService, _super);
    function Plt0RelationFromOrToTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RelationFromOrToType, options) || this;
    }
    Plt0RelationFromOrToTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0RelationFromOrToTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0RelationFromOrToTypeService.prototype.relationType = function () {
        if (!this._relationType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._relationType = new Plt0EntityTypeService(client, path, "relationType", options);
        }
        return this._relationType;
    };
    Plt0RelationFromOrToTypeService.prototype.constrictType = function () {
        if (!this._constrictType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._constrictType = new Plt0EntityTypeService(client, path, "constrictType", options);
        }
        return this._constrictType;
    };
    return Plt0RelationFromOrToTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0RelationFromOrToTypeService = Plt0RelationFromOrToTypeService;
var Plt0RelationFromOrToTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0RelationFromOrToTypeCollectionService, _super);
    function Plt0RelationFromOrToTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0RelationFromOrToType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0RelationFromOrToTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0RelationFromOrToTypeCollectionService = Plt0RelationFromOrToTypeCollectionService;
var Plt0SchemaService = /** @class */ (function (_super) {
    __extends(Plt0SchemaService, _super);
    function Plt0SchemaService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Schema, options) || this;
    }
    Plt0SchemaService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0SchemaService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0SchemaService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0SchemaService = Plt0SchemaService;
var Plt0SchemaCollectionService = /** @class */ (function (_super) {
    __extends(Plt0SchemaCollectionService, _super);
    function Plt0SchemaCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Schema, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0SchemaCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0SchemaCollectionService = Plt0SchemaCollectionService;
var Plt0BaseTypeService = /** @class */ (function (_super) {
    __extends(Plt0BaseTypeService, _super);
    function Plt0BaseTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BaseType, options) || this;
    }
    Plt0BaseTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0BaseTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0BaseTypeService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    Plt0BaseTypeService.prototype.asPlt0EntityTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeService(client, path, "BuiltInModel.Plt0EntityType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0BaseTypeService.prototype.asPlt0ComplexTypeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ComplexTypeService(client, path, "BuiltInModel.Plt0ComplexType", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0BaseTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0BaseTypeService = Plt0BaseTypeService;
var Plt0BaseTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0BaseTypeCollectionService, _super);
    function Plt0BaseTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BaseType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0BaseTypeCollectionService.prototype.asPlt0EntityTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0EntityTypeCollectionService(client, path, "BuiltInModel.Plt0EntityType", __assign(__assign({}, options), { subtype: true }));
    };
    Plt0BaseTypeCollectionService.prototype.asPlt0ComplexTypeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0ComplexTypeCollectionService(client, path, "BuiltInModel.Plt0ComplexType", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0BaseTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0BaseTypeCollectionService = Plt0BaseTypeCollectionService;
var Plt0EntityTypeService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeService, _super);
    function Plt0EntityTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityType, options) || this;
    }
    Plt0EntityTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntityTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntityTypeService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    Plt0EntityTypeService.prototype.icon = function () {
        if (!this._icon) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._icon = new Plt0FileService(client, path, "icon", options);
        }
        return this._icon;
    };
    Plt0EntityTypeService.prototype.baseType = function () {
        if (!this._baseType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._baseType = new Plt0EntityTypeService(client, path, "baseType", options);
        }
        return this._baseType;
    };
    Plt0EntityTypeService.prototype.lifecycleStatusStrategy = function () {
        if (!this._lifecycleStatusStrategy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatusStrategy = new Plt0LifecycleStatusStrategyService(client, path, "lifecycleStatusStrategy", options);
        }
        return this._lifecycleStatusStrategy;
    };
    Plt0EntityTypeService.prototype.revisionNumberRule = function () {
        if (!this._revisionNumberRule) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._revisionNumberRule = new Plt0BusinessObjectRevisionNumberRuleService(client, path, "revisionNumberRule", options);
        }
        return this._revisionNumberRule;
    };
    return Plt0EntityTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntityTypeService = Plt0EntityTypeService;
var Plt0EntityTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeCollectionService, _super);
    function Plt0EntityTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntityTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntityTypeCollectionService = Plt0EntityTypeCollectionService;
var Plt0EntityTypeUnionUniqService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeUnionUniqService, _super);
    function Plt0EntityTypeUnionUniqService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypeUnionUniq, options) || this;
    }
    Plt0EntityTypeUnionUniqService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntityTypeUnionUniqService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntityTypeUnionUniqService.prototype.ownerType = function () {
        if (!this._ownerType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ownerType = new Plt0EntityTypeService(client, path, "ownerType", options);
        }
        return this._ownerType;
    };
    return Plt0EntityTypeUnionUniqService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntityTypeUnionUniqService = Plt0EntityTypeUnionUniqService;
var Plt0EntityTypeUnionUniqCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeUnionUniqCollectionService, _super);
    function Plt0EntityTypeUnionUniqCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypeUnionUniq, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntityTypeUnionUniqCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntityTypeUnionUniqCollectionService = Plt0EntityTypeUnionUniqCollectionService;
var Plt0EntityTypeUnionUniqPropertyService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeUnionUniqPropertyService, _super);
    function Plt0EntityTypeUnionUniqPropertyService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypeUnionUniqProperty, options) || this;
    }
    Plt0EntityTypeUnionUniqPropertyService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntityTypeUnionUniqPropertyService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntityTypeUnionUniqPropertyService.prototype.ownerUnionUniq = function () {
        if (!this._ownerUnionUniq) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ownerUnionUniq = new Plt0EntityTypeUnionUniqService(client, path, "ownerUnionUniq", options);
        }
        return this._ownerUnionUniq;
    };
    Plt0EntityTypeUnionUniqPropertyService.prototype.structuralProperty = function () {
        if (!this._structuralProperty) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._structuralProperty = new Plt0StructuralPropertyService(client, path, "structuralProperty", options);
        }
        return this._structuralProperty;
    };
    Plt0EntityTypeUnionUniqPropertyService.prototype.navigationProperty = function () {
        if (!this._navigationProperty) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._navigationProperty = new Plt0NavigationPropertyService(client, path, "navigationProperty", options);
        }
        return this._navigationProperty;
    };
    return Plt0EntityTypeUnionUniqPropertyService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntityTypeUnionUniqPropertyService = Plt0EntityTypeUnionUniqPropertyService;
var Plt0EntityTypeUnionUniqPropertyCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypeUnionUniqPropertyCollectionService, _super);
    function Plt0EntityTypeUnionUniqPropertyCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypeUnionUniqProperty, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntityTypeUnionUniqPropertyCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntityTypeUnionUniqPropertyCollectionService = Plt0EntityTypeUnionUniqPropertyCollectionService;
var Plt0EntityTypePropertyOverrideService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypePropertyOverrideService, _super);
    function Plt0EntityTypePropertyOverrideService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypePropertyOverride, options) || this;
    }
    Plt0EntityTypePropertyOverrideService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntityTypePropertyOverrideService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntityTypePropertyOverrideService.prototype.ownerType = function () {
        if (!this._ownerType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ownerType = new Plt0EntityTypeService(client, path, "ownerType", options);
        }
        return this._ownerType;
    };
    Plt0EntityTypePropertyOverrideService.prototype.structuralProperty = function () {
        if (!this._structuralProperty) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._structuralProperty = new Plt0StructuralPropertyService(client, path, "structuralProperty", options);
        }
        return this._structuralProperty;
    };
    return Plt0EntityTypePropertyOverrideService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntityTypePropertyOverrideService = Plt0EntityTypePropertyOverrideService;
var Plt0EntityTypePropertyOverrideCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntityTypePropertyOverrideCollectionService, _super);
    function Plt0EntityTypePropertyOverrideCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityTypePropertyOverride, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntityTypePropertyOverrideCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntityTypePropertyOverrideCollectionService = Plt0EntityTypePropertyOverrideCollectionService;
var Plt0ComplexTypeService = /** @class */ (function (_super) {
    __extends(Plt0ComplexTypeService, _super);
    function Plt0ComplexTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ComplexType, options) || this;
    }
    Plt0ComplexTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0ComplexTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0ComplexTypeService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    Plt0ComplexTypeService.prototype.baseType = function () {
        if (!this._baseType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._baseType = new Plt0ComplexTypeService(client, path, "baseType", options);
        }
        return this._baseType;
    };
    return Plt0ComplexTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0ComplexTypeService = Plt0ComplexTypeService;
var Plt0ComplexTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0ComplexTypeCollectionService, _super);
    function Plt0ComplexTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ComplexType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0ComplexTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0ComplexTypeCollectionService = Plt0ComplexTypeCollectionService;
var Plt0EnumTypeService = /** @class */ (function (_super) {
    __extends(Plt0EnumTypeService, _super);
    function Plt0EnumTypeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EnumType, options) || this;
    }
    Plt0EnumTypeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EnumTypeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EnumTypeService.prototype.members = function () {
        if (!this._members) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._members = new odata_service_1.CollectionServiceV4(client, path, "members", new odata_query_objects_1.QStringCollection(), options);
        }
        return this._members;
    };
    Plt0EnumTypeService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    return Plt0EnumTypeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EnumTypeService = Plt0EnumTypeService;
var Plt0EnumTypeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EnumTypeCollectionService, _super);
    function Plt0EnumTypeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EnumType, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EnumTypeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EnumTypeCollectionService = Plt0EnumTypeCollectionService;
var Plt0TypeDefinitionService = /** @class */ (function (_super) {
    __extends(Plt0TypeDefinitionService, _super);
    function Plt0TypeDefinitionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0TypeDefinition, options) || this;
    }
    Plt0TypeDefinitionService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0TypeDefinitionService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0TypeDefinitionService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    return Plt0TypeDefinitionService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0TypeDefinitionService = Plt0TypeDefinitionService;
var Plt0TypeDefinitionCollectionService = /** @class */ (function (_super) {
    __extends(Plt0TypeDefinitionCollectionService, _super);
    function Plt0TypeDefinitionCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0TypeDefinition, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0TypeDefinitionCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0TypeDefinitionCollectionService = Plt0TypeDefinitionCollectionService;
var Plt0StructuralPropertyService = /** @class */ (function (_super) {
    __extends(Plt0StructuralPropertyService, _super);
    function Plt0StructuralPropertyService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0StructuralProperty, options) || this;
    }
    Plt0StructuralPropertyService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0StructuralPropertyService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0StructuralPropertyService.prototype.ownerType = function () {
        if (!this._ownerType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ownerType = new Plt0BaseTypeService(client, path, "ownerType", options);
        }
        return this._ownerType;
    };
    return Plt0StructuralPropertyService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0StructuralPropertyService = Plt0StructuralPropertyService;
var Plt0StructuralPropertyCollectionService = /** @class */ (function (_super) {
    __extends(Plt0StructuralPropertyCollectionService, _super);
    function Plt0StructuralPropertyCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0StructuralProperty, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0StructuralPropertyCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0StructuralPropertyCollectionService = Plt0StructuralPropertyCollectionService;
var Plt0NavigationPropertyService = /** @class */ (function (_super) {
    __extends(Plt0NavigationPropertyService, _super);
    function Plt0NavigationPropertyService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0NavigationProperty, options) || this;
    }
    Plt0NavigationPropertyService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0NavigationPropertyService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0NavigationPropertyService.prototype.ownerType = function () {
        if (!this._ownerType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ownerType = new Plt0BaseTypeService(client, path, "ownerType", options);
        }
        return this._ownerType;
    };
    Plt0NavigationPropertyService.prototype.type = function () {
        if (!this._type) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._type = new Plt0EntityTypeService(client, path, "type", options);
        }
        return this._type;
    };
    Plt0NavigationPropertyService.prototype.partner = function () {
        if (!this._partner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._partner = new Plt0NavigationPropertyService(client, path, "partner", options);
        }
        return this._partner;
    };
    return Plt0NavigationPropertyService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0NavigationPropertyService = Plt0NavigationPropertyService;
var Plt0NavigationPropertyCollectionService = /** @class */ (function (_super) {
    __extends(Plt0NavigationPropertyCollectionService, _super);
    function Plt0NavigationPropertyCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0NavigationProperty, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0NavigationPropertyCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0NavigationPropertyCollectionService = Plt0NavigationPropertyCollectionService;
var Plt0ActionService = /** @class */ (function (_super) {
    __extends(Plt0ActionService, _super);
    function Plt0ActionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Action, options) || this;
    }
    Plt0ActionService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0ActionService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0ActionService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0ActionService = Plt0ActionService;
var Plt0ActionCollectionService = /** @class */ (function (_super) {
    __extends(Plt0ActionCollectionService, _super);
    function Plt0ActionCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Action, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0ActionCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0ActionCollectionService = Plt0ActionCollectionService;
var Plt0FunctionService = /** @class */ (function (_super) {
    __extends(Plt0FunctionService, _super);
    function Plt0FunctionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Function, options) || this;
    }
    Plt0FunctionService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0FunctionService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0FunctionService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0FunctionService = Plt0FunctionService;
var Plt0FunctionCollectionService = /** @class */ (function (_super) {
    __extends(Plt0FunctionCollectionService, _super);
    function Plt0FunctionCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Function, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0FunctionCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0FunctionCollectionService = Plt0FunctionCollectionService;
var Plt0EntityContainerService = /** @class */ (function (_super) {
    __extends(Plt0EntityContainerService, _super);
    function Plt0EntityContainerService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityContainer, options) || this;
    }
    Plt0EntityContainerService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntityContainerService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntityContainerService.prototype.schema = function () {
        if (!this._schema) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._schema = new Plt0SchemaService(client, path, "schema", options);
        }
        return this._schema;
    };
    return Plt0EntityContainerService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntityContainerService = Plt0EntityContainerService;
var Plt0EntityContainerCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntityContainerCollectionService, _super);
    function Plt0EntityContainerCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntityContainer, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntityContainerCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntityContainerCollectionService = Plt0EntityContainerCollectionService;
var Plt0EntitySetService = /** @class */ (function (_super) {
    __extends(Plt0EntitySetService, _super);
    function Plt0EntitySetService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntitySet, options) || this;
    }
    Plt0EntitySetService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0EntitySetService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0EntitySetService.prototype.entityContainer = function () {
        if (!this._entityContainer) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityContainer = new Plt0EntityContainerService(client, path, "entityContainer", options);
        }
        return this._entityContainer;
    };
    Plt0EntitySetService.prototype.entityType = function () {
        if (!this._entityType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityType = new Plt0EntityTypeService(client, path, "entityType", options);
        }
        return this._entityType;
    };
    return Plt0EntitySetService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0EntitySetService = Plt0EntitySetService;
var Plt0EntitySetCollectionService = /** @class */ (function (_super) {
    __extends(Plt0EntitySetCollectionService, _super);
    function Plt0EntitySetCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0EntitySet, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0EntitySetCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0EntitySetCollectionService = Plt0EntitySetCollectionService;
var Plt0SingletonService = /** @class */ (function (_super) {
    __extends(Plt0SingletonService, _super);
    function Plt0SingletonService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Singleton, options) || this;
    }
    Plt0SingletonService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0SingletonService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0SingletonService.prototype.entityContainer = function () {
        if (!this._entityContainer) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityContainer = new Plt0EntityContainerService(client, path, "entityContainer", options);
        }
        return this._entityContainer;
    };
    Plt0SingletonService.prototype.entityType = function () {
        if (!this._entityType) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityType = new Plt0EntityTypeService(client, path, "entityType", options);
        }
        return this._entityType;
    };
    return Plt0SingletonService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0SingletonService = Plt0SingletonService;
var Plt0SingletonCollectionService = /** @class */ (function (_super) {
    __extends(Plt0SingletonCollectionService, _super);
    function Plt0SingletonCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0Singleton, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0SingletonCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0SingletonCollectionService = Plt0SingletonCollectionService;
var Plt0NavigationPropertyBindingService = /** @class */ (function (_super) {
    __extends(Plt0NavigationPropertyBindingService, _super);
    function Plt0NavigationPropertyBindingService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0NavigationPropertyBinding, options) || this;
    }
    Plt0NavigationPropertyBindingService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0NavigationPropertyBindingService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0NavigationPropertyBindingService.prototype.entitySet = function () {
        if (!this._entitySet) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entitySet = new Plt0EntitySetService(client, path, "entitySet", options);
        }
        return this._entitySet;
    };
    Plt0NavigationPropertyBindingService.prototype.path = function () {
        if (!this._path) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._path = new Plt0NavigationPropertyService(client, path, "path", options);
        }
        return this._path;
    };
    Plt0NavigationPropertyBindingService.prototype.target = function () {
        if (!this._target) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._target = new Plt0EntitySetService(client, path, "target", options);
        }
        return this._target;
    };
    return Plt0NavigationPropertyBindingService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0NavigationPropertyBindingService = Plt0NavigationPropertyBindingService;
var Plt0NavigationPropertyBindingCollectionService = /** @class */ (function (_super) {
    __extends(Plt0NavigationPropertyBindingCollectionService, _super);
    function Plt0NavigationPropertyBindingCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0NavigationPropertyBinding, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0NavigationPropertyBindingCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0NavigationPropertyBindingCollectionService = Plt0NavigationPropertyBindingCollectionService;
var Plt0ActionImportService = /** @class */ (function (_super) {
    __extends(Plt0ActionImportService, _super);
    function Plt0ActionImportService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ActionImport, options) || this;
    }
    Plt0ActionImportService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0ActionImportService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0ActionImportService.prototype.entityContainer = function () {
        if (!this._entityContainer) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityContainer = new Plt0EntityContainerService(client, path, "entityContainer", options);
        }
        return this._entityContainer;
    };
    Plt0ActionImportService.prototype.action = function () {
        if (!this._action) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._action = new Plt0ActionService(client, path, "action", options);
        }
        return this._action;
    };
    Plt0ActionImportService.prototype.entitySet = function () {
        if (!this._entitySet) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entitySet = new Plt0EntitySetService(client, path, "entitySet", options);
        }
        return this._entitySet;
    };
    return Plt0ActionImportService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0ActionImportService = Plt0ActionImportService;
var Plt0ActionImportCollectionService = /** @class */ (function (_super) {
    __extends(Plt0ActionImportCollectionService, _super);
    function Plt0ActionImportCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0ActionImport, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0ActionImportCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0ActionImportCollectionService = Plt0ActionImportCollectionService;
var Plt0FunctionImportService = /** @class */ (function (_super) {
    __extends(Plt0FunctionImportService, _super);
    function Plt0FunctionImportService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0FunctionImport, options) || this;
    }
    Plt0FunctionImportService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0FunctionImportService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0FunctionImportService.prototype.entityContainer = function () {
        if (!this._entityContainer) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entityContainer = new Plt0EntityContainerService(client, path, "entityContainer", options);
        }
        return this._entityContainer;
    };
    Plt0FunctionImportService.prototype.function = function () {
        if (!this._function) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._function = new Plt0FunctionService(client, path, "function", options);
        }
        return this._function;
    };
    Plt0FunctionImportService.prototype.entitySet = function () {
        if (!this._entitySet) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._entitySet = new Plt0EntitySetService(client, path, "entitySet", options);
        }
        return this._entitySet;
    };
    return Plt0FunctionImportService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0FunctionImportService = Plt0FunctionImportService;
var Plt0FunctionImportCollectionService = /** @class */ (function (_super) {
    __extends(Plt0FunctionImportCollectionService, _super);
    function Plt0FunctionImportCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0FunctionImport, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0FunctionImportCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0FunctionImportCollectionService = Plt0FunctionImportCollectionService;
var Plt0BusinessObjectRevisionNumberRuleService = /** @class */ (function (_super) {
    __extends(Plt0BusinessObjectRevisionNumberRuleService, _super);
    function Plt0BusinessObjectRevisionNumberRuleService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BusinessObjectRevisionNumberRule, options) || this;
    }
    Plt0BusinessObjectRevisionNumberRuleService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0BusinessObjectRevisionNumberRuleService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Plt0BusinessObjectRevisionNumberRuleService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0BusinessObjectRevisionNumberRuleService = Plt0BusinessObjectRevisionNumberRuleService;
var Plt0BusinessObjectRevisionNumberRuleCollectionService = /** @class */ (function (_super) {
    __extends(Plt0BusinessObjectRevisionNumberRuleCollectionService, _super);
    function Plt0BusinessObjectRevisionNumberRuleCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BusinessObjectRevisionNumberRule, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0BusinessObjectRevisionNumberRuleCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0BusinessObjectRevisionNumberRuleCollectionService = Plt0BusinessObjectRevisionNumberRuleCollectionService;
var Plt0BusinessObjectRevisionNumberCodeService = /** @class */ (function (_super) {
    __extends(Plt0BusinessObjectRevisionNumberCodeService, _super);
    function Plt0BusinessObjectRevisionNumberCodeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BusinessObjectRevisionNumberCode, options) || this;
    }
    Plt0BusinessObjectRevisionNumberCodeService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0BusinessObjectRevisionNumberCodeService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0BusinessObjectRevisionNumberCodeService.prototype.ruleCodeRef = function () {
        if (!this._ruleCodeRef) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._ruleCodeRef = new Plt0BusinessObjectRevisionNumberRuleService(client, path, "ruleCodeRef", options);
        }
        return this._ruleCodeRef;
    };
    return Plt0BusinessObjectRevisionNumberCodeService;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0BusinessObjectRevisionNumberCodeService = Plt0BusinessObjectRevisionNumberCodeService;
var Plt0BusinessObjectRevisionNumberCodeCollectionService = /** @class */ (function (_super) {
    __extends(Plt0BusinessObjectRevisionNumberCodeCollectionService, _super);
    function Plt0BusinessObjectRevisionNumberCodeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0BusinessObjectRevisionNumberCode, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0BusinessObjectRevisionNumberCodeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0BusinessObjectRevisionNumberCodeCollectionService = Plt0BusinessObjectRevisionNumberCodeCollectionService;
var CadFileService = /** @class */ (function (_super) {
    __extends(CadFileService, _super);
    function CadFileService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qCadFile, options) || this;
    }
    CadFileService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    CadFileService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    CadFileService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    CadFileService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    CadFileService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    CadFileService.prototype.asCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileService.prototype.asNeueCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileService.prototype.asNeueCadAsmService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileService.prototype.asTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileService.prototype.asNeueTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return CadFileService;
}(odata_service_1.EntityTypeServiceV4));
exports.CadFileService = CadFileService;
var CadFileCollectionService = /** @class */ (function (_super) {
    __extends(CadFileCollectionService, _super);
    function CadFileCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qCadFile, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    CadFileCollectionService.prototype.asCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new CadPartCollectionService(client, path, "neue.CadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileCollectionService.prototype.asNeueCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartCollectionService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileCollectionService.prototype.asNeueCadAsmCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmCollectionService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileCollectionService.prototype.asTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new TwoDimensionDrawingCollectionService(client, path, "neue.TwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    CadFileCollectionService.prototype.asNeueTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingCollectionService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return CadFileCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.CadFileCollectionService = CadFileCollectionService;
var CadPartService = /** @class */ (function (_super) {
    __extends(CadPartService, _super);
    function CadPartService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qCadPart, options) || this;
    }
    CadPartService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    CadPartService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    CadPartService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    CadPartService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    CadPartService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    CadPartService.prototype.thumbnail = function () {
        if (!this._thumbnail) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._thumbnail = new Plt0File2Service(client, path, "thumbnail", options);
        }
        return this._thumbnail;
    };
    CadPartService.prototype.asNeueCadPartService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadPartService.prototype.asNeueCadAsmService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    return CadPartService;
}(odata_service_1.EntityTypeServiceV4));
exports.CadPartService = CadPartService;
var CadPartCollectionService = /** @class */ (function (_super) {
    __extends(CadPartCollectionService, _super);
    function CadPartCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qCadPart, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    CadPartCollectionService.prototype.asNeueCadPartCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadPartCollectionService(client, path, "neue.NeueCadPart", __assign(__assign({}, options), { subtype: true }));
    };
    CadPartCollectionService.prototype.asNeueCadAsmCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueCadAsmCollectionService(client, path, "neue.NeueCadAsm", __assign(__assign({}, options), { subtype: true }));
    };
    return CadPartCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.CadPartCollectionService = CadPartCollectionService;
var NeueCadPartService = /** @class */ (function (_super) {
    __extends(NeueCadPartService, _super);
    function NeueCadPartService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueCadPart, options) || this;
    }
    NeueCadPartService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    NeueCadPartService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    NeueCadPartService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    NeueCadPartService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    NeueCadPartService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    NeueCadPartService.prototype.thumbnail = function () {
        if (!this._thumbnail) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._thumbnail = new Plt0File2Service(client, path, "thumbnail", options);
        }
        return this._thumbnail;
    };
    return NeueCadPartService;
}(odata_service_1.EntityTypeServiceV4));
exports.NeueCadPartService = NeueCadPartService;
var NeueCadPartCollectionService = /** @class */ (function (_super) {
    __extends(NeueCadPartCollectionService, _super);
    function NeueCadPartCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueCadPart, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return NeueCadPartCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.NeueCadPartCollectionService = NeueCadPartCollectionService;
var NeueCadAsmService = /** @class */ (function (_super) {
    __extends(NeueCadAsmService, _super);
    function NeueCadAsmService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueCadAsm, options) || this;
    }
    NeueCadAsmService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    NeueCadAsmService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    NeueCadAsmService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    NeueCadAsmService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    NeueCadAsmService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    NeueCadAsmService.prototype.thumbnail = function () {
        if (!this._thumbnail) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._thumbnail = new Plt0File2Service(client, path, "thumbnail", options);
        }
        return this._thumbnail;
    };
    return NeueCadAsmService;
}(odata_service_1.EntityTypeServiceV4));
exports.NeueCadAsmService = NeueCadAsmService;
var NeueCadAsmCollectionService = /** @class */ (function (_super) {
    __extends(NeueCadAsmCollectionService, _super);
    function NeueCadAsmCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueCadAsm, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return NeueCadAsmCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.NeueCadAsmCollectionService = NeueCadAsmCollectionService;
var TwoDimensionDrawingService = /** @class */ (function (_super) {
    __extends(TwoDimensionDrawingService, _super);
    function TwoDimensionDrawingService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTwoDimensionDrawing, options) || this;
    }
    TwoDimensionDrawingService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    TwoDimensionDrawingService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    TwoDimensionDrawingService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    TwoDimensionDrawingService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    TwoDimensionDrawingService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    TwoDimensionDrawingService.prototype.thumbnail = function () {
        if (!this._thumbnail) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._thumbnail = new Plt0File2Service(client, path, "thumbnail", options);
        }
        return this._thumbnail;
    };
    TwoDimensionDrawingService.prototype.asNeueTwoDimensionDrawingService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return TwoDimensionDrawingService;
}(odata_service_1.EntityTypeServiceV4));
exports.TwoDimensionDrawingService = TwoDimensionDrawingService;
var TwoDimensionDrawingCollectionService = /** @class */ (function (_super) {
    __extends(TwoDimensionDrawingCollectionService, _super);
    function TwoDimensionDrawingCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTwoDimensionDrawing, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    TwoDimensionDrawingCollectionService.prototype.asNeueTwoDimensionDrawingCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new NeueTwoDimensionDrawingCollectionService(client, path, "neue.NeueTwoDimensionDrawing", __assign(__assign({}, options), { subtype: true }));
    };
    return TwoDimensionDrawingCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.TwoDimensionDrawingCollectionService = TwoDimensionDrawingCollectionService;
var NeueTwoDimensionDrawingService = /** @class */ (function (_super) {
    __extends(NeueTwoDimensionDrawingService, _super);
    function NeueTwoDimensionDrawingService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueTwoDimensionDrawing, options) || this;
    }
    NeueTwoDimensionDrawingService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    NeueTwoDimensionDrawingService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    NeueTwoDimensionDrawingService.prototype.version = function () {
        if (!this._version) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._version = new Plt0VersionObjectService(client, path, "version", options);
        }
        return this._version;
    };
    NeueTwoDimensionDrawingService.prototype.owner = function () {
        if (!this._owner) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._owner = new Plt0ApplicationObjectService(client, path, "owner", options);
        }
        return this._owner;
    };
    NeueTwoDimensionDrawingService.prototype.lifecycleStatus = function () {
        if (!this._lifecycleStatus) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._lifecycleStatus = new Plt0LifecycleStatusService(client, path, "lifecycleStatus", options);
        }
        return this._lifecycleStatus;
    };
    NeueTwoDimensionDrawingService.prototype.thumbnail = function () {
        if (!this._thumbnail) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._thumbnail = new Plt0File2Service(client, path, "thumbnail", options);
        }
        return this._thumbnail;
    };
    return NeueTwoDimensionDrawingService;
}(odata_service_1.EntityTypeServiceV4));
exports.NeueTwoDimensionDrawingService = NeueTwoDimensionDrawingService;
var NeueTwoDimensionDrawingCollectionService = /** @class */ (function (_super) {
    __extends(NeueTwoDimensionDrawingCollectionService, _super);
    function NeueTwoDimensionDrawingCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qNeueTwoDimensionDrawing, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return NeueTwoDimensionDrawingCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.NeueTwoDimensionDrawingCollectionService = NeueTwoDimensionDrawingCollectionService;
var Plt0File2Service = /** @class */ (function (_super) {
    __extends(Plt0File2Service, _super);
    function Plt0File2Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0File2, options) || this;
    }
    Plt0File2Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0File2Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0File2Service.prototype.storageItem = function () {
        if (!this._storageItem) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._storageItem = new Plt0ApplicationObjectService(client, path, "storageItem", options);
        }
        return this._storageItem;
    };
    Plt0File2Service.prototype.asPlt0IdiFile2Service = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFile2Service(client, path, "neue.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0File2Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0File2Service = Plt0File2Service;
var Plt0File2CollectionService = /** @class */ (function (_super) {
    __extends(Plt0File2CollectionService, _super);
    function Plt0File2CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0File2, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    Plt0File2CollectionService.prototype.asPlt0IdiFile2CollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new Plt0IdiFile2CollectionService(client, path, "neue.Plt0IdiFile", __assign(__assign({}, options), { subtype: true }));
    };
    return Plt0File2CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0File2CollectionService = Plt0File2CollectionService;
var Plt0IdiFile2Service = /** @class */ (function (_super) {
    __extends(Plt0IdiFile2Service, _super);
    function Plt0IdiFile2Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0IdiFile2, options) || this;
    }
    Plt0IdiFile2Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Plt0IdiFile2Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    Plt0IdiFile2Service.prototype.storageItem = function () {
        if (!this._storageItem) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._storageItem = new Plt0ApplicationObjectService(client, path, "storageItem", options);
        }
        return this._storageItem;
    };
    return Plt0IdiFile2Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Plt0IdiFile2Service = Plt0IdiFile2Service;
var Plt0IdiFile2CollectionService = /** @class */ (function (_super) {
    __extends(Plt0IdiFile2CollectionService, _super);
    function Plt0IdiFile2CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qPlt0IdiFile2, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Plt0IdiFile2CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Plt0IdiFile2CollectionService = Plt0IdiFile2CollectionService;
var RelCadBomService = /** @class */ (function (_super) {
    __extends(RelCadBomService, _super);
    function RelCadBomService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadBom, options) || this;
    }
    RelCadBomService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    RelCadBomService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    RelCadBomService.prototype.from = function () {
        if (!this._from) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._from = new Plt0ApplicationObjectService(client, path, "from", options);
        }
        return this._from;
    };
    RelCadBomService.prototype.to = function () {
        if (!this._to) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._to = new Plt0ApplicationObjectService(client, path, "to", options);
        }
        return this._to;
    };
    return RelCadBomService;
}(odata_service_1.EntityTypeServiceV4));
exports.RelCadBomService = RelCadBomService;
var RelCadBomCollectionService = /** @class */ (function (_super) {
    __extends(RelCadBomCollectionService, _super);
    function RelCadBomCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadBom, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return RelCadBomCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.RelCadBomCollectionService = RelCadBomCollectionService;
var RelCadPartTwoDimensionDrawingService = /** @class */ (function (_super) {
    __extends(RelCadPartTwoDimensionDrawingService, _super);
    function RelCadPartTwoDimensionDrawingService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadPartTwoDimensionDrawing, options) || this;
    }
    RelCadPartTwoDimensionDrawingService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    RelCadPartTwoDimensionDrawingService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    RelCadPartTwoDimensionDrawingService.prototype.from = function () {
        if (!this._from) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._from = new Plt0ApplicationObjectService(client, path, "from", options);
        }
        return this._from;
    };
    RelCadPartTwoDimensionDrawingService.prototype.to = function () {
        if (!this._to) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._to = new Plt0ApplicationObjectService(client, path, "to", options);
        }
        return this._to;
    };
    return RelCadPartTwoDimensionDrawingService;
}(odata_service_1.EntityTypeServiceV4));
exports.RelCadPartTwoDimensionDrawingService = RelCadPartTwoDimensionDrawingService;
var RelCadPartTwoDimensionDrawingCollectionService = /** @class */ (function (_super) {
    __extends(RelCadPartTwoDimensionDrawingCollectionService, _super);
    function RelCadPartTwoDimensionDrawingCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadPartTwoDimensionDrawing, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return RelCadPartTwoDimensionDrawingCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.RelCadPartTwoDimensionDrawingCollectionService = RelCadPartTwoDimensionDrawingCollectionService;
var RelCadFileFileService = /** @class */ (function (_super) {
    __extends(RelCadFileFileService, _super);
    function RelCadFileFileService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadFileFile, options) || this;
    }
    RelCadFileFileService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    RelCadFileFileService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    RelCadFileFileService.prototype.from = function () {
        if (!this._from) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._from = new Plt0ApplicationObjectService(client, path, "from", options);
        }
        return this._from;
    };
    RelCadFileFileService.prototype.to = function () {
        if (!this._to) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._to = new Plt0ApplicationObjectService(client, path, "to", options);
        }
        return this._to;
    };
    return RelCadFileFileService;
}(odata_service_1.EntityTypeServiceV4));
exports.RelCadFileFileService = RelCadFileFileService;
var RelCadFileFileCollectionService = /** @class */ (function (_super) {
    __extends(RelCadFileFileCollectionService, _super);
    function RelCadFileFileCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qRelCadFileFile, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return RelCadFileFileCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.RelCadFileFileCollectionService = RelCadFileFileCollectionService;
var MappingConfigService = /** @class */ (function (_super) {
    __extends(MappingConfigService, _super);
    function MappingConfigService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qMappingConfig, options) || this;
    }
    MappingConfigService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    MappingConfigService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return MappingConfigService;
}(odata_service_1.EntityTypeServiceV4));
exports.MappingConfigService = MappingConfigService;
var MappingConfigCollectionService = /** @class */ (function (_super) {
    __extends(MappingConfigCollectionService, _super);
    function MappingConfigCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qMappingConfig, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return MappingConfigCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.MappingConfigCollectionService = MappingConfigCollectionService;
var test_productService = /** @class */ (function (_super) {
    __extends(test_productService, _super);
    function test_productService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qtest_product, options) || this;
    }
    test_productService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    test_productService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return test_productService;
}(odata_service_1.EntityTypeServiceV4));
exports.test_productService = test_productService;
var test_productCollectionService = /** @class */ (function (_super) {
    __extends(test_productCollectionService, _super);
    function test_productCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qtest_product, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return test_productCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.test_productCollectionService = test_productCollectionService;
var test_product001Service = /** @class */ (function (_super) {
    __extends(test_product001Service, _super);
    function test_product001Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qtest_product001, options) || this;
    }
    test_product001Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    test_product001Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return test_product001Service;
}(odata_service_1.EntityTypeServiceV4));
exports.test_product001Service = test_product001Service;
var test_product001CollectionService = /** @class */ (function (_super) {
    __extends(test_product001CollectionService, _super);
    function test_product001CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qtest_product001, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return test_product001CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.test_product001CollectionService = test_product001CollectionService;
var Test_ProductModelService = /** @class */ (function (_super) {
    __extends(Test_ProductModelService, _super);
    function Test_ProductModelService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel, options) || this;
    }
    Test_ProductModelService.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Test_ProductModelService.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Test_ProductModelService;
}(odata_service_1.EntityTypeServiceV4));
exports.Test_ProductModelService = Test_ProductModelService;
var Test_ProductModelCollectionService = /** @class */ (function (_super) {
    __extends(Test_ProductModelCollectionService, _super);
    function Test_ProductModelCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Test_ProductModelCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Test_ProductModelCollectionService = Test_ProductModelCollectionService;
var Test_ProductModel111Service = /** @class */ (function (_super) {
    __extends(Test_ProductModel111Service, _super);
    function Test_ProductModel111Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel111, options) || this;
    }
    Test_ProductModel111Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Test_ProductModel111Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Test_ProductModel111Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Test_ProductModel111Service = Test_ProductModel111Service;
var Test_ProductModel111CollectionService = /** @class */ (function (_super) {
    __extends(Test_ProductModel111CollectionService, _super);
    function Test_ProductModel111CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel111, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Test_ProductModel111CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Test_ProductModel111CollectionService = Test_ProductModel111CollectionService;
var Test_ProductModel222Service = /** @class */ (function (_super) {
    __extends(Test_ProductModel222Service, _super);
    function Test_ProductModel222Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel222, options) || this;
    }
    Test_ProductModel222Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Test_ProductModel222Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Test_ProductModel222Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Test_ProductModel222Service = Test_ProductModel222Service;
var Test_ProductModel222CollectionService = /** @class */ (function (_super) {
    __extends(Test_ProductModel222CollectionService, _super);
    function Test_ProductModel222CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel222, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Test_ProductModel222CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Test_ProductModel222CollectionService = Test_ProductModel222CollectionService;
var Test_ProductModel333Service = /** @class */ (function (_super) {
    __extends(Test_ProductModel333Service, _super);
    function Test_ProductModel333Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel333, options) || this;
    }
    Test_ProductModel333Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Test_ProductModel333Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Test_ProductModel333Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Test_ProductModel333Service = Test_ProductModel333Service;
var Test_ProductModel333CollectionService = /** @class */ (function (_super) {
    __extends(Test_ProductModel333CollectionService, _super);
    function Test_ProductModel333CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel333, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Test_ProductModel333CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Test_ProductModel333CollectionService = Test_ProductModel333CollectionService;
var Test_ProductModel444Service = /** @class */ (function (_super) {
    __extends(Test_ProductModel444Service, _super);
    function Test_ProductModel444Service(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel444, options) || this;
    }
    Test_ProductModel444Service.prototype.createdBy = function () {
        if (!this._createdBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._createdBy = new Plt0ApplicationObjectService(client, path, "createdBy", options);
        }
        return this._createdBy;
    };
    Test_ProductModel444Service.prototype.modifiedBy = function () {
        if (!this._modifiedBy) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._modifiedBy = new Plt0ApplicationObjectService(client, path, "modifiedBy", options);
        }
        return this._modifiedBy;
    };
    return Test_ProductModel444Service;
}(odata_service_1.EntityTypeServiceV4));
exports.Test_ProductModel444Service = Test_ProductModel444Service;
var Test_ProductModel444CollectionService = /** @class */ (function (_super) {
    __extends(Test_ProductModel444CollectionService, _super);
    function Test_ProductModel444CollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QPaaS_1.qTest_ProductModel444, new QPaaS_1.QPlt0ApplicationObjectId(name), options) || this;
    }
    return Test_ProductModel444CollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.Test_ProductModel444CollectionService = Test_ProductModel444CollectionService;
