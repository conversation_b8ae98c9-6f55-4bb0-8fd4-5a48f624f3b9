/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { CadPartsApi } from './CadPartsApi';
import { PartType } from './PartType';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "CadParts" of service "neue".
 */
export class CadParts<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements CadPartsType<T>
{
  /**
   * Technical entity name for CadParts.
   */
  static override _entityName = 'CadParts';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the CadParts entity.
   */
  static _keys = ['ncid'];
  /**
   * Gravity Center.
   * @nullable
   */
  declare gravityCenter?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Mass.
   * @nullable
   */
  declare mass?: DeserializedType<T, 'Edm.Decimal'> | null;
  /**
   * Material.
   * @nullable
   */
  declare material?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Open Surface Area.
   * @nullable
   */
  declare openSurfaceArea?: DeserializedType<T, 'Edm.Decimal'> | null;
  /**
   * Part Type.
   */
  declare partType: PartType;
  /**
   * Solid Surface Area.
   * @nullable
   */
  declare solidSurfaceArea?: DeserializedType<T, 'Edm.Decimal'> | null;
  /**
   * Volume.
   * @nullable
   */
  declare volume?: DeserializedType<T, 'Edm.Decimal'> | null;
  /**
   * Submit Description.
   * Maximum length: 1000.
   * @nullable
   */
  declare submitDescription?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Code.
   */
  declare code: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   * @nullable
   */
  declare name?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Description.
   * @nullable
   */
  declare description?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: CadPartsApi<T>) {
    super(_entityApi);
  }
}

export interface CadPartsType<T extends DeSerializers = DefaultDeSerializers> {
  gravityCenter?: DeserializedType<T, 'Edm.String'> | null;
  mass?: DeserializedType<T, 'Edm.Decimal'> | null;
  material?: DeserializedType<T, 'Edm.String'> | null;
  openSurfaceArea?: DeserializedType<T, 'Edm.Decimal'> | null;
  partType: PartType;
  solidSurfaceArea?: DeserializedType<T, 'Edm.Decimal'> | null;
  volume?: DeserializedType<T, 'Edm.Decimal'> | null;
  submitDescription?: DeserializedType<T, 'Edm.String'> | null;
  code: DeserializedType<T, 'Edm.String'>;
  name?: DeserializedType<T, 'Edm.String'> | null;
  description?: DeserializedType<T, 'Edm.String'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
