#!/bin/sh

set -e

# Windows 下 export 会生效
export TAG_VERSION=${TAG_VERSION:-"v0.0.77"}
export GIT_HEAD=$(git rev-parse HEAD)

echo "📦 使用版本: $TAG_VERSION"
echo "🔧 Git Head: $GIT_HEAD"

# --------------------------
# Step 2: 安装依赖
# --------------------------
pnpm i --frozen-lockfile

# --------------------------
# Step 3: 更新版本信息
# --------------------------
pnpm update:version

# --------------------------
# Step 4: 编译构建
# --------------------------
pnpm build

# --------------------------
# Step 5: 发布主包
# --------------------------
cd dist/neue-plus
npm publish
cd -

# --------------------------
# Step 6: 发布内部包
# --------------------------
cd internal/eslint-config
npm publish
cd -

# 如需发布 metadata 包，取消注释
# cd internal/metadata
# pnpm build
# npm publish
# cd -

echo "✅ Publish completed (TAG_VERSION=$TAG_VERSION)"
