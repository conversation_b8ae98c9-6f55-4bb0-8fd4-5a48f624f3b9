/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { ActionsApi } from './ActionsApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "Actions" of service "neue".
 */
export class Actions<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements ActionsType<T>
{
  /**
   * Technical entity name for Actions.
   */
  static override _entityName = 'Actions';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the Actions entity.
   */
  static _keys = ['ncid'];
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Schema.
   * @nullable
   */
  declare schema?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Is Bound.
   * @nullable
   */
  declare isBound?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Parameter.
   * @nullable
   */
  declare parameter?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Return Type.
   * @nullable
   */
  declare returnType?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Entity Set Path.
   * @nullable
   */
  declare entitySetPath?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: ActionsApi<T>) {
    super(_entityApi);
  }
}

export interface ActionsType<T extends DeSerializers = DefaultDeSerializers> {
  name: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  schema?: DeserializedType<T, 'Edm.String'> | null;
  isBound?: DeserializedType<T, 'Edm.Boolean'> | null;
  parameter?: DeserializedType<T, 'Edm.String'> | null;
  returnType?: DeserializedType<T, 'Edm.String'> | null;
  entitySetPath?: DeserializedType<T, 'Edm.String'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
