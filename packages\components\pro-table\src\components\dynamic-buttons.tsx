import { defineComponent, h, inject } from 'vue'
import * as icons from '@element-plus/icons-vue'
import { ElButton, ElIcon, ElSpace } from 'element-plus'
import { merge } from 'lodash-unified'
import { capitalize } from '@neue-plus/utils'
import { REFS_KEY } from '@neue-plus/components/material-render/src/context/event-flow'
import { ActionsProps } from '../pro-table'

export default defineComponent({
  name: 'DynamicButtons',
  props: {
    record: {
      type: Object,
    },
    actions: {
      type: Array as () => Array<ActionsProps>,
    },
  },
  setup(props) {
    const { actions, record } = props
    const refs = inject(REFS_KEY, {}) as Record<string, any>
    return () =>
      h(
        ElSpace,
        {},
        {
          default: () =>
            actions?.map((item) => {
              const { config, icon } = item
              if (icon) {
                const iconName = capitalize(item.icon)
                return h(
                  ElIcon,
                  merge({ size: 26 }, item.props, {
                    onClick: () => {
                      refs[config.target][config.actionType]?.(record?.row)
                    },
                  }),
                  {
                    default: () => h(icons[iconName as keyof typeof icons]),
                  }
                )
              } else {
                return h(
                  ElButton,
                  merge(
                    {
                      onClick: () => {
                        refs[config.target]?.[config.actionType]?.(record?.row)
                      },
                    },
                    item.props
                  ),
                  {
                    default: () => item.text,
                  }
                )
              }
            }),
        }
      )
  },
})
