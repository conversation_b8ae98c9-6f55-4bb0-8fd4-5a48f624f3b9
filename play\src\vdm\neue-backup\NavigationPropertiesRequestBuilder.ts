/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { NavigationProperties } from './NavigationProperties';

/**
 * Request builder class for operations supported on the {@link NavigationProperties} entity.
 */
export class NavigationPropertiesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<NavigationProperties<T>, T> {
  /**
   * Returns a request builder for querying all `NavigationProperties` entities.
   * @returns A request builder for creating requests to retrieve all `NavigationProperties` entities.
   */
  getAll(): GetAllRequestBuilder<NavigationProperties<T>, T> {
    return new GetAllRequestBuilder<NavigationProperties<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `NavigationProperties` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `NavigationProperties`.
   */
  create(
    entity: NavigationProperties<T>
  ): CreateRequestBuilder<NavigationProperties<T>, T> {
    return new CreateRequestBuilder<NavigationProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `NavigationProperties` entity based on its keys.
   * @param ncid Key property. See {@link NavigationProperties.ncid}.
   * @returns A request builder for creating requests to retrieve one `NavigationProperties` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<NavigationProperties<T>, T> {
    return new GetByKeyRequestBuilder<NavigationProperties<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `NavigationProperties`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `NavigationProperties`.
   */
  update(
    entity: NavigationProperties<T>
  ): UpdateRequestBuilder<NavigationProperties<T>, T> {
    return new UpdateRequestBuilder<NavigationProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `NavigationProperties`.
   * @param ncid Key property. See {@link NavigationProperties.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `NavigationProperties`.
   */
  delete(ncid: string): DeleteRequestBuilder<NavigationProperties<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `NavigationProperties`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `NavigationProperties` by taking the entity as a parameter.
   */
  delete(
    entity: NavigationProperties<T>
  ): DeleteRequestBuilder<NavigationProperties<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<NavigationProperties<T>, T> {
    return new DeleteRequestBuilder<NavigationProperties<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof NavigationProperties
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
