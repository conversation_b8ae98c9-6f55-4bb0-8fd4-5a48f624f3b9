/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers
} from '@sap-cloud-sdk/odata-v4';
import type { RelCadPartTwoDimensionDrawingRelationsApi } from './RelCadPartTwoDimensionDrawingRelationsApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "RelCadPartTwoDimensionDrawingRelations" of service "neue".
 */
export class RelCadPartTwoDimensionDrawingRelations<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements RelCadPartTwoDimensionDrawingRelationsType<T>
{
  /**
   * Technical entity name for RelCadPartTwoDimensionDrawingRelations.
   */
  static override _entityName = 'RelCadPartTwoDimensionDrawingRelations';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the RelCadPartTwoDimensionDrawingRelations entity.
   */
  static _keys = ['ncid'];
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: RelCadPartTwoDimensionDrawingRelationsApi<T>) {
    super(_entityApi);
  }
}

export interface RelCadPartTwoDimensionDrawingRelationsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
