import { buildProps } from '@neue-plus/utils'
import type { TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'
import type { NeTableColumnProps } from './table-column/type'

export const neTableProps = buildProps({
  bordered: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as () => Array<any>,
    default: () => [],
  },
  columns: {
    type: Array as () => Array<NeTableColumnProps>,
    default: () => [],
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'label',
      hasChildren: 'hasChildren',
    }),
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  loading: {
    type: Boolean,
    default: false,
  },
} as const)

export type NeTableProps = ExtractPropTypes<typeof neTableProps> &
  TableProps<any>
