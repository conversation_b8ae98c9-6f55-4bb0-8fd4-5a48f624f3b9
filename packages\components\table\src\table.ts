import { buildProps } from '@neue-plus/utils'
import { ProTableColumn } from '@neue-plus/components/pro-table'
import type { TableProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

export const neTableProps = buildProps({
  bordered: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as () => Array<any>,
    default: () => [],
  },
  columns: {
    type: Array as () => Array<ProTableColumn>,
    default: () => [],
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'label',
      hasChildren: 'hasChildren',
    }),
  },
  rowKey: {
    type: String,
    default: 'ncid',
  },
  loading: {
    type: Boolean,
    default: false,
  },
} as const)

export type NeTableProps = ExtractPropTypes<typeof neTableProps> &
  TableProps<any>
