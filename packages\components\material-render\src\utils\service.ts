import axios from 'axios'
import { handleMessage } from '../handlers/handleMessage'
import type { AxiosResponse } from 'axios'

const service = axios.create({
  baseURL: 'https://api-cn-hangzhou-2.cloud.neuetech.cn',
  timeout: 10000,
  paramsSerializer: (params) => {
    return Object.entries(params)
      .map(([key, val]) => `${key}=${val}`)
      .join('&')
  },
})

// 2. 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 可在此处统一携带 token
    const token = localStorage.getItem('token')
    config.headers.Accept = config.headers.Accept || 'application/json'
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 3. 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    if (response.status !== 200) {
      handleMessage(response.data?.error?.message || '系统报错')
      return Promise.reject(data)
    }
    return data
  },
  (error) => {
    handleMessage(
      error.response.data.error.message || error.message || '系统报错'
    )
    return Promise.reject(error)
  }
)

export default service
