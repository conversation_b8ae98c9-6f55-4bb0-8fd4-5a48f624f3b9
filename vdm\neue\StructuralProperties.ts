/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { StructuralPropertiesApi } from './StructuralPropertiesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "StructuralProperties" of service "neue".
 */
export class StructuralProperties<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements StructuralPropertiesType<T>
{
  /**
   * Technical entity name for StructuralProperties.
   */
  static override _entityName = 'StructuralProperties';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the StructuralProperties entity.
   */
  static _keys = ['ncid'];
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Display Name.
   */
  declare displayName: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Type.
   */
  declare type: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Collection.
   * @nullable
   */
  declare isCollection?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Nullable.
   * @nullable
   */
  declare isNullable?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Max Length.
   * @nullable
   */
  declare maxLength?: DeserializedType<T, 'Edm.Int16'> | null;
  /**
   * Is Unicode.
   * @nullable
   */
  declare isUnicode?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Srid.
   * @nullable
   */
  declare srid?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Precision.
   * @nullable
   */
  declare precision?: DeserializedType<T, 'Edm.Int16'> | null;
  /**
   * Scale.
   * @nullable
   */
  declare scale?: DeserializedType<T, 'Edm.Int16'> | null;
  /**
   * Default Value.
   * @nullable
   */
  declare defaultValue?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Is Auto Generate Code.
   * @nullable
   */
  declare isAutoGenerateCode?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Read Only.
   * @nullable
   */
  declare isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Uniq.
   * @nullable
   */
  declare isUniq?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: StructuralPropertiesApi<T>) {
    super(_entityApi);
  }
}

export interface StructuralPropertiesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  name: DeserializedType<T, 'Edm.String'>;
  displayName: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  type: DeserializedType<T, 'Edm.String'>;
  isCollection?: DeserializedType<T, 'Edm.Boolean'> | null;
  isNullable?: DeserializedType<T, 'Edm.Boolean'> | null;
  maxLength?: DeserializedType<T, 'Edm.Int16'> | null;
  isUnicode?: DeserializedType<T, 'Edm.Boolean'> | null;
  srid?: DeserializedType<T, 'Edm.String'> | null;
  precision?: DeserializedType<T, 'Edm.Int16'> | null;
  scale?: DeserializedType<T, 'Edm.Int16'> | null;
  defaultValue?: DeserializedType<T, 'Edm.String'> | null;
  isAutoGenerateCode?: DeserializedType<T, 'Edm.Boolean'> | null;
  isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  isUniq?: DeserializedType<T, 'Edm.Boolean'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
