import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import DynamicIcon from '../src/dynamic-icon.vue'

describe('DynamicIcon.vue', () => {
  test('render test', () => {
    const wrapper = mount(() => <DynamicIcon name="Edit" />)
    expect(wrapper.find('.el-icon').exists()).toBe(true)
  })

  test('icon name prop', () => {
    const wrapper = mount(() => <DynamicIcon name="Delete" />)
    expect(wrapper.find('svg').exists()).toBe(true)
  })

  test('color prop', () => {
    const color = '#ff0000'
    const wrapper = mount(() => <DynamicIcon name="Edit" color={color} />)
    const iconElement = wrapper.find('.el-icon')
    expect(iconElement.attributes('style')).toContain(`color: ${color}`)
  })

  test('size prop with number', () => {
    const size = 24
    const wrapper = mount(() => <DynamicIcon name="Edit" size={size} />)
    const iconElement = wrapper.find('.el-icon')
    expect(iconElement.attributes('style')).toContain(`font-size: ${size}px`)
  })

  test('size prop with string', () => {
    const size = '2rem'
    const wrapper = mount(() => <DynamicIcon name="Edit" size={size} />)
    const iconElement = wrapper.find('.el-icon')
    expect(iconElement.attributes('style')).toContain(`font-size: ${size}`)
  })

  test('default props', () => {
    const wrapper = mount(() => <DynamicIcon name="Edit" />)
    const iconElement = wrapper.find('.el-icon')
    expect(iconElement.attributes('style')).toContain('color: inherit')
    expect(iconElement.attributes('style')).toContain('font-size: inherit')
  })
})
