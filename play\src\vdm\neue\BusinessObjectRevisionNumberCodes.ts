/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { Entity, DeserializedType } from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers,
} from '@sap-cloud-sdk/odata-v4'
import type { BusinessObjectRevisionNumberCodesApi } from './BusinessObjectRevisionNumberCodesApi'
import { LifecycleState } from './LifecycleState'
import {
  BusinessObjectRevisionNumberRules,
  BusinessObjectRevisionNumberRulesType,
} from './BusinessObjectRevisionNumberRules'

/**
 * This class represents the entity "BusinessObjectRevisionNumberCodes" of service "neue".
 */
export class BusinessObjectRevisionNumberCodes<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements BusinessObjectRevisionNumberCodesType<T>
{
  /**
   * Technical entity name for BusinessObjectRevisionNumberCodes.
   */
  static override _entityName = 'BusinessObjectRevisionNumberCodes'
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/'
  /**
   * All key fields of the BusinessObjectRevisionNumberCodes entity.
   */
  static _keys = ['ncid']
  /**
   * Revision Order.
   */
  declare revisionOrder: DeserializedType<T, 'Edm.Int32'>
  /**
   * Revision Code.
   */
  declare revisionCode: DeserializedType<T, 'Edm.String'>
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null
  /**
   * One-to-one navigation property to the {@link BusinessObjectRevisionNumberRules} entity.
   */
  declare ruleCodeRef?: BusinessObjectRevisionNumberRules<T> | null

  constructor(_entityApi: BusinessObjectRevisionNumberCodesApi<T>) {
    super(_entityApi)
  }
}

export interface BusinessObjectRevisionNumberCodesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  revisionOrder: DeserializedType<T, 'Edm.Int32'>
  revisionCode: DeserializedType<T, 'Edm.String'>
  ncid: DeserializedType<T, 'Edm.String'>
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>
  schemaVersion: DeserializedType<T, 'Edm.String'>
  lifecycleState: LifecycleState
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null
  ruleCodeRef?: BusinessObjectRevisionNumberRulesType<T> | null
}
