import { inject, provide, reactive, ref } from 'vue'
import { keys, merge } from 'lodash-unified'
import * as handlers from '../handlers'
import { request } from '../handlers/handleRequest'

export const REFS_KEY = Symbol('REFS')
export const HANDLERS_KEY = Symbol('HANDLERS')
export const APIS_KEY = Symbol('APIS')
export const METADATA_KEY = Symbol('METADATA')
interface FuncMap {
  [key: string]: (...args: any[]) => any
}
const getApiFn = (apis: any) => {
  return keys(apis).reduce((pre, key) => {
    const apiConfig = apis[key]
    pre[key] = ({ params, data, query }: any) => {
      const mergedQuery = { ...apiConfig.query, ...query }
      const requestOptions = {
        ...apiConfig,
        //get获取单个数据query传参
        params,
        //post 数据
        data,
        //query get获取集合多个
        query: mergedQuery,
      }
      return request(requestOptions)
    }
    return pre
  }, {} as any)
}
export function provideContext(apis: any) {
  const metadataRef = ref()
  const obj = getApiFn(apis)
  const refs = reactive<Record<string, any>>({})
  const apisRef = reactive<FuncMap>(obj)
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  provide(APIS_KEY, apisRef)
  provide(METADATA_KEY, metadataRef)

  return { refs, metadataRef }
}

export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  if (!refs || !handlers) throw new Error('Event context is not provided')
  return { refs, handlers }
}
export function useApisRef(apiList?: any) {
  let apis = inject<FuncMap>(APIS_KEY, {})
  if (apiList) {
    const obj = getApiFn(apiList)
    apis = merge({}, apis, obj)
  }
  return apis
}

export function useMetadataRef() {
  const metadataRef = inject<Record<string, any>>(METADATA_KEY, {})
  return metadataRef
}
