import { inject, provide, reactive, ref } from 'vue'
import { keys, merge } from 'lodash-unified'
import * as handlers from '../handlers'
import { request } from '../handlers/handleRequest'

export const REFS_KEY = Symbol('REFS')
export const HANDLERS_KEY = Symbol('HANDLERS')
export const APIS_KEY = Symbol('APIS')
export const METADATA_KEY = Symbol('METADATA')
export const SYS_PARAMS_KEY = Symbol('SYS_INIT_PARAMS')
interface FuncMap {
  [key: string]: (...args: any[]) => any
}
const getApiFn = (apis: any) => {
  return keys(apis).reduce((pre, key) => {
    const apiConfig = apis[key]
    pre[key] = ({ params, data, query, replaceData }: any) => {
      const mergedQuery = merge({}, apiConfig.query, query)
      console.log(apiConfig, mergedQuery, 'query')
      const requestOptions = merge({}, apiConfig, {
        //get获取单个数据query传参
        params,
        //post 数据
        data,
        //query get获取集合多个
        query: keys(mergedQuery).length > 0 ? mergedQuery : undefined,
        replaceData,
      })
      return request(requestOptions)
    }
    return pre
  }, {} as any)
}

function getUrlQuery(url: string = window.location.href) {
  let queryString = ''
  // 优先取 hash 中的 query
  const hashIndex = url.indexOf('#')
  if (hashIndex !== -1) {
    const hashPart = url.slice(hashIndex + 1)
    const qIndex = hashPart.indexOf('?')
    if (qIndex !== -1) {
      queryString = hashPart.slice(qIndex + 1)
    }
  }
  // 如果 hash 中没有 query，再取普通 query
  if (!queryString) {
    const qIndex = url.indexOf('?')
    if (qIndex !== -1) {
      queryString = url.slice(qIndex + 1)
    }
  }
  if (!queryString) return {}
  // 解析参数并自动解码
  const params = new URLSearchParams(queryString)
  const result: Record<string, string> = {}
  for (const [key, value] of params.entries()) {
    result[decodeURIComponent(key)] = decodeURIComponent(value)
  }
  return result
}
export function provideContext(apis: any) {
  const queryParams = ref(getUrlQuery())
  const metadataRef = ref()
  const obj = getApiFn(apis)
  const refs = reactive<Record<string, any>>({})
  const apisRef = reactive<FuncMap>(obj)
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  provide(APIS_KEY, apisRef)
  provide(METADATA_KEY, metadataRef)
  provide(SYS_PARAMS_KEY, queryParams)

  return { refs, metadataRef }
}

export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  if (!refs || !handlers) throw new Error('Event context is not provided')
  return { refs, handlers }
}
export function useApisRef(apiList?: any) {
  let apis = inject<FuncMap>(APIS_KEY, {})
  if (apiList) {
    const obj = getApiFn(apiList)
    apis = merge({}, apis, obj)
  }
  return apis
}

export function useMetadataRef() {
  const metadataRef = inject<Record<string, any>>(METADATA_KEY, {})
  return metadataRef
}

export function useSysParamsRef() {
  const sysParamsRef = inject<Record<string, any>>(SYS_PARAMS_KEY, {})
  return sysParamsRef
}
