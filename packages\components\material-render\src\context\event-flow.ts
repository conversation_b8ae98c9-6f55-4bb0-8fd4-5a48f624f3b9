import { inject, provide, reactive } from 'vue'
import * as handlers from '../handlers'
import { ApiSchemaMap } from '../types'

export const REFS_KEY = Symbol('REFS')
export const HANDLERS_KEY = Symbol('HANDLERS')
export const APIS_KEY = Symbol('APIS')

export function provideContext(props: { apis: ApiSchemaMap }) {
  const { apis } = props
  const refs = reactive<Record<string, any>>({})
  const apiRefs = reactive<ApiSchemaMap>(apis)
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  provide(APIS_KEY, apiRefs)
  return refs
}

export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  if (!refs || !handlers) throw new Error('Event context is not provided')
  return { refs, handlers }
}
export function useApiRefs(): ApiSchemaMap {
  const apis = inject<ApiSchemaMap>(APIS_KEY)
  if (!apis) throw new Error('useApiRefs: not provided')
  return apis
}
