/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RootRelationTypes } from './RootRelationTypes';

/**
 * Request builder class for operations supported on the {@link RootRelationTypes} entity.
 */
export class RootRelationTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RootRelationTypes<T>, T> {
  /**
   * Returns a request builder for querying all `RootRelationTypes` entities.
   * @returns A request builder for creating requests to retrieve all `RootRelationTypes` entities.
   */
  getAll(): GetAllRequestBuilder<RootRelationTypes<T>, T> {
    return new GetAllRequestBuilder<RootRelationTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `RootRelationTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RootRelationTypes`.
   */
  create(
    entity: RootRelationTypes<T>
  ): CreateRequestBuilder<RootRelationTypes<T>, T> {
    return new CreateRequestBuilder<RootRelationTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `RootRelationTypes` entity based on its keys.
   * @param ncid Key property. See {@link RootRelationTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `RootRelationTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RootRelationTypes<T>, T> {
    return new GetByKeyRequestBuilder<RootRelationTypes<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `RootRelationTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RootRelationTypes`.
   */
  update(
    entity: RootRelationTypes<T>
  ): UpdateRequestBuilder<RootRelationTypes<T>, T> {
    return new UpdateRequestBuilder<RootRelationTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `RootRelationTypes`.
   * @param ncid Key property. See {@link RootRelationTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RootRelationTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<RootRelationTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RootRelationTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RootRelationTypes` by taking the entity as a parameter.
   */
  delete(
    entity: RootRelationTypes<T>
  ): DeleteRequestBuilder<RootRelationTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<RootRelationTypes<T>, T> {
    return new DeleteRequestBuilder<RootRelationTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof RootRelationTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
