import { ExtractPropTypes, PropType } from 'vue'
import { dialogProps } from 'element-plus'
import { RequestOptions } from '../../../components/material-render/src/handlers/handleRequest'

export const neBomDialogProps = {
  ...dialogProps,
  request: {
    type: Object as PropType<RequestOptions>,
    default: undefined,
  },
  submit: {
    type: Object as PropType<RequestOptions>,
    default: undefined,
  },
}
export type NeBomDialogProps = ExtractPropTypes<typeof neBomDialogProps>
