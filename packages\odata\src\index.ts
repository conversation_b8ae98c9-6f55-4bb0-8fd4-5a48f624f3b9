/**
 * @neue-plus/odata - OData 客户端库
 *
 * 基于 Olingo OData4 JS 库的 TypeScript 封装
 */

// 导入核心类
import { ODataClient, createODataClient } from './core/odata-client'
import { QueryBuilder, createQueryBuilder } from './core/query-builder'
import { EntityOperations } from './core/entity-operations'
import { OlingoWrapper } from './core/olingo-wrapper'
import { SapSdkWrapper } from './core/sap-sdk-wrapper'
import { MetadataParser } from './core/metadata-parser'
import type { ServiceMetadata } from './types'

export * as ODatajs from './odata'

// 导出核心类
export { ODataClient, createODataClient }
export { QueryBuilder, createQueryBuilder }
export { EntityOperations }
export { OlingoWrapper }
export { SapSdkWrapper }
export { MetadataParser }

// 导出工具函数
export {
  FilterBuilder,
  QueryOptionsBuilder,
  filter,
  queryOptions,
  escapeODataString,
  formatODataDate,
  buildEntityKey,
  parseODataError,
} from './utils'

// 导出所有类型
export type {
  // 基础类型
  HttpMethod,
  RequestConfig,
  Response,
  ODataQueryOptions,
  ODataResponse,
  ODataError,
  ODataServiceConfig,
  EntitySetConfig,

  // 接口类型
  QueryBuilder as IQueryBuilder,
  EntityOperations as IEntityOperations,
  ODataClient as IODataClient,
  HttpClient,

  // 缓存和插件
  CacheOptions,
  ODataPlugin,

  // 拦截器类型
  RequestInterceptor,
  ResponseInterceptor,
  ErrorInterceptor,

  // 事件类型
  ODataEventType,
  ODataEvent,

  // 元数据类型
  ServiceMetadata,
  MetadataSchema,
  MetadataEntityType,
  MetadataEntitySet,
  MetadataProperty,
  MetadataNavigationProperty,

  // SAP Cloud SDK 类型
  SapSdkConfig,
} from './types'

// 默认导出创建客户端的工厂函数
export { createODataClient as default } from './core/odata-client'

/**
 * 版本信息
 */
export const version = '1.0.0'

/**
 * 快速创建 OData 客户端的便捷函数
 */
export function createClient(
  baseUrl: string,
  servicePath?: string,
  headers?: Record<string, string>
) {
  return createODataClient({
    baseUrl,
    servicePath,
    headers,
  })
}

/**
 * 创建带认证的 OData 客户端
 */
export function createAuthenticatedClient(
  baseUrl: string,
  authentication: {
    type: 'basic' | 'bearer'
    username?: string
    password?: string
    token?: string
  },
  servicePath?: string
) {
  const headers: Record<string, string> = {}

  if (
    authentication.type === 'basic' &&
    authentication.username &&
    authentication.password
  ) {
    const credentials = btoa(
      `${authentication.username}:${authentication.password}`
    )
    headers['Authorization'] = `Basic ${credentials}`
  } else if (authentication.type === 'bearer' && authentication.token) {
    headers['Authorization'] = `Bearer ${authentication.token}`
  }

  return createODataClient({
    baseUrl,
    servicePath,
    headers,
    authentication: {
      type: authentication.type,
      credentials: {
        username: authentication.username,
        password: authentication.password,
        token: authentication.token,
      },
    },
  })
}

/**
 * 创建使用 SAP Cloud SDK 的 OData 客户端
 */
export function createSapSdkClient(
  baseUrl: string,
  servicePath?: string,
  headers?: Record<string, string>
) {
  return createODataClient({
    baseUrl,
    servicePath,
    headers,
    useSapSdk: true,
  })
}

/**
 * 解析 OData 服务元数据
 */
export function parseMetadata(xmlContent: string): ServiceMetadata {
  return MetadataParser.parseXml(xmlContent)
}
