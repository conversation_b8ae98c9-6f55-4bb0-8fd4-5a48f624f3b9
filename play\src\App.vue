<template>
  <Login />
  <!-- <modalForm /> -->
  <!-- <CadPart /> -->
  <PartProps />
  <!-- <FileProps /> -->
  <!-- <Material /> -->
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
// import login from './components/login/index.vue'
import modalForm from './components/modal-form/index.vue'
import PartProps from './components/part-props/part-props.vue'
import FileProps from './components/part-file-props/part-file-props.vue'
import Material from './components/material/odata.vue'
import Login from './components/login/index.vue'
import CadPart from './components/cad-part/cad-part.vue'
</script>
<style>
body {
  margin: 0;
  padding: 0;
}
#app,
body,
html {
  height: 100%;
}
</style>
