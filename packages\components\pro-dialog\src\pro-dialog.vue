<script setup lang="ts">
import { markRaw, onMounted, ref, shallowRef, watch } from 'vue'
import { ElButton, ElDialog } from 'element-plus'
import DynamicIcon from '../../dynamic-icon'
import { neProDialogProps } from './types'

const props = defineProps(neProDialogProps)
const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue || false)

// 同步外部 v-model 和内部 visible
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
  }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 暴露方法给父组件调用
defineExpose({
  open: () => {
    visible.value = true
  },
  close: () => {
    visible.value = false
  },
})
const { trigger, ...otherProps } = props

// 动态加载组件并标记为非响应式
const NeRenderCore = shallowRef<any>(null)
onMounted(async () => {
  const module = await import('../../material-render')
  NeRenderCore.value = markRaw(module.NeRenderCore)
})
</script>

<template>
  <DynamicIcon
    v-if="trigger?.icon"
    :name="trigger.icon"
    v-bind="trigger.props"
    @click="visible = true"
  />
  <el-button v-else v-bind="trigger?.props" @click="visible = true">{{
    trigger?.text
  }}</el-button>
  <ElDialog v-bind="otherProps" v-model="visible">
    <slot />
  </ElDialog>
</template>
