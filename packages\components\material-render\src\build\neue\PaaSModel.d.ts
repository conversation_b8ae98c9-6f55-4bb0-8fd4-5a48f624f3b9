export declare enum LifecycleState {
    CREATING = "CREATING",
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    NEEDS_ATTENTION = "NEEDS_ATTENTION",
    UPDATING = "UPDATING",
    DELETING = "DELETING",
    DELETED = "DELETED"
}
export declare enum OnDelete {
    CASCADE = "CASCADE",
    NONE = "NONE",
    SET_NULL = "SET_NULL",
    SET_DEFAULT = "SET_DEFAULT"
}
export declare enum MrvStrategyType {
    NONE = "NONE",
    REVISION = "REVISION",
    REVISION_VERSION = "REVISION_VERSION"
}
export declare enum RelationSourceTypeEnum {
    FROM = "FROM",
    TO = "TO"
}
export declare enum RelationConstrictEnum {
    MANY = "MANY",
    ONE = "ONE"
}
export declare enum RelationStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE"
}
export declare enum RelationClassify {
    ASSOCIATION = "ASSOCIATION",
    AGGREGATION = "AGGREGATION",
    COMPOSITION = "COMPOSITION"
}
export declare enum RelationVersionUpgradeActionEnum {
    NONE = "NONE",
    FLOAT = "FLOAT",
    CLONE = "CLONE"
}
export declare enum IdiConvertState {
    INIT = "INIT",
    RUNNING = "RUNNING",
    FAILURE = "FAILURE",
    SUCCESS = "SUCCESS"
}
export declare enum PartType {
    NEUE_PRT = "NEUE_PRT",
    NEUE_ASM = "NEUE_ASM"
}
export declare enum FileSignatureUrlActionType {
    UPLOAD = "UPLOAD",
    DOWNLOAD = "DOWNLOAD"
}
export declare enum RelationSourceTypeEnum2 {
    FROM = "FROM",
    TO = "TO"
}
export declare enum RelCadFileFileUsageTypeEnum {
    DESIGN_MODEL = "DESIGN_MODEL",
    EXCHANGE_FORMAT = "EXCHANGE_FORMAT",
    THUMBNAIL = "THUMBNAIL",
    PDF = "PDF",
    SIMULATION = "SIMULATION",
    TWO_DIMENSION_DRAWING = "TWO_DIMENSION_DRAWING"
}
export declare enum MappingConfigToolEnum {
    CAX = "CAX",
    NX = "NX",
    SOLIDWORKS = "SOLIDWORKS",
    CATIA = "CATIA"
}
export declare enum MappingConfigPartTypeEnum {
    PART = "PART",
    DRAWING = "DRAWING",
    FE = "FE",
    CADBOM = "CADBOM"
}
export declare enum MappingConfigDirectionEnum {
    CAX_TO_CDP = "CAX_TO_CDP",
    CDP_TO_CAX = "CDP_TO_CAX",
    BIDIRECTIONAL = "BIDIRECTIONAL"
}
export declare enum RelCadFileFileLinkTypeEnum {
    MASTER = "MASTER",
    REFERENCE = "REFERENCE"
}
export interface Plt0ApplicationObject {
    /**
     * **Key Property**: This is a key property used to identify the entity.<br/>**Managed**: This property is managed on the server side and cannot be edited.
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ncid` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    ncid: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `createdAt` |
     * | Type | `Edm.DateTimeOffset` |
     * | Nullable | `false` |
     */
    createdAt: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `modifiedAt` |
     * | Type | `Edm.DateTimeOffset` |
     * | Nullable | `false` |
     */
    modifiedAt: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schemaVersion` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    schemaVersion: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lifecycleState` |
     * | Type | `PaaS.LifecycleState` |
     * | Nullable | `false` |
     */
    lifecycleState: LifecycleState;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lifecycleNote` |
     * | Type | `Edm.String` |
     */
    lifecycleNote: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `createdBy` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     * | Nullable | `false` |
     */
    createdBy?: Plt0ApplicationObject;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `modifiedBy` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     * | Nullable | `false` |
     */
    modifiedBy?: Plt0ApplicationObject;
}
export type Plt0ApplicationObjectId = string | {
    ncid: string;
};
export interface EditablePlt0ApplicationObject extends Pick<Plt0ApplicationObject, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0ApplicationObject, "lifecycleNote">> {
}
export interface Plt0ApplicationObject_GenerateCodeParams {
    propertyName: string;
}
export interface Plt0MasterObject extends Plt0ApplicationObject {
}
export interface EditablePlt0MasterObject extends Pick<Plt0MasterObject, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0MasterObject, "lifecycleNote">> {
}
export interface Plt0RevisionObject extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revisionCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    revisionCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revisionOrder` |
     * | Type | `Edm.Int32` |
     * | Nullable | `false` |
     */
    revisionOrder: number;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isLatestRevision` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isLatestRevision: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isLocked` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isLocked: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lockedAt` |
     * | Type | `Edm.DateTimeOffset` |
     */
    lockedAt: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `preRevision` |
     * | Type | `BuiltInModel.Plt0RevisionObject` |
     */
    preRevision?: Plt0RevisionObject | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `master` |
     * | Type | `BuiltInModel.Plt0MasterObject` |
     * | Nullable | `false` |
     */
    master?: Plt0MasterObject;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lockedBy` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     */
    lockedBy?: Plt0ApplicationObject | null;
}
export interface EditablePlt0RevisionObject extends Pick<Plt0RevisionObject, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "revisionCode" | "revisionOrder" | "isLatestRevision" | "isLocked">, Partial<Pick<Plt0RevisionObject, "lifecycleNote" | "lockedAt">> {
}
export interface Plt0VersionObject extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `versionNumber` |
     * | Type | `Edm.Int32` |
     * | Nullable | `false` |
     */
    versionNumber: number;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isLatestVersion` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isLatestVersion: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revision` |
     * | Type | `BuiltInModel.Plt0RevisionObject` |
     * | Nullable | `false` |
     */
    revision?: Plt0RevisionObject;
}
export interface EditablePlt0VersionObject extends Pick<Plt0VersionObject, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "versionNumber" | "isLatestVersion">, Partial<Pick<Plt0VersionObject, "lifecycleNote">> {
}
export interface Plt0LifecycleStatus extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `statusCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    statusCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
}
export interface EditablePlt0LifecycleStatus extends Pick<Plt0LifecycleStatus, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "statusCode" | "name" | "description">, Partial<Pick<Plt0LifecycleStatus, "lifecycleNote">> {
}
export interface Plt0LifecycleStatusStrategy extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `strategyCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    strategyCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
}
export interface EditablePlt0LifecycleStatusStrategy extends Pick<Plt0LifecycleStatusStrategy, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "strategyCode" | "name" | "description">, Partial<Pick<Plt0LifecycleStatusStrategy, "lifecycleNote">> {
}
export interface Plt0MrvObject extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `code` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    code: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     */
    name: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     */
    description: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `version` |
     * | Type | `BuiltInModel.Plt0VersionObject` |
     */
    version?: Plt0VersionObject | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `owner` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     */
    owner?: Plt0ApplicationObject | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lifecycleStatus` |
     * | Type | `BuiltInModel.Plt0LifecycleStatus` |
     */
    lifecycleStatus?: Plt0LifecycleStatus | null;
}
export interface EditablePlt0MrvObject extends Pick<Plt0MrvObject, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code">, Partial<Pick<Plt0MrvObject, "lifecycleNote" | "name" | "description">> {
}
export interface Plt0File extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     */
    name: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     */
    description: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `softwareRev` |
     * | Type | `Edm.String` |
     */
    softwareRev: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `storageItem` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     */
    storageItem?: Plt0ApplicationObject | null;
}
export interface EditablePlt0File extends Pick<Plt0File, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0File, "lifecycleNote" | "name" | "description" | "softwareRev">> {
}
export interface Plt0IdiFile extends Plt0File {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `idiLightModelId` |
     * | Type | `Edm.String` |
     */
    idiLightModelId: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `convertState` |
     * | Type | `BuiltInModel.IdiConvertState` |
     */
    convertState: IdiConvertState | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `triggerTime` |
     * | Type | `Edm.DateTimeOffset` |
     */
    triggerTime: string | null;
}
export interface EditablePlt0IdiFile extends Pick<Plt0IdiFile, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0IdiFile, "lifecycleNote" | "name" | "description" | "softwareRev" | "idiLightModelId" | "convertState" | "triggerTime">> {
}
export interface Plt0RootRelationType extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `from` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     * | Nullable | `false` |
     */
    from?: Plt0ApplicationObject;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `to` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     * | Nullable | `false` |
     */
    to?: Plt0ApplicationObject;
}
export interface EditablePlt0RootRelationType extends Pick<Plt0RootRelationType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0RootRelationType, "lifecycleNote">> {
}
export interface Plt0RelationQuery extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `toRelations` |
     * | Type | `Collection(BuiltInModel.Plt0RootRelationType)` |
     */
    toRelations?: Array<Plt0RootRelationType>;
}
export interface EditablePlt0RelationQuery extends Pick<Plt0RelationQuery, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0RelationQuery, "lifecycleNote">> {
}
export interface Plt0RootRelationTypeConfig extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `relationClassify` |
     * | Type | `BuiltInModel.RelationClassify` |
     * | Nullable | `false` |
     */
    relationClassify: RelationClassify;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `sourceRelationConstrict` |
     * | Type | `BuiltInModel.RelationConstrictEnum` |
     * | Nullable | `false` |
     */
    sourceRelationConstrict: RelationConstrictEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `targetRelationConstrict` |
     * | Type | `BuiltInModel.RelationConstrictEnum` |
     * | Nullable | `false` |
     */
    targetRelationConstrict: RelationConstrictEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `sourceRelationVersionUpgradeAction` |
     * | Type | `BuiltInModel.RelationVersionUpgradeActionEnum` |
     * | Nullable | `false` |
     */
    sourceRelationVersionUpgradeAction: RelationVersionUpgradeActionEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `targetRelationVersionUpgradeAction` |
     * | Type | `BuiltInModel.RelationVersionUpgradeActionEnum` |
     * | Nullable | `false` |
     */
    targetRelationVersionUpgradeAction: RelationVersionUpgradeActionEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `relationStatus` |
     * | Type | `BuiltInModel.RelationStatus` |
     * | Nullable | `false` |
     */
    relationStatus: RelationStatus;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     */
    description: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `applyRelationType` |
     * | Type | `BuiltInModel.Plt0RootRelationType` |
     * | Nullable | `false` |
     */
    applyRelationType?: Plt0RootRelationType;
}
export interface EditablePlt0RootRelationTypeConfig extends Pick<Plt0RootRelationTypeConfig, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "relationClassify" | "sourceRelationConstrict" | "targetRelationConstrict" | "sourceRelationVersionUpgradeAction" | "targetRelationVersionUpgradeAction" | "relationStatus">, Partial<Pick<Plt0RootRelationTypeConfig, "lifecycleNote" | "description">> {
}
export interface Plt0RelationFromOrToType extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `sideType` |
     * | Type | `BuiltInModel.RelationSourceTypeEnum` |
     * | Nullable | `false` |
     */
    sideType: RelationSourceTypeEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `relationType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    relationType?: Plt0EntityType;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `constrictType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    constrictType?: Plt0EntityType;
}
export interface EditablePlt0RelationFromOrToType extends Pick<Plt0RelationFromOrToType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "sideType">, Partial<Pick<Plt0RelationFromOrToType, "lifecycleNote">> {
}
export interface Plt0Schema extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `namespace` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    namespace: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `alias` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    alias: string;
}
export interface EditablePlt0Schema extends Pick<Plt0Schema, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "namespace" | "alias">, Partial<Pick<Plt0Schema, "lifecycleNote">> {
}
export interface Plt0BaseType extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `displayName` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    displayName: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isAbstract` |
     * | Type | `Edm.Boolean` |
     */
    isAbstract: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isOpen` |
     * | Type | `Edm.Boolean` |
     */
    isOpen: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `BuiltInModel.Plt0Schema` |
     * | Nullable | `false` |
     */
    schema?: Plt0Schema;
}
export interface EditablePlt0BaseType extends Pick<Plt0BaseType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "displayName" | "description">, Partial<Pick<Plt0BaseType, "lifecycleNote" | "isAbstract" | "isOpen">> {
}
export interface Plt0EntityType extends Plt0BaseType {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityTypeCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    entityTypeCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isVirtual` |
     * | Type | `Edm.Boolean` |
     */
    isVirtual: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `hasStream` |
     * | Type | `Edm.Boolean` |
     */
    hasStream: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `mrvStrategy` |
     * | Type | `BuiltInModel.MrvStrategyType` |
     */
    mrvStrategy: MrvStrategyType | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isReadOnly` |
     * | Type | `Edm.Boolean` |
     */
    isReadOnly: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `icon` |
     * | Type | `BuiltInModel.Plt0File` |
     * | Nullable | `false` |
     */
    icon?: Plt0File;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `baseType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     */
    baseType?: Plt0EntityType | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `lifecycleStatusStrategy` |
     * | Type | `BuiltInModel.Plt0LifecycleStatusStrategy` |
     */
    lifecycleStatusStrategy?: Plt0LifecycleStatusStrategy | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revisionNumberRule` |
     * | Type | `BuiltInModel.Plt0BusinessObjectRevisionNumberRule` |
     */
    revisionNumberRule?: Plt0BusinessObjectRevisionNumberRule | null;
}
export interface EditablePlt0EntityType extends Pick<Plt0EntityType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "displayName" | "description" | "entityTypeCode">, Partial<Pick<Plt0EntityType, "lifecycleNote" | "isAbstract" | "isOpen" | "isVirtual" | "hasStream" | "mrvStrategy" | "isReadOnly">> {
}
export interface Plt0EntityTypeUnionUniq extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ownerType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    ownerType?: Plt0EntityType;
}
export interface EditablePlt0EntityTypeUnionUniq extends Pick<Plt0EntityTypeUnionUniq, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0EntityTypeUnionUniq, "lifecycleNote">> {
}
export interface Plt0EntityTypeUnionUniqProperty extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ownerUnionUniq` |
     * | Type | `BuiltInModel.Plt0EntityTypeUnionUniq` |
     * | Nullable | `false` |
     */
    ownerUnionUniq?: Plt0EntityTypeUnionUniq;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `structuralProperty` |
     * | Type | `BuiltInModel.Plt0StructuralProperty` |
     */
    structuralProperty?: Plt0StructuralProperty | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `navigationProperty` |
     * | Type | `BuiltInModel.Plt0NavigationProperty` |
     */
    navigationProperty?: Plt0NavigationProperty | null;
}
export interface EditablePlt0EntityTypeUnionUniqProperty extends Pick<Plt0EntityTypeUnionUniqProperty, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0EntityTypeUnionUniqProperty, "lifecycleNote">> {
}
export interface Plt0EntityTypePropertyOverride extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isAutoGenerateCode` |
     * | Type | `Edm.Boolean` |
     */
    isAutoGenerateCode: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ownerType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    ownerType?: Plt0EntityType;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `structuralProperty` |
     * | Type | `BuiltInModel.Plt0StructuralProperty` |
     * | Nullable | `false` |
     */
    structuralProperty?: Plt0StructuralProperty;
}
export interface EditablePlt0EntityTypePropertyOverride extends Pick<Plt0EntityTypePropertyOverride, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0EntityTypePropertyOverride, "lifecycleNote" | "isAutoGenerateCode">> {
}
export interface Plt0ComplexType extends Plt0BaseType {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `complexTypeCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    complexTypeCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `baseType` |
     * | Type | `BuiltInModel.Plt0ComplexType` |
     */
    baseType?: Plt0ComplexType | null;
}
export interface EditablePlt0ComplexType extends Pick<Plt0ComplexType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "displayName" | "description" | "complexTypeCode">, Partial<Pick<Plt0ComplexType, "lifecycleNote" | "isAbstract" | "isOpen">> {
}
export interface Plt0EnumType extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `enumTypeCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    enumTypeCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `underlyingType` |
     * | Type | `Edm.String` |
     */
    underlyingType: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isFlags` |
     * | Type | `Edm.Boolean` |
     */
    isFlags: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `members` |
     * | Type | `Collection(Edm.String)` |
     */
    members: Array<string>;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `BuiltInModel.Plt0Schema` |
     * | Nullable | `false` |
     */
    schema?: Plt0Schema;
}
export interface EditablePlt0EnumType extends Pick<Plt0EnumType, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "enumTypeCode" | "name" | "description">, Partial<Pick<Plt0EnumType, "lifecycleNote" | "underlyingType" | "isFlags" | "members">> {
}
export interface Plt0TypeDefinition extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `typeDefinitionCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    typeDefinitionCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `underlyingType` |
     * | Type | `Edm.String` |
     */
    underlyingType: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `maxLength` |
     * | Type | `Edm.Int32` |
     */
    maxLength: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isUnicode` |
     * | Type | `Edm.Boolean` |
     */
    isUnicode: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `srid` |
     * | Type | `Edm.String` |
     */
    srid: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `precision` |
     * | Type | `Edm.Int32` |
     */
    precision: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `scale` |
     * | Type | `Edm.Int32` |
     */
    scale: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `BuiltInModel.Plt0Schema` |
     * | Nullable | `false` |
     */
    schema?: Plt0Schema;
}
export interface EditablePlt0TypeDefinition extends Pick<Plt0TypeDefinition, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "typeDefinitionCode" | "name" | "description">, Partial<Pick<Plt0TypeDefinition, "lifecycleNote" | "underlyingType" | "maxLength" | "isUnicode" | "srid" | "precision" | "scale">> {
}
export interface Plt0StructuralProperty extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `displayName` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    displayName: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `type` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    type: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isCollection` |
     * | Type | `Edm.Boolean` |
     */
    isCollection: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isNullable` |
     * | Type | `Edm.Boolean` |
     */
    isNullable: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `maxLength` |
     * | Type | `Edm.Int16` |
     */
    maxLength: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isUnicode` |
     * | Type | `Edm.Boolean` |
     */
    isUnicode: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `srid` |
     * | Type | `Edm.String` |
     */
    srid: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `precision` |
     * | Type | `Edm.Int16` |
     */
    precision: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `scale` |
     * | Type | `Edm.Int16` |
     */
    scale: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `defaultValue` |
     * | Type | `Edm.String` |
     */
    defaultValue: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isAutoGenerateCode` |
     * | Type | `Edm.Boolean` |
     */
    isAutoGenerateCode: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isReadOnly` |
     * | Type | `Edm.Boolean` |
     */
    isReadOnly: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isUniq` |
     * | Type | `Edm.Boolean` |
     */
    isUniq: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ownerType` |
     * | Type | `BuiltInModel.Plt0BaseType` |
     * | Nullable | `false` |
     */
    ownerType?: Plt0BaseType;
}
export interface EditablePlt0StructuralProperty extends Pick<Plt0StructuralProperty, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "displayName" | "description" | "type">, Partial<Pick<Plt0StructuralProperty, "lifecycleNote" | "isCollection" | "isNullable" | "maxLength" | "isUnicode" | "srid" | "precision" | "scale" | "defaultValue" | "isAutoGenerateCode" | "isReadOnly" | "isUniq">> {
}
export interface Plt0NavigationProperty extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `displayName` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    displayName: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isCollection` |
     * | Type | `Edm.Boolean` |
     */
    isCollection: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isNullable` |
     * | Type | `Edm.Boolean` |
     */
    isNullable: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isContainsTarget` |
     * | Type | `Edm.Boolean` |
     */
    isContainsTarget: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `referentialConstraint` |
     * | Type | `Edm.String` |
     */
    referentialConstraint: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `onDelete` |
     * | Type | `BuiltInModel.OnDelete` |
     */
    onDelete: OnDelete | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isReadOnly` |
     * | Type | `Edm.Boolean` |
     */
    isReadOnly: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isUniq` |
     * | Type | `Edm.Boolean` |
     */
    isUniq: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ownerType` |
     * | Type | `BuiltInModel.Plt0BaseType` |
     * | Nullable | `false` |
     */
    ownerType?: Plt0BaseType;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `type` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    type?: Plt0EntityType;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `partner` |
     * | Type | `BuiltInModel.Plt0NavigationProperty` |
     */
    partner?: Plt0NavigationProperty | null;
}
export interface EditablePlt0NavigationProperty extends Pick<Plt0NavigationProperty, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "displayName" | "description">, Partial<Pick<Plt0NavigationProperty, "lifecycleNote" | "isCollection" | "isNullable" | "isContainsTarget" | "referentialConstraint" | "onDelete" | "isReadOnly" | "isUniq">> {
}
export interface Plt0Action extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `Edm.String` |
     */
    schema: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isBound` |
     * | Type | `Edm.Boolean` |
     */
    isBound: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `parameter` |
     * | Type | `Edm.String` |
     */
    parameter: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `returnType` |
     * | Type | `Edm.String` |
     */
    returnType: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entitySetPath` |
     * | Type | `Edm.String` |
     */
    entitySetPath: string | null;
}
export interface EditablePlt0Action extends Pick<Plt0Action, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0Action, "lifecycleNote" | "schema" | "isBound" | "parameter" | "returnType" | "entitySetPath">> {
}
export interface Plt0Function extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `Edm.String` |
     */
    schema: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isBound` |
     * | Type | `Edm.Boolean` |
     */
    isBound: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isComposable` |
     * | Type | `Edm.Boolean` |
     */
    isComposable: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `parameter` |
     * | Type | `Edm.String` |
     */
    parameter: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `returnType` |
     * | Type | `Edm.String` |
     */
    returnType: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entitySetPath` |
     * | Type | `Edm.String` |
     */
    entitySetPath: string | null;
}
export interface EditablePlt0Function extends Pick<Plt0Function, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0Function, "lifecycleNote" | "schema" | "isBound" | "isComposable" | "parameter" | "returnType" | "entitySetPath">> {
}
export interface Plt0EntityContainer extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `extend` |
     * | Type | `Edm.String` |
     */
    extend: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `schema` |
     * | Type | `BuiltInModel.Plt0Schema` |
     * | Nullable | `false` |
     */
    schema?: Plt0Schema;
}
export interface EditablePlt0EntityContainer extends Pick<Plt0EntityContainer, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name">, Partial<Pick<Plt0EntityContainer, "lifecycleNote" | "extend">> {
}
export interface Plt0EntitySet extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isIncludeInServiceDocument` |
     * | Type | `Edm.Boolean` |
     */
    isIncludeInServiceDocument: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityContainer` |
     * | Type | `BuiltInModel.Plt0EntityContainer` |
     * | Nullable | `false` |
     */
    entityContainer?: Plt0EntityContainer;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    entityType?: Plt0EntityType;
}
export interface EditablePlt0EntitySet extends Pick<Plt0EntitySet, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0EntitySet, "lifecycleNote" | "isIncludeInServiceDocument">> {
}
export interface Plt0Singleton extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityContainer` |
     * | Type | `BuiltInModel.Plt0EntityContainer` |
     * | Nullable | `false` |
     */
    entityContainer?: Plt0EntityContainer;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityType` |
     * | Type | `BuiltInModel.Plt0EntityType` |
     * | Nullable | `false` |
     */
    entityType?: Plt0EntityType;
}
export interface EditablePlt0Singleton extends Pick<Plt0Singleton, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0Singleton, "lifecycleNote">> {
}
export interface Plt0NavigationPropertyBinding extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entitySet` |
     * | Type | `BuiltInModel.Plt0EntitySet` |
     * | Nullable | `false` |
     */
    entitySet?: Plt0EntitySet;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `path` |
     * | Type | `BuiltInModel.Plt0NavigationProperty` |
     * | Nullable | `false` |
     */
    path?: Plt0NavigationProperty;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `target` |
     * | Type | `BuiltInModel.Plt0EntitySet` |
     * | Nullable | `false` |
     */
    target?: Plt0EntitySet;
}
export interface EditablePlt0NavigationPropertyBinding extends Pick<Plt0NavigationPropertyBinding, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0NavigationPropertyBinding, "lifecycleNote">> {
}
export interface Plt0ActionImport extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityContainer` |
     * | Type | `BuiltInModel.Plt0EntityContainer` |
     * | Nullable | `false` |
     */
    entityContainer?: Plt0EntityContainer;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `action` |
     * | Type | `BuiltInModel.Plt0Action` |
     * | Nullable | `false` |
     */
    action?: Plt0Action;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entitySet` |
     * | Type | `BuiltInModel.Plt0EntitySet` |
     */
    entitySet?: Plt0EntitySet | null;
}
export interface EditablePlt0ActionImport extends Pick<Plt0ActionImport, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0ActionImport, "lifecycleNote">> {
}
export interface Plt0FunctionImport extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    description: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isIncludeInServiceDocument` |
     * | Type | `Edm.Boolean` |
     */
    isIncludeInServiceDocument: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entityContainer` |
     * | Type | `BuiltInModel.Plt0EntityContainer` |
     * | Nullable | `false` |
     */
    entityContainer?: Plt0EntityContainer;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `function` |
     * | Type | `BuiltInModel.Plt0Function` |
     * | Nullable | `false` |
     */
    function?: Plt0Function;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `entitySet` |
     * | Type | `BuiltInModel.Plt0EntitySet` |
     */
    entitySet?: Plt0EntitySet | null;
}
export interface EditablePlt0FunctionImport extends Pick<Plt0FunctionImport, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "description">, Partial<Pick<Plt0FunctionImport, "lifecycleNote" | "isIncludeInServiceDocument">> {
}
export interface Plt0BusinessObjectRevisionNumberRule extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ruleCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    ruleCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isActive` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isActive: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isEmployed` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isEmployed: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `isLocked` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    isLocked: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     */
    description: string | null;
}
export interface EditablePlt0BusinessObjectRevisionNumberRule extends Pick<Plt0BusinessObjectRevisionNumberRule, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "ruleCode" | "name" | "isActive" | "isEmployed" | "isLocked">, Partial<Pick<Plt0BusinessObjectRevisionNumberRule, "lifecycleNote" | "description">> {
}
export interface Plt0BusinessObjectRevisionNumberCode extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revisionOrder` |
     * | Type | `Edm.Int32` |
     * | Nullable | `false` |
     */
    revisionOrder: number;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `revisionCode` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    revisionCode: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `ruleCodeRef` |
     * | Type | `BuiltInModel.Plt0BusinessObjectRevisionNumberRule` |
     * | Nullable | `false` |
     */
    ruleCodeRef?: Plt0BusinessObjectRevisionNumberRule;
}
export interface EditablePlt0BusinessObjectRevisionNumberCode extends Pick<Plt0BusinessObjectRevisionNumberCode, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "revisionOrder" | "revisionCode">, Partial<Pick<Plt0BusinessObjectRevisionNumberCode, "lifecycleNote">> {
}
export interface CadFile extends Plt0MrvObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `submitDescription` |
     * | Type | `Edm.String` |
     */
    submitDescription: string | null;
}
export interface EditableCadFile extends Pick<CadFile, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code">, Partial<Pick<CadFile, "lifecycleNote" | "name" | "description" | "submitDescription">> {
}
export interface CadPart extends CadFile {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `partType` |
     * | Type | `BuiltInModel.PartType` |
     * | Nullable | `false` |
     */
    partType: PartType;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `volume` |
     * | Type | `Edm.Decimal` |
     */
    volume: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `mass` |
     * | Type | `Edm.Decimal` |
     */
    mass: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `material` |
     * | Type | `Edm.String` |
     */
    material: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `openSurfaceArea` |
     * | Type | `Edm.Decimal` |
     */
    openSurfaceArea: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `solidSurfaceArea` |
     * | Type | `Edm.Decimal` |
     */
    solidSurfaceArea: number | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `gravityCenter` |
     * | Type | `Edm.String` |
     */
    gravityCenter: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `thumbnail` |
     * | Type | `neue.Plt0File` |
     */
    thumbnail?: Plt0File | null;
}
export interface EditableCadPart extends Pick<CadPart, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code" | "partType">, Partial<Pick<CadPart, "lifecycleNote" | "name" | "description" | "submitDescription" | "volume" | "mass" | "material" | "openSurfaceArea" | "solidSurfaceArea" | "gravityCenter">> {
}
export interface NeueCadPart extends CadPart {
}
export interface EditableNeueCadPart extends Pick<NeueCadPart, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code" | "partType">, Partial<Pick<NeueCadPart, "lifecycleNote" | "name" | "description" | "submitDescription" | "volume" | "mass" | "material" | "openSurfaceArea" | "solidSurfaceArea" | "gravityCenter">> {
}
export interface NeueCadAsm extends CadPart {
}
export interface EditableNeueCadAsm extends Pick<NeueCadAsm, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code" | "partType">, Partial<Pick<NeueCadAsm, "lifecycleNote" | "name" | "description" | "submitDescription" | "volume" | "mass" | "material" | "openSurfaceArea" | "solidSurfaceArea" | "gravityCenter">> {
}
export interface TwoDimensionDrawing extends CadFile {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `thumbnail` |
     * | Type | `neue.Plt0File` |
     */
    thumbnail?: Plt0File | null;
}
export interface EditableTwoDimensionDrawing extends Pick<TwoDimensionDrawing, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code">, Partial<Pick<TwoDimensionDrawing, "lifecycleNote" | "name" | "description" | "submitDescription">> {
}
export interface NeueTwoDimensionDrawing extends TwoDimensionDrawing {
}
export interface EditableNeueTwoDimensionDrawing extends Pick<NeueTwoDimensionDrawing, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "code">, Partial<Pick<NeueTwoDimensionDrawing, "lifecycleNote" | "name" | "description" | "submitDescription">> {
}
export interface Plt0File2 extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `description` |
     * | Type | `Edm.String` |
     */
    description: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `softwareRev` |
     * | Type | `Edm.String` |
     */
    softwareRev: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     */
    name: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `storageItem` |
     * | Type | `PaaS.Plt0ApplicationObject` |
     */
    storageItem?: Plt0ApplicationObject | null;
}
export interface EditablePlt0File2 extends Pick<Plt0File2, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0File2, "lifecycleNote" | "description" | "softwareRev" | "name">> {
}
export interface Plt0IdiFile2 extends Plt0File {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `convertState` |
     * | Type | `BuiltInModel.IdiConvertState` |
     */
    convertState: IdiConvertState | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `triggerTime` |
     * | Type | `Edm.DateTimeOffset` |
     */
    triggerTime: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `idiLightModelId` |
     * | Type | `Edm.String` |
     */
    idiLightModelId: string | null;
}
export interface EditablePlt0IdiFile2 extends Pick<Plt0IdiFile2, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Plt0IdiFile2, "lifecycleNote" | "description" | "softwareRev" | "name" | "convertState" | "triggerTime" | "idiLightModelId">> {
}
export interface RelCadBom extends Plt0RootRelationType {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `transformationMatrix` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    transformationMatrix: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `configuration` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    configuration: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `quantity` |
     * | Type | `Edm.Int32` |
     * | Nullable | `false` |
     */
    quantity: number;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `suppressed` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    suppressed: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `bomExcluded` |
     * | Type | `Edm.Boolean` |
     * | Nullable | `false` |
     */
    bomExcluded: boolean;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `instanceName` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    instanceName: string;
}
export interface EditableRelCadBom extends Pick<RelCadBom, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "transformationMatrix" | "configuration" | "quantity" | "suppressed" | "bomExcluded" | "instanceName">, Partial<Pick<RelCadBom, "lifecycleNote">> {
}
export interface RelCadPartTwoDimensionDrawing extends Plt0RootRelationType {
}
export interface EditableRelCadPartTwoDimensionDrawing extends Pick<RelCadPartTwoDimensionDrawing, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<RelCadPartTwoDimensionDrawing, "lifecycleNote">> {
}
export interface RelCadFileFile extends Plt0RootRelationType {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `linkType` |
     * | Type | `neue.RelCadFileFileLinkTypeEnum` |
     * | Nullable | `false` |
     */
    linkType: RelCadFileFileLinkTypeEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `usageType` |
     * | Type | `neue.RelCadFileFileUsageTypeEnum` |
     * | Nullable | `false` |
     */
    usageType: RelCadFileFileUsageTypeEnum;
}
export interface EditableRelCadFileFile extends Pick<RelCadFileFile, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "linkType" | "usageType">, Partial<Pick<RelCadFileFile, "lifecycleNote">> {
}
export interface MappingConfig extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `tool` |
     * | Type | `neue.MappingConfigToolEnum` |
     * | Nullable | `false` |
     */
    tool: MappingConfigToolEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `caxProperty` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    caxProperty: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `drawingSheetArea` |
     * | Type | `Edm.String` |
     */
    drawingSheetArea: string | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `partType` |
     * | Type | `neue.MappingConfigPartTypeEnum` |
     * | Nullable | `false` |
     */
    partType: MappingConfigPartTypeEnum;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `cdpProperty` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    cdpProperty: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `onCax` |
     * | Type | `Edm.Boolean` |
     */
    onCax: boolean | null;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `direction` |
     * | Type | `neue.MappingConfigDirectionEnum` |
     * | Nullable | `false` |
     */
    direction: MappingConfigDirectionEnum;
}
export interface EditableMappingConfig extends Pick<MappingConfig, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "tool" | "caxProperty" | "name" | "partType" | "cdpProperty" | "direction">, Partial<Pick<MappingConfig, "lifecycleNote" | "drawingSheetArea" | "onCax">> {
}
export interface test_product extends Plt0ApplicationObject {
}
export interface Editabletest_product extends Pick<test_product, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<test_product, "lifecycleNote">> {
}
export interface test_product001 extends Plt0ApplicationObject {
}
export interface Editabletest_product001 extends Pick<test_product001, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<test_product001, "lifecycleNote">> {
}
export interface Test_ProductModel extends Plt0ApplicationObject {
}
export interface EditableTest_ProductModel extends Pick<Test_ProductModel, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Test_ProductModel, "lifecycleNote">> {
}
export interface Test_ProductModel111 extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `Productname` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    Productname: string;
}
export interface EditableTest_ProductModel111 extends Pick<Test_ProductModel111, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "Productname">, Partial<Pick<Test_ProductModel111, "lifecycleNote">> {
}
export interface Test_ProductModel222 extends Plt0ApplicationObject {
}
export interface EditableTest_ProductModel222 extends Pick<Test_ProductModel222, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Test_ProductModel222, "lifecycleNote">> {
}
export interface Test_ProductModel333 extends Plt0ApplicationObject {
}
export interface EditableTest_ProductModel333 extends Pick<Test_ProductModel333, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState">, Partial<Pick<Test_ProductModel333, "lifecycleNote">> {
}
export interface Test_ProductModel444 extends Plt0ApplicationObject {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `name` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    name: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `prince` |
     * | Type | `Edm.Int32` |
     * | Nullable | `false` |
     */
    prince: number;
}
export interface EditableTest_ProductModel444 extends Pick<Test_ProductModel444, "createdAt" | "modifiedAt" | "schemaVersion" | "lifecycleState" | "name" | "prince">, Partial<Pick<Test_ProductModel444, "lifecycleNote">> {
}
export interface FileSignatureUrl {
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `storageItemId` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    storageItemId: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `fileId` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    fileId: string;
    /**
     *
     * OData Attributes:
     * |Attribute Name | Attribute Value |
     * | --- | ---|
     * | Name | `signatureUrl` |
     * | Type | `Edm.String` |
     * | Nullable | `false` |
     */
    signatureUrl: string;
}
export interface EditableFileSignatureUrl extends Pick<FileSignatureUrl, "storageItemId" | "fileId" | "signatureUrl"> {
}
export interface GetRelationTreeByNcidParams {
    ncid: string;
    typeNcid: string;
    isTree: boolean;
}
export interface DeployParams {
    entityTypeIds: Array<string>;
}
export interface BatchGetFileSignatureUrlParams {
    fileIds: Array<string>;
    actionType: FileSignatureUrlActionType;
}
