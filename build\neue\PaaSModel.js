"use strict";
// @ts-nocheck
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelCadFileFileLinkTypeEnum = exports.MappingConfigDirectionEnum = exports.MappingConfigPartTypeEnum = exports.MappingConfigToolEnum = exports.RelCadFileFileUsageTypeEnum = exports.RelationSourceTypeEnum2 = exports.FileSignatureUrlActionType = exports.PartType = exports.IdiConvertState = exports.RelationVersionUpgradeActionEnum = exports.RelationClassify = exports.RelationStatus = exports.RelationConstrictEnum = exports.RelationSourceTypeEnum = exports.MrvStrategyType = exports.OnDelete = exports.LifecycleState = void 0;
var LifecycleState;
(function (LifecycleState) {
    LifecycleState["CREATING"] = "CREATING";
    LifecycleState["ACTIVE"] = "ACTIVE";
    LifecycleState["INACTIVE"] = "INACTIVE";
    LifecycleState["NEEDS_ATTENTION"] = "NEEDS_ATTENTION";
    LifecycleState["UPDATING"] = "UPDATING";
    LifecycleState["DELETING"] = "DELETING";
    LifecycleState["DELETED"] = "DELETED";
})(LifecycleState = exports.LifecycleState || (exports.LifecycleState = {}));
var OnDelete;
(function (OnDelete) {
    OnDelete["CASCADE"] = "CASCADE";
    OnDelete["NONE"] = "NONE";
    OnDelete["SET_NULL"] = "SET_NULL";
    OnDelete["SET_DEFAULT"] = "SET_DEFAULT";
})(OnDelete = exports.OnDelete || (exports.OnDelete = {}));
var MrvStrategyType;
(function (MrvStrategyType) {
    MrvStrategyType["NONE"] = "NONE";
    MrvStrategyType["REVISION"] = "REVISION";
    MrvStrategyType["REVISION_VERSION"] = "REVISION_VERSION";
})(MrvStrategyType = exports.MrvStrategyType || (exports.MrvStrategyType = {}));
var RelationSourceTypeEnum;
(function (RelationSourceTypeEnum) {
    RelationSourceTypeEnum["FROM"] = "FROM";
    RelationSourceTypeEnum["TO"] = "TO";
})(RelationSourceTypeEnum = exports.RelationSourceTypeEnum || (exports.RelationSourceTypeEnum = {}));
var RelationConstrictEnum;
(function (RelationConstrictEnum) {
    RelationConstrictEnum["MANY"] = "MANY";
    RelationConstrictEnum["ONE"] = "ONE";
})(RelationConstrictEnum = exports.RelationConstrictEnum || (exports.RelationConstrictEnum = {}));
var RelationStatus;
(function (RelationStatus) {
    RelationStatus["ACTIVE"] = "ACTIVE";
    RelationStatus["INACTIVE"] = "INACTIVE";
})(RelationStatus = exports.RelationStatus || (exports.RelationStatus = {}));
var RelationClassify;
(function (RelationClassify) {
    RelationClassify["ASSOCIATION"] = "ASSOCIATION";
    RelationClassify["AGGREGATION"] = "AGGREGATION";
    RelationClassify["COMPOSITION"] = "COMPOSITION";
})(RelationClassify = exports.RelationClassify || (exports.RelationClassify = {}));
var RelationVersionUpgradeActionEnum;
(function (RelationVersionUpgradeActionEnum) {
    RelationVersionUpgradeActionEnum["NONE"] = "NONE";
    RelationVersionUpgradeActionEnum["FLOAT"] = "FLOAT";
    RelationVersionUpgradeActionEnum["CLONE"] = "CLONE";
})(RelationVersionUpgradeActionEnum = exports.RelationVersionUpgradeActionEnum || (exports.RelationVersionUpgradeActionEnum = {}));
var IdiConvertState;
(function (IdiConvertState) {
    IdiConvertState["INIT"] = "INIT";
    IdiConvertState["RUNNING"] = "RUNNING";
    IdiConvertState["FAILURE"] = "FAILURE";
    IdiConvertState["SUCCESS"] = "SUCCESS";
})(IdiConvertState = exports.IdiConvertState || (exports.IdiConvertState = {}));
var PartType;
(function (PartType) {
    PartType["NEUE_PRT"] = "NEUE_PRT";
    PartType["NEUE_ASM"] = "NEUE_ASM";
})(PartType = exports.PartType || (exports.PartType = {}));
var FileSignatureUrlActionType;
(function (FileSignatureUrlActionType) {
    FileSignatureUrlActionType["UPLOAD"] = "UPLOAD";
    FileSignatureUrlActionType["DOWNLOAD"] = "DOWNLOAD";
})(FileSignatureUrlActionType = exports.FileSignatureUrlActionType || (exports.FileSignatureUrlActionType = {}));
var RelationSourceTypeEnum2;
(function (RelationSourceTypeEnum2) {
    RelationSourceTypeEnum2["FROM"] = "FROM";
    RelationSourceTypeEnum2["TO"] = "TO";
})(RelationSourceTypeEnum2 = exports.RelationSourceTypeEnum2 || (exports.RelationSourceTypeEnum2 = {}));
var RelCadFileFileUsageTypeEnum;
(function (RelCadFileFileUsageTypeEnum) {
    RelCadFileFileUsageTypeEnum["DESIGN_MODEL"] = "DESIGN_MODEL";
    RelCadFileFileUsageTypeEnum["EXCHANGE_FORMAT"] = "EXCHANGE_FORMAT";
    RelCadFileFileUsageTypeEnum["THUMBNAIL"] = "THUMBNAIL";
    RelCadFileFileUsageTypeEnum["PDF"] = "PDF";
    RelCadFileFileUsageTypeEnum["SIMULATION"] = "SIMULATION";
    RelCadFileFileUsageTypeEnum["TWO_DIMENSION_DRAWING"] = "TWO_DIMENSION_DRAWING";
})(RelCadFileFileUsageTypeEnum = exports.RelCadFileFileUsageTypeEnum || (exports.RelCadFileFileUsageTypeEnum = {}));
var MappingConfigToolEnum;
(function (MappingConfigToolEnum) {
    MappingConfigToolEnum["CAX"] = "CAX";
    MappingConfigToolEnum["NX"] = "NX";
    MappingConfigToolEnum["SOLIDWORKS"] = "SOLIDWORKS";
    MappingConfigToolEnum["CATIA"] = "CATIA";
})(MappingConfigToolEnum = exports.MappingConfigToolEnum || (exports.MappingConfigToolEnum = {}));
var MappingConfigPartTypeEnum;
(function (MappingConfigPartTypeEnum) {
    MappingConfigPartTypeEnum["PART"] = "PART";
    MappingConfigPartTypeEnum["DRAWING"] = "DRAWING";
    MappingConfigPartTypeEnum["FE"] = "FE";
    MappingConfigPartTypeEnum["CADBOM"] = "CADBOM";
})(MappingConfigPartTypeEnum = exports.MappingConfigPartTypeEnum || (exports.MappingConfigPartTypeEnum = {}));
var MappingConfigDirectionEnum;
(function (MappingConfigDirectionEnum) {
    MappingConfigDirectionEnum["CAX_TO_CDP"] = "CAX_TO_CDP";
    MappingConfigDirectionEnum["CDP_TO_CAX"] = "CDP_TO_CAX";
    MappingConfigDirectionEnum["BIDIRECTIONAL"] = "BIDIRECTIONAL";
})(MappingConfigDirectionEnum = exports.MappingConfigDirectionEnum || (exports.MappingConfigDirectionEnum = {}));
var RelCadFileFileLinkTypeEnum;
(function (RelCadFileFileLinkTypeEnum) {
    RelCadFileFileLinkTypeEnum["MASTER"] = "MASTER";
    RelCadFileFileLinkTypeEnum["REFERENCE"] = "REFERENCE";
})(RelCadFileFileLinkTypeEnum = exports.RelCadFileFileLinkTypeEnum || (exports.RelCadFileFileLinkTypeEnum = {}));
