/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { StructuralProperties } from './StructuralProperties';

/**
 * Request builder class for operations supported on the {@link StructuralProperties} entity.
 */
export class StructuralPropertiesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<StructuralProperties<T>, T> {
  /**
   * Returns a request builder for querying all `StructuralProperties` entities.
   * @returns A request builder for creating requests to retrieve all `StructuralProperties` entities.
   */
  getAll(): GetAllRequestBuilder<StructuralProperties<T>, T> {
    return new GetAllRequestBuilder<StructuralProperties<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `StructuralProperties` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `StructuralProperties`.
   */
  create(
    entity: StructuralProperties<T>
  ): CreateRequestBuilder<StructuralProperties<T>, T> {
    return new CreateRequestBuilder<StructuralProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `StructuralProperties` entity based on its keys.
   * @param ncid Key property. See {@link StructuralProperties.ncid}.
   * @returns A request builder for creating requests to retrieve one `StructuralProperties` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<StructuralProperties<T>, T> {
    return new GetByKeyRequestBuilder<StructuralProperties<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `StructuralProperties`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `StructuralProperties`.
   */
  update(
    entity: StructuralProperties<T>
  ): UpdateRequestBuilder<StructuralProperties<T>, T> {
    return new UpdateRequestBuilder<StructuralProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `StructuralProperties`.
   * @param ncid Key property. See {@link StructuralProperties.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `StructuralProperties`.
   */
  delete(ncid: string): DeleteRequestBuilder<StructuralProperties<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `StructuralProperties`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `StructuralProperties` by taking the entity as a parameter.
   */
  delete(
    entity: StructuralProperties<T>
  ): DeleteRequestBuilder<StructuralProperties<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<StructuralProperties<T>, T> {
    return new DeleteRequestBuilder<StructuralProperties<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof StructuralProperties
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
