/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  AllFields,
  CustomField,
  DeSerializers,
  DefaultDeSerializers,
  EntityApi,
  EntityBuilderType,
  FieldBuilder,
  OrderableEdmTypeField,
  defaultDeSerializers,
  entityBuilder,
} from '@sap-cloud-sdk/odata-v4'
import { Airlines } from './Airlines'
import { AirlinesRequestBuilder } from './AirlinesRequestBuilder'

export class AirlinesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<Airlines<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): AirlinesApi<DeSerializersT> {
    return new AirlinesApi(deSerializers)
  }

  private navigationPropertyFields!: {}

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {}
    return this
  }

  entityConstructor = Airlines

  requestBuilder(): AirlinesRequestBuilder<DeSerializersT> {
    return new AirlinesRequestBuilder<DeSerializersT>(this)
  }

  entityBuilder(): EntityBuilderType<Airlines<DeSerializersT>, DeSerializersT> {
    return entityBuilder<Airlines<DeSerializersT>, DeSerializersT>(this)
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<Airlines<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any
  }

  private _fieldBuilder?: FieldBuilder<typeof Airlines, DeSerializersT>
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(Airlines, this.deSerializers)
    }
    return this._fieldBuilder
  }

  private _schema?: {
    AIRLINE_CODE: OrderableEdmTypeField<
      Airlines<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >
    NAME: OrderableEdmTypeField<
      Airlines<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >
    ALL_FIELDS: AllFields<Airlines<DeSerializers>>
  }

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder
      this._schema = {
        /**
         * Static representation of the {@link airlineCode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        AIRLINE_CODE: fieldBuilder.buildEdmTypeField(
          'AirlineCode',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('Name', 'Edm.String', true),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', Airlines),
      }
    }

    return this._schema
  }
}
