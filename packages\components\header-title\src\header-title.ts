import { buildProps, definePropType } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const neHeaderTitleProps = buildProps({
  /**
   * @description 标题文本
   */
  title: {
    type: String,
    default: '',
  },
  /**
   * @description 副标题文本
   */
  subtitle: {
    type: String,
    default: '',
  },
  /**
   * @description 标题级别 (h1-h6)
   */
  level: {
    type: Number,
    values: [1, 2, 3, 4, 5, 6],
    default: 2,
  },
  /**
   * @description 是否显示分割线
   */
  divider: {
    type: Boolean,
    default: false,
  },
  /**
   * @description 分割线位置
   */
  dividerPosition: {
    type: String,
    values: ['top', 'bottom', 'both'],
    default: 'bottom',
  },
  /**
   * @description 标题对齐方式
   */
  align: {
    type: String,
    values: ['left', 'center', 'right'],
    default: 'left',
  },
  /**
   * @description 是否显示返回按钮
   */
  showBack: {
    type: Boolean,
    default: false,
  },
  /**
   * @description 返回按钮文本
   */
  backText: {
    type: String,
    default: '返回',
  },
  /**
   * @description 标题图标
   */
  icon: {
    type: definePropType<string | object>([String, Object]),
    default: '',
  },
  /**
   * @description 标题大小
   */
  size: {
    type: String,
    values: ['small', 'default', 'large'],
    default: 'default',
  },
  /**
   * @description 是否加粗
   */
  bold: {
    type: Boolean,
    default: true,
  },
} as const)

export type NeHeaderTitleProps = ExtractPropTypes<typeof neHeaderTitleProps>

// 定义事件类型
export const neHeaderTitleEmits = {
  back: () => true,
  click: (evt: MouseEvent) => evt instanceof MouseEvent,
}

export type NeHeaderTitleEmits = typeof neHeaderTitleEmits
