/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { FunctionImports } from './FunctionImports';

/**
 * Request builder class for operations supported on the {@link FunctionImports} entity.
 */
export class FunctionImportsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<FunctionImports<T>, T> {
  /**
   * Returns a request builder for querying all `FunctionImports` entities.
   * @returns A request builder for creating requests to retrieve all `FunctionImports` entities.
   */
  getAll(): GetAllRequestBuilder<FunctionImports<T>, T> {
    return new GetAllRequestBuilder<FunctionImports<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `FunctionImports` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `FunctionImports`.
   */
  create(
    entity: FunctionImports<T>
  ): CreateRequestBuilder<FunctionImports<T>, T> {
    return new CreateRequestBuilder<FunctionImports<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `FunctionImports` entity based on its keys.
   * @param ncid Key property. See {@link FunctionImports.ncid}.
   * @returns A request builder for creating requests to retrieve one `FunctionImports` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<FunctionImports<T>, T> {
    return new GetByKeyRequestBuilder<FunctionImports<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `FunctionImports`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `FunctionImports`.
   */
  update(
    entity: FunctionImports<T>
  ): UpdateRequestBuilder<FunctionImports<T>, T> {
    return new UpdateRequestBuilder<FunctionImports<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `FunctionImports`.
   * @param ncid Key property. See {@link FunctionImports.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `FunctionImports`.
   */
  delete(ncid: string): DeleteRequestBuilder<FunctionImports<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `FunctionImports`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `FunctionImports` by taking the entity as a parameter.
   */
  delete(
    entity: FunctionImports<T>
  ): DeleteRequestBuilder<FunctionImports<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<FunctionImports<T>, T> {
    return new DeleteRequestBuilder<FunctionImports<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof FunctionImports
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
