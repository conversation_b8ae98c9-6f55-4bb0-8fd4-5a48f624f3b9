/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
/**
 * This enum represents the enum type "{@link LifecycleState}".
 * The members represent values of EDM type Edm.Int32.
 */
export enum LifecycleState {
  /**
   * Original value: 0
   */
  CREATING = 'CREATING',
  /**
   * Original value: 1
   */
  ACTIVE = 'ACTIVE',
  /**
   * Original value: 2
   */
  INACTIVE = 'INACTIVE',
  /**
   * Original value: 3
   */
  NEEDS_ATTENTION = 'NEEDS_ATTENTION',
  /**
   * Original value: 4
   */
  UPDATING = 'UPDATING',
  /**
   * Original value: 5
   */
  DELETING = 'DELETING',
  /**
   * Original value: 6
   */
  DELETED = 'DELETED'
}
