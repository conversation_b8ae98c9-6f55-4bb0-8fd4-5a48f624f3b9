/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { SchemasApi } from './SchemasApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "Schemas" of service "neue".
 */
export class Schemas<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements SchemasType<T>
{
  /**
   * Technical entity name for Schemas.
   */
  static override _entityName = 'Schemas';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the Schemas entity.
   */
  static _keys = ['ncid'];
  /**
   * Namespace.
   */
  declare namespace: DeserializedType<T, 'Edm.String'>;
  /**
   * Alias.
   */
  declare alias: DeserializedType<T, 'Edm.String'>;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: SchemasApi<T>) {
    super(_entityApi);
  }
}

export interface SchemasType<T extends DeSerializers = DefaultDeSerializers> {
  namespace: DeserializedType<T, 'Edm.String'>;
  alias: DeserializedType<T, 'Edm.String'>;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
