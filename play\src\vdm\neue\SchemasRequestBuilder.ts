/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { Schemas } from './Schemas';

/**
 * Request builder class for operations supported on the {@link Schemas} entity.
 */
export class SchemasRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<Schemas<T>, T> {
  /**
   * Returns a request builder for querying all `Schemas` entities.
   * @returns A request builder for creating requests to retrieve all `Schemas` entities.
   */
  getAll(): GetAllRequestBuilder<Schemas<T>, T> {
    return new GetAllRequestBuilder<Schemas<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `Schemas` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `Schemas`.
   */
  create(entity: Schemas<T>): CreateRequestBuilder<Schemas<T>, T> {
    return new CreateRequestBuilder<Schemas<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `Schemas` entity based on its keys.
   * @param ncid Key property. See {@link Schemas.ncid}.
   * @returns A request builder for creating requests to retrieve one `Schemas` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<Schemas<T>, T> {
    return new GetByKeyRequestBuilder<Schemas<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `Schemas`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `Schemas`.
   */
  update(entity: Schemas<T>): UpdateRequestBuilder<Schemas<T>, T> {
    return new UpdateRequestBuilder<Schemas<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `Schemas`.
   * @param ncid Key property. See {@link Schemas.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `Schemas`.
   */
  delete(ncid: string): DeleteRequestBuilder<Schemas<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `Schemas`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `Schemas` by taking the entity as a parameter.
   */
  delete(entity: Schemas<T>): DeleteRequestBuilder<Schemas<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<Schemas<T>, T> {
    return new DeleteRequestBuilder<Schemas<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof Schemas ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
