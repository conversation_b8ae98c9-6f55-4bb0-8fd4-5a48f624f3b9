/**
 * OData 客户端主类
 */

import { OlingoWrapper } from './olingo-wrapper'
import { EntityOperations as EntityOps } from './entity-operations'
import type {
  EntityOperations,
  ErrorInterceptor,
  ODataClient as IODataClient,
  ODataServiceConfig,
  RequestConfig,
  RequestInterceptor,
  Response,
  ResponseInterceptor,
} from '../types'

/**
 * OData 客户端实现
 */
export class ODataClient implements IODataClient {
  public readonly config: ODataServiceConfig
  private olingoClient: OlingoWrapper
  private entitySets: Map<string, EntityOps<any>> = new Map()
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  constructor(config: ODataServiceConfig) {
    this.config = { ...config }

    // 构建完整的服务 URL
    const baseUrl = config.baseUrl.replace(/\/$/, '')
    const servicePath = config.servicePath?.replace(/^\/|\/$/g, '') || ''
    const fullUrl = servicePath ? `${baseUrl}/${servicePath}` : baseUrl

    // 创建 Olingo 客户端
    this.olingoClient = new OlingoWrapper(fullUrl, config.headers)
  }

  /**
   * 获取实体集操作对象
   */
  entitySet<T = any>(
    name: string,
    keyProperties?: string[]
  ): EntityOperations<T> {
    if (!this.entitySets.has(name)) {
      const entityOps = new EntityOps<T>(name, this.olingoClient, keyProperties)
      this.entitySets.set(name, entityOps)
    }

    return this.entitySets.get(name)! as EntityOperations<T>
  }

  /**
   * 执行原始 HTTP 请求
   */
  async request<T = any>(config: RequestConfig): Promise<Response<T>> {
    try {
      // 应用请求拦截器
      let processedConfig = config
      for (const interceptor of this.requestInterceptors) {
        processedConfig = await interceptor(processedConfig)
      }

      // 执行请求
      let response = await this.olingoClient.request<T>(processedConfig)

      // 应用响应拦截器
      for (const interceptor of this.responseInterceptors) {
        response = await interceptor(response)
      }

      return response
    } catch (error) {
      // 应用错误拦截器
      let processedError = error
      for (const interceptor of this.errorInterceptors) {
        processedError = await interceptor(processedError)
      }

      throw processedError
    }
  }

  /**
   * 获取服务元数据
   */
  async getMetadata(): Promise<any> {
    return this.olingoClient.getMetadata()
  }

  /**
   * 批量请求（简单实现）
   */
  async batch(requests: RequestConfig[]): Promise<Response[]> {
    const results: Response[] = []

    // 简单实现：逐个执行请求
    // 实际的批量请求应该使用 OData $batch 端点
    for (const request of requests) {
      try {
        const response = await this.request(request)
        results.push(response)
      } catch (error) {
        // 将错误也包装成响应对象
        results.push({
          data: null,
          status: 500,
          statusText: 'Internal Server Error',
          headers: {},
          config: request,
          error,
        } as any)
      }
    }

    return results
  }

  /**
   * 调用函数导入
   */
  async callFunction(
    name: string,
    parameters?: Record<string, any>
  ): Promise<any> {
    let url = name

    if (parameters && Object.keys(parameters).length > 0) {
      const params = Object.entries(parameters)
        .map(([key, value]) => {
          const formattedValue =
            typeof value === 'string' ? `'${value}'` : value
          return `${key}=${formattedValue}`
        })
        .join(',')
      url = `${name}(${params})`
    }

    const response = await this.olingoClient.get(url)
    return response.data
  }

  /**
   * 调用操作导入
   */
  async callAction(
    name: string,
    parameters?: Record<string, any>
  ): Promise<any> {
    const response = await this.olingoClient.post(name, parameters)
    return response.data
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 移除请求拦截器
   */
  removeRequestInterceptor(interceptor: RequestInterceptor): void {
    const index = this.requestInterceptors.indexOf(interceptor)
    if (index > -1) {
      this.requestInterceptors.splice(index, 1)
    }
  }

  /**
   * 移除响应拦截器
   */
  removeResponseInterceptor(interceptor: ResponseInterceptor): void {
    const index = this.responseInterceptors.indexOf(interceptor)
    if (index > -1) {
      this.responseInterceptors.splice(index, 1)
    }
  }

  /**
   * 移除错误拦截器
   */
  removeErrorInterceptor(interceptor: ErrorInterceptor): void {
    const index = this.errorInterceptors.indexOf(interceptor)
    if (index > -1) {
      this.errorInterceptors.splice(index, 1)
    }
  }

  /**
   * 清除所有拦截器
   */
  clearInterceptors(): void {
    this.requestInterceptors = []
    this.responseInterceptors = []
    this.errorInterceptors = []
  }

  /**
   * 获取底层 Olingo 客户端（用于高级用法）
   */
  getOlingoClient(): OlingoWrapper {
    return this.olingoClient
  }

  /**
   * 销毁客户端
   */
  destroy(): void {
    this.entitySets.clear()
    this.clearInterceptors()
  }
}

/**
 * 创建 OData 客户端的工厂函数
 */
export function createODataClient(config: ODataServiceConfig): ODataClient {
  return new ODataClient(config)
}
