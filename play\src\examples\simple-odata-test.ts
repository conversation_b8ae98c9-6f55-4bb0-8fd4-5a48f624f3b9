/**
 * 简单的 OData 客户端测试例子
 */

import { neue } from '../vdm/neue/service'
import type { CadParts } from '../vdm/neue'
import { PartType, LifecycleState } from '../vdm/neue'

// 模拟的目标配置
const mockDestination = {
  url: 'https://mock-api.example.com',
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
}

// 初始化服务
const service = neue()

/**
 * 测试 1: 基本查询 - 获取 CAD 零件列表
 */
export async function testBasicQuery() {
  console.log('🔍 测试基本查询...')

  try {
    const { cadPartsApi } = service

    // 模拟查询（实际环境中会连接到真实的 OData 服务）
    console.log('准备查询 CAD 零件...')

    // 这里我们模拟一个成功的响应，因为没有真实的后端
    const mockResponse = [
      {
        ncid: 'CAD001',
        code: 'PART-001',
        name: '测试零件 1',
        description: '这是一个测试零件',
        partType: PartType.NEUE_PRT,
        lifecycleState: LifecycleState.ACTIVE,
      },
      {
        ncid: 'CAD002',
        code: 'PART-002',
        name: '测试零件 2',
        description: '这是另一个测试零件',
        partType: PartType.NEUE_ASM,
        lifecycleState: LifecycleState.CREATING,
      },
    ]

    console.log('✅ 查询成功！')
    console.log(`找到 ${mockResponse.length} 个零件`)

    return {
      success: true,
      data: mockResponse,
      message: '基本查询测试成功',
    }
  } catch (error) {
    console.error('❌ 查询失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      message: '基本查询测试失败',
    }
  }
}

/**
 * 测试 2: 条件查询 - 按零件类型筛选
 */
export async function testFilterQuery() {
  console.log('🔍 测试条件查询...')

  try {
    const { cadPartsApi } = service

    // 模拟按零件类型筛选的查询
    console.log('按零件类型筛选...')

    const mockFilteredResponse = [
      {
        ncid: 'CAD001',
        code: 'PART-001',
        name: '测试零件 1',
        partType: PartType.NEUE_PRT,
        lifecycleState: LifecycleState.ACTIVE,
      },
    ]

    console.log('✅ 条件查询成功！')
    console.log(`筛选后找到 ${mockFilteredResponse.length} 个零件`)

    return {
      success: true,
      data: mockFilteredResponse,
      filter: `partType eq '${PartType.NEUE_PRT}'`,
      message: '条件查询测试成功',
    }
  } catch (error) {
    console.error('❌ 条件查询失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      message: '条件查询测试失败',
    }
  }
}

/**
 * 测试 3: 服务连接测试
 */
export async function testServiceConnection() {
  console.log('🔗 测试服务连接...')

  try {
    // 检查服务是否正确初始化
    const serviceInfo = {
      hasService: !!service,
      availableApis: Object.keys(service),
      cadPartsApi: !!service.cadPartsApi,
      destination: mockDestination,
    }

    console.log('✅ 服务连接测试成功！')
    console.log('可用的 API:', serviceInfo.availableApis.slice(0, 5), '...')

    return {
      success: true,
      data: serviceInfo,
      message: '服务连接测试成功',
    }
  } catch (error) {
    console.error('❌ 服务连接测试失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      message: '服务连接测试失败',
    }
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行所有测试...')

  const results = []

  // 运行服务连接测试
  const connectionTest = await testServiceConnection()
  results.push({ test: '服务连接测试', ...connectionTest })

  // 运行基本查询测试
  const basicTest = await testBasicQuery()
  results.push({ test: '基本查询测试', ...basicTest })

  // 运行条件查询测试
  const filterTest = await testFilterQuery()
  results.push({ test: '条件查询测试', ...filterTest })

  console.log('✅ 所有测试完成！')

  return {
    success: true,
    totalTests: results.length,
    passedTests: results.filter((r) => r.success).length,
    results,
  }
}
