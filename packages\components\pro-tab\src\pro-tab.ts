import { ExtractPropTypes, PropType } from 'vue'
import { SchemaElement } from '../../material-render'

export interface TabItemProps {
  name: string
  label: string
  elements: SchemaElement[]
  disabled: boolean
  closable: boolean
  lazy: boolean
}
export type TabPaneName = string | number
export type Awaitable<T> = T | Promise<T>
export interface TabProps {
  type?: '' | 'card' | 'border-card' // 标签样式
  closable?: boolean // 标签是否可关闭
  addable?: boolean // 是否显示新增按钮
  editable?: boolean // 是否可编辑 (closable/addable)
  tabPosition?: 'top' | 'right' | 'bottom' | 'left' // 标签位置
  stretch?: boolean // 标签宽度是否自适应
  beforeLeave?: (
    activeName: TabPaneName,
    oldActiveName: TabPaneName
  ) => Awaitable<void | boolean> // 切换前回调
}
export const neProTabProps = {
  tabs: {
    type: Array as PropType<TabItemProps[]>,
    default: () => [],
  },
  activeTabName: {
    type: String,
    default: '',
  },
  tabProps: {
    type: Object as PropType<TabProps>,
    default: () => ({}),
  },
}
export type NeProTabProps = ExtractPropTypes<typeof neProTabProps>
