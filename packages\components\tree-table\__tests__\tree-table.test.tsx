import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import TreeTable from '../src/tree-table.vue'

const mockData = [
  {
    id: 1,
    name: 'Parent 1',
    children: [
      { id: 2, name: 'Child 1-1' },
      { id: 3, name: 'Child 1-2' },
    ],
  },
  {
    id: 4,
    name: 'Parent 2',
    children: [],
  },
]

const mockColumns = [
  { prop: 'name', label: '名称' },
  { prop: 'id', label: 'ID' },
]

describe('TreeTable.vue', () => {
  test('render test', () => {
    const wrapper = mount(() => (
      <TreeTable data={mockData} columns={mockColumns} />
    ))
    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('tree props', () => {
    const wrapper = mount(() => (
      <TreeTable
        data={mockData}
        columns={mockColumns}
        treeProps={{ children: 'children', hasChildren: 'hasChildren' }}
      />
    ))
    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('default expand all', () => {
    const wrapper = mount(() => (
      <TreeTable
        data={mockData}
        columns={mockColumns}
        defaultExpandAll={true}
      />
    ))
    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('loading state', () => {
    const wrapper = mount(() => (
      <TreeTable data={mockData} columns={mockColumns} loading={true} />
    ))
    expect(wrapper.find('.el-loading-mask').exists()).toBe(true)
  })

  test('row key prop', () => {
    const wrapper = mount(() => (
      <TreeTable data={mockData} columns={mockColumns} rowKey="id" />
    ))
    expect(wrapper.find('.el-table').exists()).toBe(true)
  })

  test('bordered prop', () => {
    const wrapper = mount(() => (
      <TreeTable data={mockData} columns={mockColumns} bordered={true} />
    ))
    expect(wrapper.find('.el-table').exists()).toBe(true)
  })
})
