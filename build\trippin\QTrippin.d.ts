import { QStringPath, QEnumPath, QNumberPath, QCollectionPath, QStringCollection, QEntityCollectionPath, QEntityPath, QEnumCollectionPath, QueryObject, QId, QStringParam, QFunction, QAction, QNumberParam, QGuidPath, QDateTimeOffsetPath } from "@odata2ts/odata-query-objects";
import type { PersonId, Person_GetFriendsTripsParams, Person_UpdateLastNameParams, Person_ShareTripParams, AirlineId, AirportId, TripId, PlanItemId, GetNearestAirportParams } from "./TrippinModel";
import { PersonGender, Feature } from "./TrippinModel";
export declare class QPersonBaseType extends QueryObject {
    readonly UserName: QStringPath<string>;
    readonly FirstName: QStringPath<string>;
    readonly LastName: QStringPath<string>;
    readonly MiddleName: QStringPath<string>;
    readonly Gender: QEnumPath<typeof PersonGender>;
    readonly Age: QNumberPath<number>;
    readonly Emails: QCollectionPath<QStringCollection<any>>;
    readonly AddressInfo: QEntityCollectionPath<QLocation>;
    readonly HomeAddress: QEntityPath<QLocation>;
    readonly FavoriteFeature: QEnumPath<typeof Feature>;
    readonly Features: QEnumCollectionPath<typeof Feature>;
    readonly Friends: QEntityCollectionPath<QPerson>;
    readonly BestFriend: QEntityPath<QPerson>;
    readonly Trips: QEntityCollectionPath<QTrip>;
}
export declare class QPerson extends QPersonBaseType {
    protected readonly __subtypeMapping: {
        "Trippin.Employee": string;
        "Trippin.Manager": string;
    };
    get QEmployee_Cost(): QNumberPath<number>;
    get QEmployee_Peers(): QEntityCollectionPath<QPerson>;
    get QManager_Budget(): QNumberPath<number>;
    get QManager_BossOffice(): QEntityPath<QLocation>;
    get QManager_DirectReports(): QEntityCollectionPath<QPerson>;
    private __asQEmployee;
    private __asQManager;
}
export declare const qPerson: QPerson;
export declare class QPersonId extends QId<PersonId> {
    private readonly params;
    getParams(): QStringParam<string>[];
}
export declare class Person_QGetFavoriteAirline extends QFunction {
    private readonly params;
    constructor();
    getParams(): [];
    buildUrl(notEncoded?: boolean): string;
}
export declare class Person_QGetFriendsTrips extends QFunction<Person_GetFriendsTripsParams> {
    private readonly params;
    constructor();
    getParams(): QStringParam<string>[];
}
export declare class Person_QUpdateLastName extends QAction<Person_UpdateLastNameParams> {
    private readonly params;
    constructor();
    getParams(): QStringParam<string>[];
}
export declare class Person_QShareTrip extends QAction<Person_ShareTripParams> {
    private readonly params;
    constructor();
    getParams(): (QStringParam<string> | QNumberParam<number>)[];
}
export declare class QAirline extends QueryObject {
    readonly AirlineCode: QStringPath<string>;
    readonly Name: QStringPath<string>;
}
export declare const qAirline: QAirline;
export declare class QAirlineId extends QId<AirlineId> {
    private readonly params;
    getParams(): QStringParam<string>[];
}
export declare class QAirport extends QueryObject {
    readonly Name: QStringPath<string>;
    readonly IcaoCode: QStringPath<string>;
    readonly IataCode: QStringPath<string>;
    readonly Location: QEntityPath<QAirportLocation>;
}
export declare const qAirport: QAirport;
export declare class QAirportId extends QId<AirportId> {
    private readonly params;
    getParams(): QStringParam<string>[];
}
export declare class QTrip extends QueryObject {
    readonly TripId: QNumberPath<number>;
    readonly ShareId: QGuidPath<string>;
    readonly Name: QStringPath<string>;
    readonly Budget: QNumberPath<number>;
    readonly Description: QStringPath<string>;
    readonly Tags: QCollectionPath<QStringCollection<any>>;
    readonly StartsAt: QDateTimeOffsetPath<string>;
    readonly EndsAt: QDateTimeOffsetPath<string>;
    readonly PlanItems: QEntityCollectionPath<QPlanItem>;
}
export declare const qTrip: QTrip;
export declare class QTripId extends QId<TripId> {
    private readonly params;
    getParams(): QNumberParam<number>[];
}
export declare class Trip_QGetInvolvedPeople extends QFunction {
    private readonly params;
    constructor();
    getParams(): [];
    buildUrl(notEncoded?: boolean): string;
}
export declare class QPlanItemBaseType extends QueryObject {
    readonly PlanItemId: QNumberPath<number>;
    readonly ConfirmationCode: QStringPath<string>;
    readonly StartsAt: QDateTimeOffsetPath<string>;
    readonly EndsAt: QDateTimeOffsetPath<string>;
    readonly Duration: QStringPath<string>;
}
export declare class QPlanItem extends QPlanItemBaseType {
    protected readonly __subtypeMapping: {
        "Trippin.Event": string;
        "Trippin.PublicTransportation": string;
        "Trippin.Flight": string;
    };
    get QEvent_OccursAt(): QEntityPath<QEventLocation>;
    get QEvent_Description(): QStringPath<string>;
    get QPublicTransportation_SeatNumber(): QStringPath<string>;
    get QFlight_FlightNumber(): QStringPath<string>;
    get QFlight_Airline(): QEntityPath<QAirline>;
    get QFlight_From(): QEntityPath<QAirport>;
    get QFlight_To(): QEntityPath<QAirport>;
    private __asQEvent;
    private __asQPublicTransportation;
    private __asQFlight;
}
export declare const qPlanItem: QPlanItem;
export declare class QPlanItemId extends QId<PlanItemId> {
    private readonly params;
    getParams(): QNumberParam<number>[];
}
export declare class QEvent extends QPlanItemBaseType {
    readonly OccursAt: QEntityPath<QEventLocation>;
    readonly Description: QStringPath<string>;
}
export declare const qEvent: QEvent;
export declare class QPublicTransportationBaseType extends QPlanItemBaseType {
    readonly SeatNumber: QStringPath<string>;
}
export declare class QPublicTransportation extends QPublicTransportationBaseType {
    protected readonly __subtypeMapping: {
        "Trippin.Flight": string;
    };
    get QFlight_FlightNumber(): QStringPath<string>;
    get QFlight_Airline(): QEntityPath<QAirline>;
    get QFlight_From(): QEntityPath<QAirport>;
    get QFlight_To(): QEntityPath<QAirport>;
    private __asQFlight;
}
export declare const qPublicTransportation: QPublicTransportation;
export declare class QFlight extends QPublicTransportationBaseType {
    readonly FlightNumber: QStringPath<string>;
    readonly Airline: QEntityPath<QAirline>;
    readonly From: QEntityPath<QAirport>;
    readonly To: QEntityPath<QAirport>;
}
export declare const qFlight: QFlight;
export declare class QEmployee extends QPersonBaseType {
    readonly Cost: QNumberPath<number>;
    readonly Peers: QEntityCollectionPath<QPerson>;
}
export declare const qEmployee: QEmployee;
export declare class QManager extends QPersonBaseType {
    readonly Budget: QNumberPath<number>;
    readonly BossOffice: QEntityPath<QLocation>;
    readonly DirectReports: QEntityCollectionPath<QPerson>;
}
export declare const qManager: QManager;
export declare class QLocationBaseType extends QueryObject {
    readonly Address: QStringPath<string>;
    readonly City: QEntityPath<QCity>;
}
export declare class QLocation extends QLocationBaseType {
    protected readonly __subtypeMapping: {
        "Trippin.AirportLocation": string;
        "Trippin.EventLocation": string;
    };
    get QAirportLocation_Loc(): QStringPath<string>;
    get QEventLocation_BuildingInfo(): QStringPath<string>;
    private __asQAirportLocation;
    private __asQEventLocation;
}
export declare const qLocation: QLocation;
export declare class QCity extends QueryObject {
    readonly Name: QStringPath<string>;
    readonly CountryRegion: QStringPath<string>;
    readonly Region: QStringPath<string>;
}
export declare const qCity: QCity;
export declare class QAirportLocation extends QLocationBaseType {
    readonly Loc: QStringPath<string>;
}
export declare const qAirportLocation: QAirportLocation;
export declare class QEventLocation extends QLocationBaseType {
    readonly BuildingInfo: QStringPath<string>;
}
export declare const qEventLocation: QEventLocation;
export declare class QGetPersonWithMostFriends extends QFunction {
    private readonly params;
    constructor();
    getParams(): [];
    buildUrl(notEncoded?: boolean): string;
}
export declare class QGetNearestAirport extends QFunction<GetNearestAirportParams> {
    private readonly params;
    constructor();
    getParams(): QNumberParam<number>[];
}
export declare class QResetDataSource extends QAction {
    private readonly params;
    constructor();
    getParams(): [];
}
