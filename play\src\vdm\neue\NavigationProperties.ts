/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { NavigationPropertiesApi } from './NavigationPropertiesApi';
import { OnDelete } from './OnDelete';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "NavigationProperties" of service "neue".
 */
export class NavigationProperties<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements NavigationPropertiesType<T>
{
  /**
   * Technical entity name for NavigationProperties.
   */
  static override _entityName = 'NavigationProperties';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the NavigationProperties entity.
   */
  static _keys = ['ncid'];
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Display Name.
   */
  declare displayName: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Collection.
   * @nullable
   */
  declare isCollection?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Nullable.
   * @nullable
   */
  declare isNullable?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Contains Target.
   * @nullable
   */
  declare isContainsTarget?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Referential Constraint.
   * @nullable
   */
  declare referentialConstraint?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * On Delete.
   * @nullable
   */
  declare onDelete?: OnDelete | null;
  /**
   * Is Read Only.
   * @nullable
   */
  declare isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Uniq.
   * @nullable
   */
  declare isUniq?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: NavigationPropertiesApi<T>) {
    super(_entityApi);
  }
}

export interface NavigationPropertiesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  name: DeserializedType<T, 'Edm.String'>;
  displayName: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  isCollection?: DeserializedType<T, 'Edm.Boolean'> | null;
  isNullable?: DeserializedType<T, 'Edm.Boolean'> | null;
  isContainsTarget?: DeserializedType<T, 'Edm.Boolean'> | null;
  referentialConstraint?: DeserializedType<T, 'Edm.String'> | null;
  onDelete?: OnDelete | null;
  isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  isUniq?: DeserializedType<T, 'Edm.Boolean'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
