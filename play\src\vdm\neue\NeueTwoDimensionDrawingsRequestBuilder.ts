/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { NeueTwoDimensionDrawings } from './NeueTwoDimensionDrawings';

/**
 * Request builder class for operations supported on the {@link NeueTwoDimensionDrawings} entity.
 */
export class NeueTwoDimensionDrawingsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<NeueTwoDimensionDrawings<T>, T> {
  /**
   * Returns a request builder for querying all `NeueTwoDimensionDrawings` entities.
   * @returns A request builder for creating requests to retrieve all `NeueTwoDimensionDrawings` entities.
   */
  getAll(): GetAllRequestBuilder<NeueTwoDimensionDrawings<T>, T> {
    return new GetAllRequestBuilder<NeueTwoDimensionDrawings<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `NeueTwoDimensionDrawings` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `NeueTwoDimensionDrawings`.
   */
  create(
    entity: NeueTwoDimensionDrawings<T>
  ): CreateRequestBuilder<NeueTwoDimensionDrawings<T>, T> {
    return new CreateRequestBuilder<NeueTwoDimensionDrawings<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `NeueTwoDimensionDrawings` entity based on its keys.
   * @param ncid Key property. See {@link NeueTwoDimensionDrawings.ncid}.
   * @returns A request builder for creating requests to retrieve one `NeueTwoDimensionDrawings` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<NeueTwoDimensionDrawings<T>, T> {
    return new GetByKeyRequestBuilder<NeueTwoDimensionDrawings<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `NeueTwoDimensionDrawings`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `NeueTwoDimensionDrawings`.
   */
  update(
    entity: NeueTwoDimensionDrawings<T>
  ): UpdateRequestBuilder<NeueTwoDimensionDrawings<T>, T> {
    return new UpdateRequestBuilder<NeueTwoDimensionDrawings<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `NeueTwoDimensionDrawings`.
   * @param ncid Key property. See {@link NeueTwoDimensionDrawings.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `NeueTwoDimensionDrawings`.
   */
  delete(ncid: string): DeleteRequestBuilder<NeueTwoDimensionDrawings<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `NeueTwoDimensionDrawings`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `NeueTwoDimensionDrawings` by taking the entity as a parameter.
   */
  delete(
    entity: NeueTwoDimensionDrawings<T>
  ): DeleteRequestBuilder<NeueTwoDimensionDrawings<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<NeueTwoDimensionDrawings<T>, T> {
    return new DeleteRequestBuilder<NeueTwoDimensionDrawings<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof NeueTwoDimensionDrawings
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
