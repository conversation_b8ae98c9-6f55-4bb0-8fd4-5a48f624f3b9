<template>
  <widget-wrapper>
    <div class="ne-basic-detail">
      <el-tabs
        ref="tabsRef"
        v-model="activeTab"
        tab-position="left"
        class="left-tabs"
      >
        <el-tab-pane
          v-for="panel in props.tabItems"
          :key="panel.key"
          :label="panel.name"
          :name="panel.key"
        >
          <div class="right-content">
            <div class="detail-toolbar">
              <div class="detail-left">
                <span class="detail-title">part编号</span>
              </div>
              <div class="detail-right">
                <NeButton type="primary" @click="handleLock"> 锁定 </NeButton>
                <NeButton @click="handleRevise"> 修订 </NeButton>
                <NeButton @click="handleDelete"> 删除 </NeButton>
              </div>
            </div>
            <div class="detail-content">
              <!-- <NeMaterialRender v-bind="panel.elements" /> -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </widget-wrapper>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import NeButton from '@neue-plus/components/button'
import type { BasicDetailConfig, TabItem } from './basic-detail'

const props = defineProps<{
  config: BasicDetailConfig
  tabItems: Array<TabItem>
}>()

const emit = defineEmits(['lock', 'revise', 'delete'])

const activeTab = ref('base')
const tabsRef = ref()
const handleLock = () => {
  console.log('锁定')
  emit('lock')
}
const handleRevise = () => {
  console.log('修订')
  emit('revise')
}
const handleDelete = () => {
  console.log('删除')
  emit('delete')
}

const init = () => {
  const nav = tabsRef.value?.$el.querySelector('.el-tabs__nav')
  if (nav) {
    const titleEl = document.createElement('div')
    titleEl.className = 'tabs-title'
    titleEl.textContent = props.config.title || ''
    // 关键：nav 内部最前面
    nav.prepend(titleEl)
  }
}

onMounted(() => {
  init()
})
</script>
