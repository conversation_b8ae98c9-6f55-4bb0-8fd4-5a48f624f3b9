/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { LifecycleStatuses } from './LifecycleStatuses';

/**
 * Request builder class for operations supported on the {@link LifecycleStatuses} entity.
 */
export class LifecycleStatusesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<LifecycleStatuses<T>, T> {
  /**
   * Returns a request builder for querying all `LifecycleStatuses` entities.
   * @returns A request builder for creating requests to retrieve all `LifecycleStatuses` entities.
   */
  getAll(): GetAllRequestBuilder<LifecycleStatuses<T>, T> {
    return new GetAllRequestBuilder<LifecycleStatuses<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `LifecycleStatuses` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `LifecycleStatuses`.
   */
  create(
    entity: LifecycleStatuses<T>
  ): CreateRequestBuilder<LifecycleStatuses<T>, T> {
    return new CreateRequestBuilder<LifecycleStatuses<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `LifecycleStatuses` entity based on its keys.
   * @param ncid Key property. See {@link LifecycleStatuses.ncid}.
   * @returns A request builder for creating requests to retrieve one `LifecycleStatuses` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<LifecycleStatuses<T>, T> {
    return new GetByKeyRequestBuilder<LifecycleStatuses<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `LifecycleStatuses`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `LifecycleStatuses`.
   */
  update(
    entity: LifecycleStatuses<T>
  ): UpdateRequestBuilder<LifecycleStatuses<T>, T> {
    return new UpdateRequestBuilder<LifecycleStatuses<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `LifecycleStatuses`.
   * @param ncid Key property. See {@link LifecycleStatuses.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `LifecycleStatuses`.
   */
  delete(ncid: string): DeleteRequestBuilder<LifecycleStatuses<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `LifecycleStatuses`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `LifecycleStatuses` by taking the entity as a parameter.
   */
  delete(
    entity: LifecycleStatuses<T>
  ): DeleteRequestBuilder<LifecycleStatuses<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<LifecycleStatuses<T>, T> {
    return new DeleteRequestBuilder<LifecycleStatuses<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof LifecycleStatuses
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
