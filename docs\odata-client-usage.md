# SAP Cloud SDK OData 客户端使用指南

本文档介绍如何在项目中使用 SAP Cloud SDK 生成和使用 OData 客户端。

## 🚀 快速开始

### 1. 生成 OData 客户端

```bash
# 方法 1: 使用优化的生成脚本
npm run gen:odata

# 方法 2: 完整构建流程（推荐）
npm run build:full

# 方法 3: 构建并启动开发服务器
npm run build:dev
```

### 2. 后处理修复

```bash
# 修复生成代码中的常见问题
npm run post-process:odata
```

### 3. 验证生成的代码

```bash
# 检查类型接口是否完整
npm run check:types
```

## 📁 项目结构

```
├── resource/
│   └── neue.xml                    # OData 元数据文件
├── scripts/
│   ├── gen-odata.js               # 优化的 OData 生成脚本
│   ├── post-process-odata.js      # 后处理修复脚本
│   ├── check-missing-types.js     # 类型检查脚本
│   └── build-and-run.js           # 完整构建脚本
├── play/src/
│   ├── vdm/neue/                  # 生成的 OData 客户端代码
│   ├── examples/
│   │   └── odata-client-demo.ts   # 使用示例
│   └── components/spa/
│       └── index.vue              # Vue 演示组件
└── options-per-service-new.json   # 生成配置文件
```

## 🔧 脚本说明

### gen-odata.js - OData 客户端生成

**功能**：
- 从 `resource/neue.xml` 生成 TypeScript OData 客户端
- 支持详细的日志输出和错误处理
- 自动创建输出目录
- 生成统计信息

**配置**：
```javascript
const CONFIG = {
  INPUT_DIR: '../resource',           // 元数据文件目录
  OUTPUT_DIR: '../play/src/vdm',     // 输出目录
  SERVICE_NAME: 'neue',              // 服务名称
  OData_VERSION: 'v4'                // OData 版本
}
```

### post-process-odata.js - 后处理修复

**功能**：
- 修复 moment.js 导入问题
- 修复 SAP Cloud SDK 类型导入
- 添加缺失的类型接口
- 创建备份

**修复内容**：
1. **Moment.js 导入修复**：
   ```typescript
   // 修复前
   import { Moment, Duration } from 'moment'
   
   // 修复后
   import moment from 'moment'
   import type { Moment } from 'moment'
   type Duration = moment.Duration
   ```

2. **类型导入修复**：
   ```typescript
   // 修复前
   import { DeSerializers, DefaultDeSerializers } from '@sap-cloud-sdk/odata-v4'
   
   // 修复后
   import type { DeSerializers, DefaultDeSerializers } from '@sap-cloud-sdk/odata-v4'
   ```

### check-missing-types.js - 类型检查

**功能**：
- 检查所有实体文件是否有对应的类型接口
- 识别缺失的类型导出
- 生成详细的检查报告

### build-and-run.js - 完整构建

**功能**：
- 执行完整的构建流程
- 包含所有验证步骤
- 可选启动开发服务器
- 详细的进度报告

## 📋 使用示例

### 基本查询

```typescript
import { neue } from '../vdm/neue/service'

const service = neue()
const { cadPartsApi } = service

// 获取所有 CAD 零件
const parts = await cadPartsApi
  .requestBuilder()
  .getAll()
  .top(10)
  .execute()
```

### 条件查询

```typescript
// 按类型过滤
const assemblyParts = await cadPartsApi
  .requestBuilder()
  .getAll()
  .filter(
    cadPartsApi.schema.PART_TYPE.equals(PartType.Assembly)
  )
  .execute()

// 按代码查询
const specificPart = await cadPartsApi
  .requestBuilder()
  .getAll()
  .filter(
    cadPartsApi.schema.CODE.equals('DEMO001')
  )
  .execute()
```

### 创建实体

```typescript
// 创建新零件
const newPart = cadPartsApi.entityBuilder().build({
  code: 'NEW_PART_001',
  name: '新零件',
  partType: PartType.Part,
  lifecycleState: LifecycleState.InWork
})

// 执行创建（需要有效的后端服务）
const createdPart = await cadPartsApi
  .requestBuilder()
  .create(newPart)
  .execute()
```

### 批处理操作

```typescript
import { batch, changeset } from '../vdm/neue/BatchRequest'

// 创建批处理请求
const batchRequest = batch(
  // 查询操作
  cadPartsApi.requestBuilder().getAll().top(5),
  
  // 变更集
  changeset(
    cadPartsApi.requestBuilder().create(newPart1),
    cadPartsApi.requestBuilder().update(updatedPart)
  )
)

// 执行批处理
const batchResult = await batchRequest.execute()
```

## 🎯 Vue 组件集成

在 `play/src/components/spa/index.vue` 中提供了完整的 Vue 集成示例：

```vue
<template>
  <div class="odata-demo">
    <h1>SAP Cloud SDK OData 客户端演示</h1>
    <div class="demo-controls">
      <button @click="runBasicQuery">基本查询</button>
      <button @click="runFilterQuery">条件查询</button>
      <button @click="runBusinessRules">业务规则查询</button>
    </div>
    <!-- 结果显示区域 -->
  </div>
</template>

<script setup lang="ts">
import { basicQueryExample } from '../../examples/odata-client-demo'

async function runBasicQuery() {
  const result = await basicQueryExample()
  // 处理结果...
}
</script>
```

## 🔍 故障排除

### 常见问题

1. **模块导出错误**：
   ```
   The requested module does not provide an export named 'XxxType'
   ```
   **解决方案**：运行 `npm run post-process:odata`

2. **Moment Duration 导入错误**：
   ```
   does not provide an export named 'Duration'
   ```
   **解决方案**：后处理脚本会自动修复

3. **类型导入错误**：
   ```
   must be imported using a type-only import when 'verbatimModuleSyntax' is enabled
   ```
   **解决方案**：后处理脚本会自动修复

### 调试步骤

1. 检查元数据文件是否存在：`resource/neue.xml`
2. 运行类型检查：`npm run check:types`
3. 查看生成的文件：`play/src/vdm/neue/`
4. 检查控制台错误信息
5. 运行完整构建：`npm run build:full`

## 📚 相关资源

- [SAP Cloud SDK 官方文档](https://sap.github.io/cloud-sdk/)
- [OData v4 规范](https://www.odata.org/documentation/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [Vue 3 官方文档](https://vuejs.org/)

## 🤝 贡献

如果你发现问题或有改进建议，请：

1. 检查现有的 issues
2. 创建新的 issue 描述问题
3. 提交 Pull Request

## 📄 许可证

本项目遵循 MIT 许可证。
