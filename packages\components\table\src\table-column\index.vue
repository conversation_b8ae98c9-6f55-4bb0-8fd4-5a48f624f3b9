<template>
  <el-table-column v-bind="props" :show-overflow-tooltip="showOverflowTooltip">
    <template #default="{ row }">
      <component :is="getComponentName(row)" />
    </template>
  </el-table-column>
</template>

<script lang="ts" setup>
import { computed, h, useAttrs } from 'vue'
import { get } from 'lodash-unified'
import dayjs from 'dayjs'
import { templateToData } from '@neue-plus/utils/templateToData'
import NeAvatar from '../../../avatar'
import NeImage from '../../../image'
import NeTag from '../../../tag'
import { type ValueEnum, ValueTypeEnum } from '../../../pro-table/src/pro-table'
import { neTableColumnProps } from './type'

defineOptions({
  name: 'NeTableColumn',
  inheritAttrs: false,
})
const props = defineProps(neTableColumnProps)
const showOverflowTooltip = computed(() => {
  return props.valueType === ValueTypeEnum.Avatar
    ? false
    : props.showOverflowTooltip
})
const attrs = useAttrs()
// 根据组件名返回组件
const getComponentName = (row: any) => {
  const { valueType, prop, render } = props
  const { valueEnum } = attrs as { valueEnum?: ValueEnum }
  let text = ''
  if (render) {
    try {
      text = templateToData(render, row)
      console.log(render, text, row, 555)
    } catch {
      console.error('return,Render function error:')
    }
  } else {
    text = get(row, prop.replace(/\//g, '.'))
  }
  text = valueEnum?.[text]?.text || text
  if (!text) {
    return h('span', {}, text || '-')
  }
  switch (valueType) {
    case 'avatar':
      return h(NeAvatar, { name: text })
    case 'image':
      return h(NeImage, { src: text })
    case 'select':
      return h(NeTag, {}, () => text)
    case 'time':
      return h('span', {}, dayjs(text).format('YYYY-MM-DD HH:mm:ss'))
    default:
      return h('span', {}, text || '-')
  }
}
</script>
