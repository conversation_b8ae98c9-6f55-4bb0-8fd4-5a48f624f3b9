<template>
  <el-table-column v-bind="props">
    <template #default="{ row }">
      <component :is="getComponentName(row)" />
    </template>
  </el-table-column>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { get } from 'lodash-unified'
import dayjs from 'dayjs'
import { NeAvatar, NeImage, NeTag } from '../../../index'
import { neTableColumnProps } from './type'

defineOptions({
  name: 'NeTableColumn',
  inheritAttrs: false,
})
const props = defineProps(neTableColumnProps)
// 根据组件名返回组件
const getComponentName = (row: any) => {
  const { valueType, prop, render } = props
  let text = ''
  if (render) {
    try {
      // 字符串形式函数： "({ row }) => row.status + '!'"
      const fn = eval(render)
      text = fn({ row })
    } catch (e) {
      console.error('Render function error:', e)
    }
  } else {
    text = get(row, prop)
  }
  switch (valueType) {
    case 'avatar':
      return h(NeAvatar, { name: text })
    case 'image':
      return h(NeImage, { src: text })
    case 'select':
      return h(NeTag, {}, () => text)
    case 'time':
      return h('span', {}, dayjs(text).format('YYYY-MM-DD HH:mm:ss'))
    default:
      return h('span', {}, text)
  }
}
</script>
