{"name": "@neue-plus/odata", "version": "1.0.0", "description": "OData client wrapper based on Olingo OData4 JS library", "keywords": ["odata", "olingo", "rest", "api", "client", "typescript", "vue"], "license": "MIT", "author": "Neue Plus Team", "files": ["dist"], "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/neue-plus/neue-plus.git", "directory": "packages/odata"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "vitest", "test:coverage": "vitest --coverage"}, "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {"@sap-cloud-sdk/http-client": "^3.20.0", "@sap-cloud-sdk/odata-v4": "^3.20.0", "@sap-cloud-sdk/util": "^3.20.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/node": "^20.10.0", "rollup": "^4.9.0", "rollup-plugin-dts": "^6.1.0", "typescript": "~5.5.4", "vitest": "^1.0.0"}}