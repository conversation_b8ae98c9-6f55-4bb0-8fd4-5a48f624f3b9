// 单个事件动作节点
export interface ActionNode {
  id: string
  type: 'start' | 'end' | 'normal'
  title: string
  content?: string
  config?: {
    href: string
    actionName: string
    actionType: string
    target: string
    delay?: number
    request?: {
      // axios 请求配置
      url: string
      method: 'get' | 'post' | 'put' | 'delete'
      data?: Record<string, any>
      headers?: Record<string, any>
      responsePath?: string // 根据 branchKey 获取指定response字段
    }
    branches?: Record<string, string> // ✅ 用于表示当前actionType返回的两种结果
    [key: string]: any
  }
}

// 元素绑定的事件
export interface ElementEvent {
  nickName: string
  eventName: string
  actions: ActionNode[]
}

// 页面钩子事件
export interface PageEvents {
  onBeforeMount?: () => void
  onMounted?: () => void
}
