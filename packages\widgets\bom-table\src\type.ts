import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const widgetBomTableProps = buildProps({} as const)

export type WidgetBomTable = ExtractPropTypes<typeof widgetBomTableProps>

export interface IValueEnum {
  [key: string]: {
    text: string
    status: 'Success' | 'Error' | 'Processing' | 'Warning' | 'Default'
  }
}
// 事件类型
export type WidgetBomTableEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
