import { buildProp, buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes, PropType } from 'vue'
import type * as Icons from '@element-plus/icons-vue'
import type { ElTooltipProps } from 'element-plus'

export const neDynamicIconProps = buildProps({
  /**
   * 图标名称
   */
  name: {
    type: String as PropType<keyof typeof Icons | string>,
    required: true,
  },
  /**
   * 图标颜色
   */
  color: {
    type: String,
    default: '#1856EB',
  },
  /**
   * 图标大小
   */
  size: {
    type: [Number, String],
    default: '16px',
  },
  tooltip: buildProp({
    type: Object as PropType<ElTooltipProps>,
  }),
} as const)

export type NeDynamicIconProps = ExtractPropTypes<typeof neDynamicIconProps>
