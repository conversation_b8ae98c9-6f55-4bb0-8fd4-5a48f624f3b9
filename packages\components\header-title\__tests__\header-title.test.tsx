import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import HeaderTitle from '../src/header-title.vue'

describe('HeaderTitle', () => {
  it('renders basic title', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
    })

    expect(wrapper.find('.ne-header-title__title').text()).toBe('Test Title')
  })

  it('renders title slot correctly', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Default Title',
      },
      slots: {
        title: '<span>Custom Title</span>',
      },
    })

    expect(wrapper.find('.ne-header-title__title span').text()).toBe(
      'Custom Title'
    )
  })

  it('renders extra slot correctly', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
      slots: {
        extra: '<button>Action Button</button>',
      },
    })

    expect(wrapper.find('.ne-header-title__extra button').text()).toBe(
      'Action Button'
    )
  })

  it('does not render extra area when no slot provided', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
    })

    expect(wrapper.find('.ne-header-title__extra').exists()).toBe(false)
  })

  it('has correct CSS classes', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
    })

    expect(wrapper.find('.ne-header-title').exists()).toBe(true)
    expect(wrapper.find('.ne-header-title__title').exists()).toBe(true)
  })
})
