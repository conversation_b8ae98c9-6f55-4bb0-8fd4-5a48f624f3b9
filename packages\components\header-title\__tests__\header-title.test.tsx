import { describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElIcon } from 'element-plus'
import HeaderTitle from '../src/header-title.vue'

describe('HeaderTitle', () => {
  it('renders basic title', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
    })

    expect(wrapper.find('.ne-header-title__title').text()).toBe('Test Title')
    expect(wrapper.find('h2').exists()).toBe(true) // default level is 2
  })

  it('renders with subtitle', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Main Title',
        subtitle: 'Sub Title',
      },
    })

    expect(wrapper.find('.ne-header-title__title').text()).toBe('Main Title')
    expect(wrapper.find('.ne-header-title__subtitle').text()).toBe('Sub Title')
  })

  it('renders with different heading levels', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        level: 1,
      },
    })

    expect(wrapper.find('h1').exists()).toBe(true)
  })

  it('renders with back button', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        showBack: true,
        backText: 'Go Back',
      },
      global: {
        components: {
          ElButton,
          ElIcon,
        },
      },
    })

    const backButton = wrapper.findComponent(ElButton)
    expect(backButton.exists()).toBe(true)
    expect(backButton.text()).toContain('Go Back')
  })

  it('emits back event when back button is clicked', async () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        showBack: true,
      },
      global: {
        components: {
          ElButton,
          ElIcon,
        },
      },
    })

    const backButton = wrapper.findComponent(ElButton)
    await backButton.trigger('click')

    expect(wrapper.emitted('back')).toBeTruthy()
  })

  it('renders with divider', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        divider: true,
        dividerPosition: 'both',
      },
    })

    expect(wrapper.find('.ne-header-title__divider--top').exists()).toBe(true)
    expect(wrapper.find('.ne-header-title__divider--bottom').exists()).toBe(true)
  })

  it('applies correct size classes', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        size: 'large',
      },
    })

    expect(wrapper.find('.ne-header-title--large').exists()).toBe(true)
  })

  it('applies correct alignment classes', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
        align: 'center',
      },
    })

    expect(wrapper.find('.ne-header-title--center').exists()).toBe(true)
  })

  it('renders slots correctly', () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Default Title',
      },
      slots: {
        title: '<span>Custom Title</span>',
        subtitle: '<span>Custom Subtitle</span>',
        extra: '<button>Action</button>',
      },
    })

    expect(wrapper.find('.ne-header-title__title span').text()).toBe('Custom Title')
    expect(wrapper.find('.ne-header-title__subtitle span').text()).toBe('Custom Subtitle')
    expect(wrapper.find('.ne-header-title__extra button').text()).toBe('Action')
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(HeaderTitle, {
      props: {
        title: 'Test Title',
      },
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
