const getOdataTypeName = (odataType: string) => {
  // 去掉前缀 # 后，按点分割并取最后一段
  return odataType.replace(/^#/, '').split('.').pop() ?? ''
}

function pickMatchedFieldsDbg(template: any, source: any) {
  console.log('source:', source)
  // 关键：强制 Vue 解包
  JSON.parse(JSON.stringify(source))

  /* 下面保持你原来的逻辑 */
  const res: any = {}
  Object.entries(template).forEach(([path, v]) => {
    if (v !== '') return
    const keys = path.toLowerCase().split('.')
    let cur = source
    let ok = true
    for (const k of keys) {
      const foundKey =
        cur && Object.keys(cur).find((kk) => kk.toLowerCase() === k)
      if (!foundKey) {
        ok = false
        break
      }
      cur = cur[foundKey]
    }
    if (ok) res[path] = cur
  })
  return res
}

function unflatten(flat: Record<string, any>): any {
  const root: any = {}
  for (const [k, v] of Object.entries(flat)) {
    const keys = k.split('.')
    let cur = root
    keys.slice(0, -1).forEach((p) => (cur = cur[p] ??= {}))
    cur[keys.at(-1)!] = v
  }
  return root
}

const isEmpty = (v: unknown) =>
  v == null || v === '' || (Array.isArray(v) && v.length === 0)

function filterFlat(
  flat: Record<string, any>,
  fields: Array<{ prop: string; disabled?: boolean }>
): Record<string, any> {
  const keep = new Set(
    fields
      .filter((f) => !f.disabled && !isEmpty(flat[f.prop]))
      .map((f) => f.prop)
  )

  return Object.fromEntries(Object.entries(flat).filter(([k]) => keep.has(k)))
}

/** 把两层 formItems 合并成一维数组 */
function flattenFormItems(groups: Array<{ formItems?: any[] }>): any[] {
  return groups.flatMap((g) => g.formItems || [])
}

const buildQuery = (p: Record<string, any>) => {
  const u = new URL(location.href)
  Object.entries(p).forEach(([k, v]) =>
    v == null ? u.searchParams.delete(k) : u.searchParams.set(k, String(v))
  )
  return u.toString()
}

export {
  getOdataTypeName,
  pickMatchedFieldsDbg,
  unflatten,
  filterFlat,
  // isEmpty,
  flattenFormItems,
  buildQuery,
}
