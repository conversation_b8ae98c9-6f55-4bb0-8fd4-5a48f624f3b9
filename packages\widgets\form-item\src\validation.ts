import { FieldType } from './form-item'

const ruleIdMap: Record<string, object> = {
  // 支持字母、数字、汉字，且以字母或中文开头，可以包含下划线_
  // 例如：姓名、张三、abc_123
  RULE_101: {
    required: true,
    message: '必须以字母或中文开头，可以包含下划线_',
    regex: /^[\u4E00-\u9FA5a-zA-Z][\u4E00-\u9FA5a-zA-Z0-9_]*$/,
  },
  // 支持字母、数字、下划线_，且以字母或数字开头
  // 例如：abc_123、a1b2c3、A_B_C
  RULE_102: {
    required: true,
    message: '必须以字母或数字开头，可以包含下划线_',
    regex: /^[a-zA-Z][a-zA-Z0-9_]*$/,
  },
  // 要包含@符号，且 @ 前后必须有内容；域名部分必须包含 .，且不能出现在开头或结尾
  // 例如：@neuetech.cn、<EMAIL>
  RULE_201: {
    required: true,
    type: 'email',
    message: '请输入有效的邮箱地址',
    regex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
}

export const getRules = (formItem: FieldType) => {
  if (formItem.required && formItem.ruleId && ruleIdMap[formItem.ruleId]) {
    const rule: any = ruleIdMap[formItem.ruleId]
    if (rule) {
      return [
        {
          ...rule,
          trigger: 'blur',
          message: formItem.maxLength
            ? `支持${formItem.minLength || 1} - ${formItem.maxLength}位字符，${
                rule.message
              }`
            : rule.message,
          validator: (rule: any, value: any, callback: any) => {
            if (!value || (rule.regex && !rule.regex.test(value))) {
              callback(new Error(rule.message))
            } else {
              callback()
            }
          },
        },
      ]
    } else if (formItem.required) {
      return [
        {
          required: true,
          message: `请输入${formItem.fieldName}`,
          trigger: 'blur',
        },
      ]
    } else {
      return []
    }
  }
  return []
}
