/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { NavigationProperties } from './NavigationProperties';
import { NavigationPropertiesRequestBuilder } from './NavigationPropertiesRequestBuilder';
import { OnDelete } from './OnDelete';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class NavigationPropertiesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<NavigationProperties<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): NavigationPropertiesApi<DeSerializersT> {
    return new NavigationPropertiesApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = NavigationProperties;

  requestBuilder(): NavigationPropertiesRequestBuilder<DeSerializersT> {
    return new NavigationPropertiesRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    NavigationProperties<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>(
      this
    );
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<
    NavigationProperties<DeSerializersT>,
    DeSerializersT,
    NullableT
  > {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<
    typeof NavigationProperties,
    DeSerializersT
  >;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(
        NavigationProperties,
        this.deSerializers
      );
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    NAME: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DISPLAY_NAME: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    IS_COLLECTION: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_NULLABLE: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_CONTAINS_TARGET: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    REFERENTIAL_CONSTRAINT: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ON_DELETE: EnumField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      OnDelete,
      true,
      true
    >;
    IS_READ_ONLY: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_UNIQ: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      NavigationProperties<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<NavigationProperties<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', false),
        /**
         * Static representation of the {@link displayName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DISPLAY_NAME: fieldBuilder.buildEdmTypeField(
          'displayName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link isCollection} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_COLLECTION: fieldBuilder.buildEdmTypeField(
          'isCollection',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isNullable} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_NULLABLE: fieldBuilder.buildEdmTypeField(
          'isNullable',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isContainsTarget} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_CONTAINS_TARGET: fieldBuilder.buildEdmTypeField(
          'isContainsTarget',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link referentialConstraint} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        REFERENTIAL_CONSTRAINT: fieldBuilder.buildEdmTypeField(
          'referentialConstraint',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link onDelete} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ON_DELETE: fieldBuilder.buildEnumField('onDelete', OnDelete, true),
        /**
         * Static representation of the {@link isReadOnly} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_READ_ONLY: fieldBuilder.buildEdmTypeField(
          'isReadOnly',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isUniq} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_UNIQ: fieldBuilder.buildEdmTypeField('isUniq', 'Edm.Boolean', true),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', NavigationProperties)
      };
    }

    return this._schema;
  }
}
