@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@include neb(basic-detail) {
  width: 100%;
  height: 100%;
  .el-tabs__nav-scroll {
    width: 180px;
    padding: 0 20px
  }
  .el-tabs__active-bar {
    background: none;
  }
  .el-tabs__nav {
    padding: 0 16px;
    width: 148px;
    height: 100%;
    overflow-y: auto;
    position: relative;
  }
  .el-tabs--left .el-tabs__item.is-left {
    text-align: left;
    justify-content: flex-start;
  }
  .tabs-title {
    line-height: 58px;
    font-weight: 500;
  }
  .detail-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #dcdfe6;
  }
  .el-tabs__item.is-active, .el-tabs__item:hover {
    color: #1E293B;
    background: #CCD5EA;
    border-radius: 8px;
  }
}
