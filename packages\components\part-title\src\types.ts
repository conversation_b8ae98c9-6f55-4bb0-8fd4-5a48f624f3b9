import { ExtractPropTypes, PropType } from 'vue'

export enum PartTypeEmu {
  NEUEPART = 'NEUE_PRT',
  NEUEASM = 'NEUE_ASM',
}

export const nePartTitleProps = {
  title: {
    type: [String, Number],
    default: '',
  },
  type: {
    type: String as PropType<PartTypeEmu>,
    required: true,
  },
  isTitle: {
    type: Boolean,
  },
  hover: {
    type: Boolean,
  },
}

export type NePartTitleProps = ExtractPropTypes<typeof nePartTitleProps>
