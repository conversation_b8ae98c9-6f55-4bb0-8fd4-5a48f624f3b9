import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import ProTable from '../src/pro-table.vue'

const mockColumns = [
  {
    prop: 'name',
    label: '姓名',
    valueType: 'input' as const,
  },
  {
    prop: 'age',
    label: '年龄',
    valueType: 'select' as const,
    valueEnum: [
      { label: '18-25', value: '18-25' },
      { label: '26-35', value: '26-35' },
    ],
  },
  {
    prop: 'address',
    label: '地址',
  },
]

const mockData = [
  { name: '<PERSON>', age: 20, address: 'Beijing' },
  { name: '<PERSON>', age: 25, address: 'Shanghai' },
]

describe('ProTable.vue', () => {
  test('render test', () => {
    const wrapper = mount(ProTable as any, {
      props: {
        data: mockData,
        columns: mockColumns,
      },
    })

    expect(wrapper.find('.ne-pro-table').exists()).toBe(true)
    expect(wrapper.find('.ne-pro-table__filter').exists()).toBe(true)
    expect(wrapper.find('.ne-pro-table__table').exists()).toBe(true)
    expect(wrapper.find('.ne-pro-table__pagination').exists()).toBe(true)
  })

  test('should render filter form', () => {
    const wrapper = mount(ProTable as any, {
      props: {
        data: mockData,
        columns: mockColumns,
        showFilter: true,
      },
    })

    expect(wrapper.find('.ne-pro-table__filter').exists()).toBe(true)
    expect(wrapper.findAll('.el-form-item')).toHaveLength(3) // 2 filter columns + 1 button group
  })

  test('should hide filter when showFilter is false', () => {
    const wrapper = mount(ProTable as any, {
      props: {
        data: mockData,
        columns: mockColumns,
        showFilter: false,
      },
    })

    expect(wrapper.find('.ne-pro-table__filter').exists()).toBe(false)
  })

  test('should hide pagination when showPagination is false', () => {
    const wrapper = mount(ProTable as any, {
      props: {
        data: mockData,
        columns: mockColumns,
        showPagination: false,
      },
    })

    expect(wrapper.find('.ne-pro-table__pagination').exists()).toBe(false)
  })
})
