﻿// odataUtils.ts
import { contains, find, isArray, isDate, isObject } from '../utils'

export const EDM = 'Edm.'
export const EDM_BOOLEAN = `${EDM}Boolean`
export const EDM_BYTE = `${EDM}Byte`
export const EDM_SBYTE = `${EDM}SByte`
export const EDM_INT16 = `${EDM}Int16`
export const EDM_INT32 = `${EDM}Int32`
export const EDM_INT64 = `${EDM}Int64`
export const EDM_SINGLE = `${EDM}Single`
export const EDM_DOUBLE = `${EDM}Double`
export const EDM_DECIMAL = `${EDM}Decimal`
export const EDM_STRING = `${EDM}String`
export const EDM_BINARY = `${EDM}Binary`
export const EDM_DATE = `${EDM}Date`
export const EDM_DATETIMEOFFSET = `${EDM}DateTimeOffset`
export const EDM_DURATION = `${EDM}Duration`
export const EDM_GUID = `${EDM}Guid`
export const EDM_TIMEOFDAY = `${EDM}Time`

export type GeoJsonType =
  | 'Point'
  | 'LineString'
  | 'Polygon'
  | 'MultiPoint'
  | 'MultiLineString'
  | 'MultiPolygon'
  | 'GeometryCollection'

export const GEOJSON_POINT: GeoJsonType = 'Point'
export const GEOJSON_LINESTRING: GeoJsonType = 'LineString'
export const GEOJSON_POLYGON: GeoJsonType = 'Polygon'
export const GEOJSON_MULTIPOINT: GeoJsonType = 'MultiPoint'
export const GEOJSON_MULTILINESTRING: GeoJsonType = 'MultiLineString'
export const GEOJSON_MULTIPOLYGON: GeoJsonType = 'MultiPolygon'
export const GEOJSON_GEOMETRYCOLLECTION: GeoJsonType = 'GeometryCollection'

// Geometry / Geography Types
export const GEOGRAPHY = 'Geography'
export const EDM_GEOGRAPHY = `${EDM}${GEOGRAPHY}`
export const EDM_GEOGRAPHY_POINT = `${EDM_GEOGRAPHY}Point`
export const EDM_GEOGRAPHY_LINESTRING = `${EDM_GEOGRAPHY}LineString`
export const EDM_GEOGRAPHY_POLYGON = `${EDM_GEOGRAPHY}Polygon`
export const EDM_GEOGRAPHY_COLLECTION = `${EDM_GEOGRAPHY}Collection`
export const EDM_GEOGRAPHY_MULTIPOLYGON = `${EDM_GEOGRAPHY}MultiPolygon`
export const EDM_GEOGRAPHY_MULTILINESTRING = `${EDM_GEOGRAPHY}MultiLineString`
export const EDM_GEOGRAPHY_MULTIPOINT = `${EDM_GEOGRAPHY}MultiPoint`

export const GEOMETRY = 'Geometry'
export const EDM_GEOMETRY = `${EDM}${GEOMETRY}`
export const EDM_GEOMETRY_POINT = `${EDM_GEOMETRY}Point`
export const EDM_GEOMETRY_LINESTRING = `${EDM_GEOMETRY}LineString`
export const EDM_GEOMETRY_POLYGON = `${EDM_GEOMETRY}Polygon`
export const EDM_GEOMETRY_COLLECTION = `${EDM_GEOMETRY}Collection`
export const EDM_GEOMETRY_MULTIPOLYGON = `${EDM_GEOMETRY}MultiPolygon`
export const EDM_GEOMETRY_MULTILINESTRING = `${EDM_GEOMETRY}MultiLineString`
export const EDM_GEOMETRY_MULTIPOINT = `${EDM_GEOMETRY}MultiPoint`

// Primitive EDM Types
export const primitiveEdmTypes = [
  EDM_STRING,
  EDM_INT32,
  EDM_INT64,
  EDM_BOOLEAN,
  EDM_DOUBLE,
  EDM_SINGLE,
  EDM_DATE,
  EDM_DATETIMEOFFSET,
  EDM_DURATION,
  EDM_TIMEOFDAY,
  EDM_DECIMAL,
  EDM_GUID,
  EDM_BYTE,
  EDM_INT16,
  EDM_SBYTE,
  EDM_BINARY,
]

// Geometry / Geography EDM Type Arrays
export const geometryEdmTypes = [
  EDM_GEOMETRY,
  EDM_GEOMETRY_POINT,
  EDM_GEOMETRY_LINESTRING,
  EDM_GEOMETRY_POLYGON,
  EDM_GEOMETRY_COLLECTION,
  EDM_GEOMETRY_MULTIPOLYGON,
  EDM_GEOMETRY_MULTILINESTRING,
  EDM_GEOMETRY_MULTIPOINT,
]

export const geographyEdmTypes = [
  EDM_GEOGRAPHY,
  EDM_GEOGRAPHY_POINT,
  EDM_GEOGRAPHY_LINESTRING,
  EDM_GEOGRAPHY_POLYGON,
  EDM_GEOGRAPHY_COLLECTION,
  EDM_GEOGRAPHY_MULTIPOLYGON,
  EDM_GEOGRAPHY_MULTILINESTRING,
  EDM_GEOGRAPHY_MULTIPOINT,
]

// -------------------- Utility Functions --------------------
export function dataItemTypeName(value: any, metadata?: any): string | null {
  const valueTypeName = ((value && value.__metadata) || {}).type
  return valueTypeName || (metadata ? metadata.type : null)
}

export function forEachSchema(
  metadata: any,
  callback: (schema: any) => any
): any {
  if (!metadata) return null

  if (isArray(metadata)) {
    for (let i = 0, len = metadata.length; i < len; i++) {
      const result = forEachSchema(metadata[i], callback)
      if (result) return result
    }
    return null
  } else {
    if (metadata.dataServices) {
      return forEachSchema(metadata.dataServices.schema, callback)
    }
    return callback(metadata)
  }
}
export function formatDateTimeOffsetJSON(value: Date) {
  return `/Date(${value.getTime()})/`
}

export function formatMilliseconds(ms: number, ns: number = 0): string {
  let msStr = ms === 0 ? '' : `.${formatNumberWidth(ms, 3)}`
  if (ns > 0) {
    if (!msStr) msStr = '.000'
    msStr += formatNumberWidth(ns, 4)
  }
  return msStr
}

export function formatNumberWidth(
  value: number | string,
  width: number,
  append: boolean = false
): string {
  let result = value.toString()
  while (result.length < width) {
    result = append ? `${result}0` : `0${result}`
  }
  return result
}

export function getCanonicalTimezone(timezone?: string): string {
  return !timezone ||
    timezone === 'Z' ||
    timezone === '+00:00' ||
    timezone === '-00:00'
    ? 'Z'
    : timezone
}

const collectionTypeRE = /Collection\((.*)\)/
export function isCollectionType(typeName: string): boolean {
  return collectionTypeRE.test(typeName)
}

// -------------------- Type Tests --------------------
export function isComplex(value: any): boolean {
  return !!value && isObject(value) && !isArray(value) && !isDate(value)
}

export function isDateTimeOffset(value: any): boolean {
  return (
    value.__edmType === EDM_DATETIMEOFFSET ||
    (!value.__edmType && value.__offset)
  )
}

export function isDeferred(value: any): boolean {
  if (!value || !isComplex(value)) return false
  const metadata = value.__metadata || {}
  const deferred = value.__deferred || {}
  return !metadata.type && !!deferred.uri
}

export function isEntry(value: any): boolean {
  return isComplex(value) && value.__metadata && 'uri' in value.__metadata
}

export function isFeed(value: any, typeName?: string): boolean {
  const feedData = (value && value.results) || value
  return (
    isArray(feedData) &&
    !isCollectionType(typeName || '') &&
    isComplex(feedData[0])
  )
}

export function isBatch(value: any): boolean {
  return isComplex(value) && isArray(value.__batchRequests)
}

export function isCollection(value: any, typeName?: string): boolean {
  const colData = (value && value.results) || value
  return (
    (!!colData && isCollectionType(typeName || '')) ||
    (!typeName && isArray(colData) && !isComplex(colData[0]))
  )
}

export function isGeographyEdmType(typeName: string): boolean {
  return (
    contains(geographyEdmTypes, typeName) ||
    (!typeName.includes('.') &&
      contains(
        geographyEdmTypes.map((t) => t.replace('Edm.', '')),
        typeName
      ))
  )
}

export function isGeometryEdmType(typeName: string): boolean {
  return (
    contains(geometryEdmTypes, typeName) ||
    (!typeName.includes('.') &&
      contains(
        geometryEdmTypes.map((t) => t.replace('Edm.', '')),
        typeName
      ))
  )
}

export function isNamedStream(value: any): boolean {
  if (!value || !isComplex(value)) return false
  const metadata = value.__metadata
  const mediaResource = value.__mediaresource
  return !metadata && !!mediaResource && !!mediaResource.media_src
}

// -------------------- Type Definitions --------------------
export interface PropertyModel {
  name: string
  relationship?: string
  toRole?: string
  [key: string]: any
}

export interface MetadataSchema {
  namespace?: string
  association?: any[]
  entityContainer?: any
  [key: string]: any
}

export type NavigationPropertyKind = 'deferred' | 'entry' | 'feed' | null

// -------------------- Primitive Checks --------------------
/** Tests whether a value is a primitive type value. Date objects are considered primitive. */
export function isPrimitive(value: any): boolean {
  return (
    isDate(value) ||
    typeof value === 'string' ||
    typeof value === 'number' ||
    typeof value === 'boolean'
  )
}

/** Checks whether the specified type name is a primitive EDM type. */
export function isPrimitiveEdmType(typeName: string): boolean {
  return contains(primitiveEdmTypes, typeName)
}

// -------------------- Navigation Property --------------------
/** Gets the kind of a navigation property value. */
export function navigationPropertyKind(
  value: any,
  propertyModel?: PropertyModel
): NavigationPropertyKind {
  if (isDeferred(value)) return 'deferred'
  if (isEntry(value)) return 'entry'
  if (isFeed(value)) return 'feed'
  if (propertyModel && propertyModel.relationship) {
    if (value === null || value === undefined || !isFeed(value)) return 'entry'
    return 'feed'
  }
  return null
}

// -------------------- Lookup Utilities --------------------
/** Looks up a property by name. */
export function lookupProperty(
  properties: PropertyModel[] | null,
  name: string
): PropertyModel | null {
  return find(properties, (property) => property.name === name) || null
}

/** Looks up a type object by name. */
export function lookupInMetadata(
  name: string | null | undefined,
  metadata: MetadataSchema | MetadataSchema[],
  kind: string
): any {
  return name
    ? forEachSchema(metadata, (schema) => lookupInSchema(name, schema, kind))
    : null
}

/** Looks up an entity set by name. */
export function lookupEntitySet(entitySets: any[] | null, name: string): any {
  return find(entitySets, (entitySet) => entitySet.name === name) || null
}

/** Looks up a singleton by name. */
export function lookupSingleton(singletons: any[] | null, name: string): any {
  return find(singletons, (singleton) => singleton.name === name) || null
}

/** Looks up a complex type object by name. */
export function lookupComplexType(
  name: string,
  metadata: MetadataSchema | MetadataSchema[]
): any {
  return lookupInMetadata(name, metadata, 'complexType')
}

/** Looks up an entity type object by name. */
export function lookupEntityType(
  name: string,
  metadata: MetadataSchema | MetadataSchema[]
): any {
  return lookupInMetadata(name, metadata, 'entityType')
}

/** Looks up the default entity container in metadata. */
export function lookupDefaultEntityContainer(
  metadata: MetadataSchema | MetadataSchema[]
): any {
  return forEachSchema(
    metadata,
    (schema) => isObject(schema.entityContainer) && schema.entityContainer
  )
}

/** Looks up an entity container by name. */
export function lookupEntityContainer(
  name: string,
  metadata: MetadataSchema | MetadataSchema[]
): any {
  return lookupInMetadata(name, metadata, 'entityContainer')
}

/** Looks up a function import by name. */
export function lookupFunctionImport(
  functionImports: any[] | null,
  name: string
): any {
  return find(functionImports, (fnImport) => fnImport.name === name) || null
}

export function lookupNavigationPropertyType(
  navigationProperty: PropertyModel,
  metadata: MetadataSchema | MetadataSchema[]
): string | null {
  let result: string | null = null
  if (!navigationProperty) return null

  const rel = navigationProperty.relationship
  const association = forEachSchema(metadata, (schema) => {
    const nameOnly = removeNamespace(schema.namespace, rel)
    const associations = schema.association
    if (nameOnly && associations) {
      for (let i = 0, len = associations.length; i < len; i++) {
        if (associations[i].name === nameOnly) return associations[i]
      }
    }
    return null
  })

  if (association) {
    let end = association.end[0]
    if (end.role !== navigationProperty.toRole) {
      end = association.end[1]
    }
    result = end.type
  }

  return result
}

// -------------------- Type Definitions --------------------
export interface EntitySetInfo {
  entitySet: any
  containerName: string
  functionImport?: any[]
}

export interface Request {
  method?: string
  headers?: Record<string, any>
  data?: any
  body?: any
  async?: boolean
}

export interface Timezone {
  d: 1 | -1
  h: number
  m: number
}

export interface Duration {
  ms: number
  __edmType: 'Edm.Time'
  ns?: number
}

// -------------------- EntitySet & Schema --------------------
/** Gets the entitySet info, container name and functionImports for an entitySet */
export function getEntitySetInfo(
  entitySetName: string,
  metadata: any[]
): EntitySetInfo | null {
  return forEachSchema(metadata, (schema) => {
    const container = schema.entityContainer
    const entitySets = container?.entitySet
    if (entitySets) {
      for (const entitySet of entitySets) {
        if (entitySet.name === entitySetName) {
          return {
            entitySet,
            containerName: container.name,
            functionImport: container.functionImport,
          }
        }
      }
    }
    return null
  })
}

/** Removes a namespace prefix from a qualified name */
export function removeNamespace(
  ns: string,
  fullName: string | undefined
): string | null {
  if (fullName?.indexOf(ns) === 0 && fullName?.charAt(ns.length) === '.') {
    return fullName.slice(ns.length + 1)
  }
  return null
}

/** Looks up a schema object by name and kind */
export function lookupInSchema(
  name: string,
  schema: any,
  kind: string
): any | null {
  if (name && schema) {
    const nameOnly = removeNamespace(schema.namespace, name)
    if (nameOnly) {
      return find(schema[kind], (item) => item.name === nameOnly) || null
    }
  }
  return null
}

/** Compares version strings and returns the higher one */
export function maxVersion(left: string, right: string): string {
  if (left === right) return left
  const leftParts = left.split('.')
  const rightParts = right.split('.')
  const len = Math.max(leftParts.length, rightParts.length)
  for (let i = 0; i < len; i++) {
    const l = leftParts[i] ? Number.parseInt(leftParts[i], 10) : 0
    const r = rightParts[i] ? Number.parseInt(rightParts[i], 10) : 0
    if (l > r) return left
    if (l < r) return right
  }
  return left
}

// -------------------- Headers --------------------
export const normalHeaders: Record<string, string> = {
  'content-type': 'Content-Type',
  'content-encoding': 'Content-Encoding',
  'content-length': 'Content-Length',
  'odata-version': 'OData-Version',
  accept: 'Accept',
  'accept-charset': 'Accept-Charset',
  'if-match': 'If-Match',
  'if-none-match': 'If-None-Match',
  'odata-isolation': 'OData-Isolation',
  'odata-maxversion': 'OData-MaxVersion',
  prefer: 'Prefer',
  'content-id': 'Content-ID',
  'content-transfer-encoding': 'Content-Transfer-Encoding',
  etag: 'ETag',
  location: 'Location',
  'odata-entityid': 'OData-EntityId',
  'preference-applied': 'Preference-Applied',
  'retry-after': 'Retry-After',
}

/** Normalizes headers to consistent casing */
export function normalizeHeaders(headers: Record<string, any>) {
  for (const name in headers) {
    const lowerName = name.toLowerCase()
    const normalName = normalHeaders[lowerName]
    if (normalName && name !== normalName) {
      const val = headers[name]
      delete headers[name]
      headers[normalName] = val
    }
  }
}

// -------------------- Boolean Parsing --------------------
export function parseBool(value: any): boolean {
  if (typeof value === 'boolean') return value
  return typeof value === 'string' && value.toLowerCase() === 'true'
}

export function parseDate(
  propertyValue: string,
  nullOnError = false
): Date | null {
  const parts = propertyValue.split('-')
  if (parts.length !== 3 && nullOnError) return null
  return new Date(
    Number.parseInt(parts[0], 10),
    Number.parseInt(parts[1], 10) - 1,
    Number.parseInt(parts[2], 10)
  )
}

const parseTimeOfDayRE = /^(\d+):(\d+)(:(\d+)(.(\d+))?)?$/
export function parseTimeOfDay(propertyValue: string) {
  const parts = parseTimeOfDayRE.exec(propertyValue)
  return {
    h: Number.parseInt(parts?.[1] || '0', 10),
    m: Number.parseInt(parts?.[2] || '0', 10),
    s: Number.parseInt(parts?.[4] || '0', 10),
    ms: Number.parseInt(parts?.[6] || '0', 10),
  }
}
const parseTimeRE =
  /^([+-])?P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)(?:\.(\d+))?S)?)?/

export function parseDuration(duration: string): Duration {
  const parts = parseTimeRE.exec(duration)
  if (!parts) throw new Error('Invalid duration value.')
  const years = parts[2] || '0'
  const months = parts[3] || '0'
  if (years !== '0' || months !== '0')
    throw new Error('Unsupported duration value.')
  const days = Number.parseInt(parts[4] || '0', 10)
  const hours = Number.parseInt(parts[5] || '0', 10)
  const minutes = Number.parseInt(parts[6] || '0', 10)
  const seconds = Number.parseFloat(parts[7] || '0')

  const ms = Number.parseInt(parts[8] || '0', 10)
  return {
    ms:
      ms + seconds * 1000 + minutes * 60000 + hours * 3600000 + days * 86400000,
    __edmType: 'Edm.Time',
  }
}

export function parseTimezone(timezone: string): Timezone {
  const direction = timezone[0] === '+' ? 1 : -1
  const [hours, minutes] = timezone
    .slice(1)
    .split(':')
    .map((v) => Number.parseInt(v, 10))
  return { d: direction as 1 | -1, h: hours, m: minutes }
}

// -------------------- Request Preparation --------------------
export function prepareRequest(request: Request, handler: any, context: any) {
  request.method ||= 'GET'
  request.headers ||= {}
  normalizeHeaders(request.headers)
  request.headers.Accept ||= handler.accept
  if (request.data && request.body === undefined)
    handler.write(request, context)
  request.headers['OData-MaxVersion'] ||= handler.maxDataServiceVersion || '4.0'
  request.async ??= true
}
