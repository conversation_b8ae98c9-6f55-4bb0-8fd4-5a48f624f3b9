export const proTabConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  apis: {
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
    request: {
      url: '/modeling/202512/OdataService/NeueCadParts',
      method: 'get',
      query: {
        // expand: 'Category',
      },
    },
  },
  elements: [
    {
      id: 'pro-tab-123',
      type: 'pro-tab',
      name: 'pro-tab',
      props: {
        tabProps: {
          tabPosition: 'left',
        },
        tabs: [
          {
            name: 'tab1',
            label: '标签一',
            elements: [
              {
                id: 'odata-card123',
                type: 'card',
                name: 'OData表格',
                elements: [
                  {
                    id: 'odata-table',
                    type: 'pro-table',
                    name: 'OData123456',
                    props: {
                      entityType: 'NeueCadAsm',
                      columns: [
                        { type: 'selection', with: 55 },
                        {
                          prop: 'ncid',
                          label: 'ncid',
                        },
                        {
                          prop: 'code',
                          label: 'code',
                        },
                        {
                          prop: 'schemaVersion',
                          label: 'schemaVersion',
                        },
                        {
                          prop: 'lifecycleState',
                          label: 'lifecycleState',
                          fieldProps: {
                            multiple: true,
                          },
                        },
                        {
                          prop: 'lifecycleNote',
                          label: 'lifecycleNote',
                        },
                        {
                          prop: 'volume',
                          label: 'volume',
                          fieldProps: {
                            controlsPosition: 'right',
                          },
                        },
                        {
                          prop: 'operation',
                          label: '操作',
                          actions: [
                            {
                              text: '新增',
                              // type: 'el-button',
                              props: {
                                type: 'primary',
                              },
                              config: {
                                actionType: 'open',
                                target: 'DialogForm_er4mn6jbtk',
                              },
                            },
                            {
                              text: '编辑',
                              // type: 'el-button',
                              props: {
                                type: 'primary',
                              },
                              config: {
                                actionType: 'open',
                                target: 'DialogForm_er4mn6jbtk',
                              },
                            },
                            {
                              icon: 'Edit',
                              // type: 'el-icon',
                              config: {
                                actionType: 'open',
                                actionName: '打开drawer',
                                target: 'Drawer_er4mn6jbtk',
                              },
                            },
                          ],
                        },
                      ],
                      toolbar: {
                        header: [
                          {
                            text: '操作一新增',
                            props: {
                              type: 'primary',
                            },
                            config: {
                              actionType: 'open',
                              target: 'DialogForm_er4mn6jbtk',
                            },
                          },
                          {
                            text: '操作二打开侧边栏',
                            props: {
                              type: 'success',
                            },
                            config: {
                              actionType: 'open',
                              target: 'Drawer_er4mn6jbtk',
                            },
                          },
                          {
                            text: '操作三',
                            props: {
                              type: 'info',
                            },
                            config: {
                              actionType: 'open',
                              target: 'Drawer_er4mn6jbtk',
                            },
                          },
                        ],
                        footer: [
                          {
                            text: '操作一',
                            props: {
                              type: 'primary',
                            },
                            config: {},
                          },
                          {
                            text: '操作二',
                            props: {
                              type: 'success',
                            },
                            config: {
                              actionType: 'open',
                              target: 'Drawer_er4mn6jbtk',
                            },
                          },
                          {
                            text: '操作三',
                            props: {
                              type: 'info',
                            },
                            config: {
                              actionType: 'open',
                              target: 'Drawer_er4mn6jbtk',
                            },
                          },
                        ],
                      },
                    },
                    events: [],
                    api: {},
                  },
                ],
              },
            ],
          },
          {
            name: 'tab2',
            label: '标签二',
            elements: [],
          },
        ],
      },
    },
  ],
}
