/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EntityContainers } from './EntityContainers';

/**
 * Request builder class for operations supported on the {@link EntityContainers} entity.
 */
export class EntityContainersRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntityContainers<T>, T> {
  /**
   * Returns a request builder for querying all `EntityContainers` entities.
   * @returns A request builder for creating requests to retrieve all `EntityContainers` entities.
   */
  getAll(): GetAllRequestBuilder<EntityContainers<T>, T> {
    return new GetAllRequestBuilder<EntityContainers<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `EntityContainers` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntityContainers`.
   */
  create(
    entity: EntityContainers<T>
  ): CreateRequestBuilder<EntityContainers<T>, T> {
    return new CreateRequestBuilder<EntityContainers<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `EntityContainers` entity based on its keys.
   * @param ncid Key property. See {@link EntityContainers.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntityContainers` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntityContainers<T>, T> {
    return new GetByKeyRequestBuilder<EntityContainers<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `EntityContainers`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntityContainers`.
   */
  update(
    entity: EntityContainers<T>
  ): UpdateRequestBuilder<EntityContainers<T>, T> {
    return new UpdateRequestBuilder<EntityContainers<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `EntityContainers`.
   * @param ncid Key property. See {@link EntityContainers.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntityContainers`.
   */
  delete(ncid: string): DeleteRequestBuilder<EntityContainers<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntityContainers`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntityContainers` by taking the entity as a parameter.
   */
  delete(
    entity: EntityContainers<T>
  ): DeleteRequestBuilder<EntityContainers<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<EntityContainers<T>, T> {
    return new DeleteRequestBuilder<EntityContainers<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntityContainers
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
