<template>
  <widget-wrapper>
    <div class="button-group">
      <el-button type="primary" @click="toggleRowExpansion">
        {{ !isExpand ? '展开' : '收起' }}
      </el-button>
    </div>
    <el-table
      ref="tableRef"
      :data="dataSource"
      :row-key="rowKey"
      :tree-props="treeProps"
      @row-click="rowClick"
    >
      <el-table-column
        v-for="item in columns"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :sortable="item.sortable"
      >
        <template #default="scope">
          <NeAvatar
            v-if="item.valueType === 'avatar'"
            :name="scope.row[item.prop]"
          />
          <el-tag
            v-else-if="item.valueEnum"
            :type="item.valueEnum[scope.row[item.prop]]?.status"
          >
            {{
              item.valueEnum[scope.row[item.prop]]?.text || scope.row[item.prop]
            }}
          </el-tag>

          <template v-else-if="item.valueType === 'dateTime'">{{
            dayjs(scope.row[item.prop]).format(
              item.format || 'YYYY-MM-DD HH:mm:ss'
            )
          }}</template>
          <template v-else>{{ scope.row[item.prop] }}</template>
        </template>
      </el-table-column>
    </el-table>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import NeAvatar from '@neue-plus/components/avatar'
import type { ValueEnum, ValueType } from '@neue-plus/components/pro-table'

defineOptions({
  name: 'BomTable',
  inheritAttrs: false,
})

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  columns: {
    type: Array<{
      prop: string
      label: string
      width?: string
      valueType?: ValueType
      valueEnum?: ValueEnum
      format?: string
      sortable?: boolean
    }>,
    required: true,
  },
  treeProps: {
    type: Object,
    default: () => ({
      checkStrictly: false,
      children: 'children',
      hasChildren: 'hasChildren',
    }),
  },
  rowKey: {
    type: String,
    default: 'id',
  },
})
const isExpand = ref(false)
const tableRef = ref()
const emit = defineEmits(['rowClick'])
const dataSource = ref<any[]>(props.data)
const toggleRowExpansion = () => {
  isExpand.value = !isExpand.value
  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      tableRef.value.toggleRowExpansion(node, isExpand.value)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  expandRecursive(dataSource.value)
}
watch(
  () => props.data,
  (newData) => {
    dataSource.value = newData
  }
)
function rowClick(row: any) {
  emit('rowClick', row)
}
defineExpose({
  refresh() {},
  describe: 'describe', //保留字段
})
async function fetchData() {
  // const res = await executeApi(api, ctx)
}

onMounted(() => {
  fetchData()
})
</script>
