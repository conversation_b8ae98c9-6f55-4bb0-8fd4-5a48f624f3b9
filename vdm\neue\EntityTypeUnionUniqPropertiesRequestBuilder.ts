/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EntityTypeUnionUniqProperties } from './EntityTypeUnionUniqProperties';

/**
 * Request builder class for operations supported on the {@link EntityTypeUnionUniqProperties} entity.
 */
export class EntityTypeUnionUniqPropertiesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
  /**
   * Returns a request builder for querying all `EntityTypeUnionUniqProperties` entities.
   * @returns A request builder for creating requests to retrieve all `EntityTypeUnionUniqProperties` entities.
   */
  getAll(): GetAllRequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
    return new GetAllRequestBuilder<EntityTypeUnionUniqProperties<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `EntityTypeUnionUniqProperties` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntityTypeUnionUniqProperties`.
   */
  create(
    entity: EntityTypeUnionUniqProperties<T>
  ): CreateRequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
    return new CreateRequestBuilder<EntityTypeUnionUniqProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `EntityTypeUnionUniqProperties` entity based on its keys.
   * @param ncid Key property. See {@link EntityTypeUnionUniqProperties.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntityTypeUnionUniqProperties` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
    return new GetByKeyRequestBuilder<EntityTypeUnionUniqProperties<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `EntityTypeUnionUniqProperties`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntityTypeUnionUniqProperties`.
   */
  update(
    entity: EntityTypeUnionUniqProperties<T>
  ): UpdateRequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
    return new UpdateRequestBuilder<EntityTypeUnionUniqProperties<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `EntityTypeUnionUniqProperties`.
   * @param ncid Key property. See {@link EntityTypeUnionUniqProperties.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypeUnionUniqProperties`.
   */
  delete(
    ncid: string
  ): DeleteRequestBuilder<EntityTypeUnionUniqProperties<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntityTypeUnionUniqProperties`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypeUnionUniqProperties` by taking the entity as a parameter.
   */
  delete(
    entity: EntityTypeUnionUniqProperties<T>
  ): DeleteRequestBuilder<EntityTypeUnionUniqProperties<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<EntityTypeUnionUniqProperties<T>, T> {
    return new DeleteRequestBuilder<EntityTypeUnionUniqProperties<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntityTypeUnionUniqProperties
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
