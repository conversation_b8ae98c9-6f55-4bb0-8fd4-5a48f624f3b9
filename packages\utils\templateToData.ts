/**
 * 渲染模板字符串
 * @param templateStr 模板字符串，例如 "{{row.version.revision.revisionCode}}.{{row.version.versionNumber}} 或者 {{row.version.revision.revisionCode}}==2"
 * @param context 上下文对象，例如 { row: {...} }
 * @returns 渲染后的字符串，如果出错返回空字符串
 */
export function templateToData(
  templateStr: string,
  context: Record<string, any>
): any {
  let rendered = ''
  try {
    const template = (window as any).Handlebars.compile(templateStr)
    rendered = template(context).trim()
    try {
      return new Function(`return ${rendered}`)()
    } catch {
      return rendered
    }
  } catch (e) {
    console.error('❌ 模板渲染出错:', e)
    return '模板渲染出错'
  }
}
