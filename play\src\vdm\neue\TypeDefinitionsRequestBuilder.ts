/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { TypeDefinitions } from './TypeDefinitions';

/**
 * Request builder class for operations supported on the {@link TypeDefinitions} entity.
 */
export class TypeDefinitionsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<TypeDefinitions<T>, T> {
  /**
   * Returns a request builder for querying all `TypeDefinitions` entities.
   * @returns A request builder for creating requests to retrieve all `TypeDefinitions` entities.
   */
  getAll(): GetAllRequestBuilder<TypeDefinitions<T>, T> {
    return new GetAllRequestBuilder<TypeDefinitions<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `TypeDefinitions` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `TypeDefinitions`.
   */
  create(
    entity: TypeDefinitions<T>
  ): CreateRequestBuilder<TypeDefinitions<T>, T> {
    return new CreateRequestBuilder<TypeDefinitions<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `TypeDefinitions` entity based on its keys.
   * @param ncid Key property. See {@link TypeDefinitions.ncid}.
   * @returns A request builder for creating requests to retrieve one `TypeDefinitions` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<TypeDefinitions<T>, T> {
    return new GetByKeyRequestBuilder<TypeDefinitions<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `TypeDefinitions`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `TypeDefinitions`.
   */
  update(
    entity: TypeDefinitions<T>
  ): UpdateRequestBuilder<TypeDefinitions<T>, T> {
    return new UpdateRequestBuilder<TypeDefinitions<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `TypeDefinitions`.
   * @param ncid Key property. See {@link TypeDefinitions.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `TypeDefinitions`.
   */
  delete(ncid: string): DeleteRequestBuilder<TypeDefinitions<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `TypeDefinitions`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `TypeDefinitions` by taking the entity as a parameter.
   */
  delete(
    entity: TypeDefinitions<T>
  ): DeleteRequestBuilder<TypeDefinitions<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<TypeDefinitions<T>, T> {
    return new DeleteRequestBuilder<TypeDefinitions<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof TypeDefinitions
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
