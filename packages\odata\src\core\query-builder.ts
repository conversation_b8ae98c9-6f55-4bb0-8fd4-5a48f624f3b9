/**
 * OData 查询构建器
 */

import { OlingoWrapper } from './olingo-wrapper'
import type {
  QueryBuilder as IQueryBuilder,
  ODataQueryOptions,
  ODataResponse,
  Response,
} from '../types'

/**
 * OData 查询构建器实现
 */
export class QueryBuilder<T = any> implements IQueryBuilder<T> {
  private options: ODataQueryOptions = {}
  private entitySetPath: string
  private client: OlingoWrapper

  constructor(entitySetPath: string, client: OlingoWrapper) {
    this.entitySetPath = entitySetPath
    this.client = client
  }

  /**
   * 选择字段
   */
  select(...fields: string[]): QueryBuilder<T> {
    if (fields.length > 0) {
      this.options.$select = [...(this.options.$select || []), ...fields]
    }
    return this
  }

  /**
   * 展开关联实体
   */
  expand(...relations: string[]): QueryBuilder<T> {
    if (relations.length > 0) {
      this.options.$expand = [...(this.options.$expand || []), ...relations]
    }
    return this
  }

  /**
   * 过滤条件
   */
  filter(condition: string): QueryBuilder<T> {
    if (this.options.$filter) {
      this.options.$filter = `(${this.options.$filter}) and (${condition})`
    } else {
      this.options.$filter = condition
    }
    return this
  }

  /**
   * 排序
   */
  orderBy(field: string, direction: 'asc' | 'desc' = 'asc'): QueryBuilder<T> {
    const orderClause = `${field} ${direction}`
    if (this.options.$orderby) {
      this.options.$orderby = `${this.options.$orderby}, ${orderClause}`
    } else {
      this.options.$orderby = orderClause
    }
    return this
  }

  /**
   * 限制返回数量
   */
  top(count: number): QueryBuilder<T> {
    this.options.$top = count
    return this
  }

  /**
   * 跳过指定数量
   */
  skip(count: number): QueryBuilder<T> {
    this.options.$skip = count
    return this
  }

  /**
   * 包含总数
   */
  count(include: boolean = true): QueryBuilder<T> {
    this.options.$count = include
    return this
  }

  /**
   * 搜索
   */
  search(term: string): QueryBuilder<T> {
    this.options.$search = term
    return this
  }

  /**
   * 执行查询并返回 OData 响应
   */
  async execute(): Promise<ODataResponse<T>> {
    const response = await this.executeRaw()
    return response.data
  }

  /**
   * 执行查询并返回原始 HTTP 响应
   */
  async executeRaw(): Promise<Response<ODataResponse<T>>> {
    return this.client.get<T>(this.entitySetPath, this.options)
  }

  /**
   * 克隆查询构建器
   */
  clone(): QueryBuilder<T> {
    const cloned = new QueryBuilder<T>(this.entitySetPath, this.client)
    cloned.options = { ...this.options }

    // 深拷贝数组
    if (this.options.$select) {
      cloned.options.$select = [...this.options.$select]
    }
    if (this.options.$expand) {
      cloned.options.$expand = [...this.options.$expand]
    }

    return cloned
  }

  /**
   * 重置查询选项
   */
  reset(): QueryBuilder<T> {
    this.options = {}
    return this
  }

  /**
   * 获取当前查询选项
   */
  getOptions(): ODataQueryOptions {
    return { ...this.options }
  }

  /**
   * 设置查询选项
   */
  setOptions(options: ODataQueryOptions): QueryBuilder<T> {
    this.options = { ...options }
    return this
  }

  /**
   * 构建查询字符串
   */
  buildQueryString(): string {
    const params = new URLSearchParams()

    if (this.options.$select?.length) {
      params.append('$select', this.options.$select.join(','))
    }

    if (this.options.$expand?.length) {
      params.append('$expand', this.options.$expand.join(','))
    }

    if (this.options.$filter) {
      params.append('$filter', this.options.$filter)
    }

    if (this.options.$orderby) {
      params.append('$orderby', this.options.$orderby)
    }

    if (typeof this.options.$top === 'number') {
      params.append('$top', this.options.$top.toString())
    }

    if (typeof this.options.$skip === 'number') {
      params.append('$skip', this.options.$skip.toString())
    }

    if (this.options.$count) {
      params.append('$count', 'true')
    }

    if (this.options.$search) {
      params.append('$search', this.options.$search)
    }

    if (this.options.$format) {
      params.append('$format', this.options.$format)
    }

    return params.toString()
  }

  /**
   * 获取完整的查询 URL
   */
  getUrl(): string {
    const queryString = this.buildQueryString()
    return queryString
      ? `${this.entitySetPath}?${queryString}`
      : this.entitySetPath
  }
}

/**
 * 创建查询构建器的工厂函数
 */
export function createQueryBuilder<T = any>(
  entitySetPath: string,
  client: OlingoWrapper
): QueryBuilder<T> {
  return new QueryBuilder<T>(entitySetPath, client)
}
