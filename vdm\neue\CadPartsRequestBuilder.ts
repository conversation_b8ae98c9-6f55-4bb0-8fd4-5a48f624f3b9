/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { CadParts } from './CadParts';

/**
 * Request builder class for operations supported on the {@link CadParts} entity.
 */
export class CadPartsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<CadParts<T>, T> {
  /**
   * Returns a request builder for querying all `CadParts` entities.
   * @returns A request builder for creating requests to retrieve all `CadParts` entities.
   */
  getAll(): GetAllRequestBuilder<CadParts<T>, T> {
    return new GetAllRequestBuilder<CadParts<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `CadParts` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `CadParts`.
   */
  create(entity: CadParts<T>): CreateRequestBuilder<CadParts<T>, T> {
    return new CreateRequestBuilder<CadParts<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `CadParts` entity based on its keys.
   * @param ncid Key property. See {@link CadParts.ncid}.
   * @returns A request builder for creating requests to retrieve one `CadParts` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<CadParts<T>, T> {
    return new GetByKeyRequestBuilder<CadParts<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `CadParts`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `CadParts`.
   */
  update(entity: CadParts<T>): UpdateRequestBuilder<CadParts<T>, T> {
    return new UpdateRequestBuilder<CadParts<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `CadParts`.
   * @param ncid Key property. See {@link CadParts.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `CadParts`.
   */
  delete(ncid: string): DeleteRequestBuilder<CadParts<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `CadParts`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `CadParts` by taking the entity as a parameter.
   */
  delete(entity: CadParts<T>): DeleteRequestBuilder<CadParts<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<CadParts<T>, T> {
    return new DeleteRequestBuilder<CadParts<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof CadParts ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
