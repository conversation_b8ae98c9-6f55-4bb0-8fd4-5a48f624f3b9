/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { ComplexTypes } from './ComplexTypes';

/**
 * Request builder class for operations supported on the {@link ComplexTypes} entity.
 */
export class ComplexTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<ComplexTypes<T>, T> {
  /**
   * Returns a request builder for querying all `ComplexTypes` entities.
   * @returns A request builder for creating requests to retrieve all `ComplexTypes` entities.
   */
  getAll(): GetAllRequestBuilder<ComplexTypes<T>, T> {
    return new GetAllRequestBuilder<ComplexTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `ComplexTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `ComplexTypes`.
   */
  create(entity: ComplexTypes<T>): CreateRequestBuilder<ComplexTypes<T>, T> {
    return new CreateRequestBuilder<ComplexTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `ComplexTypes` entity based on its keys.
   * @param ncid Key property. See {@link ComplexTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `ComplexTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<ComplexTypes<T>, T> {
    return new GetByKeyRequestBuilder<ComplexTypes<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `ComplexTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `ComplexTypes`.
   */
  update(entity: ComplexTypes<T>): UpdateRequestBuilder<ComplexTypes<T>, T> {
    return new UpdateRequestBuilder<ComplexTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `ComplexTypes`.
   * @param ncid Key property. See {@link ComplexTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `ComplexTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<ComplexTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `ComplexTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `ComplexTypes` by taking the entity as a parameter.
   */
  delete(entity: ComplexTypes<T>): DeleteRequestBuilder<ComplexTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<ComplexTypes<T>, T> {
    return new DeleteRequestBuilder<ComplexTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof ComplexTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
