/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { ActionImports } from './ActionImports';

/**
 * Request builder class for operations supported on the {@link ActionImports} entity.
 */
export class ActionImportsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<ActionImports<T>, T> {
  /**
   * Returns a request builder for querying all `ActionImports` entities.
   * @returns A request builder for creating requests to retrieve all `ActionImports` entities.
   */
  getAll(): GetAllRequestBuilder<ActionImports<T>, T> {
    return new GetAllRequestBuilder<ActionImports<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `ActionImports` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `ActionImports`.
   */
  create(entity: ActionImports<T>): CreateRequestBuilder<ActionImports<T>, T> {
    return new CreateRequestBuilder<ActionImports<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `ActionImports` entity based on its keys.
   * @param ncid Key property. See {@link ActionImports.ncid}.
   * @returns A request builder for creating requests to retrieve one `ActionImports` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<ActionImports<T>, T> {
    return new GetByKeyRequestBuilder<ActionImports<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `ActionImports`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `ActionImports`.
   */
  update(entity: ActionImports<T>): UpdateRequestBuilder<ActionImports<T>, T> {
    return new UpdateRequestBuilder<ActionImports<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `ActionImports`.
   * @param ncid Key property. See {@link ActionImports.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `ActionImports`.
   */
  delete(ncid: string): DeleteRequestBuilder<ActionImports<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `ActionImports`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `ActionImports` by taking the entity as a parameter.
   */
  delete(entity: ActionImports<T>): DeleteRequestBuilder<ActionImports<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<ActionImports<T>, T> {
    return new DeleteRequestBuilder<ActionImports<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof ActionImports
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
