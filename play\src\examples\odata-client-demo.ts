/**
 * SAP Cloud SDK OData 客户端使用示例
 * 展示如何使用生成的 OData 客户端进行 CRUD 操作
 */

import { neue } from '../vdm/neue/service'
import type { CadParts, BusinessObjectRevisionNumberRules } from '../vdm/neue'
import { LifecycleState, PartType } from '../vdm/neue'

// 模拟的 destination 配置（用于演示）
const mockDestination = {
  url: 'https://mock-odata-service.example.com',
  headers: {
    'Content-Type': 'application/json',
  },
}

// 初始化服务
const service = neue()

/**
 * 示例 1: 基本查询操作
 */
export async function basicQueryExample() {
  console.log('🔍 示例 1: 基本查询操作')

  try {
    const { cadPartsApi } = service

    // 获取所有 CAD 零件（限制前 10 个）
    const allParts = await cadPartsApi
      .requestBuilder()
      .getAll()
      .top(10)
      .execute(mockDestination)

    console.log(`✅ 获取到 ${allParts.length} 个 CAD 零件`)

    // 显示第一个零件的信息
    if (allParts.length > 0) {
      const firstPart = allParts[0]
      console.log('📋 第一个零件信息:')
      console.log(`   - 代码: ${firstPart.code}`)
      console.log(`   - 名称: ${firstPart.name}`)
      console.log(`   - 类型: ${firstPart.partType}`)
      console.log(`   - 生命周期状态: ${firstPart.lifecycleState}`)
    }

    return allParts
  } catch (error) {
    console.error('❌ 基本查询失败:', error)
    throw error
  }
}

/**
 * 示例 2: 条件查询和过滤
 */
export async function filterQueryExample() {
  console.log('🔍 示例 2: 条件查询和过滤')

  try {
    const { cadPartsApi } = service

    // 查询特定类型的零件
    const filteredParts = await cadPartsApi
      .requestBuilder()
      .getAll()
      .filter(cadPartsApi.schema.PART_TYPE.equals(PartType.NEUE_ASM))
      .top(5)
      .execute(mockDestination)

    console.log(`✅ 找到 ${filteredParts.length} 个装配件`)

    // 查询特定代码的零件
    const specificPart = await cadPartsApi
      .requestBuilder()
      .getAll()
      .filter(cadPartsApi.schema.CODE.equals('DEMO001'))
      .execute(mockDestination)

    console.log(`✅ 特定代码查询结果: ${specificPart.length} 个`)

    return { filteredParts, specificPart }
  } catch (error) {
    console.error('❌ 条件查询失败:', error)
    throw error
  }
}

/**
 * 示例 3: 单个实体查询
 */
export async function singleEntityExample() {
  console.log('🔍 示例 3: 单个实体查询')

  try {
    const { cadPartsApi } = service

    // 通过 ID 获取单个零件
    const partId = 'demo-part-id'
    const singlePart = await cadPartsApi
      .requestBuilder()
      .getByKey(partId)
      .execute(mockDestination)

    console.log('✅ 获取单个零件成功')
    console.log(`   - ID: ${singlePart.ncid}`)
    console.log(`   - 代码: ${singlePart.code}`)

    return singlePart
  } catch (error) {
    console.error('❌ 单个实体查询失败:', error)
    // 这里可能是因为 ID 不存在，这是正常的
    return null
  }
}

/**
 * 示例 4: 创建新实体
 */
export async function createEntityExample() {
  console.log('🔧 示例 4: 创建新实体')

  try {
    const { cadPartsApi } = service

    // 创建新的 CAD 零件
    const newPart = cadPartsApi.entityBuilder().build()
    newPart.code = `DEMO_${Date.now()}`
    newPart.name = '演示零件'
    newPart.partType = PartType.NEUE_PRT
    newPart.lifecycleState = LifecycleState.CREATING
    newPart.description = '这是一个通过 SAP Cloud SDK 创建的演示零件'

    // 注意：这里只是演示，实际执行需要有效的后端服务
    console.log('📋 准备创建的零件:')
    console.log(`   - 代码: ${newPart.code}`)
    console.log(`   - 名称: ${newPart.name}`)
    console.log(`   - 类型: ${newPart.partType}`)

    // 实际创建（需要有效的后端）
    // const createdPart = await cadPartsApi
    //   .requestBuilder()
    //   .create(newPart)
    //   .execute()

    console.log('ℹ️  创建操作已准备就绪（需要有效的后端服务）')

    return newPart
  } catch (error) {
    console.error('❌ 创建实体失败:', error)
    throw error
  }
}

/**
 * 示例 5: 更新实体
 */
export async function updateEntityExample() {
  console.log('🔧 示例 5: 更新实体')

  try {
    const { cadPartsApi } = service

    // 首先获取一个现有的零件
    const parts = await cadPartsApi
      .requestBuilder()
      .getAll()
      .top(1)
      .execute(mockDestination)

    if (parts.length === 0) {
      console.log('ℹ️  没有找到可更新的零件')
      return null
    }

    const partToUpdate = parts[0]

    // 更新零件信息
    const updatedPart = cadPartsApi.entityBuilder().build()
    Object.assign(updatedPart, partToUpdate)
    updatedPart.description = `更新于 ${new Date().toISOString()}`

    console.log('📋 准备更新的零件:')
    console.log(`   - ID: ${updatedPart.ncid}`)
    console.log(`   - 新描述: ${updatedPart.description}`)

    // 实际更新（需要有效的后端）
    // const result = await cadPartsApi
    //   .requestBuilder()
    //   .update(updatedPart)
    //   .execute()

    console.log('ℹ️  更新操作已准备就绪（需要有效的后端服务）')

    return updatedPart
  } catch (error) {
    console.error('❌ 更新实体失败:', error)
    throw error
  }
}

/**
 * 示例 6: 批处理操作
 */
export async function batchOperationExample() {
  console.log('🔧 示例 6: 批处理操作')

  try {
    const { batch, changeset } = await import('../vdm/neue/BatchRequest')
    const { cadPartsApi } = service

    // 创建批处理请求
    const batchRequest = batch(
      // 查询操作
      cadPartsApi.requestBuilder().getAll().top(5),

      // 变更集（创建、更新、删除操作）
      changeset()
      // 这里可以添加多个变更操作
      // cadPartsApi.requestBuilder().create(newPart1),
      // cadPartsApi.requestBuilder().update(updatedPart),
    )

    console.log('📋 批处理请求已准备就绪')
    console.log('ℹ️  批处理操作需要有效的后端服务支持')

    // 实际执行批处理
    // const batchResult = await batchRequest.execute()

    return batchRequest
  } catch (error) {
    console.error('❌ 批处理操作失败:', error)
    throw error
  }
}

/**
 * 示例 7: 业务对象版本号规则查询
 */
export async function businessObjectRulesExample() {
  console.log('🔍 示例 7: 业务对象版本号规则查询')

  try {
    const { businessObjectRevisionNumberRulesApi } = service

    // 查询所有版本号规则
    const rules = await businessObjectRevisionNumberRulesApi
      .requestBuilder()
      .getAll()
      .filter(
        businessObjectRevisionNumberRulesApi.schema.IS_ACTIVE.equals(true)
      )
      .execute(mockDestination)

    console.log(`✅ 找到 ${rules.length} 个活跃的版本号规则`)

    rules.forEach((rule, index) => {
      console.log(`📋 规则 ${index + 1}:`)
      console.log(`   - 规则代码: ${rule.ruleCode}`)
      console.log(`   - 名称: ${rule.name}`)
      console.log(`   - 是否启用: ${rule.isActive}`)
      console.log(`   - 是否使用: ${rule.isEmployed}`)
    })

    return rules
  } catch (error) {
    console.error('❌ 业务对象规则查询失败:', error)
    throw error
  }
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  console.log('🚀 开始运行 SAP Cloud SDK OData 客户端示例')
  console.log('='.repeat(50))

  const examples = [
    basicQueryExample,
    filterQueryExample,
    singleEntityExample,
    createEntityExample,
    updateEntityExample,
    batchOperationExample,
    businessObjectRulesExample,
  ]

  for (const example of examples) {
    try {
      await example()
      console.log('✅ 示例执行成功\n')
    } catch (error) {
      console.error(
        `❌ 示例执行失败: ${
          error instanceof Error ? error.message : String(error)
        }\n`
      )
    }
  }

  console.log('🎉 所有示例执行完成!')
}

// 如果直接运行此文件，则执行所有示例
if (require.main === module) {
  runAllExamples().catch(console.error)
}
