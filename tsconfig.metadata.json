{
  "compilerOptions": {
    "target": "ES2020", // 编译目标，现代浏览器支持
    "module": "ESNext", // 输出 ES Module
    "esModuleInterop": true, // CommonJS -> ES Module 兼容
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "outDir": "build/es", // 输出目录
    "declaration": true, // 生成 .d.ts 类型声明
    "declarationMap": true, // 可选，生成声明映射
    "sourceMap": true // 可选，生成源码映射
  },
  "include": ["packages/components/material-render/src/build/**/*"],
  "exclude": [
    "node_modules",
    "build/es" // 防止循环编译
  ]
}
