import { buildProps } from '@neue-plus/utils'
import { ColumnType, ValueType } from '../../../pro-table'
import type { ExtractPropTypes, PropType } from 'vue'

export const neTableColumnProps = buildProps({
  prop: {
    type: String,
    default: '',
  },
  type: {
    type: String as PropType<ColumnType>,
    default: 'default',
  },
  label: {
    type: String,
    default: '',
  },
  align: {
    type: String,
    default: '',
  },
  width: {
    type: [String, Number],
    default: '',
  },
  showOverflowTooltip: {
    type: Boolean,
    default: true,
  },
  sortable: {
    type: [Boolean, String],
    default: false,
  },
  valueType: {
    type: String as PropType<ValueType>,
    default: 'text',
  },
  render: String,
} as const)

export type NeTableColumnProps = ExtractPropTypes<typeof neTableColumnProps>
