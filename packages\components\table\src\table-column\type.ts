import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const neTableColumnProps = buildProps({
  prop: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  align: {
    type: String,
    default: '',
  },
  width: {
    type: [String, Number],
    default: '',
  },
  showOverflowTooltip: {
    type: Boolean,
    default: true,
  },
  sortable: {
    type: [Boolean, String],
    default: false,
  },
} as const)

export type NeTableColumnProps = ExtractPropTypes<typeof neTableColumnProps>
