/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { MappingConfigs } from './MappingConfigs';

/**
 * Request builder class for operations supported on the {@link MappingConfigs} entity.
 */
export class MappingConfigsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<MappingConfigs<T>, T> {
  /**
   * Returns a request builder for querying all `MappingConfigs` entities.
   * @returns A request builder for creating requests to retrieve all `MappingConfigs` entities.
   */
  getAll(): GetAllRequestBuilder<MappingConfigs<T>, T> {
    return new GetAllRequestBuilder<MappingConfigs<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `MappingConfigs` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `MappingConfigs`.
   */
  create(
    entity: MappingConfigs<T>
  ): CreateRequestBuilder<MappingConfigs<T>, T> {
    return new CreateRequestBuilder<MappingConfigs<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `MappingConfigs` entity based on its keys.
   * @param ncid Key property. See {@link MappingConfigs.ncid}.
   * @returns A request builder for creating requests to retrieve one `MappingConfigs` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<MappingConfigs<T>, T> {
    return new GetByKeyRequestBuilder<MappingConfigs<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `MappingConfigs`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `MappingConfigs`.
   */
  update(
    entity: MappingConfigs<T>
  ): UpdateRequestBuilder<MappingConfigs<T>, T> {
    return new UpdateRequestBuilder<MappingConfigs<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `MappingConfigs`.
   * @param ncid Key property. See {@link MappingConfigs.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `MappingConfigs`.
   */
  delete(ncid: string): DeleteRequestBuilder<MappingConfigs<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `MappingConfigs`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `MappingConfigs` by taking the entity as a parameter.
   */
  delete(entity: MappingConfigs<T>): DeleteRequestBuilder<MappingConfigs<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<MappingConfigs<T>, T> {
    return new DeleteRequestBuilder<MappingConfigs<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof MappingConfigs
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
