/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { EntityTypesApi } from './EntityTypesApi';
import { MrvStrategyType } from './MrvStrategyType';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "EntityTypes" of service "neue".
 */
export class EntityTypes<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements EntityTypesType<T>
{
  /**
   * Technical entity name for EntityTypes.
   */
  static override _entityName = 'EntityTypes';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the EntityTypes entity.
   */
  static _keys = ['ncid'];
  /**
   * Entity Type Code.
   */
  declare entityTypeCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Virtual.
   * @nullable
   */
  declare isVirtual?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Has Stream.
   * @nullable
   */
  declare hasStream?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Mrv Strategy.
   * @nullable
   */
  declare mrvStrategy?: MrvStrategyType | null;
  /**
   * Is Read Only.
   * @nullable
   */
  declare isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Display Name.
   */
  declare displayName: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Abstract.
   * @nullable
   */
  declare isAbstract?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Is Open.
   * @nullable
   */
  declare isOpen?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: EntityTypesApi<T>) {
    super(_entityApi);
  }
}

export interface EntityTypesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  entityTypeCode: DeserializedType<T, 'Edm.String'>;
  isVirtual?: DeserializedType<T, 'Edm.Boolean'> | null;
  hasStream?: DeserializedType<T, 'Edm.Boolean'> | null;
  mrvStrategy?: MrvStrategyType | null;
  isReadOnly?: DeserializedType<T, 'Edm.Boolean'> | null;
  name: DeserializedType<T, 'Edm.String'>;
  displayName: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  isAbstract?: DeserializedType<T, 'Edm.Boolean'> | null;
  isOpen?: DeserializedType<T, 'Edm.Boolean'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
