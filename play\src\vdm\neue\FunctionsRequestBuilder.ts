/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { Functions } from './Functions';

/**
 * Request builder class for operations supported on the {@link Functions} entity.
 */
export class FunctionsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<Functions<T>, T> {
  /**
   * Returns a request builder for querying all `Functions` entities.
   * @returns A request builder for creating requests to retrieve all `Functions` entities.
   */
  getAll(): GetAllRequestBuilder<Functions<T>, T> {
    return new GetAllRequestBuilder<Functions<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `Functions` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `Functions`.
   */
  create(entity: Functions<T>): CreateRequestBuilder<Functions<T>, T> {
    return new CreateRequestBuilder<Functions<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `Functions` entity based on its keys.
   * @param ncid Key property. See {@link Functions.ncid}.
   * @returns A request builder for creating requests to retrieve one `Functions` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<Functions<T>, T> {
    return new GetByKeyRequestBuilder<Functions<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `Functions`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `Functions`.
   */
  update(entity: Functions<T>): UpdateRequestBuilder<Functions<T>, T> {
    return new UpdateRequestBuilder<Functions<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `Functions`.
   * @param ncid Key property. See {@link Functions.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `Functions`.
   */
  delete(ncid: string): DeleteRequestBuilder<Functions<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `Functions`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `Functions` by taking the entity as a parameter.
   */
  delete(entity: Functions<T>): DeleteRequestBuilder<Functions<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<Functions<T>, T> {
    return new DeleteRequestBuilder<Functions<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof Functions ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
