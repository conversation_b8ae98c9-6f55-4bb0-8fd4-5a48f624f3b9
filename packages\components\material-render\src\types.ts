import { buildProp, buildProps } from '@neue-plus/utils'
import { ElementEvent, SchemaElement } from '../types'
import type { ExtractPropTypes, PropType } from 'vue'

// HTTP 方法类型
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete'

export interface ApiSchema {
  url: string
  method: HttpMethod
  query: Record<string, any>
  dataPath: string
}
export interface ApiSchemaMap {
  [key: string]: ApiSchema
}
export interface ApiBaseConfig {
  [key: string]: ElementEvent
}
export const neMaterialRenderProps = buildProps({
  config: buildProp({
    type: Object as () => {
      configProvider?: Record<string, any>
      events?: ElementEvent[]
      apiBaseConfig?: ApiBaseConfig
    },
    default: () => ({ configProvider: {}, events: [], api: {} }),
  }),
  elements: {
    type: Array as PropType<SchemaElement[]>,
    required: true,
  },
  events: {
    type: Object,
    default: () => {},
  },
  apis: {
    type: Object as () => ApiSchemaMap,
    default: () => ({}),
  },
} as const)

export type NeMaterialRenderProps = ExtractPropTypes<
  typeof neMaterialRenderProps
>
// 事件类型
export type NeMaterialRenderEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}
