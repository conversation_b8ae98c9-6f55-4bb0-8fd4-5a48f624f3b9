import { withInstall } from '@neue-plus/utils'

import MaterialRender from './src/material-render.vue'
import RenderCore from './src/render-core/render-core'
import type { SFCWithInstall } from '@neue-plus/utils'

export const NeMaterialRender: SFCWithInstall<typeof MaterialRender> =
  withInstall(MaterialRender)
export default NeMaterialRender

export const NeRenderCore: SFCWithInstall<typeof RenderCore> =
  withInstall(RenderCore)

export * from './types'
