// import { xml2js } from 'xml-js'
import { xmlToXmlJsStyle } from '@neue-plus/utils/xmlToXmlJsStyle'
import service from './service'

/**
 * 获取 OData 元数据并返回 JSON 格式
 * @param url OData 服务的根地址，例如：https://services.odata.org/V4/OData/OData.svc
 */
export async function getODataMetadataJson(url: string) {
  try {
    const res: string = await service(`${url}?$format=xml`, {})
    const jsonData = xmlToXmlJsStyle(res)
    // const jsonData = xml2js(res, { compact: true })

    return jsonData
  } catch (err) {
    console.error('获取 OData 元数据失败:', err)
    throw err
  }
}
