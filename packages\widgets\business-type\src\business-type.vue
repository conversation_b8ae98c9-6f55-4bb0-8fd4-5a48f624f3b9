<template>
  <widget-wrapper>
    <el-dialog
      v-model="dialogVisible"
      :title="props.config.title || '业务类型'"
      width="500"
      :before-close="handleClose"
    >
      <div class="depart-filter">
        <el-input
          id="filterDepart"
          v-model="filterDepartName"
          class="filter-depart"
          placeholder="请输入名称进行模糊查询"
          :clearable="true"
          :clear="handleClearFilters"
          @clear="handleClearFilters"
          @keyup.enter="handleSearchIconClick"
        >
          <template #suffix>
            <i
              id="searchIcon"
              class="iconfont icon-interactive-search"
              @click="handleSearchIconClick"
            />
          </template>
        </el-input>
      </div>
      <div class="depart-tree">
        <el-tree
          id="tree"
          ref="treeRef"
          :data="treeData"
          node-key="id"
          :highlight-current="true"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <template #default="{ node }">
            <div class="custom-tree-node">
              <div class="custom-node-content">
                <!-- <i v-if="data.leafType === 'group'" class="iconfont icon-general-group"></i>
                <i v-else class="iconfont icon-general-member"></i> -->
                <!-- 处理图标 -->
                <span>=</span>
                <div class="node-label">{{ node.label }}</div>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleOk"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElTree } from 'element-plus'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import type { PropType } from 'vue'
import type { BusinessTypeConfig } from './business-type'

defineOptions({
  name: 'BusinessType',
  inheritAttrs: false,
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object as PropType<BusinessTypeConfig>,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:visible', 'businessTypeChecked'])

const dialogVisible = ref(false)
const filterDepartName = ref('')
const treeRef = ref<InstanceType<typeof ElTree> | null>(null)
const currentNode: any = ref(null)

const filterNode = (value: string, data: any) => {
  if (!value) return true
  // 与 data.label 比较即可
  return data.label.toLowerCase().includes(value.toLowerCase())
}
const handleClearFilters = () => {
  filterDepartName.value = ''
}
const handleNodeClick = (node: any) => {
  currentNode.value = node
  console.log('节点点击:', node)
}
const handleSearchIconClick = (val: any) => {
  filterDepartName.value = val
}

const handleClose = (done: () => void) => {
  dialogVisible.value = false
  done()
}

watch(
  () => filterDepartName.value,
  (val) => {
    treeRef.value?.filter(val)
  },
  { immediate: true }
)

interface Tree {
  label: string
  children?: Tree[]
}
const treeData: Tree[] = [
  {
    label: 'Level one 1',
    children: [
      {
        label: 'Level two 1-1',
        children: [
          {
            label: 'Level three 1-1-1',
          },
        ],
      },
    ],
  },
  {
    label: 'Level one 2',
    children: [
      {
        label: 'Level two 2-1',
        children: [
          {
            label: 'Level three 2-1-1',
          },
        ],
      },
      {
        label: 'Level two 2-2',
        children: [
          {
            label: 'Level three 2-2-1',
          },
        ],
      },
    ],
  },
]
// const defaultProps = {
//   children: 'children',
//   label: 'label',
// }

const handleOk = () => {
  emit('businessTypeChecked', currentNode.value)
  emit('update:visible', false)
  console.log('业务类型已选择')
}
const handleCancel = () => {
  emit('update:visible', false)
  console.log('业务类型已取消')
}

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  },
  { immediate: true }
)

defineExpose({
  open,
  close,
})
</script>

<style scoped></style>
