<template>
  <widget-wrapper>
    <el-dialog
      v-model="dialogVisible"
      :title="props.config.title || '业务类型'"
      width="500"
      :before-close="handleClose"
      :close-on-click-modal="false"
      class="ne-business-type"
    >
      <div class="depart-filter">
        <el-input
          id="filterDepart"
          v-model="filterDepartName"
          class="filter-depart"
          placeholder="请输入名称进行模糊查询"
          :clearable="true"
          :clear="handleClearFilters"
          @clear="handleClearFilters"
          @keyup.enter="handleSearchIconClick"
        >
          <template #suffix>
            <i
              id="searchIcon"
              class="iconfont icon-interactive-search"
              @click="handleSearchIconClick"
            />
          </template>
        </el-input>
      </div>
      <div class="depart-tree">
        <!-- <el-tree
          id="tree"
          ref="treeRef"
          :data="treeData"
          node-key="id"
          show-checkbox
          :highlight-current="true"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        > -->
        <el-tree
          ref="treeRef"
          :data="treeData"
          node-key="id"
          :check-strictly="true"
          :default-expand-all="true"
          :highlight-current="false"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        >
          <template #default="{ node }">
            <div
              class="custom-tree-node"
              :class="node.disabled ? 'is-disabled' : ''"
              :style="{ cursor: node.disabled ? 'not-allowed' : 'pointer' }"
            >
              <div class="custom-node-content">
                <!-- <i v-if="node.leafType === 'group'" class="iconfont icon-general-group"></i>
                <i v-else class="iconfont icon-general-member"></i> -->
                <!-- 处理图标 -->
                <!-- <span>=</span> -->
                <div class="node-label">{{ node.label }}</div>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleOk"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import { useApisRef } from '@neue-plus/components/material-render/src/context/event-flow'
import type { ElTree } from 'element-plus'
import type { PropType } from 'vue'
import type {
  BusinessTypeConfig,
  TreeItemSource,
  TreeNode,
} from './business-type'

const apiRefs = useApisRef()

defineOptions({
  name: 'BusinessType',
  inheritAttrs: false,
})

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  config: {
    type: Object as PropType<BusinessTypeConfig>,
    default: () => ({}),
  },
  fetchByInit: {
    type: Boolean,
    default: false,
  },
  businessTypeRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    required: true,
  },
})

const emit = defineEmits(['update:visible', 'businessTypeChecked'])

const dialogVisible = ref(false)
const filterDepartName = ref('')
const treeRef = ref<InstanceType<typeof ElTree> | null>(null)
const currentNode: any = ref(null)

const rootNode: TreeNode = {
  id: 'root',
  label: 'CadPart',
  disabled: true,
  parentId: undefined,
  children: [],
  raw: {},
}
const loadedMap = new Set<string>() // 记录已加载的节点
const treeData = ref<TreeNode[]>([rootNode])

const toTreeData = (
  list: TreeItemSource[],
  parentId?: string,
  parenName?: string
) => {
  console.log('list:', list)
  return list.map((item: TreeItemSource) => ({
    id: item.ncid,
    label: item.displayName || item.name,
    disabled: item.isAbstract || item.isVirtual,
    parentId,
    parenName,
    children: [],
    raw: item,
  }))
}

// function handleTreeChange(value) {
//   console.log('选中的值:', value)
// }

// function renderNode(h, { node, data }) {
//   return h('div', { class: 'custom-tree-node' }, [
//     h('div', { class: 'custom-node-content' }, [
//       h('div', { class: 'node-label' }, node.label),
//     ]),
//   ])
// }

const initBusinessTypeData = async () => {
  console.log('initBusinessTypeData fetchByInit')
  // TODO 后续再考虑,拉取后端数据
  if (loadedMap.has('root')) return // 已加载过
  await loadChildren('root', 'CadPart')
}
// TODO 前期不请求后续再考虑，数据写到本地
const loadChildren = async (parentId: string, parentName?: string) => {
  if (loadedMap.has(parentId)) return
  console.log('loading=====', parentName)
  try {
    const res = await apiRefs['businessTypeRequest']({
      params: {
        $filter: `baseType/name eq '${
          parentName === 'root' ? 'CadPart' : parentName
        }'`,
      },
    })
    const newNodes = toTreeData(res?.data?.value || [], parentId)
    console.log('newNodes===>:', newNodes)
    // 找到父节点并追加 children
    const parent = findNode(treeData.value, parentId)
    if (parent) {
      parent.children.push(...newNodes)
    }
    loadedMap.add(parentId)
  } catch (e) {
    console.error('加载失败:', e)
  }
}

function findNode(list: TreeNode[], id: string): TreeNode | any {
  for (const node of list) {
    if (node.id === id) return node
    if (node.children) {
      const child = findNode(node.children, id)
      if (child) return child
    }
  }
}

const handleNodeClick = (node: TreeNode) => {
  console.log('handleNodeClick:', node)
  if (node.disabled) {
    currentNode.value.id = node.id && (currentNode.value = {})
    return
  }
  if (currentNode.value && currentNode.value.id === node.id) {
    // 再次点击同一节点时，取消选择
    // treeRef?.value?.setCheckedKeys([])
    // currentNode.value = {}
    return
  }
  loadChildren(node.id)
  treeRef?.value?.setCheckedKeys([node.id])
  currentNode.value = node
  // }
}

const filterNode = (value: string, data: any) => {
  if (!value) return true
  // 与 data.label 比较即可
  return data.label.toLowerCase().includes(value.toLowerCase())
}
const handleClearFilters = () => {
  filterDepartName.value = ''
}

const handleSearchIconClick = (val: any) => {
  filterDepartName.value = val
  console.log('handleSearchIconClick:', val)
}

const handleClose = (done: () => void) => {
  dialogVisible.value = false
  emit('update:visible', false)
  done()
}

watch(
  () => filterDepartName.value,
  (val) => {
    treeRef.value?.filter(val)
  },
  { immediate: true }
)

const handleOk = () => {
  emit('businessTypeChecked', currentNode.value)
  emit('update:visible', false)
}
const handleCancel = () => {
  emit('update:visible', false)
  console.log('业务类型已取消')
}

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      console.log('业务类型弹窗打开')
      initBusinessTypeData()
    }
  },
  { immediate: true }
)

watch(
  () => currentNode.value,
  (newVal) => {
    console.log('currentNode changed:', newVal)
  },
  { deep: true }
)

defineExpose({
  open,
  close,
})
</script>

<style scoped></style>
