{"config": {"apiBaseConfig": {"baseUrl": ""}}, "apis": {"submit": {"url": "/cdp/202512/OdataService/CadFile", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision)"}}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}, "panelFormEditRequestDraw": {"url": "/modeling/202512/OdataService/NeueTwoDimensionDrawings({{ncid}})", "method": "get"}}, "elements": [{"id": "pro-tab", "type": "pro-tab", "name": "OData表格", "props": {"tabProps": {"tabPosition": "left"}, "tabs": [{"name": "base-info", "label": "基本信息", "elements": [{"id": "PanelForm_er4mn6jbtk1234562", "type": "wg-panel-form", "name": "面板表单", "props": {"config": {"labelWidth": "100px", "title": "属性信息(工程图)", "span": 12, "gutter": 12, "odataType": "drawing"}, "formItems": [{"name": "基本属性", "key": "base", "viewImg": "https://example.com/image1.png", "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101"}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "disabled": false, "minLength": 6, "maxLength": 18}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "placeholder": ""}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "ruleId": "RULE_EMAIL"}, {"prop": "owner", "fieldName": "责任人", "fieldType": "text", "disabled": false, "minLength": 1, "maxLength": 32}, {"prop": "lifecycleStatus.name", "fieldName": "状态", "fieldType": "text", "disabled": true}, {"prop": "locked<PERSON>y", "fieldName": "锁定人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "lockedAt", "fieldName": "锁定时间", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}]}, {"name": "设计属性", "key": "design", "formItems": []}]}, "elements": [], "events": []}]}]}, "elements": []}]}