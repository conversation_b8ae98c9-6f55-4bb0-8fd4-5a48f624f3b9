const path = require('path')
const fs = require('fs')
const esbuild = require('esbuild')
const fg = require('fast-glob')

const srcDir = path.resolve(process.cwd(), 'metadata')
const outDir = path.resolve(
  process.cwd(),
  'packages/components/material-render/src/metadata'
)

;(async () => {
  try {
    if (fs.existsSync(outDir)) {
      fs.rmSync(outDir, { recursive: true, force: true })
    }
    // 获取所有普通 JS 文件，排除 .d.js
    const files = await fg('**/*.js', {
      cwd: srcDir,
      absolute: true,
      ignore: ['**/*.d.js'],
    })
    for (const file of files) {
      // 相对于 srcDir 的路径，用于保持目录结构
      const relPath = path.relative(srcDir, file)
      // 输出文件名，加上 .esm.js
      const outFile = path.resolve(outDir, relPath.replace(/\.js$/, '.js'))

      // 确保目录存在
      fs.mkdirSync(path.dirname(outFile), { recursive: true })

      // 打包
      await esbuild.build({
        entryPoints: [file],
        outfile: outFile,
        bundle: true,
        format: 'esm',
        platform: 'browser',
        target: ['es2020'],
      })
    }
    const dtsFiles = await fg('**/*.d.ts', {
      cwd: srcDir,
      absolute: true,
    })
    for (const file of dtsFiles) {
      const relPath = path.relative(srcDir, file)
      const outFile = path.resolve(outDir, relPath)

      fs.mkdirSync(path.dirname(outFile), { recursive: true })
      fs.copyFileSync(file, outFile)
    }
    console.log('🎉 All JS files converted to ES Modules successfully!')
  } catch (err) {
    console.error(err)
    process.exit(1)
  }
})()
