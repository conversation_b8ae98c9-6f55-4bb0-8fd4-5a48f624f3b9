/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { EnumTypesApi } from './EnumTypesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "EnumTypes" of service "neue".
 */
export class EnumTypes<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements EnumTypesType<T>
{
  /**
   * Technical entity name for EnumTypes.
   */
  static override _entityName = 'EnumTypes';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the EnumTypes entity.
   */
  static _keys = ['ncid'];
  /**
   * Enum Type Code.
   */
  declare enumTypeCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Underlying Type.
   * @nullable
   */
  declare underlyingType?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Is Flags.
   * @nullable
   */
  declare isFlags?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Members.
   * @nullable
   */
  declare members?: DeserializedType<T, 'Edm.String'>[] | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: EnumTypesApi<T>) {
    super(_entityApi);
  }
}

export interface EnumTypesType<T extends DeSerializers = DefaultDeSerializers> {
  enumTypeCode: DeserializedType<T, 'Edm.String'>;
  name: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  underlyingType?: DeserializedType<T, 'Edm.String'> | null;
  isFlags?: DeserializedType<T, 'Edm.Boolean'> | null;
  members?: DeserializedType<T, 'Edm.String'>[] | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
