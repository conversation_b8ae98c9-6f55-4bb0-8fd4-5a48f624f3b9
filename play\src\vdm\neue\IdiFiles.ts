/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers
} from '@sap-cloud-sdk/odata-v4';
import type { IdiFilesApi } from './IdiFilesApi';
import { IdiConvertState } from './IdiConvertState';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "IdiFiles" of service "neue".
 */
export class IdiFiles<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements IdiFilesType<T>
{
  /**
   * Technical entity name for IdiFiles.
   */
  static override _entityName = 'IdiFiles';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the IdiFiles entity.
   */
  static _keys = ['ncid'];
  /**
   * Idi Light Model Id.
   * @nullable
   */
  declare idiLightModelId?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Convert State.
   * @nullable
   */
  declare convertState?: IdiConvertState | null;
  /**
   * Trigger Time.
   * @nullable
   */
  declare triggerTime?: DeserializedType<T, 'Edm.DateTimeOffset'> | null;
  /**
   * Name.
   * @nullable
   */
  declare name?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Description.
   * @nullable
   */
  declare description?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Software Rev.
   * @nullable
   */
  declare softwareRev?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: IdiFilesApi<T>) {
    super(_entityApi);
  }
}

export interface IdiFilesType<T extends DeSerializers = DefaultDeSerializers> {
  idiLightModelId?: DeserializedType<T, 'Edm.String'> | null;
  convertState?: IdiConvertState | null;
  triggerTime?: DeserializedType<T, 'Edm.DateTimeOffset'> | null;
  name?: DeserializedType<T, 'Edm.String'> | null;
  description?: DeserializedType<T, 'Edm.String'> | null;
  softwareRev?: DeserializedType<T, 'Edm.String'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
