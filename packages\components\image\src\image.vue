<template>
  <el-image
    v-bind="props"
    ref="imageRef"
    @load="(evt:Event) => emits('load', evt)"
    @error="(evt:Event)=>emits('error',evt)"
    @switch="(index:number)=>emits('switch',index)"
    @close="() => emits('close')"
    @show="() => emits('show')"
  >
    <template #placeholder>
      <slot name="placeholder" />
    </template>
    <template #error>
      <slot name="error" />
    </template>
    <template #progress="progressProps">
      <slot name="progress" v-bind="progressProps" />
    </template>
    <template #viewer>
      <slot name="viewer" />
    </template>
    <template #toolbar="toolbarProps">
      <slot name="toolbar" v-bind="toolbarProps" />
    </template>
  </el-image>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { neImageEmits, neImageProps } from './image'
import type { ElImage } from 'element-plus'

// 定义 props 和 emits
const props = defineProps(neImageProps)
const imageRef = ref<InstanceType<typeof ElImage>>()
defineOptions({
  name: 'NeImage',
})

const emits = defineEmits(neImageEmits)

const showPreview = () => {
  imageRef.value?.showPreview()
}

defineExpose({ showPreview })
</script>
