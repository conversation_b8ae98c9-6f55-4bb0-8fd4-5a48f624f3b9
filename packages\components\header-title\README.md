# NeHeaderTitle 页面标题组件

一个功能丰富的页面标题组件，支持多种样式和交互功能。

## 基础用法

```vue
<template>
  <ne-header-title title="页面标题" />
</template>
```

## 带副标题

```vue
<template>
  <ne-header-title 
    title="主标题" 
    subtitle="这是一个副标题说明"
  />
</template>
```

## 带返回按钮

```vue
<template>
  <ne-header-title 
    title="详情页面" 
    :show-back="true"
    back-text="返回列表"
    @back="handleBack"
  />
</template>

<script setup>
const handleBack = () => {
  // 处理返回逻辑
  console.log('返回上一页')
}
</script>
```

## 带图标

```vue
<template>
  <ne-header-title 
    title="设置页面" 
    icon="el-icon-setting"
  />
</template>
```

## 带分割线

```vue
<template>
  <ne-header-title 
    title="页面标题" 
    :divider="true"
    divider-position="bottom"
  />
</template>
```

## 不同尺寸

```vue
<template>
  <div>
    <ne-header-title title="小标题" size="small" />
    <ne-header-title title="默认标题" size="default" />
    <ne-header-title title="大标题" size="large" />
  </div>
</template>
```

## 不同对齐方式

```vue
<template>
  <div>
    <ne-header-title title="左对齐" align="left" />
    <ne-header-title title="居中对齐" align="center" />
    <ne-header-title title="右对齐" align="right" />
  </div>
</template>
```

## 带操作按钮

```vue
<template>
  <ne-header-title title="用户管理">
    <template #extra>
      <el-button type="primary">新增用户</el-button>
      <el-button>导出</el-button>
    </template>
  </ne-header-title>
</template>
```

## 自定义插槽

```vue
<template>
  <ne-header-title>
    <template #title>
      <span style="color: #409eff;">自定义标题样式</span>
    </template>
    <template #subtitle>
      <el-tag size="small">Beta</el-tag>
      <span>这是一个测试功能</span>
    </template>
    <template #extra>
      <el-switch v-model="enabled" />
    </template>
  </ne-header-title>
</template>

<script setup>
import { ref } from 'vue'
const enabled = ref(false)
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| title | 标题文本 | string | — | '' |
| subtitle | 副标题文本 | string | — | '' |
| level | 标题级别 | number | 1-6 | 2 |
| divider | 是否显示分割线 | boolean | — | false |
| dividerPosition | 分割线位置 | string | top/bottom/both | bottom |
| align | 标题对齐方式 | string | left/center/right | left |
| showBack | 是否显示返回按钮 | boolean | — | false |
| backText | 返回按钮文本 | string | — | '返回' |
| icon | 标题图标 | string/object | — | '' |
| size | 标题大小 | string | small/default/large | default |
| bold | 是否加粗 | boolean | — | true |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| back | 点击返回按钮时触发 | — |
| click | 点击标题区域时触发 | event: MouseEvent |

### Slots

| 插槽名 | 说明 |
|--------|------|
| title | 自定义标题内容 |
| subtitle | 自定义副标题内容 |
| extra | 自定义操作区域内容 |

## 样式变量

组件使用以下 CSS 变量，可以通过覆盖这些变量来自定义样式：

```scss
:root {
  --ne-header-title-font-size-small: 14px;
  --ne-header-title-font-size-default: 18px;
  --ne-header-title-font-size-large: 24px;
  --ne-header-title-color: var(--el-text-color-primary);
  --ne-header-title-subtitle-color: var(--el-text-color-regular);
}
```
