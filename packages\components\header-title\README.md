# NeHeaderTitle 页面标题组件

一个简洁的页面标题组件，支持左侧标题和右侧操作区域。

## 基础用法

```vue
<template>
  <ne-header-title title="页面标题" />
</template>
```

## 带操作按钮

```vue
<template>
  <ne-header-title title="用户管理">
    <template #extra>
      <el-button type="primary">新增用户</el-button>
      <el-button>导出</el-button>
    </template>
  </ne-header-title>
</template>
```

## 自定义标题内容

```vue
<template>
  <ne-header-title>
    <template #title>
      <span style="color: #409eff;">自定义标题样式</span>
      <el-tag size="small" style="margin-left: 8px;">Beta</el-tag>
    </template>
    <template #extra>
      <el-switch v-model="enabled" />
      <el-button type="primary">保存</el-button>
    </template>
  </ne-header-title>
</template>

<script setup>
import { ref } from 'vue'
const enabled = ref(false)
</script>
```

## API

### Props

| 参数  | 说明     | 类型   | 可选值 | 默认值 |
| ----- | -------- | ------ | ------ | ------ |
| title | 标题文本 | string | —      | ''     |

### Slots

| 插槽名 | 说明                              |
| ------ | --------------------------------- |
| title  | 自定义标题内容，会覆盖 title 属性 |
| extra  | 自定义右侧操作区域内容            |

## 样式说明

- 组件采用 flex 布局，左侧标题区域自动占据剩余空间
- 右侧操作区域根据内容自适应宽度
- 在移动端会自动切换为垂直布局
- 标题默认使用大号字体和中等字重
