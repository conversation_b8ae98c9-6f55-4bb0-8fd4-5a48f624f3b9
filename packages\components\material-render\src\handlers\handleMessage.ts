import { isString } from 'lodash-unified'
import { message } from '../utils/ElGlobal'

const handleMessage = (action: any) => {
  const data = isString(action) ? { type: 'error', content: action } : action
  const { type, content } = data
  message({
    type,
    message: content,
    duration: action.duration ?? 2000,
  })
}
handleMessage.info = (content: string) => {
  handleMessage({ type: 'info', content })
}
handleMessage.success = (content: string) => {
  handleMessage({ type: 'success', content })
}
handleMessage.warning = (content: string) => {
  handleMessage({ type: 'warning', content })
}
handleMessage.error = (content: string) => {
  handleMessage({ type: 'error', content })
}
handleMessage.primary = (content: string) => {
  handleMessage({ type: 'primary', content })
}
export { handleMessage }
