@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(header-title) {
  position: relative;
  margin-bottom: getCssVar('spacing-lg', 16px);

  @include e(divider) {
    height: 1px;
    background-color: getCssVar('border-color-light');
    
    @include m(top) {
      margin-bottom: getCssVar('spacing-md', 12px);
    }
    
    @include m(bottom) {
      margin-top: getCssVar('spacing-md', 12px);
    }
  }

  @include e(content) {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: getCssVar('spacing-md', 12px);
  }

  @include e(back) {
    display: flex;
    align-items: center;
    gap: getCssVar('spacing-xs', 4px);
    padding: 0;
    margin-right: getCssVar('spacing-md', 12px);
    color: getCssVar('color-primary');
    font-size: getCssVar('font-size-base');
    
    &:hover {
      color: getCssVar('color-primary-light-3');
    }
  }

  @include e(back-icon) {
    font-size: getCssVar('font-size-base');
  }

  @include e(main) {
    flex: 1;
    display: flex;
    align-items: center;
    gap: getCssVar('spacing-sm', 8px);
    min-width: 0;
  }

  @include e(icon) {
    flex-shrink: 0;
    font-size: getCssVar('font-size-large');
    color: getCssVar('color-primary');
  }

  @include e(title) {
    margin: 0;
    color: getCssVar('text-color-primary');
    font-weight: 500;
    line-height: 1.2;
    word-break: break-word;
  }

  @include e(subtitle) {
    margin-left: getCssVar('spacing-sm', 8px);
    color: getCssVar('text-color-regular');
    font-size: getCssVar('font-size-small');
    line-height: 1.4;
  }

  @include e(extra) {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: getCssVar('spacing-sm', 8px);
  }

  // 尺寸变体
  @include m(small) {
    margin-bottom: getCssVar('spacing-md', 12px);

    .ne-header-title__title {
      font-size: getCssVar('font-size-base');
    }

    .ne-header-title__subtitle {
      font-size: getCssVar('font-size-extra-small');
    }

    .ne-header-title__icon {
      font-size: getCssVar('font-size-base');
    }
  }

  @include m(default) {
    .ne-header-title__title {
      font-size: getCssVar('font-size-large');
    }
  }

  @include m(large) {
    margin-bottom: getCssVar('spacing-xl', 20px);

    .ne-header-title__title {
      font-size: getCssVar('font-size-extra-large');
    }

    .ne-header-title__subtitle {
      font-size: getCssVar('font-size-base');
    }

    .ne-header-title__icon {
      font-size: getCssVar('font-size-extra-large');
    }
  }

  // 对齐方式
  @include m(center) {
    .ne-header-title__content {
      justify-content: center;
      text-align: center;
    }

    .ne-header-title__main {
      justify-content: center;
    }
  }

  @include m(right) {
    .ne-header-title__content {
      justify-content: flex-end;
      text-align: right;
    }

    .ne-header-title__main {
      justify-content: flex-end;
    }
  }

  // 加粗样式
  @include m(bold) {
    .ne-header-title__title {
      font-weight: 600;
    }
  }

  // 分割线样式
  @include m(with-divider) {
    padding: getCssVar('spacing-sm', 8px) 0;
  }

  @include m(divider-top) {
    padding-top: getCssVar('spacing-md', 12px);
  }

  @include m(divider-bottom) {
    padding-bottom: getCssVar('spacing-md', 12px);
  }

  // 响应式设计
  @media (max-width: getCssVar('breakpoint-sm', 576px)) {
    @include e(content) {
      flex-direction: column;
      align-items: flex-start;
      gap: getCssVar('spacing-sm', 8px);
    }

    @include e(main) {
      width: 100%;
    }

    @include e(extra) {
      width: 100%;
      justify-content: flex-start;
    }

    @include m(center) {
      .ne-header-title__content {
        align-items: center;
      }

      .ne-header-title__extra {
        justify-content: center;
      }
    }

    @include m(right) {
      .ne-header-title__content {
        align-items: flex-end;
      }

      .ne-header-title__extra {
        justify-content: flex-end;
      }
    }
  }
}
