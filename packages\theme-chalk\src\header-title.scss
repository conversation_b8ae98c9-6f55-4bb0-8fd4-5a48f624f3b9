@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include neb(header-title) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: getCssVar('spacing-lg', 16px);
  gap: getCssVar('spacing-md', 12px);
  padding-bottom: 16px;

  @include e(title) {
    flex: 1;
    margin: 0;
    color: getCssVar('text-color-primary');
    font-size: getCssVar('font-size-medium');
    font-weight: 500;
    line-height: 1.2;
    word-break: break-word;
  }

  @include e(extra) {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: getCssVar('spacing-sm', 8px);
  }

  // 响应式设计
  @media (max-width: getCssVar('breakpoint-sm', 576px)) {
    flex-direction: column;
    align-items: flex-start;
    gap: getCssVar('spacing-sm', 8px);

    @include e(extra) {
      width: 100%;
      justify-content: flex-start;
    }
  }
}
