<template>
  <widget-wrapper>
    <el-form
      ref="formRef"
      :model="formModel"
      :label-width="`${props.config.labelWidth || 80}px`"
      :style="{
        width: (props.config.formWidth || 500) + 'px',
      }"
    >
      <el-form-item
        v-for="item in newFormItems"
        :key="item.prop"
        :prop="item.prop"
        :label="item.fieldName"
        :rules="getRules(item)"
      >
        <!-- input -->
        <el-input
          v-if="item.fieldType === 'text'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || '请输入'"
          :type="item.inputType || 'text'"
          :disabled="item.disabled || false"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- textarea -->
        <el-input
          v-else-if="item.fieldType === 'textarea'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || '请输入'"
          :disabled="item.disabled || false"
          type="textarea"
          :rows="item.rows || 4"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- select -->
        <el-select
          v-if="item.fieldType === 'select'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || '请选择'"
          :disabled="item.disabled || false"
          :rules="[
            { required: item.required, message: '请选择', trigger: 'change' },
          ]"
          style="width: 100%"
        >
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          v-else-if="item.fieldType === 'number'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || '请输入'"
          :min="item.min || 0"
          :max="item.max || 100"
          :step="item.step || 1"
          :disabled="item.disabled || false"
          style="width: 100%"
        />
        <!-- switch -->
        <el-switch
          v-else-if="item.fieldType === 'switch'"
          v-model="formModel[item.prop]"
          :active-text="item.activeText || '开'"
          :inactive-text="item.inactiveText || '关'"
          :disabled="item.disabled || false"
        />
        <!-- checkbox -->
        <el-checkbox-group
          v-else-if="item.fieldType === 'checkbox'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled || false"
          style="width: 100%"
        >
          <el-checkbox
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-checkbox-group>
        <!-- radio -->
        <el-radio-group
          v-else-if="item.fieldType === 'radio'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled || false"
          style="width: 100%"
        >
          <el-radio
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-radio-group>
        <!-- date picker -->
        <el-date-picker
          v-else-if="item.fieldType === 'date'"
          v-model="formModel[item.prop]"
          :type="item.dateType || 'date'"
          :placeholder="item.placeholder"
          :format="item.format || 'yyyy-MM-dd'"
          :value-format="item.valueFormat || 'yyyy-MM-dd'"
          style="width: 100%"
        />
        <!-- WgBusinessType -->
        <div
          v-else-if="item.fieldType === 'businessType'"
          class="business-type"
          style="display: flex; align-items: center; gap: 10px; width: 100%"
        >
          <el-input
            v-model="formModel.businessType"
            type="text"
            style="flex: 1"
          />
          <NeButton @click="handleBusinessTypeClick(item)">···</NeButton>
          <widget-business-type
            v-model:visible="businessTypeDialogVisible"
            style="width: 100%"
            @business-type-checked="handleBusinessTypeChecked"
          />
        </div>
      </el-form-item>
      <el-form-item>
        <NeButton type="primary" @click="submitForm">{{
          props.config.submitText || '提交'
        }}</NeButton>
        <NeButton @click="resetForm">{{
          props.config.resetText || '重置'
        }}</NeButton>
        <NeButton @click="cancelForm">{{
          props.config.cancelText || '取消'
        }}</NeButton>
      </el-form-item>
    </el-form>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import NeButton from '@neue-plus/components/button'
import WidgetBusinessType from '../../business-type/src/business-type.vue'
import { getRules } from '../src/validation'
import type { PropType } from 'vue'
import type { FieldType, FormConfig, FormField } from './form'

defineOptions({
  name: 'BasicForm',
  inheritAttrs: false,
})

const props = defineProps({
  config: {
    type: Object as PropType<FormConfig>,
    default: () => ({}),
  },
  formItems: {
    type: Array as PropType<any>,
    default: () => [],
  },
  initRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
})

const formComponents = ref([
  'text',
  'select',
  'number',
  'textarea',
  'date',
  'businessType',
])
// const formComponents = ref(['text', 'select', 'number', 'switch', 'checkbox', 'radio', 'textarea', 'date'])

const emit = defineEmits([
  'formSubmit',
  'formReset',
  'formCancel',
  'update:visible',
])

const formConfig = ref<FormConfig>({
  layout: 'horizontal',
  submitText: '提交',
  resetText: '重置',
  cancelText: '取消',
  showSubmit: true,
  showReset: false,
  showCancel: true,
  buttonAlign: 'center',
})
const formItemsData = ref<FieldType[]>([])
const formRef = ref()
const businessTypeDialogVisible = ref(false)

const formModel = ref<any>({
  name: '1',
})

const handleBusinessTypeClick = (item: FormField) => {
  console.log('handleBusinessTypeClick', item)
  businessTypeDialogVisible.value = true
}

const submitForm = async () => {
  await formRef.value?.validate((valid: any) => {
    if (valid) {
      console.log('submitForm')
      emit('formSubmit', formModel.value)
    }
  })
}

const formReset = () => {
  formRef.value.resetFields()
  formModel.value = {}
  console.log('formWidget: 表单已重置')
}

const resetForm = () => {
  formReset()
  emit('formReset', {})
}

const cancelForm = () => {
  emit('formCancel', formModel.value)
  emit('update:visible', false)
  if (props.config.clearOnClose !== false) {
    formReset()
  }
}

const newFormItems = computed(() => {
  return props.formItems.filter((i: { fieldType: string }) =>
    formComponents.value.includes(i.fieldType)
  )
})

const handleBusinessTypeChecked = (value: any) => {
  formModel.value.businessType = value.label
  businessTypeDialogVisible.value = false
  console.log('业务类型已选中:', value, formModel.value)
}

const initFromData = async () => {
  if (props.initRequest) {
    try {
      const response = await props.initRequest({})
      formModel.value = response.data || {}
      console.log('初始化数据:', formModel.value)
    } catch (error) {
      console.error('初始化请求失败:', error)
    }
  }
}

initFromData()

watch(
  () => props.config,
  (newConfig: FormConfig) => {
    console.log('配置已更新:', newConfig)
    // 可以在这里处理配置更新逻辑
    formConfig.value = { ...formConfig.value, ...newConfig }
  },
  { deep: true }
)

watch(
  () => props.formItems,
  (newItems) => {
    console.log('表单项已更新:', newItems)
    // 可以在这里处理表单项更新逻辑
    formItemsData.value = [...newItems]
  },
  { deep: true }
)
</script>

<style scoped />
