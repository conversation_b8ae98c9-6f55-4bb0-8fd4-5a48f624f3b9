<template>
  <widget-wrapper>
    <el-form
      ref="formRef"
      :model="formModel"
      class="ne-basic-form"
      :label-width="`${props.config.labelWidth || 80}px`"
      :style="{
        width: (props.config.formWidth || 500) + 'px',
      }"
    >
      <el-form-item
        v-for="item in newFormItems"
        :key="item.prop"
        :prop="item.prop"
        :label="item.fieldName"
        :rules="getRules(item)"
      >
        <el-input
          v-if="item.fieldType === 'text'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || ''"
          :type="item.inputType || 'text'"
          :disabled="item.disabled"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- textarea -->
        <el-input
          v-else-if="item.fieldType === 'textarea'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || ''"
          :disabled="item.disabled"
          type="textarea"
          :rows="item.rows || 4"
          :minlength="item.minLength || 0"
          :maxlength="item.maxLength || 255"
          :show-word-limit="item.showWordLimit || true"
          style="width: 100%"
        />
        <!-- select -->
        <el-select
          v-if="item.fieldType === 'select'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || ''"
          :disabled="item.disabled"
          :rules="[
            { required: item.required, message: '请选择', trigger: 'change' },
          ]"
          style="width: 100%"
        >
          <el-option
            v-for="option in item.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        <el-input-number
          v-else-if="item.fieldType === 'number'"
          v-model="formModel[item.prop]"
          :placeholder="item.placeholder || ''"
          :min="item.min || 0"
          :max="item.max || 100"
          step-strictly
          :step="item.step || 1"
          :disabled="item.disabled"
          style="width: 100%"
          :rules="[
            { required: item.required, message: '请输入', trigger: 'change' },
          ]"
        />
        <!-- switch -->
        <el-switch
          v-else-if="item.fieldType === 'switch'"
          v-model="formModel[item.prop]"
          :active-text="item.activeText || '开'"
          :inactive-text="item.inactiveText || '关'"
          :disabled="item.disabled"
        />
        <!-- checkbox -->
        <el-checkbox-group
          v-else-if="item.fieldType === 'checkbox'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled"
          style="width: 100%"
        >
          <el-checkbox
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-checkbox-group>
        <!-- radio -->
        <el-radio-group
          v-else-if="item.fieldType === 'radio'"
          v-model="formModel[item.prop]"
          :disabled="item.disabled"
          style="width: 100%"
        >
          <el-radio
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-radio-group>
        <!-- date picker -->
        <el-date-picker
          v-else-if="item.fieldType === 'date'"
          v-model="formModel[item.prop]"
          :type="item.dateType || 'date'"
          :placeholder="item.placeholder"
          :format="item.format || 'yyyy-MM-dd'"
          :value-format="item.valueFormat || 'yyyy-MM-dd'"
          style="width: 100%"
        />
        <!-- WgBusinessType -->
        <div
          v-else-if="item.fieldType === 'businessType'"
          class="business-type"
          style="display: flex; align-items: center; gap: 10px; width: 100%"
        >
          <el-input
            v-model="formModel[item.prop]"
            type="text"
            style="flex: 1"
            :placeholder="item.placeholder || ''"
            :disabled="item.disabled"
            readonly
            @click="openDialog(item)"
          />
          <NeButton :disabled="item.disabled" @click="openDialog(item)"
            >···</NeButton
          >
        </div>
      </el-form-item>
      <el-form-item :class="`form-footer form-footer-${config.buttonAlign}`">
        <NeButton v-if="config.showCancel" @click="cancelForm">{{
          config.cancelText || '取消'
        }}</NeButton>
        <NeButton v-if="config.showReset" @click="resetForm">{{
          config.resetText || '重置'
        }}</NeButton>
        <NeButton v-if="config.showSubmit" type="primary" @click="submitForm">{{
          config.submitText || '提交'
        }}</NeButton>
      </el-form-item>
    </el-form>
    <widget-business-type
      v-model:visible="businessVisible"
      :fetch-by-init="true"
      :business-type-request="props.businessTypeRequest"
      @business-type-checked="onBusinessChecked"
    />
  </widget-wrapper>
</template>

<script setup lang="ts">
import { type Ref, computed, inject, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
// import { tryOnUnmounted } from '@vueuse/core'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import NeButton from '@neue-plus/components/button'
import { useApisRef } from '@neue-plus/components/material-render/src/context/event-flow'
import { buildQuery, getOdataTypeName } from '@neue-plus/utils/form'
import WidgetBusinessType from '../../business-type/src/business-type.vue'
import { getRules } from '../src/validation'
import { formApiMap } from '../../basic-form/src/form'
import type { PropType } from 'vue'
import type { FieldType, FormConfig } from './form'

const apiRefs = useApisRef()

defineOptions({
  name: 'BasicForm',
  inheritAttrs: false,
})

const props = defineProps({
  config: {
    type: Object as PropType<FormConfig>,
    default: () => ({}),
  },
  formItems: {
    type: Array as PropType<any>,
    default: () => [],
  },
  dialogFormEditRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormSubmitAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormSubmitPart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormEditRequestAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormEditRequestPart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormUpdateAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormUpdatePart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  dialogFormSubmitRelation: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  businessTypeRequest: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
})

const formComponents = ref([
  'text',
  'select',
  'number',
  'textarea',
  'date',
  'businessType',
])

const emit = defineEmits([
  'formSubmit',
  'formReset',
  'formCancel',
  'update:visible',
])

const formConfig = ref<FormConfig>({
  layout: 'horizontal',
  cancelText: '取消',
  submitText: '提交',
  resetText: '重置',
  showSubmit: true,
  showReset: false,
  showCancel: true,
  buttonAlign: 'center',
})
const formItemsData = ref<FieldType[]>([])
const formRef = ref()
// const businessTypeDialogVisible = ref(false)
interface FormModel {
  [key: string]: any
}
const formModel = ref<FormModel>({
  ncid: '',
})

const businessVisible = ref(false)
const activeBusinessField = ref<any>()
const editNcid = ref('')
const editData = ref<any>({})
const formModelInited = ref(false) // 表单是否inited
const dialogvisible = inject<Ref<boolean>>('dialogVisible')!
const odataType = ref('')

/* 关闭弹窗并回填 */
const onBusinessChecked = (node: any) => {
  console.log('onBusinessChecked', node)
  if (activeBusinessField.value) {
    formModel.value[activeBusinessField.value.prop] = node.label
  }
  businessVisible.value = false
}

const submitForm = async () => {
  console.log('submitForm-提交表单', formModel.value)
  await formRef.value?.validate(async (valid: any) => {
    if (valid) {
      const businessType =
        formModel.value?.businessType || editData.value?.odataType
      const apiConf = formApiMap[businessType || 'NeueCadPart']
      delete formModel.value.businessType
      console.log('apiConf', apiConf, editData.value)
      if (
        ['bomRelation'].includes(editData.value?.odataType) &&
        apiConf.submitApi
      ) {
        console.log('===============', editData.value.ncid)
        await apiRefs[apiConf.submitApi]?.({
          replaceData: {
            ncid: `'${editData.value.ncid}'`,
          },
          data: { ...formModel.value },
        })
        emit('formSubmit', formModel.value)
        return
      }
      // 不需要拉取详情的，视为新建提交
      console.log('props.config?.noRequest', props.config?.noRequest)
      if (props.config?.noRequest) {
        // 创建提交
        const submitData = unflatten(
          filterFlat(formModel.value, props.formItems)
        )
        delete submitData.ncid
        const extra = !props.config.noRequest
          ? { replaceData: { ncid: editNcid.value, data: submitData } }
          : {}
        console.log('submit submitData1====>', props.config.noRequest, extra)
        await apiRefs[apiConf.submitApi]?.({
          ...extra,
          data: { ...submitData },
        })
        emit('formSubmit', submitData)
        return
      }
      // 编辑提交
      if (editNcid.value && editData.value.odataType) {
        const formData = unflatten(filterFlat(formModel.value, props.formItems))
        if (!apiConf || typeof apiRefs[apiConf.updateApi!] !== 'function') {
          throw new TypeError(`apiRefs.${apiConf.updateApi!} is not a function`)
        }
        delete formData.ncid
        const res = await apiRefs[apiConf.updateApi!]({
          data: {
            ...formData,
            ncid: `${editNcid.value}`,
          },
        })
        emit('formSubmit', formModel.value)
        const rData = res.data || {}
        if (props.config.refreshAfterSave && rData.ncid) {
          location.href = buildQuery({
            ncid: rData.ncid,
            odataType: odataType.value,
          })
        }
        return
      }
      // }
    }
  })
}

const openDialog = (item: any) => {
  console.log('openDialog', item)
  if (item.disabled) return
  businessVisible.value = true
  activeBusinessField.value = item
}

const formReset = () => {
  formRef.value.resetFields()
  formModel.value = {}
  editNcid.value = ''
  formModelInited.value = false
  console.log('formWidget: 表单已重置')
}

const resetForm = () => {
  // formReset()
  emit('formReset', {})
}

const cancelForm = () => {
  emit('formCancel', formModel.value)
  emit('update:visible', false)
  if (props.config.clearOnClose !== false) {
    formReset()
  }
}

const newFormItems = computed(() => {
  return props.formItems.filter((i: { fieldType: string }) =>
    formComponents.value.includes(i.fieldType)
  )
})

const fetchForm = async (params: { ncid: string; odataType: string }) => {
  const businessTypeName = getOdataTypeName(params.odataType)
  // console.log('fetchForm==>', params, formApiMap[businessTypeName])
  const apiConf = formApiMap[businessTypeName]
  if (!apiConf || typeof apiRefs[apiConf.editApi!] !== 'function') {
    throw new TypeError(`apiRefs.${businessTypeName}.editApi is not a function`)
  }
  // console.log('=========fetchForm request=============')
  // console.log('fetchForm', params, params.ncid, formModel.value)

  // 这里直接返回接口原始数据
  return apiRefs[apiConf.editApi!]({
    replaceData: {
      ncid: `'${params.ncid}'`,
    },
    params: {
      $expand: 'version($expand=revision),thumbnail,lifecycleStatus',
    },
  })
}

const { data: remoteData, run: loadForm } = useRequest(fetchForm, {
  manual: true,
  initialData: {},
})

const initModel = () => {
  const model: FormModel = {}
  formItemsData.value.forEach((item: any) => {
    model[item.prop] = item.value ?? ''
  })
  formModel.value = model
  console.log('initModel formModel', formModel.value)
  formModelInited.value = true
}

const initEdit = (eData: any) => {
  console.log('initEdit==>', eData)
  editNcid.value = eData.ncid
  editData.value = { ...eData }
  console.log('initEdit111==>', editNcid.value, editData.value, eData)
}
function unflatten(flat: Record<string, any>): any {
  const root: any = {}
  for (const [k, v] of Object.entries(flat)) {
    const keys = k.split('.')
    let cur = root
    keys.slice(0, -1).forEach((p) => (cur = cur[p] ??= {}))
    cur[keys.at(-1)!] = v
  }
  return root
}

const isEmpty = (v: unknown) =>
  v == null || v === '' || (Array.isArray(v) && v.length === 0)

// 过滤
function filterFlat(
  flat: Record<string, any>,
  fields: Array<{ prop: string; disabled?: boolean }>
): Record<string, any> {
  const keep = new Set(
    fields
      .filter((f) => !f.disabled && !isEmpty(flat[f.prop]))
      .map((f) => f.prop)
  )

  return Object.fromEntries(Object.entries(flat).filter(([k]) => keep.has(k)))
}
/**
 * 忽略大小写、按点路径从深层对象取值
 * @param template   扁平模板（空串 → 需要取值）
 * @param source     后端完整对象
 * @returns          仅含模板中非空字段的新对象
 */
function pickMatchedFieldsDbg(template: any, source: any) {
  console.log('pickMatchedFields', JSON.stringify(source))
  // 关键：强制 Vue 解包
  JSON.parse(JSON.stringify(source))

  /* 下面保持你原来的逻辑 */
  const res: any = {}
  Object.entries(template).forEach(([path, v]) => {
    if (v !== '') return
    const keys = path.toLowerCase().split('.')
    let cur = source
    let ok = true
    for (const k of keys) {
      const foundKey =
        cur && Object.keys(cur).find((kk) => kk.toLowerCase() === k)
      if (!foundKey) {
        ok = false
        break
      }
      cur = cur[foundKey]
    }
    if (ok) res[path] = cur
  })
  return res
}

function tryFillForm() {
  // 三个必要条件
  if (!formModelInited.value || !editData.value || !remoteData.value?.data)
    return

  const rData = remoteData.value.data
  const newModel = pickMatchedFieldsDbg(formModel.value, rData)
  odataType.value =
    getOdataTypeName(rData['@odata.type'] || '') || 'NeueCadPart'
  console.log('tryFillForm newModel', newModel)
  formModel.value = {
    ...newModel,
    businessType: odataType.value,
  }
  formModelInited.value = false
}

// watchEffect(() => {
//   if (!formModelInited.value || !editData.value || !remoteData.value) return
//   if (!remoteData.value.data) return
//   const rData = remoteData.value.data || {}
//   console.log('watch newModel==1', remoteData.value, editData?.value)
//   const newModel = pickMatchedFieldsDbg(formModel.value, rData)
//   console.log('watch newModel==2', newModel)
//   formModel.value = {
//     ...newModel,
//     businessType: getOdataTypeName(rData['@odata.type'] || '') || 'NeueCadPart',
//   }
// })

// watch(
//   () => editData.value,
//   (newVal) => {
//     console.log('editData已更新:', newVal)
//     if (props.config?.noReqest) {
//       return
//     }
//     newVal && loadForm({ ...newVal })
//   },
//   { immediate: true, deep: true }
// )

watch(
  () => editData.value,
  (newVal) => {
    console.log('editData已更新:', newVal)
    if (props.config?.noRequest) return
    newVal && loadForm({ ...newVal })
    tryFillForm()
  },
  { immediate: true, deep: true }
)

watch(
  () => props.config,
  (newConfig: FormConfig) => {
    console.log('配置已更新:', newConfig)
    formConfig.value = { ...formConfig.value, ...newConfig }
  },
  { deep: true }
)

watch(
  () => remoteData.value?.data,
  () => tryFillForm(),
  { immediate: true }
)

watch(
  () => dialogvisible.value,
  (newVal) => {
    if (!newVal) {
      formModelInited.value = false
      // resetForm()
      formReset()
    } else {
      formModelInited.value = true
      formItemsData.value = [...props.formItems]
      initModel()
      tryFillForm()
    }
  },
  { immediate: true, deep: true }
)

defineExpose({
  initEdit,
  formReset,
})
</script>

<style scoped />
