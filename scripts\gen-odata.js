#!/usr/bin/env node
/**
 * SAP Cloud SDK OData Client Generator
 * 优化版本 - 支持多种配置和调试模式
 */

const fs = require('fs')
const path = require('path')
const { generate } = require('@sap-cloud-sdk/generator')

// 配置常量
const CONFIG = {
  // 输入输出路径
  INPUT_DIR: path.resolve(__dirname, '../resource'),
  OUTPUT_DIR: path.resolve(__dirname, '../play/src/vdm'),
  OPTIONS_FILE: path.resolve(__dirname, '../options-per-service-new.json'),

  // 服务配置
  SERVICE_NAME: 'neue',
  OData_VERSION: 'v4',

  // 生成选项
  GENERATION_OPTIONS: {
    overwrite: true,
    transpile: false,
    readme: false,
    clearOutputDir: true,
    generateTypedocJson: false,
    packageJson: false,
    generateNpmrc: false,
    generatePackageJson: false,
    generateCSN: false,
    forceOverwrite: true,
    skipValidation: true,
    // 新增选项
    generateJs: false,
    generateBundle: false,
    verbose: true,
  },
}

/**
 * 检查必要文件是否存在
 */
function validateEnvironment() {
  console.log('🔍 检查环境...')

  // 检查输入目录
  if (!fs.existsSync(CONFIG.INPUT_DIR)) {
    throw new Error(`输入目录不存在: ${CONFIG.INPUT_DIR}`)
  }

  // 检查元数据文件
  const metadataFile = path.join(CONFIG.INPUT_DIR, 'neue.xml')
  if (!fs.existsSync(metadataFile)) {
    throw new Error(`元数据文件不存在: ${metadataFile}`)
  }

  // 检查配置文件（可选）
  if (CONFIG.OPTIONS_FILE && !fs.existsSync(CONFIG.OPTIONS_FILE)) {
    console.log(`⚠️  配置文件不存在，将使用默认配置: ${CONFIG.OPTIONS_FILE}`)
    CONFIG.OPTIONS_FILE = undefined
  }

  // 确保输出目录存在
  if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true })
    console.log(`📁 创建输出目录: ${CONFIG.OUTPUT_DIR}`)
  }

  console.log('✅ 环境检查通过')
}

/**
 * 构建生成配置
 */
function buildGenerationConfig() {
  const config = {
    input: CONFIG.INPUT_DIR,
    outputDir: CONFIG.OUTPUT_DIR,
    serviceName: CONFIG.SERVICE_NAME,
    version: CONFIG.OData_VERSION,
    ...CONFIG.GENERATION_OPTIONS,
  }

  // 添加配置文件（如果存在）
  if (CONFIG.OPTIONS_FILE) {
    config.optionsPerService = CONFIG.OPTIONS_FILE
  }

  return config
}

/**
 * 主要生成函数
 */
async function generateODataClient() {
  try {
    console.log('🚀 开始生成 OData 客户端...')
    console.log(`📂 输入目录: ${CONFIG.INPUT_DIR}`)
    console.log(`📂 输出目录: ${CONFIG.OUTPUT_DIR}`)
    console.log(`🏷️  服务名称: ${CONFIG.SERVICE_NAME}`)
    console.log(`📋 OData 版本: ${CONFIG.OData_VERSION}`)

    // 验证环境
    validateEnvironment()

    // 构建配置
    const generationConfig = buildGenerationConfig()

    console.log('⚙️  生成配置:')
    console.log(JSON.stringify(generationConfig, null, 2))

    // 执行生成
    console.log('🔄 正在生成代码...')
    await generate(generationConfig)

    console.log('✅ OData 客户端生成成功!')
    console.log(`📁 生成的文件位于: ${CONFIG.OUTPUT_DIR}`)

    // 显示生成的文件统计
    showGeneratedFiles()
  } catch (error) {
    console.error('❌ 生成 OData 客户端时发生错误:')
    console.error(error.message)

    if (error.stack) {
      console.error('\n📋 错误堆栈:')
      console.error(error.stack)
    }

    process.exit(1)
  }
}

/**
 * 显示生成的文件统计
 */
function showGeneratedFiles() {
  try {
    const outputPath = path.join(CONFIG.OUTPUT_DIR, CONFIG.SERVICE_NAME)
    if (fs.existsSync(outputPath)) {
      const files = fs.readdirSync(outputPath)
      console.log(`\n📊 生成统计:`)
      console.log(`   - 总文件数: ${files.length}`)
      console.log(
        `   - TypeScript 文件: ${files.filter((f) => f.endsWith('.ts')).length}`
      )
      console.log(
        `   - API 文件: ${files.filter((f) => f.includes('Api.ts')).length}`
      )
      console.log(
        `   - 实体文件: ${
          files.filter(
            (f) =>
              !f.includes('Api.ts') &&
              !f.includes('RequestBuilder.ts') &&
              f.endsWith('.ts')
          ).length
        }`
      )
    }
  } catch {
    console.log('⚠️  无法获取文件统计信息')
  }
}

// 执行生成
if (require.main === module) {
  generateODataClient()
}
