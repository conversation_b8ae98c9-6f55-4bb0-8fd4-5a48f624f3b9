// scripts/gen-odata.js
const path = require('path')
const { generate } = require('@sap-cloud-sdk/generator')

const input = path.resolve(__dirname, '../resource')
const outputDir = path.resolve(__dirname, '../vdm')
const optionsPerService = path.resolve(__dirname, '../options-per-service.json')

generate({
  input,
  outputDir,
  overwrite: true,
  transpile: false,
  readme: false,
  clearOutputDir: true,
  generateTypedocJson: false,
  packageJson: false,
  serviceName: 'neue',
  version: 'v4',
  optionsPerService,
  skipValidation: true, // ✅ 跳过类型检查
})
  .then(() => console.log('✅ OData client generated successfully!'))
  .catch((err) => console.error('❌ Error generating OData client:', err))
