/**
 * OData 客户端测试
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { createODataClient, filter, queryOptions } from '../index'
import type { ODataServiceConfig } from '../types'

describe('ODataClient', () => {
  let client: ReturnType<typeof createODataClient>
  let config: ODataServiceConfig

  beforeEach(() => {
    config = {
      baseUrl: 'https://services.odata.org/V4/Northwind/Northwind.svc',
      headers: {
        'Accept': 'application/json'
      }
    }
    client = createODataClient(config)
  })

  describe('客户端创建', () => {
    it('应该成功创建客户端', () => {
      expect(client).toBeDefined()
      expect(client.config).toEqual(config)
    })

    it('应该正确处理服务路径', () => {
      const configWithPath = {
        ...config,
        servicePath: 'api/v1'
      }
      const clientWithPath = createODataClient(configWithPath)
      expect(clientWithPath.config.servicePath).toBe('api/v1')
    })
  })

  describe('实体集操作', () => {
    it('应该创建实体集操作对象', () => {
      const products = client.entitySet('Products')
      expect(products).toBeDefined()
      expect(products.getEntitySetName()).toBe('Products')
    })

    it('应该复用相同的实体集操作对象', () => {
      const products1 = client.entitySet('Products')
      const products2 = client.entitySet('Products')
      expect(products1).toBe(products2)
    })
  })

  describe('查询构建器', () => {
    it('应该创建查询构建器', () => {
      const products = client.entitySet('Products')
      const query = products.query()
      expect(query).toBeDefined()
    })

    it('应该正确构建查询选项', () => {
      const products = client.entitySet('Products')
      const query = products.query()
        .select('ProductID', 'ProductName')
        .filter('CategoryID eq 1')
        .orderBy('ProductName')
        .top(10)

      const options = query.getOptions()
      expect(options.$select).toEqual(['ProductID', 'ProductName'])
      expect(options.$filter).toBe('CategoryID eq 1')
      expect(options.$orderby).toBe('ProductName asc')
      expect(options.$top).toBe(10)
    })
  })

  describe('过滤器构建器', () => {
    it('应该构建简单过滤器', () => {
      const filterStr = filter()
        .eq('CategoryID', 1)
        .build()
      
      expect(filterStr).toBe('CategoryID eq 1')
    })

    it('应该构建复合过滤器', () => {
      const filterStr = filter()
        .eq('CategoryID', 1)
        .and()
        .contains('ProductName', 'Chai')
        .build()
      
      expect(filterStr).toBe("CategoryID eq 1 and contains(ProductName, 'Chai')")
    })

    it('应该处理分组', () => {
      const filterStr = filter()
        .groupStart()
        .eq('CategoryID', 1)
        .or()
        .eq('CategoryID', 2)
        .groupEnd()
        .and()
        .gt('UnitPrice', 10)
        .build()
      
      expect(filterStr).toBe('( CategoryID eq 1 or CategoryID eq 2 ) and UnitPrice gt 10')
    })
  })

  describe('查询选项构建器', () => {
    it('应该构建查询选项', () => {
      const options = queryOptions()
        .select('ProductID', 'ProductName')
        .expand('Category')
        .filter('CategoryID eq 1')
        .orderBy('ProductName')
        .top(10)
        .skip(5)
        .count(true)
        .build()

      expect(options).toEqual({
        $select: ['ProductID', 'ProductName'],
        $expand: ['Category'],
        $filter: 'CategoryID eq 1',
        $orderby: 'ProductName asc',
        $top: 10,
        $skip: 5,
        $count: true
      })
    })
  })

  describe('拦截器', () => {
    it('应该添加请求拦截器', () => {
      const interceptor = (config: any) => {
        config.headers = { ...config.headers, 'X-Custom': 'test' }
        return config
      }

      client.addRequestInterceptor(interceptor)
      // 这里只是测试添加，实际执行需要真实的网络请求
      expect(true).toBe(true)
    })

    it('应该移除拦截器', () => {
      const interceptor = (config: any) => config

      client.addRequestInterceptor(interceptor)
      client.removeRequestInterceptor(interceptor)
      // 这里只是测试移除，实际验证需要真实的网络请求
      expect(true).toBe(true)
    })
  })

  describe('工具函数', () => {
    it('应该正确转义字符串', () => {
      const { escapeODataString } = require('../utils')
      expect(escapeODataString("O'Reilly")).toBe("O''Reilly")
    })

    it('应该格式化日期', () => {
      const { formatODataDate } = require('../utils')
      const date = new Date('2023-01-01T00:00:00.000Z')
      expect(formatODataDate(date)).toBe('2023-01-01T00:00:00.000Z')
    })

    it('应该构建实体键', () => {
      const { buildEntityKey } = require('../utils')
      
      // 单键
      expect(buildEntityKey({ id: 1 })).toBe('1')
      expect(buildEntityKey({ id: 'test' })).toBe("'test'")
      
      // 复合键
      expect(buildEntityKey({ id: 1, code: 'A' })).toBe("id=1,code='A'")
    })
  })
})
