﻿<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
*/
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>datajs test manager</title>
    <script type="text/javascript" src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-1.5.1.js"></script>
    <script type="text/javascript" src="test-list.js"></script>
    <script type="text/javascript">
        var serviceRoot = "./common/TestLogger.svc/";
        $(function () {
            $(getAllTestFiles()).each(function () {
                $("#pages").append("<input type='checkbox' name='page' value='" + this + "' checked />" + this + "<br />");
            });
            refreshActiveRuns();
        });

        var createTestRun = function (form) {
            $.getJSON(serviceRoot + "CreateTestRun", function (data) {
                var testRunId = data.d;

                // Build pages list
                var pages = [];
                $(form).find("input[name='page']:checked").each(function () {
                    pages.push(this.value);
                });

                var firstPage = pages[0];
                pages.shift();

                $.get(serviceRoot + "MarkInProgress?testRunId=" + testRunId, function () {
                    $.get(serviceRoot + "SetTestNamePrefix?testRunId=" + testRunId + "&prefix=" + $("#browser").val() + "-", function () {
                        var renderLinks = function () {
                            $("#runLink").attr("href", firstPage + "?testRunId=" + testRunId);
                            $("#runLink").text(testRunId);
                            $("#resultsLink").attr("href", serviceRoot + "GetTestRunResults?testRunId=" + testRunId);
                            $("#resultsLink").text(testRunId);
                            refreshActiveRuns();
                        };

                        if (pages.length > 0) {
                            $.get(serviceRoot + "AddTestPages?testRunId=" + testRunId + "&pages=" + pages.join(","), renderLinks);
                        }
                        else {
                            renderLinks();
                        }
                    });
                });
            });
        };

        var refreshActiveRuns = function () {
            $("#activeRuns").empty();
            $.getJSON(serviceRoot + "GetActiveTestRuns", function (data) {
                if (data.d.length === 0) {
                    $("#activeRuns").text("There are no active runs");
                } else {
                    $.each(data.d, function (_, id) {
                        $("#activeRuns").append("<a href='" + serviceRoot + "GetTestRunResults?testRunId=" + id + "'>" + id + "</a><br />");
                    })
                }
            });
        };
    </script>
</head>
<body>
    <h1>datajs test manager</h1>
    <table style="width:100%"><tr><td style="vertical-align:top">
        <h4>1. Create Test Run</h4>
        <form onsubmit="createTestRun(this); return false;">
            <div>Pages</div>
            <div id="pages"></div>
            <br />
            <div>Browser: <input type="text" id="browser" /></div>
            <br />
            <input type="submit" value="Create Test Run" />
        </form>

        <h4>2. Run Tests</h4>
        Test Run ID: <a id="runLink"></a>

        <h4>3. View Results</h4>
        Test Run ID: <a id="resultsLink"></a>

    </td><td style="vertical-align:top">
        <h4>Active Runs <input type="button" value="Refresh" onclick="refreshActiveRuns()" /></h4>
        <div id="activeRuns"></div>
    </td></tr></table>
</body>
</html>