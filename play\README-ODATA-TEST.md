# SAP Cloud SDK OData 简单测试例子

这是一个简单的 SAP Cloud SDK OData 客户端测试例子，展示如何在 Vue 3 项目中使用生成的 OData 客户端。

## 🚀 快速开始

1. **启动开发服务器**
   ```bash
   cd play
   npm run dev
   ```

2. **打开浏览器**
   访问 http://localhost:5174

3. **运行测试**
   点击页面上的按钮来运行不同的测试

## 📁 文件结构

```
play/src/
├── components/spa/index.vue          # 主测试界面
├── examples/simple-odata-test.ts     # 简单的 OData 测试函数
└── vdm/neue/                         # 生成的 OData 客户端代码
    ├── service.ts                    # 主服务文件
    ├── CadParts.ts                   # CAD 零件实体
    ├── PartType.ts                   # 零件类型枚举
    ├── LifecycleState.ts             # 生命周期状态枚举
    └── ...                           # 其他生成的文件
```

## 🧪 测试功能

### 1. 服务连接测试
- 验证 OData 服务是否正确初始化
- 检查可用的 API 列表
- 确认服务配置

### 2. 基本查询测试
- 模拟获取 CAD 零件列表
- 展示基本的查询操作
- 返回模拟数据

### 3. 条件查询测试
- 模拟按零件类型筛选
- 展示 OData 过滤语法
- 返回筛选后的数据

### 4. 运行所有测试
- 依次执行所有测试
- 显示测试结果汇总

## 🔧 技术栈

- **Vue 3** - 前端框架
- **TypeScript** - 类型安全
- **SAP Cloud SDK** - OData 客户端生成
- **Vite** - 构建工具

## 📝 代码示例

### 基本用法

```typescript
import { neue } from '../vdm/neue/service'
import { PartType, LifecycleState } from '../vdm/neue'

// 初始化服务
const service = neue()

// 获取 CAD 零件 API
const { cadPartsApi } = service

// 模拟查询（实际使用时需要真实的后端）
const mockDestination = {
  url: 'https://your-odata-service.com',
  headers: {
    'Content-Type': 'application/json'
  }
}

// 执行查询
const parts = await cadPartsApi
  .requestBuilder()
  .getAll()
  .top(10)
  .execute(mockDestination)
```

### 枚举使用

```typescript
// 零件类型
PartType.NEUE_PRT    // 零件
PartType.NEUE_ASM    // 装配

// 生命周期状态
LifecycleState.ACTIVE      // 活跃
LifecycleState.CREATING    // 创建中
LifecycleState.INACTIVE    // 非活跃
```

## 🔍 调试

1. **浏览器控制台**
   - 打开开发者工具查看详细日志
   - 所有测试都会输出详细信息

2. **网络面板**
   - 查看实际的 HTTP 请求（如果连接真实后端）

3. **Vue DevTools**
   - 安装 Vue DevTools 扩展来调试组件状态

## 🚨 注意事项

1. **模拟数据**
   - 当前示例使用模拟数据，不会发送真实的 HTTP 请求
   - 要连接真实后端，需要配置正确的 destination

2. **类型安全**
   - 所有 OData 实体都有完整的 TypeScript 类型定义
   - 编译时会检查类型错误

3. **错误处理**
   - 示例包含基本的错误处理
   - 生产环境需要更完善的错误处理机制

## 🔗 相关链接

- [SAP Cloud SDK 文档](https://sap.github.io/cloud-sdk/)
- [Vue 3 文档](https://vuejs.org/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [Vite 文档](https://vitejs.dev/)

## 📞 支持

如果遇到问题，请检查：
1. 开发服务器是否正常启动
2. 浏览器控制台是否有错误信息
3. TypeScript 编译是否通过
4. 所有依赖是否正确安装
