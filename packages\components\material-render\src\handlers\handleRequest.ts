// request.ts
import odataQuery from 'odata-query'
import { replaceTemplate, strReplaceTemplate } from '@neue-plus/utils'
import { HttpMethod } from '../types'
import service from '../utils/service'

export interface RequestOptions {
  method: HttpMethod
  url: string
  params?: Record<string, any>
  data?: Record<string, any>
  query?: Record<string, any> // OData 查询参数
  //url替换参数
  replaceData?: Record<string, any> // OData 查询参数
}

/**
 * 统一请求函数（支持 query/get/post/put/patch/delete）
 */
export async function request<T = any>(options: RequestOptions) {
  let {
    method,
    url,
    params = {},
    data = {},
    replaceData,
    query,
    ...restConfig
  } = options
  // 如果是 query 方法，则转成 GET 并自动拼接 OData 查询字符串
  if (query) {
    const obj: any = {}
    Object.entries(query).forEach(([key, value]) => {
      if (typeof value === 'string') {
        obj[key] = encodeURIComponent(value)
      } else {
        obj[key] = value
      }
    })
    console.log('111', obj)
    url += odataQuery(obj)
  }
  if (replaceData) {
    url = strReplaceTemplate(url, replaceData)
    data = replaceTemplate(data, replaceData)
    params = replaceTemplate(params, replaceData)
  }
  const result: any = await service<T>({
    method,
    url,
    ...restConfig,
    ...(method === 'get' || method === 'delete' ? { params } : { data }),
  })
  if (query) {
    return {
      data: result['value'], // 根据odata返回的结构写
      total: result['@odata.count'],
      success: true,
    }
  } else {
    return {
      data: result, // 根据odata返回的结构写
      success: true,
    }
  }
}
