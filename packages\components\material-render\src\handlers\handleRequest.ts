// request.ts
import odataQuery from 'odata-query'
import { merge } from 'lodash-unified'
import { HttpMethod } from '../types'
import service from '../utils/service'
import { replaceTemplate } from '../utils/replaceTemplate'

interface RequestOptions {
  method: HttpMethod
  url: string
  params: Record<string, any>
  data: Record<string, any>
  query?: Record<string, any> // OData 查询参数
}

/**
 * 统一请求函数（支持 query/get/post/put/patch/delete）
 */
export async function request<T = any>(options: RequestOptions) {
  let { method, url, params, data, query, ...restConfig } = options

  // 如果是 query 方法，则转成 GET 并自动拼接 OData 查询字符串
  if (query) {
    url += odataQuery(query)
  }
  if (method === 'get') {
    url = replaceTemplate(url, merge({}, params, data))
  } else if (method === 'put' || method === 'patch' || method === 'delete') {
    url = replaceTemplate(url, data)
  }
  const result: any = await service<T>({
    method,
    url,
    ...(method === 'get' || method === 'delete' ? { params } : { data }),
    ...restConfig,
  })
  if (query) {
    return {
      data: result['value'], // 根据odata返回的结构写
      total: result['@odata.count'],
      success: true,
    }
  } else {
    return {
      data: result, // 根据odata返回的结构写
      success: true,
    }
  }
}
