/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadFileFileRelations } from './RelCadFileFileRelations';

/**
 * Request builder class for operations supported on the {@link RelCadFileFileRelations} entity.
 */
export class RelCadFileFileRelationsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadFileFileRelations<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadFileFileRelations` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadFileFileRelations` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadFileFileRelations<T>, T> {
    return new GetAllRequestBuilder<RelCadFileFileRelations<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `RelCadFileFileRelations` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadFileFileRelations`.
   */
  create(
    entity: RelCadFileFileRelations<T>
  ): CreateRequestBuilder<RelCadFileFileRelations<T>, T> {
    return new CreateRequestBuilder<RelCadFileFileRelations<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `RelCadFileFileRelations` entity based on its keys.
   * @param ncid Key property. See {@link RelCadFileFileRelations.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadFileFileRelations` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadFileFileRelations<T>, T> {
    return new GetByKeyRequestBuilder<RelCadFileFileRelations<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadFileFileRelations`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadFileFileRelations`.
   */
  update(
    entity: RelCadFileFileRelations<T>
  ): UpdateRequestBuilder<RelCadFileFileRelations<T>, T> {
    return new UpdateRequestBuilder<RelCadFileFileRelations<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadFileFileRelations`.
   * @param ncid Key property. See {@link RelCadFileFileRelations.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadFileFileRelations`.
   */
  delete(ncid: string): DeleteRequestBuilder<RelCadFileFileRelations<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadFileFileRelations`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadFileFileRelations` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadFileFileRelations<T>
  ): DeleteRequestBuilder<RelCadFileFileRelations<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<RelCadFileFileRelations<T>, T> {
    return new DeleteRequestBuilder<RelCadFileFileRelations<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof RelCadFileFileRelations
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
