<template>
  <div class="odata-example">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>OData 客户端示例</span>
        </div>
      </template>

      <!-- 基本查询示例 -->
      <div class="example-section">
        <h3>1. 基本查询示例</h3>
        <el-button @click="basicQuery" type="primary">执行基本查询</el-button>
        <pre v-if="basicResult">{{ basicResult }}</pre>
      </div>

      <!-- 过滤查询示例 -->
      <div class="example-section">
        <h3>2. 过滤查询示例</h3>
        <el-button @click="filterQuery" type="success">执行过滤查询</el-button>
        <pre v-if="filterResult">{{ filterResult }}</pre>
      </div>

      <!-- 复杂查询示例 -->
      <div class="example-section">
        <h3>3. 复杂查询示例</h3>
        <el-button @click="complexQuery" type="warning">执行复杂查询</el-button>
        <pre v-if="complexResult">{{ complexResult }}</pre>
      </div>

      <!-- 查询构建器示例 -->
      <div class="example-section">
        <h3>4. 查询构建器示例</h3>
        <el-button @click="queryBuilderExample" type="info"
          >查询构建器</el-button
        >
        <pre v-if="queryBuilderResult">{{ queryBuilderResult }}</pre>
      </div>

      <!-- 错误处理示例 -->
      <div class="example-section">
        <h3>5. 错误处理示例</h3>
        <el-button @click="errorHandlingExample" type="danger"
          >错误处理</el-button
        >
        <pre v-if="errorResult" class="error">{{ errorResult }}</pre>
      </div>

      <!-- 现有 OData 组件示例 -->
      <div class="example-section">
        <h3>5. 元数据查询示例</h3>
        <el-button @click="metadataQuery" type="info">查询服务元数据</el-button>
        <pre>{{ metadataResult }}</pre>
      </div>

      <div class="example-section">
        <h3>6. 现有 OData 组件示例</h3>
        <el-button @click="showExistingComponent" type="warning"
          >显示现有组件</el-button
        >
        <div v-if="showExisting" class="existing-component">
          <div class="config-display">
            <h4>OData 配置示例：</h4>
            <pre>{{ JSON.stringify(odataMaterialConfig, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  createClient,
  createODataClient,
  filter,
  queryOptions,
} from '@neue-plus/odata'
import { ElCard, ElButton, ElMessage } from 'element-plus'
// import { NeMaterialRender } from '@neue-plus/widgets'
import { odataMaterialConfig } from '../../constant/index'

// 响应式数据
const basicResult = ref('')
const filterResult = ref('')
const complexResult = ref('')
const queryBuilderResult = ref('')
const errorResult = ref('')

// 创建 OData 客户端 - 使用正式环境
const client = createClient(
  'https://api-cn-hangzhou-2.cloud.neuetech.cn',
  '/modeling/202512/OdataService'
)

// 创建带配置的 OData 客户端
const configuredClient = createODataClient({
  baseUrl: 'https://api-cn-hangzhou-2.cloud.neuetech.cn',
  servicePath: '/modeling/202512/OdataService',
  timeout: 15000,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
})

const showExisting = ref(false)
const metadataResult = ref('')

// 元数据查询示例
const metadataQuery = async () => {
  try {
    metadataResult.value = '正在查询元数据...'

    // 直接访问元数据端点
    const metadataUrl =
      'https://api-cn-hangzhou-2.cloud.neuetech.cn/modeling/202512/OdataService/$metadata?$format=xml'

    try {
      const response = await fetch(metadataUrl, {
        method: 'GET',
        headers: {
          Accept: 'application/xml, text/xml',
        },
      })

      if (response.ok) {
        const xmlText = await response.text()
        metadataResult.value = `元数据查询成功!\n\n元数据端点: ${metadataUrl}\n\n响应状态: ${
          response.status
        } ${
          response.statusText
        }\n\n元数据内容 (前500字符):\n${xmlText.substring(
          0,
          500
        )}...\n\n完整的元数据包含了服务的所有实体类型、属性和关系定义。`
      } else {
        metadataResult.value = `元数据查询失败!\n\n元数据端点: ${metadataUrl}\n\n响应状态: ${response.status} ${response.statusText}\n\n说明:\n- 可能需要认证\n- 可能存在 CORS 限制\n- 服务可能不可用`
      }
    } catch (fetchError) {
      metadataResult.value = `元数据查询示例:\n\n元数据端点: ${metadataUrl}\n\n说明:\n- 这是正式环境的 OData 服务元数据端点\n- 元数据描述了服务的所有实体类型和操作\n- 包含 CadParts 实体的完整定义\n- 在实际应用中，可以解析元数据来动态生成客户端代码\n\n网络错误: ${fetchError}\n\n注意: 由于浏览器的 CORS 限制，可能无法直接访问。在实际应用中，应该通过后端代理或配置适当的 CORS 头。`
    }

    ElMessage.success('元数据查询完成！')
  } catch (error) {
    metadataResult.value = `错误: ${error}`
    ElMessage.error('元数据查询失败')
  }
}

// 显示现有组件的函数
const showExistingComponent = () => {
  showExisting.value = !showExisting.value
  if (showExisting.value) {
    ElMessage.success('显示现有 OData 组件')
  } else {
    ElMessage.info('隐藏现有 OData 组件')
  }
}

// 1. 基本查询示例
const basicQuery = async () => {
  try {
    basicResult.value = '正在查询...'

    // 获取 CAD 零件列表
    const query = client
      .entitySet('CadParts')
      .query()
      .select('ncid', 'code', 'name', 'schemaVersion')
      .top(5)

    // 实际查询执行
    try {
      const result = await query.execute()
      basicResult.value = `基本查询成功!\n\n查询配置:\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion\n- 限制条数: 5\n\n查询结果:\n${JSON.stringify(
        result,
        null,
        2
      )}`
    } catch (executeError) {
      basicResult.value = `基本查询示例:\n\n查询配置:\n- 服务端点: https://api-cn-hangzhou-2.cloud.neuetech.cn/modeling/202512/OdataService\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion\n- 限制条数: 5\n\n注意: 由于跨域或认证限制，无法直接执行查询。\n在实际应用中，需要配置适当的认证和CORS设置。\n\n执行错误: ${executeError}`
    }

    ElMessage.success('基本查询构建成功！')
  } catch (error) {
    basicResult.value = `错误: ${error}`
    ElMessage.error('基本查询失败')
  }
}

// 2. 过滤查询示例
const filterQuery = async () => {
  try {
    filterResult.value = '正在构建过滤查询...'

    // 使用过滤器查询特定版本的 CAD 零件
    const query = client
      .entitySet('CadParts')
      .query()
      .select('ncid', 'code', 'name', 'schemaVersion', 'lifecycleState')
      .filter("schemaVersion eq '1.0'")
      .orderBy('code', 'asc')
      .top(10)

    // 尝试执行查询
    try {
      const result = await query.execute()
      filterResult.value = `过滤查询成功!\n\n查询配置:\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion, lifecycleState\n- 过滤条件: schemaVersion = '1.0'\n- 排序: 按代码升序\n- 限制: 前10条记录\n\n查询结果:\n${JSON.stringify(
        result,
        null,
        2
      )}`
    } catch (executeError) {
      filterResult.value = `过滤查询示例:\n\n查询配置:\n- 服务端点: https://api-cn-hangzhou-2.cloud.neuetech.cn/modeling/202512/OdataService\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion, lifecycleState\n- 过滤条件: schemaVersion = '1.0'\n- 排序: 按代码升序\n- 限制: 前10条记录\n\n执行错误: ${executeError}`
    }

    ElMessage.success('过滤查询构建成功！')
  } catch (error) {
    filterResult.value = `错误: ${error}`
    ElMessage.error('过滤查询失败')
  }
}

// 3. 复杂查询示例
const complexQuery = async () => {
  try {
    complexResult.value = '正在构建复杂查询...'

    // 复杂查询：包含展开、多重过滤、排序
    const query = client
      .entitySet('CadParts')
      .query()
      .select(
        'ncid',
        'code',
        'name',
        'schemaVersion',
        'lifecycleState',
        'volume'
      )
      .filter("lifecycleState eq 'Active' and schemaVersion eq '1.0'")
      .expand('thumbnail,version')
      .orderBy('code')
      .skip(5)
      .top(15)

    // 尝试执行查询
    try {
      const result = await query.execute()
      complexResult.value = `复杂查询成功!\n\n查询配置:\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion, lifecycleState, volume\n- 过滤条件: lifecycleState = 'Active' AND schemaVersion = '1.0'\n- 展开关联: thumbnail, version\n- 排序: 按代码\n- 分页: 跳过5条，取15条\n\n查询结果:\n${JSON.stringify(
        result,
        null,
        2
      )}`
    } catch (executeError) {
      complexResult.value = `复杂查询示例:\n\n查询配置:\n- 服务端点: https://api-cn-hangzhou-2.cloud.neuetech.cn/modeling/202512/OdataService\n- 实体集: CadParts\n- 选择字段: ncid, code, name, schemaVersion, lifecycleState, volume\n- 过滤条件: lifecycleState = 'Active' AND schemaVersion = '1.0'\n- 展开关联: thumbnail, version\n- 排序: 按代码\n- 分页: 跳过5条，取15条\n\n这展示了 OData 查询的强大功能，包括复杂过滤、关联数据展开和分页。\n\n执行错误: ${executeError}`
    }

    ElMessage.success('复杂查询构建成功！')
  } catch (error) {
    complexResult.value = `错误: ${error}`
    ElMessage.error('复杂查询失败')
  }
}

// 4. 查询构建器示例
const queryBuilderExample = async () => {
  try {
    queryBuilderResult.value = '正在使用查询构建器...'

    // 使用过滤器构建器
    const filterBuilder = filter()
      .eq('schemaVersion', '1.0')
      .and()
      .eq('lifecycleState', 'Active')
      .or()
      .contains('name', 'Part')

    // 使用查询选项构建器
    const options = queryOptions()
      .select('ncid', 'code', 'name')
      .filter(filterBuilder.build())
      .orderBy('code')
      .top(10)

    queryBuilderResult.value = `查询构建器示例:\n\n过滤器构建:\n- schemaVersion = '1.0'\n- AND lifecycleState = 'Active'\n- OR name contains 'Part'\n\n查询选项:\n- 选择字段: ncid, code, name\n- 排序: 按代码\n- 限制: 前10条\n\n说明:\n- 使用流畅接口构建复杂过滤条件\n- 支持 AND、OR 逻辑操作\n- 支持各种比较操作符\n- 可组合多个查询选项\n\n这些构建器可以与 CadParts 实体一起使用，构建复杂的查询条件。`

    ElMessage.success('查询构建器示例完成！')
  } catch (error) {
    queryBuilderResult.value = `错误: ${error}`
    ElMessage.error('查询构建器示例失败')
  }
}

// 5. 错误处理示例
const errorHandlingExample = async () => {
  try {
    errorResult.value = '演示错误处理...'

    // 故意创建一个可能出错的查询
    const invalidClient = createODataClient({
      baseUrl: 'https://invalid-url.example.com',
      servicePath: '/invalid/service',
      timeout: 5000,
    })

    const query = invalidClient
      .entitySet('NonExistentEntity')
      .query()
      .select('InvalidField')

    // 尝试执行无效查询
    try {
      await query.execute()
      errorResult.value = '意外：查询成功了（这不应该发生）'
    } catch (executeError) {
      errorResult.value = `错误处理示例:\n\n配置信息:\n- 基础URL: https://invalid-url.example.com\n- 服务路径: /invalid/service\n- 实体集: NonExistentEntity\n- 字段: InvalidField\n\n捕获到的错误:\n${executeError}\n\n说明:\n- 使用了无效的服务端点\n- 查询不存在的实体集\n- 在实际应用中，调用 query.execute() 会触发网络错误\n- 应该使用 try-catch 块处理此类错误\n\n在真实环境中，错误可能包括:\n- 网络连接错误\n- 认证失败\n- 服务器错误\n- 无效的查询语法\n- CORS 跨域问题`
    }

    ElMessage.warning('这是一个错误处理演示')
  } catch (error) {
    errorResult.value = `捕获到错误: ${error}\n\n这演示了如何处理 OData 客户端中的错误。`
    ElMessage.error('演示了错误处理')
  }
}
</script>

<style scoped>
.odata-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.example-section pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  margin-top: 15px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.example-section pre.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.el-button {
  margin-right: 10px;
}

.existing-component {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.config-display h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #495057;
}

.config-display pre {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 12px;
  font-size: 11px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
