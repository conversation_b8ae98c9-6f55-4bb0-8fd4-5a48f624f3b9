<template>
  <div class="odata-example"></div>
</template>

<script setup lang="ts">
import { parseMetadata, createClient } from '@neue-plus/odata'

// 示例 XML 元数据字符串（实际使用时应该从服务获取）
const xmlString = `<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
  <edmx:DataServices>
    <Schema Namespace="NorthwindModel" xmlns="http://docs.oasis-open.org/odata/ns/edm">
      <EntitySet Name="Products" EntityType="NorthwindModel.Product"/>
      <EntityType Name="Product">
        <Key>
          <PropertyRef Name="ProductID"/>
        </Key>
        <Property Name="ProductID" Type="Edm.Int32" Nullable="false"/>
        <Property Name="ProductName" Type="Edm.String"/>
        <Property Name="UnitPrice" Type="Edm.Decimal"/>
      </EntityType>
    </Schema>
  </edmx:DataServices>
</edmx:Edmx>`

// 解析元数据
const metadata = parseMetadata(xmlString)

// 创建 OData 客户端
const client = createClient(
  'https://services.odata.org/V4/Northwind/Northwind.svc/'
)

// 获取某个 EntitySet 对应的实体类型
const entitySet = metadata.schemas?.[0]?.entitySets?.find(
  (es) => es.name === 'Products'
)

console.log('Entity Set:', entitySet)
console.log('Metadata:', metadata)
</script>
