<template>
  <div class="odata-example"></div>
</template>

<script setup lang="ts">
const service = new Service(
  '***********************************/path/to/service/'
)
service.init
  .then(() => {
    // 假设实体集叫 Entity_Set_Name
    return service.Entity_Set_Name.get(1)
  })
  .then((result) => {
    console.log(result)
  })
  .catch((err) => {
    console.error('❌ OData 请求失败:', err)
  })
</script>
