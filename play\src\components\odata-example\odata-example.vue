<template>
  <div class="odata-example">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>OData 客户端示例</span>
        </div>
      </template>

      <!-- 基本查询示例 -->
      <div class="example-section">
        <h3>1. 基本查询示例</h3>
        <el-button @click="basicQuery" type="primary">执行基本查询</el-button>
        <pre v-if="basicResult">{{ basicResult }}</pre>
      </div>

      <!-- 过滤查询示例 -->
      <div class="example-section">
        <h3>2. 过滤查询示例</h3>
        <el-button @click="filterQuery" type="success">执行过滤查询</el-button>
        <pre v-if="filterResult">{{ filterResult }}</pre>
      </div>

      <!-- 复杂查询示例 -->
      <div class="example-section">
        <h3>3. 复杂查询示例</h3>
        <el-button @click="complexQuery" type="warning">执行复杂查询</el-button>
        <pre v-if="complexResult">{{ complexResult }}</pre>
      </div>

      <!-- 查询构建器示例 -->
      <div class="example-section">
        <h3>4. 查询构建器示例</h3>
        <el-button @click="queryBuilderExample" type="info"
          >查询构建器</el-button
        >
        <pre v-if="queryBuilderResult">{{ queryBuilderResult }}</pre>
      </div>

      <!-- 错误处理示例 -->
      <div class="example-section">
        <h3>5. 错误处理示例</h3>
        <el-button @click="errorHandlingExample" type="danger"
          >错误处理</el-button
        >
        <pre v-if="errorResult" class="error">{{ errorResult }}</pre>
      </div>

      <!-- 现有 OData 组件示例 -->
      <div class="example-section">
        <h3>6. 现有 OData 组件示例</h3>
        <el-button @click="showExistingComponent" type="warning"
          >显示现有组件</el-button
        >
        <div v-if="showExisting" class="existing-component">
          <div class="config-display">
            <h4>OData 配置示例：</h4>
            <pre>{{ JSON.stringify(odataMaterialConfig, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElButton, ElMessage } from 'element-plus'
// import { NeMaterialRender } from '@neue-plus/widgets'
import { odataMaterialConfig } from '../../constant/index'

// 响应式数据
const basicResult = ref('')
const filterResult = ref('')
const complexResult = ref('')
const queryBuilderResult = ref('')
const errorResult = ref('')
const showExisting = ref(false)

// 模拟 OData 客户端类
class MockODataClient {
  baseUrl: string
  servicePath: string

  constructor(baseUrl: string, servicePath: string) {
    this.baseUrl = baseUrl
    this.servicePath = servicePath
  }

  entitySet(name: string) {
    return {
      query: () => new MockQueryBuilder(this.baseUrl, this.servicePath, name),
    }
  }
}

// 模拟查询构建器
class MockQueryBuilder {
  private baseUrl: string
  private servicePath: string
  private entityName: string
  private selectFields: string[] = []
  private filterCondition = ''
  private expandEntity = ''
  private orderByField = ''
  private orderDirection = 'asc'
  private topCount = 0
  private skipCount = 0

  constructor(baseUrl: string, servicePath: string, entityName: string) {
    this.baseUrl = baseUrl
    this.servicePath = servicePath
    this.entityName = entityName
  }

  select(...fields: string[]) {
    this.selectFields = fields
    return this
  }

  filter(condition: string) {
    this.filterCondition = condition
    return this
  }

  expand(entity: string) {
    this.expandEntity = entity
    return this
  }

  orderBy(field: string, direction = 'asc') {
    this.orderByField = field
    this.orderDirection = direction
    return this
  }

  top(count: number) {
    this.topCount = count
    return this
  }

  skip(count: number) {
    this.skipCount = count
    return this
  }

  getUrl() {
    const params: string[] = []

    if (this.selectFields.length > 0) {
      params.push(`$select=${this.selectFields.join(',')}`)
    }

    if (this.filterCondition) {
      params.push(`$filter=${this.filterCondition}`)
    }

    if (this.expandEntity) {
      params.push(`$expand=${this.expandEntity}`)
    }

    if (this.orderByField) {
      params.push(`$orderby=${this.orderByField} ${this.orderDirection}`)
    }

    if (this.skipCount > 0) {
      params.push(`$skip=${this.skipCount}`)
    }

    if (this.topCount > 0) {
      params.push(`$top=${this.topCount}`)
    }

    const queryString = params.length > 0 ? '?' + params.join('&') : ''
    return `${this.baseUrl}${this.servicePath}/${this.entityName}${queryString}`
  }
}

// 模拟过滤器构建器
class MockFilterBuilder {
  private conditions: string[] = []

  eq(field: string, value: any) {
    this.conditions.push(
      `${field} eq ${typeof value === 'string' ? `'${value}'` : value}`
    )
    return this
  }

  gt(field: string, value: any) {
    this.conditions.push(`${field} gt ${value}`)
    return this
  }

  contains(field: string, value: string) {
    this.conditions.push(`contains(${field}, '${value}')`)
    return this
  }

  and() {
    this.conditions.push('and')
    return this
  }

  or() {
    this.conditions.push('or')
    return this
  }

  build() {
    return this.conditions.join(' ')
  }
}

// 模拟查询选项构建器
class MockQueryOptionsBuilder {
  private options: string[] = []

  select(fields: string[]) {
    this.options.push(`$select=${fields.join(',')}`)
    return this
  }

  filter(condition: string) {
    this.options.push(`$filter=${condition}`)
    return this
  }

  orderBy(field: string) {
    this.options.push(`$orderby=${field}`)
    return this
  }

  top(count: number) {
    this.options.push(`$top=${count}`)
    return this
  }

  build() {
    return this.options.join('&')
  }
}

// 创建模拟客户端
const client = new MockODataClient(
  'https://services.odata.org',
  '/V4/Northwind/Northwind.svc'
)

// 工厂函数
const filter = () => new MockFilterBuilder()
const queryOptions = () => new MockQueryOptionsBuilder()
const createODataClient = (config: any) =>
  new MockODataClient(config.baseUrl, config.servicePath || '')

// 显示现有组件的函数
const showExistingComponent = () => {
  showExisting.value = !showExisting.value
  if (showExisting.value) {
    ElMessage.success('显示现有 OData 组件')
  } else {
    ElMessage.info('隐藏现有 OData 组件')
  }
}

// 1. 基本查询示例
const basicQuery = async () => {
  try {
    basicResult.value = '正在查询...'

    // 获取产品列表
    const query = client
      .entitySet('Products')
      .query()
      .select('ProductID', 'ProductName', 'UnitPrice')
      .top(5)

    const url = query.getUrl()
    basicResult.value = `查询 URL: ${url}\n\n注意：这是一个演示，实际的 HTTP 请求需要配置正确的服务端点。`

    ElMessage.success('基本查询构建成功！')
  } catch (error) {
    basicResult.value = `错误: ${error}`
    ElMessage.error('基本查询失败')
  }
}

// 2. 过滤查询示例
const filterQuery = async () => {
  try {
    filterResult.value = '正在构建过滤查询...'

    // 使用过滤器查询价格大于20的产品
    const query = client
      .entitySet('Products')
      .query()
      .select('ProductID', 'ProductName', 'UnitPrice', 'CategoryID')
      .filter('UnitPrice gt 20')
      .orderBy('UnitPrice', 'desc')
      .top(10)

    const url = query.getUrl()
    filterResult.value = `过滤查询 URL: ${url}\n\n过滤条件: UnitPrice > 20\n排序: 按价格降序\n限制: 前10条记录`

    ElMessage.success('过滤查询构建成功！')
  } catch (error) {
    filterResult.value = `错误: ${error}`
    ElMessage.error('过滤查询失败')
  }
}

// 3. 复杂查询示例
const complexQuery = async () => {
  try {
    complexResult.value = '正在构建复杂查询...'

    // 复杂查询：包含展开、多重过滤、排序
    const query = client
      .entitySet('Products')
      .query()
      .select('ProductID', 'ProductName', 'UnitPrice', 'UnitsInStock')
      .filter('CategoryID eq 1 and UnitPrice gt 10')
      .expand('Category')
      .orderBy('ProductName')
      .skip(5)
      .top(15)

    const url = query.getUrl()
    complexResult.value = `复杂查询 URL: ${url}\n\n包含功能:\n- 选择特定字段\n- 多重过滤条件\n- 展开关联实体\n- 排序\n- 分页（跳过5条，取15条）`

    ElMessage.success('复杂查询构建成功！')
  } catch (error) {
    complexResult.value = `错误: ${error}`
    ElMessage.error('复杂查询失败')
  }
}

// 4. 查询构建器示例
const queryBuilderExample = async () => {
  try {
    queryBuilderResult.value = '正在使用查询构建器...'

    // 使用过滤器构建器
    const filterBuilder = filter()
      .eq('CategoryID', 1)
      .and()
      .gt('UnitPrice', 15)
      .or()
      .contains('ProductName', 'Chai')

    // 使用查询选项构建器
    const options = queryOptions()
      .select(['ProductID', 'ProductName', 'UnitPrice'])
      .filter(filterBuilder.build())
      .orderBy('ProductName')
      .top(10)

    const optionsString = options.build()

    queryBuilderResult.value = `查询构建器结果:\n\n过滤器: ${filterBuilder.build()}\n\n完整查询选项: ${optionsString}\n\n说明:\n- 使用流畅接口构建复杂过滤条件\n- 支持 AND、OR 逻辑操作\n- 支持各种比较操作符\n- 可组合多个查询选项`

    ElMessage.success('查询构建器示例完成！')
  } catch (error) {
    queryBuilderResult.value = `错误: ${error}`
    ElMessage.error('查询构建器示例失败')
  }
}

// 5. 错误处理示例
const errorHandlingExample = async () => {
  try {
    errorResult.value = '演示错误处理...'

    // 故意创建一个可能出错的查询
    const invalidClient = createODataClient({
      baseUrl: 'https://invalid-url.example.com',
      servicePath: '/invalid/service',
      timeout: 5000,
    })

    const query = invalidClient
      .entitySet('NonExistentEntity')
      .query()
      .select('InvalidField')

    const url = query.getUrl()

    errorResult.value = `错误处理示例:\n\n尝试的 URL: ${url}\n\n说明:\n- 使用了无效的服务端点\n- 查询不存在的实体集\n- 在实际应用中，这会触发网络错误\n- 应该使用 try-catch 块处理此类错误`

    ElMessage.warning('这是一个错误处理演示')
  } catch (error) {
    errorResult.value = `捕获到错误: ${error}\n\n这演示了如何处理 OData 客户端中的错误。`
    ElMessage.error('演示了错误处理')
  }
}
</script>

<style scoped>
.odata-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.example-section pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  margin-top: 15px;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.example-section pre.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.el-button {
  margin-right: 10px;
}

.existing-component {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.config-display h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #495057;
}

.config-display pre {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 12px;
  font-size: 11px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
