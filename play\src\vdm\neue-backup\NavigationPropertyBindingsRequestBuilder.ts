/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { NavigationPropertyBindings } from './NavigationPropertyBindings';

/**
 * Request builder class for operations supported on the {@link NavigationPropertyBindings} entity.
 */
export class NavigationPropertyBindingsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<NavigationPropertyBindings<T>, T> {
  /**
   * Returns a request builder for querying all `NavigationPropertyBindings` entities.
   * @returns A request builder for creating requests to retrieve all `NavigationPropertyBindings` entities.
   */
  getAll(): GetAllRequestBuilder<NavigationPropertyBindings<T>, T> {
    return new GetAllRequestBuilder<NavigationPropertyBindings<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `NavigationPropertyBindings` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `NavigationPropertyBindings`.
   */
  create(
    entity: NavigationPropertyBindings<T>
  ): CreateRequestBuilder<NavigationPropertyBindings<T>, T> {
    return new CreateRequestBuilder<NavigationPropertyBindings<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `NavigationPropertyBindings` entity based on its keys.
   * @param ncid Key property. See {@link NavigationPropertyBindings.ncid}.
   * @returns A request builder for creating requests to retrieve one `NavigationPropertyBindings` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<NavigationPropertyBindings<T>, T> {
    return new GetByKeyRequestBuilder<NavigationPropertyBindings<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `NavigationPropertyBindings`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `NavigationPropertyBindings`.
   */
  update(
    entity: NavigationPropertyBindings<T>
  ): UpdateRequestBuilder<NavigationPropertyBindings<T>, T> {
    return new UpdateRequestBuilder<NavigationPropertyBindings<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `NavigationPropertyBindings`.
   * @param ncid Key property. See {@link NavigationPropertyBindings.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `NavigationPropertyBindings`.
   */
  delete(ncid: string): DeleteRequestBuilder<NavigationPropertyBindings<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `NavigationPropertyBindings`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `NavigationPropertyBindings` by taking the entity as a parameter.
   */
  delete(
    entity: NavigationPropertyBindings<T>
  ): DeleteRequestBuilder<NavigationPropertyBindings<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<NavigationPropertyBindings<T>, T> {
    return new DeleteRequestBuilder<NavigationPropertyBindings<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof NavigationPropertyBindings
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
