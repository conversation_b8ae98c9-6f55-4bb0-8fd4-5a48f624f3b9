<template></template>
<script setup lang="ts">
// example/odata-client-full.ts
import { DefaultNeuesService } from '../../../../vdm/neue/service'
import { NeueCadPart } from '../../../../vdm/neue/exports'
import { odata } from '@sap-cloud-sdk/odata-v4'

// 初始化服务
const service = new DefaultNeuesService({
  defaultServicePath: '/sap/opu/odata/sap/NEUE_SRV/',
  destinationName: 'MY_SAP_DEST', // 或直接用 baseURL
})

// -----------------------------
// READ 查询
// -----------------------------
async function fetchCadParts() {
  try {
    const cadParts = await service.cadPart
      .requestBuilder()
      .getAll()
      .filter(NeueCadPart.CAD_PART_ID.startsWith('A')) // 类型安全 filter
      .top(5)
      .execute()

    console.log('Fetched CadParts:', cadParts)
  } catch (err) {
    console.error('Error fetching CadParts:', err)
  }
}

// -----------------------------
// CREATE 创建
// -----------------------------
async function createCadPart() {
  try {
    const newPart = await service.cadPart
      .requestBuilder()
      .create({
        CAD_PART_ID: 'A9999',
        DESCRIPTION: 'New CAD Part',
        CREATED_BY: 'SAP_USER',
      })
      .execute()

    console.log('Created CadPart:', newPart)
  } catch (err) {
    console.error('Error creating CadPart:', err)
  }
}

// -----------------------------
// UPDATE 更新
// -----------------------------
async function updateCadPart() {
  try {
    const updatedPart = await service.cadPart
      .requestBuilder()
      .update({
        CAD_PART_ID: 'A9999',
        DESCRIPTION: 'Updated Description',
      })
      .execute()

    console.log('Updated CadPart:', updatedPart)
  } catch (err) {
    console.error('Error updating CadPart:', err)
  }
}

// -----------------------------
// DELETE 删除
// -----------------------------
async function deleteCadPart() {
  try {
    await service.cadPart
      .requestBuilder()
      .delete({ CAD_PART_ID: 'A9999' })
      .execute()

    console.log('Deleted CadPart A9999')
  } catch (err) {
    console.error('Error deleting CadPart:', err)
  }
}

// -----------------------------
// CALL ACTION / FUNCTION
// -----------------------------
async function callActionAndFunction() {
  try {
    // Action 示例
    // 注意：如果 Action 在 metadata 中返回类型未定义，需要在生成器中排除或手动处理
    // const actionResult = await service.deploy
    //   .requestBuilder()
    //   .execute();
    // console.log('Action Deploy result:', actionResult);

    // Function 示例
    // const functionResult = await service.batchGetFileSignatureUrl
    //   .requestBuilder()
    //   .parameters({
    //     fileIds: ['file1', 'file2'],
    //     actionType: 'SomeType',
    //   })
    //   .execute();
    // console.log('Function BatchGetFileSignatureUrl result:', functionResult);

    console.log('Action/Function 示例请根据 metadata 调整参数和返回值类型')
  } catch (err) {
    console.error('Error calling Action/Function:', err)
  }
}

// -----------------------------
// 执行示例
// -----------------------------
async function main() {
  await fetchCadParts()
  await createCadPart()
  await updateCadPart()
  await deleteCadPart()
  await callActionAndFunction()
}

main()
</script>
