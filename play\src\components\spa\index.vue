<template></template>
<script setup lang="ts">
// example/odata-client-full.ts
import { neue } from '../../vdm/neue/service'
import { PartType, LifecycleState } from '../../vdm/neue'

// 初始化服务
const service = neue()

// -----------------------------
// READ 查询
// -----------------------------
async function fetchCadParts() {
  try {
    const { cadPartsApi } = service
    const cadParts = await cadPartsApi
      .requestBuilder()
      .getAll()
      .filter(cadPartsApi.schema.CODE.equals('A9999')) // 使用 equals 方法
      .top(5)
      .execute({
        destinationName: 'MY_SAP_DEST', // 或者使用其他配置
      })

    console.log('Fetched CadParts:', cadParts)
  } catch (err) {
    console.error('Error fetching CadParts:', err)
  }
}

// -----------------------------
// CREATE 创建
// -----------------------------
async function createCadPart() {
  try {
    const { cadPartsApi } = service

    // 使用实体构建器创建实体
    const newCadPart = cadPartsApi.entityBuilder().fromJson({
      code: 'A9999',
      description: 'New CAD Part',
      ncid: 'new-ncid-' + Date.now(),
      partType: PartType.NEUE_PRT,
      lifecycleState: LifecycleState.ACTIVE,
      schemaVersion: '1.0',
    })

    const newPart = await cadPartsApi
      .requestBuilder()
      .create(newCadPart)
      .execute({
        destinationName: 'MY_SAP_DEST',
      })

    console.log('Created CadPart:', newPart)
  } catch (err) {
    console.error('Error creating CadPart:', err)
  }
}

// -----------------------------
// UPDATE 更新
// -----------------------------
async function updateCadPart() {
  try {
    const { cadPartsApi } = service

    // 使用实体构建器创建更新实体
    const updateEntity = cadPartsApi.entityBuilder().fromJson({
      ncid: 'existing-ncid',
      description: 'Updated Description',
    })

    const updatedPart = await cadPartsApi
      .requestBuilder()
      .update(updateEntity)
      .execute({
        destinationName: 'MY_SAP_DEST',
      })

    console.log('Updated CadPart:', updatedPart)
  } catch (err) {
    console.error('Error updating CadPart:', err)
  }
}

// -----------------------------
// DELETE 删除
// -----------------------------
async function deleteCadPart() {
  try {
    const { cadPartsApi } = service

    // 删除方法需要传入 key 值（ncid）
    await cadPartsApi
      .requestBuilder()
      .delete('existing-ncid') // 直接传入 key 值
      .execute({
        destinationName: 'MY_SAP_DEST',
      })

    console.log('Deleted CadPart')
  } catch (err) {
    console.error('Error deleting CadPart:', err)
  }
}

// -----------------------------
// CALL ACTION / FUNCTION
// -----------------------------
async function callActionAndFunction() {
  try {
    // Action 示例
    // 注意：如果 Action 在 metadata 中返回类型未定义，需要在生成器中排除或手动处理
    // const actionResult = await service.deploy
    //   .requestBuilder()
    //   .execute();
    // console.log('Action Deploy result:', actionResult);

    // Function 示例
    // const functionResult = await service.batchGetFileSignatureUrl
    //   .requestBuilder()
    //   .parameters({
    //     fileIds: ['file1', 'file2'],
    //     actionType: 'SomeType',
    //   })
    //   .execute();
    // console.log('Function BatchGetFileSignatureUrl result:', functionResult);

    console.log('Action/Function 示例请根据 metadata 调整参数和返回值类型')
  } catch (err) {
    console.error('Error calling Action/Function:', err)
  }
}

// -----------------------------
// 执行示例
// -----------------------------
async function main() {
  await fetchCadParts()
  // await createCadPart()
  // await updateCadPart()
  // await deleteCadPart()
  // await callActionAndFunction()
}

main()
</script>
