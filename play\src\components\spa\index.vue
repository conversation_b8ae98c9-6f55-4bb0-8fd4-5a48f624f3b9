<template>
  <div class="odata-demo">
    <h1>SAP Cloud SDK OData 简单测试</h1>
    <div class="test-controls">
      <button @click="runServiceTest" :disabled="loading" class="test-btn">
        {{ loading ? '测试中...' : '服务连接测试' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { testServiceConnection } from '../../examples/simple-odata-test'

// 响应式数据
const loading = ref(false)
const testResults = ref<Array<any>>([])
const error = ref<string | null>(null)

// 清除结果
function clearResults() {
  testResults.value = []
  error.value = null
}

// 运行服务连接测试
async function runServiceTest() {
  loading.value = true
  clearResults()

  try {
    console.log('🔗 开始服务连接测试...')
    const result = await testServiceConnection()
    testResults.value = [{ test: '服务连接测试', ...result }]
  } catch (err: any) {
    console.error('服务连接测试失败:', err)
    error.value = `服务连接测试失败: ${err.message || err}`
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.odata-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

.test-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 12px 24px;
  border: 2px solid #3498db;
  background: white;
  color: #3498db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:hover:not(:disabled) {
  background: #3498db;
  color: white;
}

.test-btn.primary {
  background: #3498db;
  color: white;
}

.test-btn.primary:hover:not(:disabled) {
  background: #2980b9;
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.results-section,
.error-section {
  margin-top: 30px;
}

h2 {
  color: #34495e;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.result-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.result-title {
  font-weight: 600;
  color: #2c3e50;
}

.result-status.success {
  color: #27ae60;
  font-weight: 600;
}

.result-status.error {
  color: #e74c3c;
  font-weight: 600;
}

.result-content {
  padding: 20px;
}

.result-content pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}

.error-content {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 20px;
}

.error-content pre {
  background: #e53e3e;
  color: white;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}
</style>
