<template>
  <div class="odata-demo">
    <h1>SAP Cloud SDK OData 客户端演示</h1>

    <div class="demo-controls">
      <button @click="runBasicQuery" :disabled="loading">
        {{ loading ? '执行中...' : '基本查询' }}
      </button>
      <button @click="runFilterQuery" :disabled="loading">条件查询</button>
      <button @click="runBusinessRules" :disabled="loading">
        业务规则查询
      </button>
      <button @click="runAllExamples" :disabled="loading">运行所有示例</button>
    </div>

    <div class="results" v-if="results.length > 0">
      <h2>执行结果</h2>
      <div class="result-item" v-for="(result, index) in results" :key="index">
        <h3>{{ result.title }}</h3>
        <pre>{{ result.data }}</pre>
      </div>
    </div>

    <div class="error" v-if="error">
      <h2>错误信息</h2>
      <pre>{{ error }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  basicQueryExample,
  filterQueryExample,
  businessObjectRulesExample,
  runAllExamples as runAllExamplesDemo,
} from '../../examples/odata-client-demo'

// 响应式数据
const loading = ref(false)
const results = ref<Array<{ title: string; data: any }>>([])
const error = ref<string | null>(null)

// 清除结果
function clearResults() {
  results.value = []
  error.value = null
}

// 添加结果
function addResult(title: string, data: any) {
  results.value.push({
    title,
    data: JSON.stringify(data, null, 2),
  })
}

// 执行基本查询
async function runBasicQuery() {
  loading.value = true
  clearResults()

  try {
    console.log('🔍 执行基本查询示例...')
    const result = await basicQueryExample()
    addResult('基本查询结果', result)
  } catch (err: any) {
    error.value = err.message
    console.error('基本查询失败:', err)
  } finally {
    loading.value = false
  }
}

// 执行条件查询
async function runFilterQuery() {
  loading.value = true
  clearResults()

  try {
    console.log('🔍 执行条件查询示例...')
    const result = await filterQueryExample()
    addResult('条件查询结果', result)
  } catch (err: any) {
    error.value = err.message
    console.error('条件查询失败:', err)
  } finally {
    loading.value = false
  }
}

// 执行业务规则查询
async function runBusinessRules() {
  loading.value = true
  clearResults()

  try {
    console.log('🔍 执行业务规则查询示例...')
    const result = await businessObjectRulesExample()
    addResult('业务规则查询结果', result)
  } catch (err: any) {
    error.value = err.message
    console.error('业务规则查询失败:', err)
  } finally {
    loading.value = false
  }
}

// 运行所有示例
async function runAllExamples() {
  loading.value = true
  clearResults()

  try {
    console.log('🚀 运行所有示例...')
    await runAllExamplesDemo()
    addResult('所有示例执行完成', '请查看控制台输出')
  } catch (err: any) {
    error.value = err.message
    console.error('运行所有示例失败:', err)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.odata-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

.demo-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

button {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover:not(:disabled) {
  background-color: #2980b9;
}

button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.results {
  margin-top: 30px;
}

.result-item {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
}

.result-item h3 {
  background-color: #f8f9fa;
  margin: 0;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
  color: #495057;
}

.result-item pre {
  margin: 0;
  padding: 15px;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

.error {
  margin-top: 30px;
  border: 1px solid #e74c3c;
  border-radius: 5px;
  overflow: hidden;
}

.error h2 {
  background-color: #e74c3c;
  color: white;
  margin: 0;
  padding: 10px 15px;
}

.error pre {
  margin: 0;
  padding: 15px;
  background-color: #fdf2f2;
  color: #721c24;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
