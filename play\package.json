{"name": "@neue/play", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@neue-plus/components": "workspace:*", "element-plus": "^2.10.1", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.9.2", "unplugin-vue-components": "0.27.4", "unplugin-vue-macros": "^0.11.2", "vite": "^6.3.5", "vite-plugin-inspect": "^0.8.7", "vite-plugin-mkcert": "^1.17.6", "vue-tsc": "^2.2.8"}}