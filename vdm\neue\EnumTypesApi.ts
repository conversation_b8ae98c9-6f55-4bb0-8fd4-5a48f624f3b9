/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { EnumTypes } from './EnumTypes';
import { EnumTypesRequestBuilder } from './EnumTypesRequestBuilder';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  CollectionField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class EnumTypesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<EnumTypes<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): EnumTypesApi<DeSerializersT> {
    return new EnumTypesApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = EnumTypes;

  requestBuilder(): EnumTypesRequestBuilder<DeSerializersT> {
    return new EnumTypesRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    EnumTypes<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<EnumTypes<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<EnumTypes<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof EnumTypes, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(EnumTypes, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    ENUM_TYPE_CODE: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    NAME: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    UNDERLYING_TYPE: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    IS_FLAGS: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    MEMBERS: CollectionField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      EnumTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<EnumTypes<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link enumTypeCode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ENUM_TYPE_CODE: fieldBuilder.buildEdmTypeField(
          'enumTypeCode',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', false),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link underlyingType} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        UNDERLYING_TYPE: fieldBuilder.buildEdmTypeField(
          'underlyingType',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link isFlags} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_FLAGS: fieldBuilder.buildEdmTypeField(
          'isFlags',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link members} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MEMBERS: fieldBuilder.buildCollectionField(
          'members',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', EnumTypes)
      };
    }

    return this._schema;
  }
}
