/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { People } from './People';
import { PeopleRequestBuilder } from './PeopleRequestBuilder';
import { Location, LocationField } from './Location';
import { PersonGender } from './PersonGender';
import { Feature } from './Feature';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField,
  CollectionField,
  OneToOneLink,
  OneToManyLink
} from '@sap-cloud-sdk/odata-v4';
export class PeopleApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<People<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): PeopleApi<DeSerializersT> {
    return new PeopleApi(deSerializers);
  }

  private navigationPropertyFields!: {
    /**
     * Static representation of the one-to-one navigation property {@link bestFriend} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    BEST_FRIEND: OneToOneLink<
      People<DeSerializersT>,
      DeSerializersT,
      PeopleApi<DeSerializersT>
    >;
    /**
     * Static representation of the one-to-many navigation property {@link friends} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    FRIENDS: OneToManyLink<
      People<DeSerializersT>,
      DeSerializersT,
      PeopleApi<DeSerializersT>
    >;
  };

  _addNavigationProperties(
    linkedApis: [PeopleApi<DeSerializersT>, PeopleApi<DeSerializersT>]
  ): this {
    this.navigationPropertyFields = {
      BEST_FRIEND: new OneToOneLink('BestFriend', this, linkedApis[0]),
      FRIENDS: new OneToManyLink('Friends', this, linkedApis[1])
    };
    return this;
  }

  entityConstructor = People;

  requestBuilder(): PeopleRequestBuilder<DeSerializersT> {
    return new PeopleRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<People<DeSerializersT>, DeSerializersT> {
    return entityBuilder<People<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<People<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof People, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(People, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    USER_NAME: OrderableEdmTypeField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    FIRST_NAME: OrderableEdmTypeField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LAST_NAME: OrderableEdmTypeField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    MIDDLE_NAME: OrderableEdmTypeField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    GENDER: EnumField<
      People<DeSerializers>,
      DeSerializersT,
      PersonGender,
      false,
      true
    >;
    AGE: OrderableEdmTypeField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.Int64',
      true,
      true
    >;
    EMAILS: CollectionField<
      People<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ADDRESS_INFO: CollectionField<
      People<DeSerializers>,
      DeSerializersT,
      Location,
      true,
      true
    >;
    HOME_ADDRESS: LocationField<
      People<DeSerializers>,
      DeSerializersT,
      true,
      true
    >;
    FAVORITE_FEATURE: EnumField<
      People<DeSerializers>,
      DeSerializersT,
      Feature,
      false,
      true
    >;
    FEATURES: CollectionField<
      People<DeSerializers>,
      DeSerializersT,
      typeof Feature,
      false,
      true
    >;
    /**
     * Static representation of the one-to-one navigation property {@link bestFriend} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    BEST_FRIEND: OneToOneLink<
      People<DeSerializersT>,
      DeSerializersT,
      PeopleApi<DeSerializersT>
    >;
    /**
     * Static representation of the one-to-many navigation property {@link friends} for query construction.
     * Use to reference this property in query operations such as 'select' in the fluent request API.
     */
    FRIENDS: OneToManyLink<
      People<DeSerializersT>,
      DeSerializersT,
      PeopleApi<DeSerializersT>
    >;
    ALL_FIELDS: AllFields<People<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link userName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        USER_NAME: fieldBuilder.buildEdmTypeField(
          'UserName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link firstName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        FIRST_NAME: fieldBuilder.buildEdmTypeField(
          'FirstName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lastName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LAST_NAME: fieldBuilder.buildEdmTypeField(
          'LastName',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link middleName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MIDDLE_NAME: fieldBuilder.buildEdmTypeField(
          'MiddleName',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link gender} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        GENDER: fieldBuilder.buildEnumField('Gender', PersonGender, false),
        /**
         * Static representation of the {@link age} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        AGE: fieldBuilder.buildEdmTypeField('Age', 'Edm.Int64', true),
        /**
         * Static representation of the {@link emails} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        EMAILS: fieldBuilder.buildCollectionField('Emails', 'Edm.String', true),
        /**
         * Static representation of the {@link addressInfo} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ADDRESS_INFO: fieldBuilder.buildCollectionField(
          'AddressInfo',
          Location,
          true
        ),
        /**
         * Static representation of the {@link homeAddress} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        HOME_ADDRESS: fieldBuilder.buildComplexTypeField(
          'HomeAddress',
          LocationField,
          true
        ),
        /**
         * Static representation of the {@link favoriteFeature} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        FAVORITE_FEATURE: fieldBuilder.buildEnumField(
          'FavoriteFeature',
          Feature,
          false
        ),
        /**
         * Static representation of the {@link features} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        FEATURES: fieldBuilder.buildCollectionField('Features', Feature, false),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', People)
      };
    }

    return this._schema;
  }
}
