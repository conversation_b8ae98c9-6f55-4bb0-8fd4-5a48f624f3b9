# @neue-plus/odata

基于 Olingo OData4 JS 库的 TypeScript OData 客户端封装。

## 特性

- 🚀 **TypeScript 支持** - 完整的类型定义和智能提示
- 🔧 **基于 Olingo** - 使用成熟的 Apache Olingo OData4 JS 库
- 🎯 **简单易用** - 链式 API 和直观的查询构建器
- 🔌 **可扩展** - 支持拦截器和插件系统
- 📦 **轻量级** - 最小化的依赖和打包体积
- 🧪 **测试覆盖** - 完整的单元测试

## 安装

```bash
npm install @neue-plus/odata
# 或
yarn add @neue-plus/odata
# 或
pnpm add @neue-plus/odata
```

## 快速开始

### 创建客户端

```typescript
import { createODataClient } from '@neue-plus/odata'

// 基本用法
const client = createODataClient({
  baseUrl: 'https://services.odata.org/V4/Northwind/Northwind.svc'
})

// 带服务路径
const client = createODataClient({
  baseUrl: 'https://api.example.com',
  servicePath: 'odata/v1',
  headers: {
    'Accept': 'application/json'
  }
})
```

### 基本查询

```typescript
// 获取所有产品
const products = await client.entitySet('Products').getAll()

// 根据 ID 获取产品
const product = await client.entitySet('Products').getById(1)

// 使用查询选项
const filteredProducts = await client.entitySet('Products').getAll({
  $select: ['ProductID', 'ProductName'],
  $filter: 'CategoryID eq 1',
  $top: 10
})
```

### 查询构建器

```typescript
// 链式查询
const query = client.entitySet('Products')
  .query()
  .select('ProductID', 'ProductName', 'UnitPrice')
  .expand('Category')
  .filter('CategoryID eq 1')
  .orderBy('ProductName')
  .top(10)

const result = await query.execute()
```

### 过滤器构建器

```typescript
import { filter } from '@neue-plus/odata'

// 简单过滤器
const simpleFilter = filter()
  .eq('CategoryID', 1)
  .build()

// 复合过滤器
const complexFilter = filter()
  .groupStart()
    .eq('CategoryID', 1)
    .or()
    .eq('CategoryID', 2)
  .groupEnd()
  .and()
  .gt('UnitPrice', 10)
  .build()

// 在查询中使用
const products = await client.entitySet('Products')
  .query()
  .filter(complexFilter)
  .execute()
```

### CRUD 操作

```typescript
const products = client.entitySet('Products')

// 创建
const newProduct = await products.create({
  ProductName: 'New Product',
  CategoryID: 1,
  UnitPrice: 25.00
})

// 更新
const updatedProduct = await products.update(1, {
  ProductName: 'Updated Product',
  UnitPrice: 30.00
})

// 部分更新
const patchedProduct = await products.patch(1, {
  UnitPrice: 35.00
})

// 删除
await products.delete(1)
```

### 批量操作

```typescript
// 批量创建
const newProducts = await products.createBatch([
  { ProductName: 'Product 1', CategoryID: 1 },
  { ProductName: 'Product 2', CategoryID: 2 }
])

// 批量更新
const updates = [
  { id: 1, entity: { UnitPrice: 25.00 } },
  { id: 2, entity: { UnitPrice: 30.00 } }
]
await products.updateBatch(updates)

// 批量删除
await products.deleteBatch([1, 2, 3])
```

### 函数和操作调用

```typescript
// 调用函数
const result = await client.callFunction('GetProductsByCategory', {
  categoryId: 1
})

// 调用操作
const actionResult = await client.callAction('DiscontinueProduct', {
  productId: 1
})
```

### 拦截器

```typescript
// 请求拦截器
client.addRequestInterceptor((config) => {
  config.headers = {
    ...config.headers,
    'X-Custom-Header': 'value'
  }
  return config
})

// 响应拦截器
client.addResponseInterceptor((response) => {
  console.log('Response received:', response.status)
  return response
})

// 错误拦截器
client.addErrorInterceptor((error) => {
  console.error('Request failed:', error.message)
  throw error
})
```

### 认证

```typescript
import { createAuthenticatedClient } from '@neue-plus/odata'

// Basic 认证
const basicClient = createAuthenticatedClient(
  'https://api.example.com',
  {
    type: 'basic',
    username: 'user',
    password: 'pass'
  },
  'odata/v1'
)

// Bearer Token 认证
const bearerClient = createAuthenticatedClient(
  'https://api.example.com',
  {
    type: 'bearer',
    token: 'your-jwt-token'
  },
  'odata/v1'
)
```

## API 参考

### ODataClient

主要的客户端类，提供所有 OData 操作。

#### 方法

- `entitySet<T>(name: string): EntityOperations<T>` - 获取实体集操作对象
- `request<T>(config: RequestConfig): Promise<Response<T>>` - 执行原始 HTTP 请求
- `getMetadata(): Promise<any>` - 获取服务元数据
- `batch(requests: RequestConfig[]): Promise<Response[]>` - 批量请求
- `callFunction(name: string, parameters?: Record<string, any>): Promise<any>` - 调用函数
- `callAction(name: string, parameters?: Record<string, any>): Promise<any>` - 调用操作

### EntityOperations

实体集操作类，提供 CRUD 和查询功能。

#### 方法

- `getAll(options?: ODataQueryOptions): Promise<ODataResponse<T>>` - 获取所有实体
- `getById(id: string | number, options?: ODataQueryOptions): Promise<T>` - 根据 ID 获取实体
- `query(): QueryBuilder<T>` - 创建查询构建器
- `create(entity: Partial<T>): Promise<T>` - 创建实体
- `update(id: string | number, entity: Partial<T>): Promise<T>` - 更新实体
- `patch(id: string | number, entity: Partial<T>): Promise<T>` - 部分更新实体
- `delete(id: string | number): Promise<void>` - 删除实体

### QueryBuilder

查询构建器，支持链式调用构建复杂查询。

#### 方法

- `select(...fields: string[]): QueryBuilder<T>` - 选择字段
- `expand(...relations: string[]): QueryBuilder<T>` - 展开关联
- `filter(condition: string): QueryBuilder<T>` - 过滤条件
- `orderBy(field: string, direction?: 'asc' | 'desc'): QueryBuilder<T>` - 排序
- `top(count: number): QueryBuilder<T>` - 限制数量
- `skip(count: number): QueryBuilder<T>` - 跳过数量
- `count(include?: boolean): QueryBuilder<T>` - 包含总数
- `search(term: string): QueryBuilder<T>` - 搜索
- `execute(): Promise<ODataResponse<T>>` - 执行查询

## 许可证

MIT
