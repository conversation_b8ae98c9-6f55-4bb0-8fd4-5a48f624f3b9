Chrome 41 and Firefox 35
*OK   115 /  125 odata-batch-functional-tests.html
*OK   904 /  904 odata-cache-filter-functional-tests.html
*OK    12 /   12 odata-cache-fperf-tests.html
*OK  1062 / 1062 odata-cache-functional-tests.html
*OK    42 /   42 odata-cache-rx-functional-tests.html
*OK              odata-fuzz.html
*OK    27 /   27 odata-json-parse-tests.html
*OK    16 /   16 odata-links-functional-tests.html
FAIL   23 /   52 odata-metadata-awareness-functional-tests.html ----> Tests 1-6 OK; Accepted: Tests 7-27 are using old __metadata
*OK    36 /   36 odata-perf-tests.html
*OK   975 /  975 odata-qunit-tests.htm
FAIL     0 /  31 odata-read-crossdomain-functional-tests.html ----> Accepted: calls to odatasampleservices.azurewebsites.net fail
*OK  124/124     odata-read-functional-tests.html
*OK   52/ 52     odata-request-functional-tests.html
*OK   41/ 42     odatajs-cache-large-collection-functional-tests.html ------> Accepted: one timeout on server side, no bug on client side
FAIL             odatajs-cache-long-haul-tests.html  -------> Accepted: uses netflix
*OK    2/  2     odatajs-startup-perf-test.html
??               test-manager.html


Test results by IE on win8.1:
*OK  115 /  115 passed, 0 failed.   odata-batch-functional-tests.html
*OK  904 /  904 passed, 0 failed.   odata-cache-filter-functional-tests.html
*OK   12 /   12 passed, 0 failed.   odata-cache-fperf-tests.html
*OK 1062 / 1062 passed, 0 failed.   odata-cache-functional-tests.html
*OK   42 /   42 passed, 0 failed.   odata-cache-rx-functional-tests.html
*OK                                 odata-fuzz.html
*OK   27 /   27 passed, 0 failed.   odata-json-parse-tests.html
*OK   16 /   16 passed, 0 failed.   odata-links-functional-tests.html
*OK   23 /   52 passed, 29 failed.  odata-metadata-awareness-functional-tests.html  --->  Test 1-6 OK.
*OK   36 /   36 passed, 0 failed.   odata-perf-tests.html
*OK  975 /  975 passed, 0 failed.   odata-qunit-tests.htm
      15 /   44 passed, 29 failed.  odata-read-crossdomain-functional-tests.html   --->
*OK  124 /  124 passed, 0 failed.   odata-read-functional-tests.html
*OK   52 /   52 passed, 0 failed.   odata-request-functional-tests.html
*OK   41 /   42 passed, 1 failed.   odatajs-cache-large-collection-functional-tests.html ---> 1 case timeout
*OK    2 /   2 passed, 0 failed.    odatajs-startup-perf-test.html
