<template>
  <div class="ne-pro-table">
    <div
      v-if="searchConfig !== false && filterColumns.length > 0"
      class="ne-pro-table__filter"
    >
      <ne-pro-form
        v-bind="finalSearchConfig"
        :default-value="defaultValue"
        :form-items="filterColumns"
        @submit="handleSearch"
        @reset="handleSearch"
      >
        <template #extra-buttons>
          <el-button @click="refresh">刷新</el-button>
        </template>
      </ne-pro-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <div class="ne-pro-table__header-toolbar">
        <div />
        <div>
          <pro-button :items="toolbar?.header" />
        </div>
      </div>
      <NeTable
        v-bind="tableProps"
        :data="dataSource"
        :columns="columns"
        :loading="loading"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
        @row-click="rowClick"
      />
    </div>

    <div class="ne-pro-table__footer-toolbar">
      <div>
        <pro-button
          v-if="multipleSelection?.length"
          :actions="toolbar?.footer"
        />
      </div>
      <div v-if="paginationConfig !== false" class="ne-pro-table__pagination">
        <NePagination
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { find, merge, set } from 'lodash-unified'
import { useRequest } from 'vue-request'
import { useRequestQuery } from '@neue-plus/hooks'
import { replaceTemplate } from '@neue-plus/utils'
import {
  useApisRef,
  useMetadataRef,
  useSysParamsRef,
} from '../../material-render/src/context/event-flow'
import NeProForm from '../../pro-form'
import NePagination from '../../pagination'
import NeTable from '../../table'
import { request } from '../../material-render/src/handlers/handleRequest'
import ProButton from '../../pro-button/src/pro-button.vue'
import {
  type ProTableFilters,
  ValueTypeEnum,
  edmToValueTypeMap,
  proTableProps,
} from './pro-table'
import type { ProTableColumn } from './pro-table'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})
const props = defineProps(proTableProps)
const filters = ref<ProTableFilters>({})
const pagination = ref({ current: 1, pageSize: 10, total: 0 })
const dataSource = ref(props.data)
const multipleSelection = ref<any[]>()
const apisRef = useApisRef()
const metadataRef = useMetadataRef()
const sysParamsRef = useSysParamsRef()
const tableProps = computed(() => {
  const {
    columns,
    data,
    entityType,
    paginationConfig,
    searchConfig,
    toolbar,
    request,
    ...otherProps
  } = props
  console.log(
    columns,
    data,
    entityType,
    paginationConfig,
    searchConfig,
    toolbar,
    request
  )
  return otherProps
})

const getValueType = (column: ProTableColumn) => {
  if (column.valueType) {
    return column.valueType
  } else if (
    (!edmToValueTypeMap[column.entityType] && column.entityType) ||
    column.valueEnum
  ) {
    return ValueTypeEnum.Select
  } else if (edmToValueTypeMap[column.entityType]) {
    return edmToValueTypeMap[column.entityType]
  } else {
    return ValueTypeEnum.Text
  }
}

const metaEntityData = computed(() => {
  if (!props?.entityType) {
    return {}
  }
  return metadataRef.value?.entities[props.entityType] || {}
})

const columns = computed(() => {
  const { properties = [] } = metaEntityData.value
  return props.columns
    .map((column) => {
      const node = find(properties, (item) => item.name === column.prop)
      if (typeof column.valueEnum === 'string') {
        const { enums } = metadataRef.value
        return {
          ...node,
          ...column,
          valueEnum: enums?.[column.valueEnum]?.valueEnum,
        }
      } else {
        return {
          ...node,
          ...column,
        }
      }
    })
    .map((column) => {
      return { ...column, valueType: getValueType(column) }
    })
})

const { getRequestQuery, buildODataFilterObject } = useRequestQuery(
  columns.value
)
// 可筛选的列
const filterColumns = computed(() => {
  const { span } = props.searchConfig || {}
  const result = columns.value
    .filter(
      (column) =>
        column.hideInForm !== true && column.prop !== 'operation' && column.prop
    )
    .map((column) => {
      return {
        ...column,
        span,
      }
    })
  return result
})
// 最终的搜索表单配置
const finalSearchConfig = computed(() => {
  return merge(
    {
      span: 8,
      submitButtonProps: {
        type: 'primary',
      },
      resetButtonProps: {
        type: 'default',
      },
    },
    props.searchConfig
  )
})
// 初始化筛选表单
const defaultValue = computed(() => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach((column) => {
    if (column.prop) {
      set(newFilters, column.prop, column.defaultValue || undefined)
    }
  })
  return newFilters
})
const { data, loading, run } = useRequest(fetchData, {
  // refreshOnWindowFocus: true,
  // refocusTimespan: 1000, // 请求间隔时间
})
watch(
  () => data.value,
  (result) => {
    dataSource.value = result?.data || []
    pagination.value = { ...pagination.value, total: result?.total || 0 }
  }
)

const filterFormat = () => {
  const obj = buildODataFilterObject(filters.value)
  const { filter = {} } = props.request?.query || {}
  const result = replaceTemplate(
    filter,
    merge({}, filters.value, sysParamsRef.value)
  )
  return merge({}, obj, result)
}
async function fetchData(data?: any) {
  const { orderBy } = data || {}
  const queryFormat = filterFormat()
  const query = getRequestQuery(queryFormat, pagination.value, orderBy)
  if (props.request) {
    const { expand = {} } = props.request.query || {}
    return request({
      ...props.request,
      query: {
        expand,
        ...query,
      },
      replaceData: sysParamsRef.value,
    })
  } else if (typeof apisRef['request'] === 'function') {
    return apisRef['request']({ query })
  } else {
    dataSource.value = props.data
  }
}

const emit = defineEmits([
  'filter-change',
  'page-change',
  'sort-change',
  'selection-change',
  'row-click',
])

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
  emit('page-change', 1, pageSize)
  run()
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  emit('page-change', current, pagination.value.pageSize)
  run()
}

// 排序变化
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  const { prop, order } = data
  run({ orderBy: { prop, order: order?.replace('ending', '') } })
  emit('sort-change', prop, order)
}
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}
const refresh = () => {
  run()
}
const handleSearch = (val: any) => {
  filters.value = val
  refresh()
}
function rowClick(row: any) {
  emit('row-click', row)
}
onMounted(() => {
  run()
})
defineExpose({
  refresh,
})
</script>
