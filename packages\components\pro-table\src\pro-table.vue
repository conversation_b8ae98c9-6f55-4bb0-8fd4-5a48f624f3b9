<!-- eslint-disable @typescript-eslint/no-unused-vars -->

<template>
  <div class="ne-pro-table">
    <div
      v-if="searchConfig !== false && filterColumns.length > 0"
      class="ne-pro-table__filter"
    >
      <ne-pro-form
        v-model="filters"
        v-bind="finalSearchConfig"
        :form-items="filterColumns"
        @submit="handleSearch"
        @reset="handleReset"
      >
        <template #extra-buttons>
          <el-button @click="handleRefresh">刷新</el-button>
        </template>
      </ne-pro-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <NeTable
        v-bind="tableProps"
        :data="dataSource"
        :columns="visibleColumns"
        :loading="loading"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="date" label="操作">123456</el-table-column>
      </NeTable>
    </div>

    <!-- 分页 -->
    <div v-if="paginationConfig !== false" class="ne-pro-table__pagination">
      <NePagination
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { merge, set } from 'lodash-unified'
import { useRequest } from 'vue-request'
import { useRequestQuery } from '@neue-plus/hooks'
import NeProForm from '../../pro-form'
import NePagination from '../../pagination'
import NeTable from '../../table'
import { type ProTableFilters, ValueTypeEnum, proTableProps } from './pro-table'
import type { ProTableColumn } from './pro-table'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})

const props = defineProps(proTableProps)
// 筛选数据
const filters = ref<ProTableFilters>({})
const pagination = ref({ current: 1, pageSize: 10, total: 0 })
const dataSource = ref<any[]>(props.data)

const { data, loading, run } = useRequest(fetchData, {
  refreshOnWindowFocus: true,
  refocusTimespan: 1000, // 请求间隔时间
})

const tableProps = computed(() => {
  const { data, columns, loading, bordered } = props
  return { data, columns, loading, bordered }
})
const getValueType = (column: ProTableColumn) => {
  return column.valueType
    ? column.valueType
    : column.valueEnum
    ? ValueTypeEnum.Select
    : ValueTypeEnum.Text
}
// 可见的列
const visibleColumns = computed(() => {
  return props.columns
    ?.filter((column) => !column.hideInTable)
    .map((column) => ({
      ...column,
      valueType: getValueType(column),
    }))
})
const { getRequestQuery } = useRequestQuery(props.columns)

// 可筛选的列
const filterColumns = computed(() => {
  const { span } = props.searchConfig || {}
  console.log(props.columns)
  const result = props.columns
    .filter(
      (column) => column.hideInForm !== true && column.prop !== 'operation'
    )
    .map((column) => {
      return {
        ...column,
        span,
        valueType: getValueType(column),
      }
    })
  return result
})
// 最终的搜索表单配置
const finalSearchConfig = computed(() => {
  return merge(
    {
      span: 8,
      submitButtonProps: {
        type: 'primary',
      },
      resetButtonProps: {
        type: 'default',
      },
    },
    props.searchConfig
  )
})
// 初始化筛选表单
const initFilters = () => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach((column) => {
    if (column.prop) {
      set(newFilters, column.prop, column.defaultValue || undefined)
    }
  })
  filters.value = newFilters
}

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
  emit('page-change', 1, pageSize)
  run()
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  emit('page-change', current, pagination.value.pageSize)
  run()
}

// 排序变化
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  const { prop, order } = data
  run({ prop, order: order?.replace('ending', '') })
  emit('sort-change', prop, order)
}
watch(
  () => data,
  (result) => {
    const { data, total } = result
    dataSource.value = data || []
    pagination.value = { ...pagination.value, total }
  }
)
// 监听columns变化，重新初始化筛选表单
watch(
  () => props.columns,
  () => {
    initFilters()
  },
  { immediate: true }
)
watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)
async function fetchData(orderBy = {}) {
  if (typeof props.request === 'function') {
    return props.request({
      query: getRequestQuery(filters.value, pagination.value, orderBy),
    })
  } else {
    dataSource.value = props.data
  }
}

const emit = defineEmits([
  'filter-change',
  'page-change',
  'sort-change',
  'refresh',
])

const handleSearch = () => {
  emit('filter-change', filters.value)
  run()
}

const handleReset = () => {
  initFilters()
  emit('filter-change', filters.value)
  run()
}

const handleRefresh = () => {
  emit('refresh')
  run()
}
onMounted(() => {
  run()
})
</script>
