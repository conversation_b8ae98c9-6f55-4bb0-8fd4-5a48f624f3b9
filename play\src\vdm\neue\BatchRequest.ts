/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeSerializers,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  ODataBatchRequestBuilder,
  UpdateRequestBuilder,
  BatchChangeSet
} from '@sap-cloud-sdk/odata-v4';
import { transformVariadicArgumentToArray } from '@sap-cloud-sdk/util';
import {
  Schemas,
  EntityTypes,
  ComplexTypes,
  EnumTypes,
  TypeDefinitions,
  StructuralProperties,
  NavigationProperties,
  Actions,
  Functions,
  EntityContainers,
  EntitySets,
  Singletons,
  NavigationPropertyBindings,
  ActionImports,
  FunctionImports,
  BusinessObjectRevisionNumberRules,
  BusinessObjectRevisionNumberCodes,
  LifecycleStatuses,
  LifecycleStatusStrategies,
  FromOrToTypes,
  RootRelationTypes,
  EntityTypeUnionUniqs,
  EntityTypeUnionUniqProperties,
  Files,
  IdiFiles,
  EntityTypePropertyOverrides,
  RelCadBomWithTypes,
  RelCadPartTwoDimensionDrawingWithTypes,
  RelCadFileFileWithTypes,
  MappingConfigs,
  RelCadFileFileRelations,
  RelCadPartTwoDimensionDrawingRelations,
  RelCadBomRelations,
  CadParts,
  NeueCadAsms,
  NeueCadParts,
  NeueTwoDimensionDrawings
} from './index';

/**
 * Batch builder for operations supported on the Neue.
 * @param requests The requests of the batch.
 * @returns A request builder for batch.
 */
export function batch<DeSerializersT extends DeSerializers>(
  ...requests: Array<
    ReadNeueRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT>;
export function batch<DeSerializersT extends DeSerializers>(
  requests: Array<
    ReadNeueRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT>;
export function batch<DeSerializersT extends DeSerializers>(
  first:
    | undefined
    | ReadNeueRequestBuilder<DeSerializersT>
    | BatchChangeSet<DeSerializersT>
    | Array<
        ReadNeueRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
      >,
  ...rest: Array<
    ReadNeueRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT> {
  return new ODataBatchRequestBuilder(
    defaultNeuePath,
    transformVariadicArgumentToArray(first, rest)
  );
}

/**
 * Change set constructor consists of write operations supported on the Neue.
 * @param requests The requests of the change set.
 * @returns A change set for batch.
 */
export function changeset<DeSerializersT extends DeSerializers>(
  ...requests: Array<WriteNeueRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT>;
export function changeset<DeSerializersT extends DeSerializers>(
  requests: Array<WriteNeueRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT>;
export function changeset<DeSerializersT extends DeSerializers>(
  first:
    | undefined
    | WriteNeueRequestBuilder<DeSerializersT>
    | Array<WriteNeueRequestBuilder<DeSerializersT>>,
  ...rest: Array<WriteNeueRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT> {
  return new BatchChangeSet(transformVariadicArgumentToArray(first, rest));
}

export const defaultNeuePath = '/';
export type ReadNeueRequestBuilder<DeSerializersT extends DeSerializers> =
  | GetAllRequestBuilder<Schemas<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<EntityTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<ComplexTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<EnumTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<TypeDefinitions<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<Actions<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<Functions<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<EntityContainers<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<EntitySets<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<Singletons<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      NavigationPropertyBindings<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<ActionImports<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<FunctionImports<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      BusinessObjectRevisionNumberRules<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<LifecycleStatuses<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      LifecycleStatusStrategies<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<FromOrToTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<RootRelationTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<EntityTypeUnionUniqs<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      EntityTypeUnionUniqProperties<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<Files<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<IdiFiles<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      EntityTypePropertyOverrides<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<RelCadBomWithTypes<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<
      RelCadFileFileWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      RelCadFileFileRelations<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<DeSerializersT>,
      DeSerializersT
    >
  | GetAllRequestBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<CadParts<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<NeueCadAsms<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<NeueCadParts<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<
      NeueTwoDimensionDrawings<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<Schemas<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<EntityTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<ComplexTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<EnumTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<TypeDefinitions<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<Actions<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<Functions<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<EntityContainers<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<EntitySets<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<Singletons<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      NavigationPropertyBindings<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<ActionImports<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<FunctionImports<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      BusinessObjectRevisionNumberRules<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<LifecycleStatuses<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      LifecycleStatusStrategies<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<FromOrToTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<RootRelationTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<EntityTypeUnionUniqs<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      EntityTypeUnionUniqProperties<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<Files<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<IdiFiles<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      EntityTypePropertyOverrides<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<RelCadBomWithTypes<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<
      RelCadFileFileWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      RelCadFileFileRelations<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<DeSerializersT>,
      DeSerializersT
    >
  | GetByKeyRequestBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<CadParts<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<NeueCadAsms<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<NeueCadParts<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<
      NeueTwoDimensionDrawings<DeSerializersT>,
      DeSerializersT
    >;
export type WriteNeueRequestBuilder<DeSerializersT extends DeSerializers> =
  | CreateRequestBuilder<Schemas<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<Schemas<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<Schemas<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<EntityTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<EntityTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<EntityTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<ComplexTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<ComplexTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<ComplexTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<EnumTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<EnumTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<EnumTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<TypeDefinitions<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<TypeDefinitions<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<TypeDefinitions<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<StructuralProperties<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<NavigationProperties<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<Actions<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<Actions<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<Actions<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<Functions<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<Functions<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<Functions<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<EntityContainers<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<EntityContainers<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<EntityContainers<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<EntitySets<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<EntitySets<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<EntitySets<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<Singletons<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<Singletons<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<Singletons<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      NavigationPropertyBindings<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      NavigationPropertyBindings<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      NavigationPropertyBindings<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<ActionImports<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<ActionImports<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<ActionImports<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<FunctionImports<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<FunctionImports<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<FunctionImports<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      BusinessObjectRevisionNumberRules<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      BusinessObjectRevisionNumberRules<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      BusinessObjectRevisionNumberRules<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      BusinessObjectRevisionNumberCodes<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<LifecycleStatuses<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<LifecycleStatuses<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<LifecycleStatuses<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      LifecycleStatusStrategies<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      LifecycleStatusStrategies<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      LifecycleStatusStrategies<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<FromOrToTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<FromOrToTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<FromOrToTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<RootRelationTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<RootRelationTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<RootRelationTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<EntityTypeUnionUniqs<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<EntityTypeUnionUniqs<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<EntityTypeUnionUniqs<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      EntityTypeUnionUniqProperties<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      EntityTypeUnionUniqProperties<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      EntityTypeUnionUniqProperties<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<Files<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<Files<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<Files<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<IdiFiles<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<IdiFiles<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<IdiFiles<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      EntityTypePropertyOverrides<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      EntityTypePropertyOverrides<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      EntityTypePropertyOverrides<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<RelCadBomWithTypes<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<RelCadBomWithTypes<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<RelCadBomWithTypes<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<
      RelCadFileFileWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      RelCadFileFileWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      RelCadFileFileWithTypes<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      RelCadFileFileRelations<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      RelCadFileFileRelations<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      RelCadFileFileRelations<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<DeSerializersT>,
      DeSerializersT
    >
  | CreateRequestBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<RelCadBomRelations<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<CadParts<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<CadParts<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<CadParts<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<NeueCadAsms<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<NeueCadAsms<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<NeueCadAsms<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<NeueCadParts<DeSerializersT>, DeSerializersT>
  | UpdateRequestBuilder<NeueCadParts<DeSerializersT>, DeSerializersT>
  | DeleteRequestBuilder<NeueCadParts<DeSerializersT>, DeSerializersT>
  | CreateRequestBuilder<
      NeueTwoDimensionDrawings<DeSerializersT>,
      DeSerializersT
    >
  | UpdateRequestBuilder<
      NeueTwoDimensionDrawings<DeSerializersT>,
      DeSerializersT
    >
  | DeleteRequestBuilder<
      NeueTwoDimensionDrawings<DeSerializersT>,
      DeSerializersT
    >;
