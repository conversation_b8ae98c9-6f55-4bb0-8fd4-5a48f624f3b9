/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import { AirportLocation, AirportLocationField } from './AirportLocation';
import type { AirportsApi } from './AirportsApi';

/**
 * This class represents the entity "Airports" of service "Trippin".
 */
export class Airports<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements AirportsType<T>
{
  /**
   * Technical entity name for Airports.
   */
  static override _entityName = 'Airports';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the Airports entity.
   */
  static _keys = ['IcaoCode'];
  /**
   * Name.
   * @nullable
   */
  declare name?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Icao Code.
   */
  declare icaoCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Iata Code.
   * @nullable
   */
  declare iataCode?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Location.
   * @nullable
   */
  declare location?: AirportLocation<T> | null;

  constructor(_entityApi: AirportsApi<T>) {
    super(_entityApi);
  }
}

export interface AirportsType<T extends DeSerializers = DefaultDeSerializers> {
  name?: DeserializedType<T, 'Edm.String'> | null;
  icaoCode: DeserializedType<T, 'Edm.String'>;
  iataCode?: DeserializedType<T, 'Edm.String'> | null;
  location?: AirportLocation<T> | null;
}
