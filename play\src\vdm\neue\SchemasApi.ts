/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { Schemas } from './Schemas';
import { SchemasRequestBuilder } from './SchemasRequestBuilder';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class SchemasApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<Schemas<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): SchemasApi<DeSerializersT> {
    return new SchemasApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = Schemas;

  requestBuilder(): SchemasRequestBuilder<DeSerializersT> {
    return new SchemasRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<Schemas<DeSerializersT>, DeSerializersT> {
    return entityBuilder<Schemas<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<Schemas<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof Schemas, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(Schemas, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    NAMESPACE: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    ALIAS: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    NCID: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      Schemas<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      Schemas<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<Schemas<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link namespace} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAMESPACE: fieldBuilder.buildEdmTypeField(
          'namespace',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link alias} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ALIAS: fieldBuilder.buildEdmTypeField('alias', 'Edm.String', false),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', Schemas)
      };
    }

    return this._schema;
  }
}
