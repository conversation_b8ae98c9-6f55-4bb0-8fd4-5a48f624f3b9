<template>
  <el-tooltip v-bind="realTooltipProps">
    <!-- 如果是 element-plus 内置图标 -->
    <el-icon v-if="iconComponent" :style="iconStyle" @click="onClick">
      <component :is="iconComponent" />
    </el-icon>
    <i
      v-else
      :class="['iconfont', props.name]"
      :style="iconStyle"
      @click="onClick"
    />
  </el-tooltip>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import * as Icons from '@element-plus/icons-vue'
import { ElIcon, ElTooltip } from 'element-plus'
import { neDynamicIconProps } from './dynamic-icon'
import type { Placement } from 'element-plus'

defineOptions({
  name: 'NeDynamicIcon',
})

const props = defineProps(neDynamicIconProps)
const realTooltipProps = computed(() => {
  const { tooltip } = props
  return {
    placement: 'top' as Placement,
    visible: !tooltip?.content ? false : undefined,
    ...tooltip,
  }
})

// 判断是否在 element-plus 图标集合里
const iconComponent = computed(() => {
  return props.name in Icons ? Icons[props.name as keyof typeof Icons] : null
})

const iconStyle = computed(() => ({
  color: props.color || 'inherit',
  fontSize: props.size
    ? typeof props.size === 'number'
      ? `${props.size}px`
      : props.size
    : 'inherit',
}))
const emit = defineEmits(['click'])
const onClick = (event: Event) => {
  event.stopPropagation()
  emit('click', event)
}
</script>
