import { cardProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { CSSProperties, ExtractPropTypes, PropType } from 'vue'

export const neCardProps = buildProps({
  ...cardProps,
  shadow: {
    type: String,
    values: ['always', 'hover', 'never'],
    default: 'never',
  },
  headerStyle: {
    type: Object as PropType<CSSProperties>,
    default: () => ({
      fontWeight: 500,
      fontSize: '20px',
      color: '#0F172A',
    }),
  },
} as const)
export type NeCardProps = ExtractPropTypes<typeof neCardProps>
