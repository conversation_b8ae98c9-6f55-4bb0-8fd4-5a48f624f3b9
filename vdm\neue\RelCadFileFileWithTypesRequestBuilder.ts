/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadFileFileWithTypes } from './RelCadFileFileWithTypes';

/**
 * Request builder class for operations supported on the {@link RelCadFileFileWithTypes} entity.
 */
export class RelCadFileFileWithTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadFileFileWithTypes<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadFileFileWithTypes` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadFileFileWithTypes` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadFileFileWithTypes<T>, T> {
    return new GetAllRequestBuilder<RelCadFileFileWithTypes<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `RelCadFileFileWithTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadFileFileWithTypes`.
   */
  create(
    entity: RelCadFileFileWithTypes<T>
  ): CreateRequestBuilder<RelCadFileFileWithTypes<T>, T> {
    return new CreateRequestBuilder<RelCadFileFileWithTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `RelCadFileFileWithTypes` entity based on its keys.
   * @param ncid Key property. See {@link RelCadFileFileWithTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadFileFileWithTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadFileFileWithTypes<T>, T> {
    return new GetByKeyRequestBuilder<RelCadFileFileWithTypes<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadFileFileWithTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadFileFileWithTypes`.
   */
  update(
    entity: RelCadFileFileWithTypes<T>
  ): UpdateRequestBuilder<RelCadFileFileWithTypes<T>, T> {
    return new UpdateRequestBuilder<RelCadFileFileWithTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadFileFileWithTypes`.
   * @param ncid Key property. See {@link RelCadFileFileWithTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadFileFileWithTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<RelCadFileFileWithTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadFileFileWithTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadFileFileWithTypes` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadFileFileWithTypes<T>
  ): DeleteRequestBuilder<RelCadFileFileWithTypes<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<RelCadFileFileWithTypes<T>, T> {
    return new DeleteRequestBuilder<RelCadFileFileWithTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof RelCadFileFileWithTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
