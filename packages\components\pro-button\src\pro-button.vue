<template>
  <div class="ne-pro-button">
    <template v-for="(item, index) in items" :key="`${item.text}_${index}`">
      <template v-if="item.type === 'element'">
        <component
          :is="NeRenderCore"
          v-for="element in item.elements"
          :key="element.id"
          v-bind="element"
        />
      </template>
      <DynamicIcon
        v-else-if="item.icon"
        :name="item.icon"
        v-bind="item.props"
        @click="(e) => handleClick(item.id, e)"
      />
      <el-tooltip
        v-else-if="item.props.disabled"
        v-bind="getRealTooltipProps(item)"
      >
        <!-- 如果是 element-plus 内置图标 -->
        <el-button
          v-bind="item.props"
          @click="(e: Event) => handleClick(item.id, e)"
          >{{ item.text }}</el-button
        >
      </el-tooltip>
      <el-button
        v-else
        v-bind="item.props"
        @click="(e: Event) => handleClick(item.id, e)"
        >{{ item.text }}</el-button
      >
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, markRaw, onMounted, shallowRef } from 'vue'
import { find, isUndefined, merge } from 'lodash-unified'
import { strReplaceTemplate } from '@neue-plus/utils'
import { templateToData } from '@neue-plus/utils/templateToData'
import { useEventFlow } from '../../material-render/src/hooks/useEventFlow'
import DynamicIcon from '../../dynamic-icon'
import {
  REFS_KEY,
  useSysParamsRef,
} from '../../material-render/src/context/event-flow'
import { neProButtonProps } from './pro-button'
import type { Placement } from 'element-plus'
import type { ActionsProps } from './pro-button'

const { run } = useEventFlow()
defineOptions({ name: 'NeProButton', inheritAttrs: false })
const props = defineProps(neProButtonProps)
const { record, query } = props

const getRealTooltipProps = (item: ActionsProps) => {
  const { tooltip } = item
  return {
    placement: 'top' as Placement,
    ...tooltip,
    content: item.props.disabled ? tooltip?.content : undefined,
  }
}
const items = computed(() => {
  const arr = props.items
    .map((item) => {
      if (item.type === 'element') return item
      const { disabled, hidden } = item.props
      const reD = !isUndefined(disabled)
        ? templateToData(disabled, record?.row)
        : undefined
      const hid = !isUndefined(hidden)
        ? templateToData(hidden, record?.row)
        : undefined
      return merge({}, item, {
        props: {
          disabled: reD,
          hidden: hid,
        },
      })
    })
    .filter((item) => item.props?.hidden !== true)
  return arr
})

const refs = inject(REFS_KEY, {}) as Record<string, any>
const sysParamsRef = useSysParamsRef()
const handleClick = (id: string, event: Event) => {
  event.stopPropagation()
  const node = find(props.items, (item) => item.id === id) as ActionsProps
  const { config, actions } = node
  const replaceData =
    config?.dataSource === 'query'
      ? sysParamsRef.value
      : merge({}, record?.row, config?.params)
  if (config?.href) {
    const href = strReplaceTemplate(config?.href, replaceData)
    window.open(href, config?.blank || '_self')
  } else if (actions) {
    run(actions, merge({}, query, record?.row))
  } else {
    config && refs[config.target]?.[config.actionType]?.(replaceData)
  }
}
defineExpose({})
// 动态加载组件并标记为非响应式
const NeRenderCore = shallowRef<any>(null)
onMounted(async () => {
  const module = await import('../../material-render')
  NeRenderCore.value = markRaw(module.NeRenderCore)
})
</script>
