<template>
  <div class="ne-pro-button">
    <template v-for="(item, index) in items" :key="`${item.text}_${index}`">
      <DynamicIcon
        v-if="item.icon"
        :name="item.icon"
        v-bind="item.props"
        @click="(e) => handleClick(item.id, e)"
      />
      <el-button
        v-else
        v-bind="item.props"
        :disabled="getDisabled(item)"
        @click="(e:Event) => handleClick(item.id, e)"
        >{{ item.text }}</el-button
      >
    </template>
  </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue'
import { find, merge } from 'lodash-unified'
import { strReplaceTemplate } from '@neue-plus/utils'
import { resolveTemplateAdvanced } from '@neue-plus/utils/resolveTemplate'
import { useEventFlow } from '../../material-render/src/hooks/useEventFlow'
import DynamicIcon from '../../dynamic-icon'
import { REFS_KEY } from '../../material-render/src/context/event-flow'
import { neProButtonProps } from './pro-button'
import type { ActionsProps } from './pro-button'

const { run } = useEventFlow()
defineOptions({ name: 'NeProButton', inheritAttrs: false })
const props = defineProps(neProButtonProps)
const { record } = props
const getDisabled = (item: ActionsProps) => {
  const { disabled } = item.props
  if (item.props?.disabled) {
    const obj = resolveTemplateAdvanced({ disabled }, record?.row)
    console.log(123456, obj.disabled, record?.row)
    return obj.disabled
  } else {
    return false
  }
}
const refs = inject(REFS_KEY, {}) as Record<string, any>
const handleClick = (id: string, event: Event) => {
  event.stopPropagation()
  const node = find(props.items, (item) => item.id === id) as ActionsProps
  const { config, actions, requestMap } = node
  if (config?.href) {
    const newHref = strReplaceTemplate(
      config?.href,
      merge({}, record?.row, config?.params)
    )
    window.open(newHref, '_blank')
  } else if (actions) {
    run(actions, record?.row)
  } else if (requestMap) {
    // TODO:
    // const requestMap[]
  } else {
    config &&
      refs[config.target]?.[config.actionType]?.(
        merge({}, record?.row, config?.params)
      )
  }
}
defineExpose({})
</script>
