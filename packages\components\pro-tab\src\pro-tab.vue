<template>
  <el-tabs
    v-model="activeTab"
    v-bind="realTabProps"
    @tab-click="handleTabClick"
  >
    <el-tab-pane
      v-for="tab in tabs"
      :key="tab.name"
      :label="tab.label"
      :name="tab.name"
    >
      <template v-if="tab.elements && tab.elements.length > 0">
        <NeRenderCore
          v-for="element in tab.elements"
          :key="element.id"
          :apis="{}"
          v-bind="element"
        />
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { NeRenderCore } from '../../material-render'
import { neProTabProps } from './pro-tab'

defineOptions({ name: 'NeProTabs', inheritAttrs: false })

const props = defineProps(neProTabProps)

const realTabProps = computed(() => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { tabProps } = props
  return {
    ...tabProps,
  }
})
const emit = defineEmits(['change', 'tabClick'])

const activeTab = ref(
  props.activeTabName || (props.tabs[0] && props.tabs[0].name)
)
const tabData = ref<Record<string, Record<string, any>>>({})
const handleTabClick = (name: string) => {
  emit('tabClick', name)
}
// 暴露方法
defineExpose({
  setActiveTab: (name: string) => {
    activeTab.value = name
  },
  getTabData: () => tabData.value,
})
</script>
