<template>
  <el-tabs
    v-model="activeTab"
    v-bind="realTabProps"
    @tab-click="handleTabClick"
  >
    <div class="ne-pro-table__header-toolbar">
      <div>{{ title }}</div>
      <div>
        <pro-button :items="toolbar?.actions?.header" />
      </div>
    </div>

    <el-tab-pane
      v-for="tab in tabs"
      :key="tab.name"
      :label="tab.label"
      :name="tab.name"
    >
      <template v-if="tab.elements && tab.elements.length > 0 && NeRenderCore">
        <component
          :is="NeRenderCore"
          v-for="element in tab.elements"
          :key="element.id"
          :apis="{}"
          v-bind="element"
        />
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { computed, markRaw, onMounted, ref, shallowRef } from 'vue'
import { strReplaceTemplate } from '@neue-plus/utils'
import ProButton from '../../pro-button/src/pro-button.vue'
import { neProTabProps } from './pro-tab'

defineOptions({ name: 'NeProTab', inheritAttrs: false })

const props = defineProps(neProTabProps)
const emit = defineEmits(['change', 'tabClick'])

const title = computed(() => strReplaceTemplate(props.toolbar?.title, {}))

const realTabProps = computed(() => {
  const { tabProps } = props
  return { ...tabProps }
})

const activeTab = ref(props.activeTabName || props.tabs[0]?.name)
const tabData = ref<Record<string, Record<string, any>>>({})

const handleTabClick = (name: string) => emit('tabClick', name)

defineExpose({
  setActiveTab: (name: string) => {
    activeTab.value = name
  },
  getTabData: () => tabData.value,
})

// 动态加载组件并标记为非响应式
const NeRenderCore = shallowRef<any>(null)
onMounted(async () => {
  const module = await import('../../material-render')
  NeRenderCore.value = markRaw(module.NeRenderCore)
})
</script>
