<template>
  <el-tabs
    v-model="activeTab"
    class="ne-pro-tab"
    v-bind="realTabProps"
    @tab-click="handleTabClick"
  >
    <ne-card>
      <template #header>
        <div class="ne-pro-tab__header-toolbar">
          <part-title
            v-if="partInfo.code"
            :title="partInfo.code"
            is-title
            :type="partInfo.partType"
          />
          <pro-button :items="toolbar?.actions?.header" :query="sysParamsRef" />
        </div>
      </template>

      <div class="ne-pro-tab__content">
        <el-tab-pane v-for="tab in tabs" :key="tab.name" :name="tab.name">
          <template #label>
            <dynamic-icon
              v-if="tab.icon"
              v-bind="getIconProps(tab)"
              :name="tab.icon"
            />
            <span>{{ tab.label }}</span>
          </template>
        </el-tab-pane>
      </div>
      <template
        v-if="newTab?.elements && newTab.elements.length > 0 && NeRenderCore"
      >
        <component
          :is="NeRenderCore"
          v-for="element in newTab.elements"
          :key="element.id"
          :apis="{}"
          v-bind="element"
        />
      </template>
    </ne-card>
  </el-tabs>
</template>

<script lang="ts" setup>
import { computed, markRaw, onMounted, ref, shallowRef } from 'vue'
// import { strReplaceTemplate } from '@neue-plus/utils'
import { find, merge } from 'lodash-unified'
import ProButton from '../../pro-button/src/pro-button.vue'
import { useSysParamsRef } from '../../material-render/src/context/event-flow'
import PartTitle from '../../part-title/src/part-title.vue'
import DynamicIcon from '../../dynamic-icon'
import NeCard from '../../card'
import { neProTabProps } from './pro-tab'

const sysParamsRef = useSysParamsRef()

defineOptions({ name: 'NeProTab', inheritAttrs: false })

const props = defineProps(neProTabProps)
const emit = defineEmits(['change', 'tabClick'])

const activeTab = ref(props.activeTabName || props.tabs[0]?.name)
const tabData = ref<Record<string, Record<string, any>>>({})
// const title = computed(() => strReplaceTemplate(props.toolbar?.title, {}))
const newTab = computed(() => {
  return find(props.tabs, (tab) => {
    return activeTab.value === tab.name
  })
})
const getIconProps = (tab: any) => {
  console.log(tab, 'tab')
  return merge(
    {
      size: 22,
      color: '#334155',
    },
    tab
  )
}
const realTabProps = computed(() => {
  const { tabProps } = props
  console.log(props, 123456)
  return { ...tabProps }
})

const partInfo = computed(() => {
  const { code, partType } = sysParamsRef.value
  return { code, partType }
})
const handleTabClick = (name: string) => emit('tabClick', name)

defineExpose({
  setActiveTab: (name: string) => {
    activeTab.value = name
  },
  getTabData: () => tabData.value,
})

// 动态加载组件并标记为非响应式
const NeRenderCore = shallowRef<any>(null)
onMounted(async () => {
  const module = await import('../../material-render')
  NeRenderCore.value = markRaw(module.NeRenderCore)
})
</script>
