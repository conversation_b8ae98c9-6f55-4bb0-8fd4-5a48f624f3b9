<template>
  <el-pagination
    v-bind="props"
    v-model:current-page="currentPage"
    v-model:page-size="currentSize"
    hide-on-single-page
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

import { nePaginationProps } from './types'

defineOptions({
  name: 'NePagination',
})
const props = defineProps(nePaginationProps)
const currentPage = ref(1)
const currentSize = ref(props.pageSize)

watch(
  () => props.currentPage,
  (val) => {
    currentPage.value = val || 1
  }
)

const emit = defineEmits(['size-change', 'current-change'])
const handleSizeChange = (val: number) => {
  currentSize.value = val
  emit('size-change', val)
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emit('current-change', val)
}
</script>
