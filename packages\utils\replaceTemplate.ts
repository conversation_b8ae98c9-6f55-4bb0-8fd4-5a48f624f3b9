/**
 * 替换对象中的 {{key}} 占位符
 * @param obj 需要替换的对象
 * @param source 替换源数据
 * @returns 替换后的新对象
 */
export function replaceTemplate<T>(obj: T, source: Record<string, any>): T {
  const regex = /\{\{(.*?)\}\}/g

  // 安全取值函数（支持 a.b.c）
  function get(obj: any, path: string): any {
    return path.split('.').reduce((o, k) => (o != null ? o[k] : undefined), obj)
  }

  function deepReplace(value: any): any {
    if (typeof value === 'string') {
      // 如果整个字符串就是 {{xxx}}
      const match = value.match(/^{{\s*(.*?)\s*}}$/)
      if (match) {
        const key = match[1].trim()
        const result = get(source, key)
        return result !== undefined ? result : ''
      }

      // 否则替换字符串里的多个 {{xxx}}
      return value.replace(regex, (_, key: string) => {
        const k = key.trim()
        const result = get(source, k)
        return result !== undefined ? String(result) : ''
      })
    }

    if (Array.isArray(value)) {
      return value.map(deepReplace)
    }

    if (value && typeof value === 'object') {
      return Object.fromEntries(
        Object.entries(value).map(([k, v]) => [k, deepReplace(v)])
      )
    }

    return value
  }

  return deepReplace(obj)
}

export function strReplaceTemplate(
  template: string | undefined | null,
  context: Record<string, any>
): string {
  if (!template || typeof template !== 'string') {
    return ''
  }

  return template.replace(/\{\{(.*?)\}\}/g, (_, key) => {
    key = key.trim()

    // 优先尝试直接取值（解决 @odata.type 这种 key）
    if (Object.prototype.hasOwnProperty.call(context, key)) {
      return encodeURIComponent(String(context[key] ?? ''))
    }

    const value = key.split('.').reduce((acc: any, k: string) => {
      if (acc && typeof acc === 'object' && k in acc) {
        return acc[k]
      }
      return undefined
    }, context as any)

    return value === undefined || value === null
      ? ''
      : encodeURIComponent(String(value))
  })
}
