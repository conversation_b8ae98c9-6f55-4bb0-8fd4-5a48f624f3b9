<?xml version='1.0'?>
<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
-->
<configuration>
  <system.web>
    <compilation debug='true'>
      <assemblies>
        <add assembly='System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089'/>
        <add assembly='System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089'/>
        <add assembly='System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35'/>
        <add assembly='System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089'/>
        <add assembly='System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089'/>
        <add assembly='System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089'/>
        <add assembly='System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35'/>
        <add assembly="Microsoft.OData.Core, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        <add assembly="Microsoft.OData.Service, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        <add assembly="Microsoft.OData.Client, Version=6.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      </assemblies>
    </compilation>
  </system.web>
  <system.codedom>
    <compilers>
      <compiler language='c#;cs;csharp' extension='.cs' type='Microsoft.CSharp.CSharpCodeProvider,System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089'>
        <providerOption name='CompilerVersion' value='v4.0' />
      </compiler>
    </compilers>
  </system.codedom>
</configuration>
