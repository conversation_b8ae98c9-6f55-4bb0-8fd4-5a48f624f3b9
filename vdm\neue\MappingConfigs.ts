/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { MappingConfigsApi } from './MappingConfigsApi';
import { MappingConfigDirectionEnum } from './MappingConfigDirectionEnum';
import { MappingConfigPartTypeEnum } from './MappingConfigPartTypeEnum';
import { MappingConfigToolEnum } from './MappingConfigToolEnum';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "MappingConfigs" of service "neue".
 */
export class MappingConfigs<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements MappingConfigsType<T>
{
  /**
   * Technical entity name for MappingConfigs.
   */
  static override _entityName = 'MappingConfigs';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the MappingConfigs entity.
   */
  static _keys = ['ncid'];
  /**
   * Direction.
   */
  declare direction: MappingConfigDirectionEnum;
  /**
   * On Cax.
   * @nullable
   */
  declare onCax?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Cdp Property.
   * Maximum length: 255.
   */
  declare cdpProperty: DeserializedType<T, 'Edm.String'>;
  /**
   * Part Type.
   * Maximum length: 255.
   */
  declare partType: MappingConfigPartTypeEnum;
  /**
   * Drawing Sheet Area.
   * Maximum length: 255.
   * @nullable
   */
  declare drawingSheetArea?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Name.
   * Maximum length: 255.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Cax Property.
   * Maximum length: 64.
   */
  declare caxProperty: DeserializedType<T, 'Edm.String'>;
  /**
   * Tool.
   * Maximum length: 64.
   */
  declare tool: MappingConfigToolEnum;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: MappingConfigsApi<T>) {
    super(_entityApi);
  }
}

export interface MappingConfigsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  direction: MappingConfigDirectionEnum;
  onCax?: DeserializedType<T, 'Edm.Boolean'> | null;
  cdpProperty: DeserializedType<T, 'Edm.String'>;
  partType: MappingConfigPartTypeEnum;
  drawingSheetArea?: DeserializedType<T, 'Edm.String'> | null;
  name: DeserializedType<T, 'Edm.String'>;
  caxProperty: DeserializedType<T, 'Edm.String'>;
  tool: MappingConfigToolEnum;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
