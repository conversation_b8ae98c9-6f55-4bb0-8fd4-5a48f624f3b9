// 测试 moment 导入是否正常工作
import moment, { Moment } from 'moment'

type Duration = moment.Duration

console.log('✅ Moment import successful!')
console.log('Current time:', moment().format())

// 测试 Duration 类型
const duration: Duration = moment.duration(1, 'hour')
console.log('Duration test:', duration.humanize())

// 测试 Moment 类型
const now: Moment = moment()
console.log('Moment test:', now.toISOString())

console.log('✅ All moment types working correctly!')
