import { type PaginationProps } from 'element-plus'
import { buildProp, buildProps } from '@neue-plus/utils'
import { ProFormProps } from '../../pro-form'
import { RequestOptions } from '../../material-render/src/handlers/handleRequest'
import { ActionsProps } from '../../pro-button'
import { SchemaElement } from '../../material-render'
import type { ExtractPropTypes, PropType } from 'vue'

export enum ValueTypeEnum {
  Avatar = 'avatar',
  Cascader = 'cascader',
  Checkbox = 'checkbox',
  Code = 'code',
  Color = 'color',
  Date = 'date',
  DateMonth = 'dateMonth',
  DateQuarter = 'dateQuarter',
  DateRange = 'dateRange',
  DateTime = 'dateTime',
  DateTimeRange = 'dateTimeRange',
  DateWeek = 'dateWeek',
  DateYear = 'dateYear',
  Dependency = 'dependency',
  Digit = 'digit',
  Divider = 'divider',
  FormList = 'formList',
  FormSet = 'formSet',
  FromNow = 'fromNow',
  Group = 'group',
  Image = 'image',
  Input = 'input',
  JsonCode = 'jsonCode',
  Money = 'money',
  Password = 'password',
  Percent = 'percent',
  Progress = 'progress',
  Radio = 'radio',
  RadioButton = 'radioButton',
  Rate = 'rate',
  Second = 'second',
  Select = 'select',
  Segmented = 'segmented',
  Switch = 'switch',
  Text = 'text',
  Textarea = 'textarea',
  Time = 'time',
  TimeRange = 'timeRange',
  TreeSelect = 'treeSelect',
}
export const edmToValueTypeMap: Record<string, ValueTypeEnum> = {
  'Edm.String': ValueTypeEnum.Text,
  'Edm.Guid': ValueTypeEnum.Text,
  'Edm.Boolean': ValueTypeEnum.Switch,
  'Edm.Int16': ValueTypeEnum.Second,
  'Edm.Int32': ValueTypeEnum.Second,
  'Edm.Int64': ValueTypeEnum.Second,
  'Edm.Decimal': ValueTypeEnum.Digit,
  'Edm.Double': ValueTypeEnum.Digit,
  'Edm.Single': ValueTypeEnum.Digit,
  'Edm.Date': ValueTypeEnum.Date,
  'Edm.DateTimeOffset': ValueTypeEnum.DateTime,
  'Edm.Time': ValueTypeEnum.Time,
  'Edm.Duration': ValueTypeEnum.Text,
  'Edm.Binary': ValueTypeEnum.Text,
  // 枚举类型可以默认映射为 Select
  'Edm.Enum': ValueTypeEnum.Select, // 可根据具体命名空间动态处理
}

export type ValueType =
  | 'avatar'
  | 'cascader'
  | 'checkbox'
  | 'code'
  | 'color'
  | 'date'
  | 'dateMonth'
  | 'dateQuarter'
  | 'dateRange'
  | 'dateTime'
  | 'dateTimeRange'
  | 'dateWeek'
  | 'dateYear'
  | 'dependency'
  | 'digit'
  | 'divider'
  | 'formList'
  | 'formSet'
  | 'fromNow'
  | 'group'
  | 'image'
  | 'input'
  | 'jsonCode'
  | 'money'
  | 'password'
  | 'percent'
  | 'progress'
  | 'radio'
  | 'radioButton'
  | 'rate'
  | 'second'
  | 'select'
  | 'segmented'
  | 'switch'
  | 'text'
  | 'textarea'
  | 'time'
  | 'timeRange'
  | 'treeSelect'
/** 单个枚举项的定义 */
export interface ValueEnumItem {
  /** 展示的文本 */
  text: string
  /** 标签颜色状态，参考 ElementPlus / Ant Design 等组件库 */
  status?: 'success' | 'error' | 'processing' | 'default' | 'warning'
  /** 自定义颜色，例如 '#13c2c2' */
  color?: string
  /** 是否禁用该项 */
  disabled?: boolean
}

/** valueEnum 类型：键值对映射（key 可以是字符串或数字） */
export type ValueEnum = Record<string | number, ValueEnumItem>

export type ColumnType = 'default' | 'selection' | 'index' | 'expand'

// ProTable列配置，扩展了筛选功能
export interface ProTableColumn {
  prop: string
  label?: string
  type?: ColumnType
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  // 筛选相关
  //类型
  valueType?: ValueType
  valueEnum?: ValueEnum
  placeholder?: string

  // 在 Table 中不展示此列
  hideInTable?: boolean
  // 在 Form 中不展示此列
  hideInForm?: boolean
  //传递给 Form.Item 的配置
  defaultValue?: number | string | boolean
  //控制操作列按钮
  actions?: ActionsProps[]
  fieldProps?: Record<string, any>
  //控制筛选使用哪种过滤
  entityType?: string
  elements?: SchemaElement[]
}
// 分页配置
export interface ProTablePagination extends Partial<PaginationProps> {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

// 筛选表单数据
export interface ProTableFilters {
  [key: string]: any
}

export type FilterType = 'query' | 'light'
export const proTableProps = buildProps({
  // odata相关用于描述哪一个实体类型定义  用于表格表单字段显示
  entityType: { type: String },
  // 表格数据
  data: buildProp({
    type: Array,
    default: () => [],
  }),
  rowKey: buildProp({
    type: String,
    default: 'ncid',
  }),
  // 列配置
  columns: buildProp({
    type: Array as () => ProTableColumn[],
    required: true,
  }),
  // 搜索表单配置
  searchConfig: buildProp({
    type: [Object, Boolean] as PropType<Partial<ProFormProps> | false>,
    default: () => ({}),
  }),
  paginationConfig: buildProp({
    type: [Object, Boolean] as PropType<ProTablePagination | false>,
    default: () => ({}),
  }),
  request: buildProp({
    type: Object as PropType<RequestOptions>,
    default: undefined,
  }),
  loading: buildProp({
    type: Boolean,
    default: false,
  }),
  bordered: buildProp({
    type: Boolean,
    default: false,
  }),
  maxHeight: buildProp({
    type: [String, Number],
  }),
  toolbar: buildProp({
    type: Object as PropType<{
      header: ActionsProps[]
      footer: ActionsProps[]
    }>,
  }),
} as const)

export type ProTableProps = ExtractPropTypes<typeof proTableProps>

// 事件类型 - 使用函数类型定义
export type ProTableEmits = {
  (e: 'filter-change', filters: ProTableFilters): void
  (e: 'page-change', current: number, pageSize: number): void
  (
    e: 'sort-change',
    prop: string,
    order: 'ascending' | 'descending' | null
  ): void
  (e: 'refresh'): void
}
