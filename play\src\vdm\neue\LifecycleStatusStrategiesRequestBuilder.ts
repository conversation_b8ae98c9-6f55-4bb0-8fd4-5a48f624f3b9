/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { LifecycleStatusStrategies } from './LifecycleStatusStrategies';

/**
 * Request builder class for operations supported on the {@link LifecycleStatusStrategies} entity.
 */
export class LifecycleStatusStrategiesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<LifecycleStatusStrategies<T>, T> {
  /**
   * Returns a request builder for querying all `LifecycleStatusStrategies` entities.
   * @returns A request builder for creating requests to retrieve all `LifecycleStatusStrategies` entities.
   */
  getAll(): GetAllRequestBuilder<LifecycleStatusStrategies<T>, T> {
    return new GetAllRequestBuilder<LifecycleStatusStrategies<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `LifecycleStatusStrategies` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `LifecycleStatusStrategies`.
   */
  create(
    entity: LifecycleStatusStrategies<T>
  ): CreateRequestBuilder<LifecycleStatusStrategies<T>, T> {
    return new CreateRequestBuilder<LifecycleStatusStrategies<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `LifecycleStatusStrategies` entity based on its keys.
   * @param ncid Key property. See {@link LifecycleStatusStrategies.ncid}.
   * @returns A request builder for creating requests to retrieve one `LifecycleStatusStrategies` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<LifecycleStatusStrategies<T>, T> {
    return new GetByKeyRequestBuilder<LifecycleStatusStrategies<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `LifecycleStatusStrategies`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `LifecycleStatusStrategies`.
   */
  update(
    entity: LifecycleStatusStrategies<T>
  ): UpdateRequestBuilder<LifecycleStatusStrategies<T>, T> {
    return new UpdateRequestBuilder<LifecycleStatusStrategies<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `LifecycleStatusStrategies`.
   * @param ncid Key property. See {@link LifecycleStatusStrategies.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `LifecycleStatusStrategies`.
   */
  delete(ncid: string): DeleteRequestBuilder<LifecycleStatusStrategies<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `LifecycleStatusStrategies`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `LifecycleStatusStrategies` by taking the entity as a parameter.
   */
  delete(
    entity: LifecycleStatusStrategies<T>
  ): DeleteRequestBuilder<LifecycleStatusStrategies<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<LifecycleStatusStrategies<T>, T> {
    return new DeleteRequestBuilder<LifecycleStatusStrategies<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof LifecycleStatusStrategies
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
