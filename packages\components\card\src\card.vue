<template>
  <el-card v-bind="cardProps" class="ne-card">
    <template v-if="$slots.header || header" #header>
      <slot name="header">
        <span :style="headerStyle">{{ header }}</span>
      </slot>
    </template>
    <slot />
    <template v-if="$slots.footer || footer" #footer>
      <slot name="footer">{{ footer }}</slot>
    </template>
  </el-card>
</template>

<script lang="ts" setup>
import { neCardProps } from './card'

defineOptions({
  name: 'NeCard',
})
const props = defineProps(neCardProps)
const { headerStyle, ...cardProps } = props
</script>
