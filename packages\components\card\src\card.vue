<template>
  <el-card v-bind="props">
    <template v-if="$slots.header || header" #header>
      <slot name="header">{{ header }}</slot>
    </template>
    <slot />
    <template v-if="$slots.footer || footer" #footer>
      <slot name="footer">{{ footer }}</slot>
    </template>
  </el-card>
</template>

<script lang="ts" setup>
import { neCardProps } from './card'

defineOptions({
  name: 'NeCard',
})

const props = defineProps(neCardProps)
</script>
