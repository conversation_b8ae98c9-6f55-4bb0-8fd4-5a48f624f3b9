import { cleanObject } from './cleanObject'

export interface ODataEntityProperties {
  name: string
  entityType: string
}

interface ODataEntity {
  name: string
  baseType?: string
  properties: ODataEntityProperties[]
  navigation: { name: string; type: string; partner?: string }[]
}

interface ODataEnumMember {
  label: string
  value: string
}

interface ODataEnumType {
  name: string
  fullName: string
  members: ODataEnumMember[]
  valueEnum: Record<string, { text: string }>
}

interface ODataModel {
  entities: Record<string, ODataEntity>
  sets: Record<string, string>
  enums: Record<string, ODataEnumType>
}

// 工具方法：合并并去重
function mergeUnique<T extends { name: string }>(
  parentArr: T[],
  childArr: T[]
): T[] {
  const childNames = new Set(childArr.map((p) => p.name))
  return [...parentArr.filter((p) => !childNames.has(p.name)), ...childArr]
}

export function parseODataMetadata(metadataJson: any): ODataModel {
  const schema = metadataJson?.['edmx:Edmx']?.['edmx:DataServices']?.Schema
  if (!schema) return { entities: {}, sets: {}, enums: {} }

  const schemaArray = Array.isArray(schema) ? schema : [schema]

  const entities: Record<string, ODataEntity> = {}
  const sets: Record<string, string> = {}
  const enums: Record<string, ODataEnumType> = {}

  schemaArray.forEach((sch) => {
    const namespace = sch._attributes?.Namespace || ''

    // ===== 解析枚举 =====
    const enumTypes = sch?.EnumType
      ? Array.isArray(sch.EnumType)
        ? sch.EnumType
        : [sch.EnumType]
      : []

    enumTypes.forEach((enumType: any) => {
      const name = enumType._attributes?.Name || 'UNKNOWN'
      const fullName = namespace ? `${namespace}.${name}` : name
      const members = (enumType.Member || []).map((m: any) => ({
        label: m._attributes?.Name || '',
        value: `${fullName}'${m._attributes?.Value || m._attributes?.Name}'`,
      }))
      const valueEnum = members.reduce((pre: any, item: any) => {
        pre[item.value] = { text: item.label }
        return pre
      }, {})
      enums[fullName] = { name, fullName, members, valueEnum }
    })
    // ===== 解析实体 =====
    const entityTypes = sch?.EntityType
      ? Array.isArray(sch.EntityType)
        ? sch.EntityType
        : [sch.EntityType]
      : []

    entityTypes.forEach((et: any) => {
      const name = et?._attributes?.Name || 'UNKNOWN'
      const baseTypeRaw = et?._attributes?.BaseType
      const baseType = baseTypeRaw ? baseTypeRaw.split('.').pop() : undefined
      const properties = Array.isArray(et.Property)
        ? et.Property.map((p: any) => {
            const entityType = p?._attributes?.Type || ''
            const valueEnum = enums[entityType]?.valueEnum || undefined
            // 解析 Annotation
            const annotations = Array.isArray(p.Annotation)
              ? p.Annotation
              : p.Annotation
              ? [p.Annotation]
              : []

            const isReadOnly = annotations.some(
              (a: any) => a._attributes?.Term === 'BuiltInModel.ReadOnly'
            )
            // const isImmutable = annotations.some(
            //   (a: any) => a._attributes?.Term === 'Org.OData.Core.V1.Immutable'
            // )
            // const required = annotations.some(
            //   (a: any) =>
            //     a._attributes?.Term === 'Org.OData.Validation.V1.Required'
            // )

            return cleanObject({
              name: p?._attributes?.Name || '',
              entityType: p?._attributes?.Type || '',
              maxLength: p?._attributes?.MaxLength
                ? Number(p._attributes.MaxLength)
                : undefined,
              nullable: p?._attributes?.Nullable === 'true', // 转成 boolean
              valueEnum,
              // required,
              // isImmutable,
              isReadOnly,
            })
          })
        : []

      const navigationRaw = et.NavigationProperty || []
      const navigationProps = Array.isArray(navigationRaw)
        ? navigationRaw
        : [navigationRaw]
      const navigation = navigationProps
        .filter((np) => np && np._attributes)
        .map((np: any) => ({
          name: np?._attributes?.Name || '',
          type: np?._attributes?.Type || '',
          partner: np?._attributes?.Partner || '',
        }))

      entities[name] = { name, baseType, properties, navigation }
    })

    // ===== 解析实体集 =====
    const entitySets = sch?.EntityContainer?.EntitySet
      ? Array.isArray(sch.EntityContainer.EntitySet)
        ? sch.EntityContainer.EntitySet
        : [sch.EntityContainer.EntitySet]
      : []

    entitySets.forEach((es: any) => {
      if (es?._attributes?.Name && es?._attributes?.EntityType) {
        sets[es._attributes.Name] = es._attributes.EntityType
      }
    })
  })

  // ===== 处理继承（BaseType） =====
  const visited = new Set<string>()
  const mergeBaseProps = (entityName: string) => {
    if (visited.has(entityName)) return
    visited.add(entityName)

    const entity = entities[entityName]
    if (!entity || !entity.baseType) return

    const parent = entities[entity.baseType]
    if (parent) {
      mergeBaseProps(entity.baseType) // 递归处理父类
      entity.properties = mergeUnique(parent.properties, entity.properties)
      entity.navigation = mergeUnique(parent.navigation, entity.navigation)
    }
  }

  Object.keys(entities).forEach((name) => mergeBaseProps(name))

  return { entities, sets, enums }
}
