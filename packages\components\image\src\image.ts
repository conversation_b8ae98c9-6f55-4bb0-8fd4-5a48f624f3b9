import {
  imageEmits,
  imageProps,
  imageViewerEmits,
  imageViewerProps,
} from 'element-plus'
import type { ExtractPropTypes } from 'vue'

export const neImageProps = {
  ...imageProps,
  ...imageViewerProps,
}

export const neImageEmits = {
  ...imageEmits,
  ...imageViewerEmits,
}

export type NeImageProps = ExtractPropTypes<typeof neImageProps>
export type NeImageEmits = ExtractPropTypes<typeof neImageEmits>
