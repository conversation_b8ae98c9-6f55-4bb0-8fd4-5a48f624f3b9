/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import { Location, LocationField } from './Location';
import type { PeopleApi } from './PeopleApi';
import { PersonGender } from './PersonGender';
import { Feature } from './Feature';

/**
 * This class represents the entity "People" of service "Trippin".
 */
export class People<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements PeopleType<T>
{
  /**
   * Technical entity name for People.
   */
  static override _entityName = 'People';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the People entity.
   */
  static _keys = ['UserName'];
  /**
   * User Name.
   */
  declare userName: DeserializedType<T, 'Edm.String'>;
  /**
   * First Name.
   */
  declare firstName: DeserializedType<T, 'Edm.String'>;
  /**
   * Last Name.
   * Maximum length: 26.
   * @nullable
   */
  declare lastName?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Middle Name.
   * @nullable
   */
  declare middleName?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Gender.
   */
  declare gender: PersonGender;
  /**
   * Age.
   * @nullable
   */
  declare age?: DeserializedType<T, 'Edm.Int64'> | null;
  /**
   * Emails.
   * @nullable
   */
  declare emails?: DeserializedType<T, 'Edm.String'>[] | null;
  /**
   * Address Info.
   * @nullable
   */
  declare addressInfo?: Location<T>[] | null;
  /**
   * Home Address.
   * @nullable
   */
  declare homeAddress?: Location<T> | null;
  /**
   * Favorite Feature.
   */
  declare favoriteFeature: Feature;
  /**
   * Features.
   */
  declare features: Feature[];
  /**
   * One-to-one navigation property to the {@link People} entity.
   */
  declare bestFriend?: People<T> | null;
  /**
   * One-to-many navigation property to the {@link People} entity.
   */
  declare friends: People<T>[];

  constructor(_entityApi: PeopleApi<T>) {
    super(_entityApi);
  }
}

export interface PeopleType<T extends DeSerializers = DefaultDeSerializers> {
  userName: DeserializedType<T, 'Edm.String'>;
  firstName: DeserializedType<T, 'Edm.String'>;
  lastName?: DeserializedType<T, 'Edm.String'> | null;
  middleName?: DeserializedType<T, 'Edm.String'> | null;
  gender: PersonGender;
  age?: DeserializedType<T, 'Edm.Int64'> | null;
  emails?: DeserializedType<T, 'Edm.String'>[] | null;
  addressInfo?: Location<T>[] | null;
  homeAddress?: Location<T> | null;
  favoriteFeature: Feature;
  features: Feature[];
  bestFriend?: PeopleType<T> | null;
  friends: PeopleType<T>[];
}
