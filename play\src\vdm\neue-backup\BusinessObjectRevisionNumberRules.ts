/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { BusinessObjectRevisionNumberRulesApi } from './BusinessObjectRevisionNumberRulesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "BusinessObjectRevisionNumberRules" of service "neue".
 */
export class BusinessObjectRevisionNumberRules<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements BusinessObjectRevisionNumberRulesType<T>
{
  /**
   * Technical entity name for BusinessObjectRevisionNumberRules.
   */
  static override _entityName = 'BusinessObjectRevisionNumberRules';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the BusinessObjectRevisionNumberRules entity.
   */
  static _keys = ['ncid'];
  /**
   * Rule Code.
   * Maximum length: 255.
   */
  declare ruleCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   * Maximum length: 255.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Is Active.
   */
  declare isActive: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Is Employed.
   */
  declare isEmployed: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Is Locked.
   */
  declare isLocked: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Description.
   * Maximum length: 1000.
   * @nullable
   */
  declare description?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: BusinessObjectRevisionNumberRulesApi<T>) {
    super(_entityApi);
  }
}

export interface BusinessObjectRevisionNumberRulesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  ruleCode: DeserializedType<T, 'Edm.String'>;
  name: DeserializedType<T, 'Edm.String'>;
  isActive: DeserializedType<T, 'Edm.Boolean'>;
  isEmployed: DeserializedType<T, 'Edm.Boolean'>;
  isLocked: DeserializedType<T, 'Edm.Boolean'>;
  description?: DeserializedType<T, 'Edm.String'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
