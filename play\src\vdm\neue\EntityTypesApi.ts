/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { EntityTypes } from './EntityTypes';
import { EntityTypesRequestBuilder } from './EntityTypesRequestBuilder';
import { MrvStrategyType } from './MrvStrategyType';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4'
import type {
  DefaultDeSerializers,
  DeSerializers
} from '@sap-cloud-sdk/odata-v4';
export class EntityTypesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<EntityTypes<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): EntityTypesApi<DeSerializersT> {
    return new EntityTypesApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = EntityTypes;

  requestBuilder(): EntityTypesRequestBuilder<DeSerializersT> {
    return new EntityTypesRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    EntityTypes<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<EntityTypes<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<EntityTypes<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof EntityTypes, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(EntityTypes, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    ENTITY_TYPE_CODE: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    IS_VIRTUAL: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    HAS_STREAM: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    MRV_STRATEGY: EnumField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      MrvStrategyType,
      true,
      true
    >;
    IS_READ_ONLY: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    NAME: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DISPLAY_NAME: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    IS_ABSTRACT: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    IS_OPEN: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      EntityTypes<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<EntityTypes<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link entityTypeCode} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ENTITY_TYPE_CODE: fieldBuilder.buildEdmTypeField(
          'entityTypeCode',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link isVirtual} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_VIRTUAL: fieldBuilder.buildEdmTypeField(
          'isVirtual',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link hasStream} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        HAS_STREAM: fieldBuilder.buildEdmTypeField(
          'hasStream',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link mrvStrategy} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MRV_STRATEGY: fieldBuilder.buildEnumField(
          'mrvStrategy',
          MrvStrategyType,
          true
        ),
        /**
         * Static representation of the {@link isReadOnly} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_READ_ONLY: fieldBuilder.buildEdmTypeField(
          'isReadOnly',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', false),
        /**
         * Static representation of the {@link displayName} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DISPLAY_NAME: fieldBuilder.buildEdmTypeField(
          'displayName',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link isAbstract} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_ABSTRACT: fieldBuilder.buildEdmTypeField(
          'isAbstract',
          'Edm.Boolean',
          true
        ),
        /**
         * Static representation of the {@link isOpen} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IS_OPEN: fieldBuilder.buildEdmTypeField('isOpen', 'Edm.Boolean', true),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', EntityTypes)
      };
    }

    return this._schema;
  }
}
