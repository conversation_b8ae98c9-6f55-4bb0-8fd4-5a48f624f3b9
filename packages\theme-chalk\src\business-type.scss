@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@include neb(business-type) {
  .depart-tree .custom-node-content {
    display: flex;
    flex-direction: row;
    flex: 1;
    min-width: 150px;
  }
  .depart-tree .custom-node-content .node-label {
    flex: 1;
  }
  .depart-tree .is-disabled {
    color: #c0c4cc;
  }
  .el-tree-node:focus>.el-tree-node__content {
    background: 0 none!important;
  }
  .el-tree-node.is-checked>.el-tree-node__content {
    background-color: #DEE8FF !important;
  }
}
