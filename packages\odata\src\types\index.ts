/**
 * OData 客户端类型定义
 */

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// OData 查询选项
export interface ODataQueryOptions {
  $select?: string[]
  $expand?: string[]
  $filter?: string
  $orderby?: string
  $top?: number
  $skip?: number
  $count?: boolean
  $search?: string
  $format?: 'json' | 'xml'
}

// HTTP 请求配置
export interface RequestConfig {
  url: string
  method?: HttpMethod
  headers?: Record<string, string>
  data?: any
  params?: Record<string, any>
  timeout?: number
  withCredentials?: boolean
}

// HTTP 响应
export interface Response<T = any> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
  config: RequestConfig
}

// OData 响应格式
export interface ODataResponse<T = any> {
  '@odata.context'?: string
  '@odata.count'?: number
  '@odata.nextLink'?: string
  value?: T[]
  data?: T
}

// OData 错误响应
export interface ODataError {
  error: {
    code: string
    message: string
    details?: Array<{
      code: string
      message: string
      target?: string
    }>
  }
}

// OData 服务配置
export interface ODataServiceConfig {
  baseUrl: string
  servicePath?: string
  headers?: Record<string, string>
  timeout?: number
  withCredentials?: boolean
  csrfToken?: string
  authentication?: {
    type: 'basic' | 'bearer' | 'oauth2'
    credentials?: {
      username?: string
      password?: string
      token?: string
    }
  }
}

// 实体集配置
export interface EntitySetConfig {
  name: string
  entityType: string
  keyProperties: string[]
  navigationProperties?: Record<
    string,
    {
      type: 'single' | 'collection'
      target: string
    }
  >
}

// 查询构建器接口
export interface QueryBuilder<T = any> {
  select(...fields: string[]): QueryBuilder<T>
  expand(...relations: string[]): QueryBuilder<T>
  filter(condition: string): QueryBuilder<T>
  orderBy(field: string, direction?: 'asc' | 'desc'): QueryBuilder<T>
  top(count: number): QueryBuilder<T>
  skip(count: number): QueryBuilder<T>
  count(include?: boolean): QueryBuilder<T>
  search(term: string): QueryBuilder<T>
  execute(): Promise<ODataResponse<T>>
  executeRaw(): Promise<Response<ODataResponse<T>>>
}

// 实体操作接口
export interface EntityOperations<T = any> {
  // 查询操作
  getAll(options?: ODataQueryOptions): Promise<ODataResponse<T>>
  getById(id: string | number, options?: ODataQueryOptions): Promise<T>
  query(): QueryBuilder<T>

  // 修改操作
  create(entity: Partial<T>): Promise<T>
  update(id: string | number, entity: Partial<T>): Promise<T>
  patch(id: string | number, entity: Partial<T>): Promise<T>
  delete(id: string | number): Promise<void>

  // 批量操作
  createBatch(entities: Partial<T>[]): Promise<T[]>
  updateBatch(
    updates: Array<{ id: string | number; entity: Partial<T> }>
  ): Promise<T[]>
  deleteBatch(ids: Array<string | number>): Promise<void>
}

// OData 客户端接口
export interface ODataClient {
  // 配置
  config: ODataServiceConfig

  // 实体集操作
  entitySet<T = any>(name: string): EntityOperations<T>

  // 原始请求
  request<T = any>(config: RequestConfig): Promise<Response<T>>

  // 元数据
  getMetadata(): Promise<any>

  // 批量请求
  batch(requests: RequestConfig[]): Promise<Response[]>

  // 功能导入
  callFunction(name: string, parameters?: Record<string, any>): Promise<any>
  callAction(name: string, parameters?: Record<string, any>): Promise<any>
}

// 缓存接口
export interface CacheOptions {
  enabled: boolean
  ttl?: number // 缓存时间（毫秒）
  maxSize?: number // 最大缓存条目数
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
}

// 拦截器类型
export type RequestInterceptor = (
  config: RequestConfig
) => RequestConfig | Promise<RequestConfig>
export type ResponseInterceptor = (
  response: Response
) => Response | Promise<Response>
export type ErrorInterceptor = (error: any) => any | Promise<any>

// 插件接口
export interface ODataPlugin {
  name: string
  install(client: ODataClient): void
}

// 事件类型
export type ODataEventType =
  | 'request:start'
  | 'request:success'
  | 'request:error'
  | 'request:complete'
  | 'cache:hit'
  | 'cache:miss'

export interface ODataEvent {
  type: ODataEventType
  data?: any
  timestamp: number
}

// 元数据相关类型
export interface MetadataProperty {
  name: string
  type: string
  nullable: boolean
  maxLength?: number
  precision?: number
  scale?: number
}

export interface MetadataNavigationProperty {
  name: string
  type: string
  partner?: string
  containsTarget?: boolean
}

export interface MetadataEntityType {
  name: string
  namespace: string
  properties: MetadataProperty[]
  navigationProperties: MetadataNavigationProperty[]
  keys: string[]
}

export interface MetadataEntitySet {
  name: string
  entityType: string
  navigationPropertyBinding?: Record<string, string>
}

export interface MetadataSchema {
  namespace: string
  entityTypes: MetadataEntityType[]
  entitySets: MetadataEntitySet[]
}

export interface ServiceMetadata {
  version: string
  schemas: MetadataSchema[]
}

// SAP Cloud SDK 相关配置
export interface SapSdkConfig {
  useSapSdk?: boolean
  destination?: {
    name: string
    url?: string
    [key: string]: any
  }
}

// 通用 HTTP 客户端接口
export interface HttpClient {
  get<T = any>(
    url: string,
    queryOptions?: ODataQueryOptions,
    headers?: Record<string, string>
  ): Promise<Response<ODataResponse<T>>>

  post<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>>

  put<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>>

  patch<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<Response<T>>

  delete(url: string, headers?: Record<string, string>): Promise<Response<void>>

  getMetadata(): Promise<any>

  request<T = any>(config: RequestConfig): Promise<Response<T>>
}
