import { ConfigFileOptions, EmitModes } from '@odata2ts/odata2ts'

const config: ConfigFileOptions = {
  emitMode: EmitModes.js_dts,
  prettier: true,
  // tsconfig: 'tsconfig.json',
  services: {
    trippin: {
      sourceUrl: 'https://services.odata.org/TripPinRESTierService',
      source: 'resource/metadata.xml',
      output: 'packages/components/material-render/src/build/trippin',
    },
    neue: {
      // sourceUrl: 'https://services.odata.org/TripPinRESTierService',
      source: 'resource/neue.xml',
      output: 'packages/components/material-render/src/build/neue',
    },
  },
}

export default config
