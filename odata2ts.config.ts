import { ConfigFileOptions } from '@odata2ts/odata2ts'

const config: ConfigFileOptions = {
  services: {
    trippin: {
      sourceUrl: 'https://services.odata.org/TripPinRESTierService',
      source: 'resource/metadata.xml',
      output: 'build/trippin',
    },
    neue: {
      // sourceUrl: 'https://services.odata.org/TripPinRESTierService',
      source: 'resource/neue.xml',
      output: 'build/neue',
    },
  },
}

export default config
