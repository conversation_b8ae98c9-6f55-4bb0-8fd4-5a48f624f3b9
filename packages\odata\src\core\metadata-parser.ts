/**
 * OData 元数据解析器
 */

export interface EntityType {
  name: string
  namespace: string
  properties: Property[]
  navigationProperties: NavigationProperty[]
  keys: string[]
}

export interface Property {
  name: string
  type: string
  nullable: boolean
  maxLength?: number
  precision?: number
  scale?: number
}

export interface NavigationProperty {
  name: string
  type: string
  partner?: string
  containsTarget?: boolean
}

export interface EntitySet {
  name: string
  entityType: string
  navigationPropertyBinding?: Record<string, string>
}

export interface Schema {
  namespace: string
  entityTypes: EntityType[]
  entitySets: EntitySet[]
}

export interface ServiceMetadata {
  version: string
  schemas: Schema[]
}

/**
 * 元数据解析器类
 */
export class MetadataParser {
  /**
   * 解析 XML 元数据
   */
  static parseXml(xmlContent: string): ServiceMetadata {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xmlContent, 'application/xml')

    // 检查解析错误
    const parseError = doc.querySelector('parsererror')
    if (parseError) {
      throw new Error(`XML parsing error: ${parseError.textContent}`)
    }

    const edmx = doc.documentElement
    const dataServices = edmx.querySelector('DataServices')

    if (!dataServices) {
      throw new Error('Invalid OData metadata: DataServices element not found')
    }

    const schemas: Schema[] = []
    const schemaElements = dataServices.querySelectorAll('Schema')

    schemaElements.forEach((schemaElement) => {
      const schema = this.parseSchema(schemaElement)
      schemas.push(schema)
    })

    return {
      version: edmx.getAttribute('Version') || '4.0',
      schemas,
    }
  }

  /**
   * 解析 Schema 元素
   */
  private static parseSchema(schemaElement: Element): Schema {
    const namespace = schemaElement.getAttribute('Namespace') || ''
    const entityTypes: EntityType[] = []
    const entitySets: EntitySet[] = []

    // 解析实体类型
    const entityTypeElements = schemaElement.querySelectorAll('EntityType')
    entityTypeElements.forEach((element) => {
      const entityType = this.parseEntityType(element, namespace)
      entityTypes.push(entityType)
    })

    // 解析实体集
    const entityContainer = schemaElement.querySelector('EntityContainer')
    if (entityContainer) {
      const entitySetElements = entityContainer.querySelectorAll('EntitySet')
      entitySetElements.forEach((element) => {
        const entitySet = this.parseEntitySet(element)
        entitySets.push(entitySet)
      })
    }

    return {
      namespace,
      entityTypes,
      entitySets,
    }
  }

  /**
   * 解析实体类型
   */
  private static parseEntityType(
    element: Element,
    namespace: string
  ): EntityType {
    const name = element.getAttribute('Name') || ''
    const properties: Property[] = []
    const navigationProperties: NavigationProperty[] = []
    const keys: string[] = []

    // 解析键
    const keyElement = element.querySelector('Key')
    if (keyElement) {
      const propertyRefs = keyElement.querySelectorAll('PropertyRef')
      propertyRefs.forEach((ref) => {
        const keyName = ref.getAttribute('Name')
        if (keyName) {
          keys.push(keyName)
        }
      })
    }

    // 解析属性
    const propertyElements = element.querySelectorAll('Property')
    propertyElements.forEach((propElement) => {
      const property = this.parseProperty(propElement)
      properties.push(property)
    })

    // 解析导航属性
    const navPropertyElements = element.querySelectorAll('NavigationProperty')
    navPropertyElements.forEach((navElement) => {
      const navProperty = this.parseNavigationProperty(navElement)
      navigationProperties.push(navProperty)
    })

    return {
      name,
      namespace,
      properties,
      navigationProperties,
      keys,
    }
  }

  /**
   * 解析属性
   */
  private static parseProperty(element: Element): Property {
    const name = element.getAttribute('Name') || ''
    const type = element.getAttribute('Type') || ''
    const nullable = element.getAttribute('Nullable') !== 'false'
    const maxLength = element.getAttribute('MaxLength')
    const precision = element.getAttribute('Precision')
    const scale = element.getAttribute('Scale')

    const property: Property = {
      name,
      type,
      nullable,
    }

    if (maxLength) {
      property.maxLength = Number.parseInt(maxLength, 10)
    }

    if (precision) {
      property.precision = Number.parseInt(precision, 10)
    }

    if (scale) {
      property.scale = Number.parseInt(scale, 10)
    }

    return property
  }

  /**
   * 解析导航属性
   */
  private static parseNavigationProperty(element: Element): NavigationProperty {
    const name = element.getAttribute('Name') || ''
    const type = element.getAttribute('Type') || ''
    const partner = element.getAttribute('Partner') || undefined
    const containsTarget = element.getAttribute('ContainsTarget') === 'true'

    return {
      name,
      type,
      partner,
      containsTarget,
    }
  }

  /**
   * 解析实体集
   */
  private static parseEntitySet(element: Element): EntitySet {
    const name = element.getAttribute('Name') || ''
    const entityType = element.getAttribute('EntityType') || ''
    const navigationPropertyBinding: Record<string, string> = {}

    // 解析导航属性绑定
    const bindingElements = element.querySelectorAll(
      'NavigationPropertyBinding'
    )
    bindingElements.forEach((binding) => {
      const path = binding.getAttribute('Path')
      const target = binding.getAttribute('Target')
      if (path && target) {
        navigationPropertyBinding[path] = target
      }
    })

    return {
      name,
      entityType,
      navigationPropertyBinding:
        Object.keys(navigationPropertyBinding).length > 0
          ? navigationPropertyBinding
          : undefined,
    }
  }

  /**
   * 根据实体集名称查找实体类型
   */
  static findEntityType(
    metadata: ServiceMetadata,
    entitySetName: string
  ): EntityType | undefined {
    for (const schema of metadata.schemas) {
      const entitySet = schema.entitySets.find(
        (es) => es.name === entitySetName
      )
      if (entitySet) {
        // 移除命名空间前缀
        const typeName =
          entitySet.entityType.split('.').pop() || entitySet.entityType
        return schema.entityTypes.find((et) => et.name === typeName)
      }
    }
    return undefined
  }

  /**
   * 获取所有实体集名称
   */
  static getEntitySetNames(metadata: ServiceMetadata): string[] {
    const names: string[] = []
    for (const schema of metadata.schemas) {
      names.push(...schema.entitySets.map((es) => es.name))
    }
    return names
  }
}
