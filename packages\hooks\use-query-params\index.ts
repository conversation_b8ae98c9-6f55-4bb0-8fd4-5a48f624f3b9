// useQueryParams.ts
import { computed, onMounted, onUnmounted, ref } from 'vue'

/**
 * useQueryParams - 获取浏览器地址栏的查询参数
 */
export function useQueryParams() {
  const queryParams = ref(new URLSearchParams(window.location.search))
  const updateParams = () => {
    queryParams.value = new URLSearchParams(window.location.search)
  }
  const queryObj = computed(() =>
    Object.fromEntries(queryParams.value.entries())
  )

  onMounted(() => {
    window.addEventListener('popstate', updateParams)
  })

  onUnmounted(() => {
    window.removeEventListener('popstate', updateParams)
  })

  return queryObj
}
