<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<template>
  <el-form ref="formRef" :model="formData" v-bind="formProps">
    <el-row :gutter="props.gutter">
      <el-col
        v-for="item in visibleItems"
        :key="item.prop"
        v-bind="item.colProps"
      >
        <el-form-item v-bind="item" :rules="getItemRules(item)">
          <!-- 自定义渲染 -->
          <component
            :is="getFormItemComponent(item.valueType)"
            v-bind="getFormItemProps(item)"
            v-model="formData[item.prop]"
            @change="(value: any) => handleChange(item.prop, value)"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 操作按钮 -->
    <el-form-item>
      <el-space :style="{ justifyContent: buttonAlign, width: '100%' }">
        <el-button v-bind="submitButtonProps" @click="handleSubmit">{{
          '提交'
        }}</el-button>
        <el-button v-bind="resetButtonProps" @click="handleReset">{{
          '重置'
        }}</el-button>
        <slot name="extra-buttons" />
      </el-space>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue'
import { get, set } from 'lodash-unified'
import { type ProFormItemProps, proFormProps } from './types'
import { useComponentMap } from './formItemMap'

const {
  getComponentMap: getFormItemComponent,
  getDefaultValue,
  getFormItemProps,
} = useComponentMap()

defineOptions({
  name: 'NeProForm',
  inheritAttrs: false,
})

const props = defineProps(proFormProps)
const formRef = ref()
const formData = ref<Record<string, any>>({})
const formProps = computed(() => {
  const { size, labelWidth, disabled, inline } = props
  return {
    size,
    labelWidth,
    disabled,
    inline,
  }
})

// 可见的表单项
const visibleItems = computed(() => {
  return props.formItems
    ?.filter((item) => item.show !== false)
    .map((item) => {
      const { span = 8, offset = 0, ...restProps } = item
      return {
        colProps: { span, offset },
        ...restProps,
      }
    })
})

// 获取表单项规则
const getItemRules = (item: ProFormItemProps) => {
  const { rules = [] } = item
  if (item.required) {
    rules.unshift({
      required: true,
      message: `请输入${item.label}`,
      trigger: ['blur', 'change'],
    })
  }
  return rules
}
const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'cancel',
  'change',
  'validate',
])
// 处理值变化
const handleChange = (prop: string, value: any) => {
  formData.value[prop] = value
  // emit('update:modelValue', formData.value)
  // emit('change', prop, value)
}

// 提交表单
const handleSubmit = async () => {
  await formRef.value?.validate()
  emit('update:modelValue', formData.value)
  emit('submit', formData.value)
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  emit('update:modelValue', formData.value)
  emit('reset', formData.value)
}

// 初始化表单数据
watchEffect(() => {
  const { defaultValue } = props
  const newFormData = {}
  visibleItems.value?.forEach((item) => {
    set(
      newFormData,
      item.prop,
      get(defaultValue, item.prop) || getDefaultValue(item.valueType)
    )
  })
  formData.value = newFormData
})

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
})
</script>
