{"config": {"apiBaseConfig": {"baseUrl": ""}}, "apis": {"submit": {"url": "/cdp/202512/OdataService/CadFile", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"expand": "lifecycleStatus,thumbnail,version($expand=revision)"}}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}, "apiJson": {"sourceType": "json", "source": []}}, "elements": [{"id": "odata-card", "type": "card", "name": "OData表格", "props": {"headerClass": "123"}, "elements": [{"id": "odata-table", "type": "pro-table", "name": "OData表格", "props": {"entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "code", "label": "零部件编号"}, {"prop": "schemaVersion", "label": "缩略图", "valueType": "image"}, {"prop": "name", "label": "零部件名称"}, {"prop": "partType", "label": "子类别"}, {"prop": "version.versionNumber", "label": "版本号", "render": "({ row }) => 'v'+row.version.versionNumber "}, {"prop": "lifecycleStatus", "label": "状态", "valueEnum": "BuiltInModel.Plt0LifecycleStatus"}, {"prop": "schemaVersion", "label": "修改人", "valueType": "avatar"}, {"prop": "modifiedAt", "label": "修改时间", "valueType": "time"}, {"prop": "schemaVersion", "label": "责任人", "valueType": "avatar"}, {"prop": "operation", "label": "操作", "minWidth": 100, "actions": [{"text": "编辑", "props": {"link": true, "type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk"}}, {"text": "删除", "props": {"link": true, "type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk"}}]}], "toolbar": {"header": [{"text": "新建", "props": {"type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk"}}, {"text": "导出", "props": {"type": "success"}, "config": {"actionType": "open", "target": "Drawer_er4mn6jbtk"}}], "footer": [{"text": "编辑", "props": {"type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk"}}, {"text": "删除", "props": {"type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk"}}]}}, "events": [], "api": {}}], "slots": {"header": "设计零部件"}}]}