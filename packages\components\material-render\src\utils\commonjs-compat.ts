// CommonJS 兼容性工具
// 用于在浏览器环境中支持 CommonJS 模块

// 定义全局的 CommonJS 变量
declare global {
  interface Window {
    exports?: any
    module?: any
    require?: any
  }
}

// 模块缓存
const moduleCache = new Map<string, any>()

// 初始化 CommonJS 环境
export const initCommonJSEnvironment = () => {
  if (typeof window !== 'undefined') {
    // 如果还没有定义，则创建 exports 和 module 对象
    if (!window.exports) {
      window.exports = {}
    }
    if (!window.module) {
      window.module = { exports: window.exports }
    }

    // 实现一个更完整的 require 函数
    if (!window.require) {
      window.require = (id: string) => {
        console.warn(`require('${id}') called but not implemented`)

        // 检查缓存
        if (moduleCache.has(id)) {
          return moduleCache.get(id)
        }

        // 对于已知的模块，返回空的模拟对象
        let mockModule: any = {}

        if (id === '@odata2ts/odata-query-objects') {
          // 模拟 odata-query-objects 的基本结构
          mockModule = {
            QueryObject: class QueryObject {},
            QStringPath: class QStringPath {},
            QNumberPath: class QNumberPath {},
            QBooleanPath: class QBooleanPath {},
            QEntityPath: class QEntityPath {},
            QEntityCollectionPath: class QEntityCollectionPath {},
            QCollectionPath: class QCollectionPath {},
            QEnumPath: class QEnumPath {},
            QId: class QId {},
            QFunction: class QFunction {},
            QAction: class QAction {},
            // 添加其他可能需要的类...
          }
        } else if (id.startsWith('./')) {
          // 对于相对路径的模块，返回空对象
          mockModule = {}
        }

        // 缓存模块
        moduleCache.set(id, mockModule)
        return mockModule
      }
    }
  }
}

// 安全地加载 CommonJS 模块
export const loadCommonJSModule = async (modulePath: string) => {
  // 确保 CommonJS 环境已初始化
  initCommonJSEnvironment()

  try {
    // 动态导入模块
    const module = await import(modulePath)
    return module
  } catch (error) {
    console.error(`Failed to load CommonJS module: ${modulePath}`, error)
    throw error
  }
}

// 专门用于加载 PaaS 模块的函数
export const loadPaaSModules = async () => {
  initCommonJSEnvironment()

  try {
    // 先只加载 PaaSModel，因为它不依赖其他复杂模块
    const PaaSModel = await import('../build/neue/PaaSModel')

    console.log('PaaSModel loaded successfully')

    // 尝试加载其他模块，如果失败就跳过
    let PaaSService, QPaaS

    try {
      PaaSService = await import('../build/neue/PaaSService')
      console.log('PaaSService loaded successfully')
    } catch (serviceError) {
      console.warn('Failed to load PaaSService:', serviceError)
      PaaSService = null
    }

    try {
      QPaaS = await import('../build/neue/QPaaS')
      console.log('QPaaS loaded successfully')
    } catch (qpaasError) {
      console.warn('Failed to load QPaaS:', qpaasError)
      QPaaS = null
    }

    return {
      PaaSModel,
      PaaSService,
      QPaaS,
      // 便捷访问常用枚举（只从 PaaSModel 获取）
      LifecycleState: PaaSModel.LifecycleState,
      OnDelete: PaaSModel.OnDelete,
      PartType: PaaSModel.PartType,
      MrvStrategyType: PaaSModel.MrvStrategyType,
      RelationStatus: PaaSModel.RelationStatus,
    }
  } catch (error) {
    console.error('Failed to load PaaS modules:', error)
    throw error
  }
}
