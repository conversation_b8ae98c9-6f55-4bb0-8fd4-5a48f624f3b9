// CommonJS 兼容性工具
// 用于在浏览器环境中支持 CommonJS 模块

// 定义全局的 CommonJS 变量
declare global {
  interface Window {
    exports?: any
    module?: any
    require?: any
  }
}

// 初始化 CommonJS 环境
export const initCommonJSEnvironment = () => {
  if (typeof window !== 'undefined') {
    // 如果还没有定义，则创建 exports 和 module 对象
    if (!window.exports) {
      window.exports = {}
    }
    if (!window.module) {
      window.module = { exports: window.exports }
    }
    
    // 简单的 require 函数模拟（如果需要的话）
    if (!window.require) {
      window.require = (id: string) => {
        console.warn(`require('${id}') called but not implemented`)
        return {}
      }
    }
  }
}

// 安全地加载 CommonJS 模块
export const loadCommonJSModule = async (modulePath: string) => {
  // 确保 CommonJS 环境已初始化
  initCommonJSEnvironment()
  
  try {
    // 动态导入模块
    const module = await import(modulePath)
    return module
  } catch (error) {
    console.error(`Failed to load CommonJS module: ${modulePath}`, error)
    throw error
  }
}

// 专门用于加载 PaaS 模块的函数
export const loadPaaSModules = async () => {
  initCommonJSEnvironment()
  
  try {
    const [PaaSModel, PaaSService, QPaaS] = await Promise.all([
      import('../build/neue/PaaSModel'),
      import('../build/neue/PaaSService'),
      import('../build/neue/QPaaS')
    ])
    
    return {
      PaaSModel,
      PaaSService,
      QPaaS,
      // 便捷访问常用枚举
      LifecycleState: PaaSModel.LifecycleState,
      OnDelete: PaaSModel.OnDelete,
      PartType: PaaSModel.PartType,
      MrvStrategyType: PaaSModel.MrvStrategyType,
      RelationStatus: PaaSModel.RelationStatus,
    }
  } catch (error) {
    console.error('Failed to load PaaS modules:', error)
    throw error
  }
}
