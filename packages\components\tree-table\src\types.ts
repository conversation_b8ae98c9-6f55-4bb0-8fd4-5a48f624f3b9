import { buildProp, buildProps } from '@neue-plus/utils'
import { ProTableColumn } from '@neue-plus/components/pro-table'
import { RequestOptions } from '../../material-render/src/handlers/handleRequest'
import type { TableProps } from 'element-plus'
import type { ExtractPropTypes, PropType } from 'vue'

export const neTreeTableProps = buildProps({
  bordered: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as () => Array<any>,
    default: () => [],
  },
  columns: {
    type: Array as () => Array<ProTableColumn>,
    default: () => [],
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'toRelations',
      label: 'label',
      hasChildren: 'hasChildren',
    }),
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
  indent: {
    type: Number,
    default: 16,
  },
  request: buildProp({
    type: Object as PropType<RequestOptions>,
    default: undefined,
  }),
} as const)

export type NeTreeTableProps = ExtractPropTypes<typeof neTreeTableProps> &
  TableProps<any>
