<template>
  <!-- textarea -->
  <el-input
    v-if="filed.fieldType === 'textarea'"
    v-model="innerValue"
    :placeholder="filed.placeholder || ''"
    :disabled="isDisabled"
    :type="filed.fieldType"
    :rows="filed.rows || 4"
    :minlength="filed.minLength || 0"
    :maxlength="filed.maxLength || 255"
    :show-word-limit="filed.showWordLimit || true"
    style="width: 100%"
  />
  <!-- input -->
  <el-input
    v-else-if="formItem.fieldType === 'text' || 'dateShow'"
    v-model="innerValue"
    :placeholder="filed.placeholder || ''"
    :type="filed.inputType || 'text'"
    :disabled="isDisabled"
    :minlength="filed.minLength || 0"
    :maxlength="filed.maxLength || 255"
    :show-word-limit="filed.showWordLimit || true"
    style="width: 100%"
    :formatter="
      (val: any) =>
        item.fieldType === 'dateShow' && val
          ? dayjs(val).format(item?.dateformat?.format || 'YYYY-MM-DD HH:mm:ss')
          : val
    "
    :parser="
      (val: any) => {
        return val
      }
    "
  />
  <!-- textarea -->
  <!-- <el-input
    v-else-if="filed.fieldType === 'textarea'"
    v-model="innerValue"
    :placeholder="filed.placeholder || ''"
    :disabled="isDisabled"
    :type="filed.fieldType || 'text'"
    :rows="filed.rows || 4"
    :minlength="filed.minLength || 0"
    :maxlength="filed.maxLength || 255"
    :show-word-limit="filed.showWordLimit || true"
    style="width: 100%"
  /> -->
  <!-- select -->
  <el-select
    v-if="formItem.fieldType === 'select'"
    v-model="innerValue[filed.prop]"
    :placeholder="formItem.placeholder || ''"
    :disabled="isDisabled"
    :rules="[
      { required: formItem.required, message: ' 请选择', trigger: 'change' },
    ]"
    style="width: 100%"
  >
    <el-option
      v-for="option in formItem.options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    />
  </el-select>
  <el-input-number
    v-else-if="formItem.fieldType === 'number'"
    v-model="innerValue[filed.prop]"
    :placeholder="formItem.placeholder || ''"
    :min="formItem.min || 0"
    :max="formItem.max || 100"
    :step="formItem.step || 1"
    :disabled="isDisabled"
    style="width: 100%"
  />
  <!-- switch -->
  <el-switch
    v-else-if="formItem.fieldType === 'switch'"
    v-model="innerValue[filed.prop]"
    :active-text="formItem.activeText || '开'"
    :inactive-text="formItem.inactiveText || '关'"
    :disabled="isDisabled"
  />
  <!-- checkbox -->
  <el-checkbox-group
    v-else-if="formItem.fieldType === 'checkbox'"
    v-model="innerValue[filed.prop]"
    :disabled="isDisabled"
    style="width: 100%"
  >
    <el-checkbox
      v-for="option in formItem.options"
      :key="option.value"
      :value="option.value"
      :label="option.label"
    />
  </el-checkbox-group>
  <!-- radio -->
  <el-radio-group
    v-else-if="formItem.fieldType === 'radio'"
    v-model="innerValue[filed.prop]"
    :disabled="isDisabled"
    style="width: 100%"
  >
    <el-radio
      v-for="option in formItem.options"
      :key="option.value"
      :value="option.value"
      :label="option.label"
    />
  </el-radio-group>
  <!-- date picker -->
  <el-date-picker
    v-else-if="formItem.fieldType === 'date'"
    v-model="innerValue[filed.prop]"
    :type="formItem.dateType || 'date'"
    :placeholder="formItem.placeholder"
    :format="formItem.format || 'yyyy-MM-dd'"
    :value-format="formItem.valueFormat || 'yyyy-MM-dd'"
    style="width: 100%"
  />
  <!-- WgBusinessType -->
  <div
    v-else-if="formItem.fieldType === 'businessType'"
    class="business-type"
    style="display: flex; align-items: center; gap: 10px; width: 100%"
  >
    <el-input
      v-model="formItem.businessType"
      type="text"
      style="flex: 1"
      :disabled="isDisabled"
    />
    <NeButton :disabled="isDisabled" @click="openDialog(formItem)"
      >···</NeButton
    >
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
// import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import dayjs from 'dayjs'
import NeButton from '@neue-plus/components/button'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  modelValue: {
    type: Object,
    required: true,
  },
  isEdit: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue'])
interface FormModel {
  [key: string]: any
}

const formItem = ref<any>(props.item)
const formModel = ref<FormModel>({})
// const businessTypeDialogVisible = ref(false)

// const innerValue = computed({
//   get: () => props.modelValue,
//   set(val) {
//     emit('update:modelValue', {
//       ...props.modelValue,
//       [props.item.prop]: val,
//     })
//   },
// })

const valueEnum = computed(() => props.item.valueEnum || {})

const innerValue = computed({
  get() {
    const raw = props.modelValue?.[props.item.prop]
    return valueEnum.value[raw] ?? raw
  },

  set(text) {
    // “文案 → key”
    const enumKey = Object.keys(valueEnum.value).find(
      (k) => valueEnum.value[k] === text
    )

    // 2. 如果找到 key 就用 key，否则用用户输入
    const finalValue = enumKey ?? text

    emit('update:modelValue', {
      ...props.modelValue,
      [props.item.prop]: finalValue,
    })
  },
})

const filed = computed(() => props.item)

const isDisabled = computed(
  () => Boolean(props.item.disabled) || (!props.item.disabled && !props.isEdit)
)

const openDialog = (item: FormModel) => {
  console.log('openDialog', item)
  // console.log('openDialog', item)
  // if (isDisabled.value) return
  // openBusinessDialog(item.fieldType)
}

watch(
  () => props.item,
  (res) => {
    formItem.value = res
  },
  { immediate: true }
)

watch(
  () => props.modelValue,
  (res) => {
    // console.log('form-item: modelValue', res)
    formModel.value = res
  },
  { immediate: true }
)
</script>
