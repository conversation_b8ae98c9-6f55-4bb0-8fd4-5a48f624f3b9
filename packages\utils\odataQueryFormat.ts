import { keys } from 'lodash-unified'

interface QueryProps {
  filter: Record<string, any>
  pagination?: { current: number; pageSize: number }
  orderBy?: Record<string, any>
  expand?: Record<string, any>
}
export function odataQueryFormat({
  filter,
  pagination,
  orderBy,
  expand,
}: QueryProps) {
  const getOrderBy = (orderBy?: Record<string, any>) => {
    if (keys(orderBy).length != 0) {
      return [`${orderBy?.prop} ${orderBy?.order}`]
    } else {
      return undefined
    }
  }
  const getFilter = (filters: Record<string, any>) => {
    const odataFilter = Object.entries(filters)
      .map(([key, opObj]) => {
        return Object.entries(opObj)
          .map(([op, value]) => {
            switch (op) {
              case 'contains':
                return `contains(${key}, '${value}')`
              case 'startswith':
                return `startswith(${key}, '${value}')`
              case 'endswith':
                return `endswith(${key}, '${value}')`
              case 'eq':
                return `${key} eq ${value}`
              case 'ge':
                return `${key} ge ${value}`
              case 'le':
                return `${key} le ${value}`
              default:
                return ''
            }
          })
          .join(' and ')
      })
      .join(' and ')
    return odataFilter
  }
  console.log(filter, pagination, orderBy, 'query')
  const query: any = {}
  if (filter) {
    query['filter'] = getFilter(filter)
  }
  if (pagination) {
    const { current, pageSize } = pagination
    query['skip'] = (current - 1) * pageSize
    query['top'] = pageSize
    query['count'] = true
  }
  if (orderBy) {
    query['orderBy'] = getOrderBy(orderBy)
  }
  if (expand) {
    query['expand'] = expand
  }
  return query
}
