/**
 * 简单测试脚本，验证构建的包是否可以正常导入和使用
 */

// 测试 CommonJS 导入
const {
  createClient,
  createODataClient,
  ODataClient,
} = require('./dist/index.js')

console.log('✅ CommonJS 导入成功')
console.log('createClient:', typeof createClient)
console.log('createODataClient:', typeof createODataClient)
console.log('ODataClient:', typeof ODataClient)

// 测试创建客户端
try {
  const client = createClient('https://api.example.com', '/odata/v4')
  console.log('✅ 客户端创建成功')
  console.log('客户端类型:', client.constructor.name)
} catch (error) {
  console.error('❌ 客户端创建失败:', error.message)
}

console.log('\n🎉 OData 包构建和基本功能测试通过！')
