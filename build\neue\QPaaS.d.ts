import { QStringPath, QDateTimeOffsetPath, QEnumPath, QEntityPath, QueryObject, QId, QStringParam, QAction, QNumberPath, QBooleanPath, QEntityCollectionPath, QCollectionPath, QStringCollection, QFunction, QBooleanParam, QEnumParam } from "@odata2ts/odata-query-objects";
import type { Plt0ApplicationObjectId, Plt0ApplicationObject_GenerateCodeParams, GetRelationTreeByNcidParams, DeployParams, BatchGetFileSignatureUrlParams } from "./PaaSModel";
import { LifecycleState, IdiConvertState, RelationClassify, RelationConstrictEnum, RelationVersionUpgradeActionEnum, RelationStatus, RelationSourceTypeEnum, MrvStrategyType, OnDelete, PartType, RelCadFileFileLinkTypeEnum, RelCadFileFileUsageTypeEnum, MappingConfigToolEnum, MappingConfigPartTypeEnum, MappingConfigDirectionEnum } from "./PaaSModel";
export declare class QPlt0ApplicationObjectBaseType extends QueryObject {
    readonly ncid: QStringPath<string>;
    readonly createdAt: QDateTimeOffsetPath<string>;
    readonly modifiedAt: QDateTimeOffsetPath<string>;
    readonly schemaVersion: QStringPath<string>;
    readonly lifecycleState: QEnumPath<typeof LifecycleState>;
    readonly lifecycleNote: QStringPath<string>;
    readonly createdBy: QEntityPath<QPlt0ApplicationObject>;
    readonly modifiedBy: QEntityPath<QPlt0ApplicationObject>;
}
export declare class QPlt0ApplicationObject extends QPlt0ApplicationObjectBaseType {
    protected readonly __subtypeMapping: {
        "BuiltInModel.Plt0MasterObject": string;
        "BuiltInModel.Plt0RevisionObject": string;
        "BuiltInModel.Plt0VersionObject": string;
        "BuiltInModel.Plt0LifecycleStatus": string;
        "BuiltInModel.Plt0LifecycleStatusStrategy": string;
        "BuiltInModel.Plt0MrvObject": string;
        "BuiltInModel.Plt0File": string;
        "BuiltInModel.Plt0IdiFile": string;
        "BuiltInModel.Plt0RootRelationType": string;
        "BuiltInModel.Plt0RelationQuery": string;
        "BuiltInModel.Plt0RootRelationTypeConfig": string;
        "BuiltInModel.Plt0RelationFromOrToType": string;
        "BuiltInModel.Plt0Schema": string;
        "BuiltInModel.Plt0BaseType": string;
        "BuiltInModel.Plt0EntityType": string;
        "BuiltInModel.Plt0EntityTypeUnionUniq": string;
        "BuiltInModel.Plt0EntityTypeUnionUniqProperty": string;
        "BuiltInModel.Plt0EntityTypePropertyOverride": string;
        "BuiltInModel.Plt0ComplexType": string;
        "BuiltInModel.Plt0EnumType": string;
        "BuiltInModel.Plt0TypeDefinition": string;
        "BuiltInModel.Plt0StructuralProperty": string;
        "BuiltInModel.Plt0NavigationProperty": string;
        "BuiltInModel.Plt0Action": string;
        "BuiltInModel.Plt0Function": string;
        "BuiltInModel.Plt0EntityContainer": string;
        "BuiltInModel.Plt0EntitySet": string;
        "BuiltInModel.Plt0Singleton": string;
        "BuiltInModel.Plt0NavigationPropertyBinding": string;
        "BuiltInModel.Plt0ActionImport": string;
        "BuiltInModel.Plt0FunctionImport": string;
        "BuiltInModel.Plt0BusinessObjectRevisionNumberRule": string;
        "BuiltInModel.Plt0BusinessObjectRevisionNumberCode": string;
        "neue.CadFile": string;
        "neue.CadPart": string;
        "neue.NeueCadPart": string;
        "neue.NeueCadAsm": string;
        "neue.TwoDimensionDrawing": string;
        "neue.NeueTwoDimensionDrawing": string;
        "neue.Plt0File": string;
        "neue.Plt0IdiFile": string;
        "neue.RelCadBom": string;
        "neue.RelCadPartTwoDimensionDrawing": string;
        "neue.RelCadFileFile": string;
        "neue.MappingConfig": string;
        "neue.test_product": string;
        "neue.test_product001": string;
        "neue.Test_ProductModel": string;
        "neue.Test_ProductModel111": string;
        "neue.Test_ProductModel222": string;
        "neue.Test_ProductModel333": string;
        "neue.Test_ProductModel444": string;
    };
    get QPlt0RevisionObject_revisionCode(): QStringPath<string>;
    get QPlt0RevisionObject_revisionOrder(): QNumberPath<number>;
    get QPlt0RevisionObject_isLatestRevision(): QBooleanPath<boolean>;
    get QPlt0RevisionObject_isLocked(): QBooleanPath<boolean>;
    get QPlt0RevisionObject_lockedAt(): QDateTimeOffsetPath<string>;
    get QPlt0RevisionObject_preRevision(): QEntityPath<QPlt0RevisionObject>;
    get QPlt0RevisionObject_master(): QEntityPath<QPlt0MasterObject>;
    get QPlt0RevisionObject_lockedBy(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0VersionObject_versionNumber(): QNumberPath<number>;
    get QPlt0VersionObject_isLatestVersion(): QBooleanPath<boolean>;
    get QPlt0VersionObject_revision(): QEntityPath<QPlt0RevisionObject>;
    get QPlt0LifecycleStatus_statusCode(): QStringPath<string>;
    get QPlt0LifecycleStatus_name(): QStringPath<string>;
    get QPlt0LifecycleStatus_description(): QStringPath<string>;
    get QPlt0LifecycleStatusStrategy_strategyCode(): QStringPath<string>;
    get QPlt0LifecycleStatusStrategy_name(): QStringPath<string>;
    get QPlt0LifecycleStatusStrategy_description(): QStringPath<string>;
    get QPlt0MrvObject_code(): QStringPath<string>;
    get QPlt0MrvObject_name(): QStringPath<string>;
    get QPlt0MrvObject_description(): QStringPath<string>;
    get QPlt0MrvObject_version(): QEntityPath<QPlt0VersionObject>;
    get QPlt0MrvObject_owner(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0MrvObject_lifecycleStatus(): QEntityPath<QPlt0LifecycleStatus>;
    get QPlt0File_name(): QStringPath<string>;
    get QPlt0File_description(): QStringPath<string>;
    get QPlt0File_softwareRev(): QStringPath<string>;
    get QPlt0File_storageItem(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0IdiFile_idiLightModelId(): QStringPath<string>;
    get QPlt0IdiFile_convertState(): QEnumPath<typeof IdiConvertState>;
    get QPlt0IdiFile_triggerTime(): QDateTimeOffsetPath<string>;
    get QPlt0RootRelationType_from(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0RootRelationType_to(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0RelationQuery_toRelations(): QEntityCollectionPath<QPlt0RootRelationType>;
    get QPlt0RootRelationTypeConfig_relationClassify(): QEnumPath<typeof RelationClassify>;
    get QPlt0RootRelationTypeConfig_sourceRelationConstrict(): QEnumPath<typeof RelationConstrictEnum>;
    get QPlt0RootRelationTypeConfig_targetRelationConstrict(): QEnumPath<typeof RelationConstrictEnum>;
    get QPlt0RootRelationTypeConfig_sourceRelationVersionUpgradeAction(): QEnumPath<typeof RelationVersionUpgradeActionEnum>;
    get QPlt0RootRelationTypeConfig_targetRelationVersionUpgradeAction(): QEnumPath<typeof RelationVersionUpgradeActionEnum>;
    get QPlt0RootRelationTypeConfig_relationStatus(): QEnumPath<typeof RelationStatus>;
    get QPlt0RootRelationTypeConfig_description(): QStringPath<string>;
    get QPlt0RootRelationTypeConfig_applyRelationType(): QEntityPath<QPlt0RootRelationType>;
    get QPlt0RelationFromOrToType_sideType(): QEnumPath<typeof RelationSourceTypeEnum>;
    get QPlt0RelationFromOrToType_relationType(): QEntityPath<QPlt0EntityType>;
    get QPlt0RelationFromOrToType_constrictType(): QEntityPath<QPlt0EntityType>;
    get QPlt0Schema_namespace(): QStringPath<string>;
    get QPlt0Schema_alias(): QStringPath<string>;
    get QPlt0BaseType_name(): QStringPath<string>;
    get QPlt0BaseType_displayName(): QStringPath<string>;
    get QPlt0BaseType_description(): QStringPath<string>;
    get QPlt0BaseType_isAbstract(): QBooleanPath<boolean>;
    get QPlt0BaseType_isOpen(): QBooleanPath<boolean>;
    get QPlt0BaseType_schema(): QEntityPath<QPlt0Schema>;
    get QPlt0EntityType_entityTypeCode(): QStringPath<string>;
    get QPlt0EntityType_isVirtual(): QBooleanPath<boolean>;
    get QPlt0EntityType_hasStream(): QBooleanPath<boolean>;
    get QPlt0EntityType_mrvStrategy(): QEnumPath<typeof MrvStrategyType>;
    get QPlt0EntityType_isReadOnly(): QBooleanPath<boolean>;
    get QPlt0EntityType_icon(): QEntityPath<QPlt0File>;
    get QPlt0EntityType_baseType(): QEntityPath<QPlt0EntityType>;
    get QPlt0EntityType_lifecycleStatusStrategy(): QEntityPath<QPlt0LifecycleStatusStrategy>;
    get QPlt0EntityType_revisionNumberRule(): QEntityPath<QPlt0BusinessObjectRevisionNumberRule>;
    get QPlt0EntityTypeUnionUniq_name(): QStringPath<string>;
    get QPlt0EntityTypeUnionUniq_description(): QStringPath<string>;
    get QPlt0EntityTypeUnionUniq_ownerType(): QEntityPath<QPlt0EntityType>;
    get QPlt0EntityTypeUnionUniqProperty_ownerUnionUniq(): QEntityPath<QPlt0EntityTypeUnionUniq>;
    get QPlt0EntityTypeUnionUniqProperty_structuralProperty(): QEntityPath<QPlt0StructuralProperty>;
    get QPlt0EntityTypeUnionUniqProperty_navigationProperty(): QEntityPath<QPlt0NavigationProperty>;
    get QPlt0EntityTypePropertyOverride_isAutoGenerateCode(): QBooleanPath<boolean>;
    get QPlt0EntityTypePropertyOverride_ownerType(): QEntityPath<QPlt0EntityType>;
    get QPlt0EntityTypePropertyOverride_structuralProperty(): QEntityPath<QPlt0StructuralProperty>;
    get QPlt0ComplexType_complexTypeCode(): QStringPath<string>;
    get QPlt0ComplexType_baseType(): QEntityPath<QPlt0ComplexType>;
    get QPlt0EnumType_enumTypeCode(): QStringPath<string>;
    get QPlt0EnumType_name(): QStringPath<string>;
    get QPlt0EnumType_description(): QStringPath<string>;
    get QPlt0EnumType_underlyingType(): QStringPath<string>;
    get QPlt0EnumType_isFlags(): QBooleanPath<boolean>;
    get QPlt0EnumType_members(): QCollectionPath<QStringCollection<any>>;
    get QPlt0EnumType_schema(): QEntityPath<QPlt0Schema>;
    get QPlt0TypeDefinition_typeDefinitionCode(): QStringPath<string>;
    get QPlt0TypeDefinition_name(): QStringPath<string>;
    get QPlt0TypeDefinition_description(): QStringPath<string>;
    get QPlt0TypeDefinition_underlyingType(): QStringPath<string>;
    get QPlt0TypeDefinition_maxLength(): QNumberPath<number>;
    get QPlt0TypeDefinition_isUnicode(): QBooleanPath<boolean>;
    get QPlt0TypeDefinition_srid(): QStringPath<string>;
    get QPlt0TypeDefinition_precision(): QNumberPath<number>;
    get QPlt0TypeDefinition_scale(): QNumberPath<number>;
    get QPlt0TypeDefinition_schema(): QEntityPath<QPlt0Schema>;
    get QPlt0StructuralProperty_name(): QStringPath<string>;
    get QPlt0StructuralProperty_displayName(): QStringPath<string>;
    get QPlt0StructuralProperty_description(): QStringPath<string>;
    get QPlt0StructuralProperty_type(): QStringPath<string>;
    get QPlt0StructuralProperty_isCollection(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_isNullable(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_maxLength(): QNumberPath<number>;
    get QPlt0StructuralProperty_isUnicode(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_srid(): QStringPath<string>;
    get QPlt0StructuralProperty_precision(): QNumberPath<number>;
    get QPlt0StructuralProperty_scale(): QNumberPath<number>;
    get QPlt0StructuralProperty_defaultValue(): QStringPath<string>;
    get QPlt0StructuralProperty_isAutoGenerateCode(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_isReadOnly(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_isUniq(): QBooleanPath<boolean>;
    get QPlt0StructuralProperty_ownerType(): QEntityPath<QPlt0BaseType>;
    get QPlt0NavigationProperty_name(): QStringPath<string>;
    get QPlt0NavigationProperty_displayName(): QStringPath<string>;
    get QPlt0NavigationProperty_description(): QStringPath<string>;
    get QPlt0NavigationProperty_isCollection(): QBooleanPath<boolean>;
    get QPlt0NavigationProperty_isNullable(): QBooleanPath<boolean>;
    get QPlt0NavigationProperty_isContainsTarget(): QBooleanPath<boolean>;
    get QPlt0NavigationProperty_referentialConstraint(): QStringPath<string>;
    get QPlt0NavigationProperty_onDelete(): QEnumPath<typeof OnDelete>;
    get QPlt0NavigationProperty_isReadOnly(): QBooleanPath<boolean>;
    get QPlt0NavigationProperty_isUniq(): QBooleanPath<boolean>;
    get QPlt0NavigationProperty_ownerType(): QEntityPath<QPlt0BaseType>;
    get QPlt0NavigationProperty_type(): QEntityPath<QPlt0EntityType>;
    get QPlt0NavigationProperty_partner(): QEntityPath<QPlt0NavigationProperty>;
    get QPlt0Action_name(): QStringPath<string>;
    get QPlt0Action_description(): QStringPath<string>;
    get QPlt0Action_schema(): QStringPath<string>;
    get QPlt0Action_isBound(): QBooleanPath<boolean>;
    get QPlt0Action_parameter(): QStringPath<string>;
    get QPlt0Action_returnType(): QStringPath<string>;
    get QPlt0Action_entitySetPath(): QStringPath<string>;
    get QPlt0Function_name(): QStringPath<string>;
    get QPlt0Function_description(): QStringPath<string>;
    get QPlt0Function_schema(): QStringPath<string>;
    get QPlt0Function_isBound(): QBooleanPath<boolean>;
    get QPlt0Function_isComposable(): QBooleanPath<boolean>;
    get QPlt0Function_parameter(): QStringPath<string>;
    get QPlt0Function_returnType(): QStringPath<string>;
    get QPlt0Function_entitySetPath(): QStringPath<string>;
    get QPlt0EntityContainer_name(): QStringPath<string>;
    get QPlt0EntityContainer_extend(): QStringPath<string>;
    get QPlt0EntityContainer_schema(): QEntityPath<QPlt0Schema>;
    get QPlt0EntitySet_name(): QStringPath<string>;
    get QPlt0EntitySet_description(): QStringPath<string>;
    get QPlt0EntitySet_isIncludeInServiceDocument(): QBooleanPath<boolean>;
    get QPlt0EntitySet_entityContainer(): QEntityPath<QPlt0EntityContainer>;
    get QPlt0EntitySet_entityType(): QEntityPath<QPlt0EntityType>;
    get QPlt0Singleton_name(): QStringPath<string>;
    get QPlt0Singleton_description(): QStringPath<string>;
    get QPlt0Singleton_entityContainer(): QEntityPath<QPlt0EntityContainer>;
    get QPlt0Singleton_entityType(): QEntityPath<QPlt0EntityType>;
    get QPlt0NavigationPropertyBinding_entitySet(): QEntityPath<QPlt0EntitySet>;
    get QPlt0NavigationPropertyBinding_path(): QEntityPath<QPlt0NavigationProperty>;
    get QPlt0NavigationPropertyBinding_target(): QEntityPath<QPlt0EntitySet>;
    get QPlt0ActionImport_name(): QStringPath<string>;
    get QPlt0ActionImport_description(): QStringPath<string>;
    get QPlt0ActionImport_entityContainer(): QEntityPath<QPlt0EntityContainer>;
    get QPlt0ActionImport_action(): QEntityPath<QPlt0Action>;
    get QPlt0ActionImport_entitySet(): QEntityPath<QPlt0EntitySet>;
    get QPlt0FunctionImport_name(): QStringPath<string>;
    get QPlt0FunctionImport_description(): QStringPath<string>;
    get QPlt0FunctionImport_isIncludeInServiceDocument(): QBooleanPath<boolean>;
    get QPlt0FunctionImport_entityContainer(): QEntityPath<QPlt0EntityContainer>;
    get QPlt0FunctionImport_function(): QEntityPath<QPlt0Function>;
    get QPlt0FunctionImport_entitySet(): QEntityPath<QPlt0EntitySet>;
    get QPlt0BusinessObjectRevisionNumberRule_ruleCode(): QStringPath<string>;
    get QPlt0BusinessObjectRevisionNumberRule_name(): QStringPath<string>;
    get QPlt0BusinessObjectRevisionNumberRule_isActive(): QBooleanPath<boolean>;
    get QPlt0BusinessObjectRevisionNumberRule_isEmployed(): QBooleanPath<boolean>;
    get QPlt0BusinessObjectRevisionNumberRule_isLocked(): QBooleanPath<boolean>;
    get QPlt0BusinessObjectRevisionNumberRule_description(): QStringPath<string>;
    get QPlt0BusinessObjectRevisionNumberCode_revisionOrder(): QNumberPath<number>;
    get QPlt0BusinessObjectRevisionNumberCode_revisionCode(): QStringPath<string>;
    get QPlt0BusinessObjectRevisionNumberCode_ruleCodeRef(): QEntityPath<QPlt0BusinessObjectRevisionNumberRule>;
    get QCadFile_submitDescription(): QStringPath<string>;
    get QCadPart_partType(): QEnumPath<typeof PartType>;
    get QCadPart_volume(): QNumberPath<number>;
    get QCadPart_mass(): QNumberPath<number>;
    get QCadPart_material(): QStringPath<string>;
    get QCadPart_openSurfaceArea(): QNumberPath<number>;
    get QCadPart_solidSurfaceArea(): QNumberPath<number>;
    get QCadPart_gravityCenter(): QStringPath<string>;
    get QCadPart_thumbnail(): QEntityPath<QPlt0File>;
    get QTwoDimensionDrawing_thumbnail(): QEntityPath<QPlt0File>;
    get QPlt0File2_description(): QStringPath<string>;
    get QPlt0File2_softwareRev(): QStringPath<string>;
    get QPlt0File2_name(): QStringPath<string>;
    get QPlt0File2_storageItem(): QEntityPath<QPlt0ApplicationObject>;
    get QPlt0IdiFile2_convertState(): QEnumPath<typeof IdiConvertState>;
    get QPlt0IdiFile2_triggerTime(): QDateTimeOffsetPath<string>;
    get QPlt0IdiFile2_idiLightModelId(): QStringPath<string>;
    get QRelCadBom_transformationMatrix(): QStringPath<string>;
    get QRelCadBom_configuration(): QStringPath<string>;
    get QRelCadBom_quantity(): QNumberPath<number>;
    get QRelCadBom_suppressed(): QBooleanPath<boolean>;
    get QRelCadBom_bomExcluded(): QBooleanPath<boolean>;
    get QRelCadBom_instanceName(): QStringPath<string>;
    get QRelCadFileFile_linkType(): QEnumPath<typeof RelCadFileFileLinkTypeEnum>;
    get QRelCadFileFile_usageType(): QEnumPath<typeof RelCadFileFileUsageTypeEnum>;
    get QMappingConfig_tool(): QEnumPath<typeof MappingConfigToolEnum>;
    get QMappingConfig_caxProperty(): QStringPath<string>;
    get QMappingConfig_name(): QStringPath<string>;
    get QMappingConfig_drawingSheetArea(): QStringPath<string>;
    get QMappingConfig_partType(): QEnumPath<typeof MappingConfigPartTypeEnum>;
    get QMappingConfig_cdpProperty(): QStringPath<string>;
    get QMappingConfig_onCax(): QBooleanPath<boolean>;
    get QMappingConfig_direction(): QEnumPath<typeof MappingConfigDirectionEnum>;
    get QTest_ProductModel111_Productname(): QStringPath<string>;
    get QTest_ProductModel444_name(): QStringPath<string>;
    get QTest_ProductModel444_prince(): QNumberPath<number>;
    private __asQPlt0MasterObject;
    private __asQPlt0RevisionObject;
    private __asQPlt0VersionObject;
    private __asQPlt0LifecycleStatus;
    private __asQPlt0LifecycleStatusStrategy;
    private __asQPlt0MrvObject;
    private __asQPlt0File;
    private __asQPlt0IdiFile;
    private __asQPlt0RootRelationType;
    private __asQPlt0RelationQuery;
    private __asQPlt0RootRelationTypeConfig;
    private __asQPlt0RelationFromOrToType;
    private __asQPlt0Schema;
    private __asQPlt0BaseType;
    private __asQPlt0EntityType;
    private __asQPlt0EntityTypeUnionUniq;
    private __asQPlt0EntityTypeUnionUniqProperty;
    private __asQPlt0EntityTypePropertyOverride;
    private __asQPlt0ComplexType;
    private __asQPlt0EnumType;
    private __asQPlt0TypeDefinition;
    private __asQPlt0StructuralProperty;
    private __asQPlt0NavigationProperty;
    private __asQPlt0Action;
    private __asQPlt0Function;
    private __asQPlt0EntityContainer;
    private __asQPlt0EntitySet;
    private __asQPlt0Singleton;
    private __asQPlt0NavigationPropertyBinding;
    private __asQPlt0ActionImport;
    private __asQPlt0FunctionImport;
    private __asQPlt0BusinessObjectRevisionNumberRule;
    private __asQPlt0BusinessObjectRevisionNumberCode;
    private __asQCadFile;
    private __asQCadPart;
    private __asQNeueCadPart;
    private __asQNeueCadAsm;
    private __asQTwoDimensionDrawing;
    private __asQNeueTwoDimensionDrawing;
    private __asQPlt0File2;
    private __asQPlt0IdiFile2;
    private __asQRelCadBom;
    private __asQRelCadPartTwoDimensionDrawing;
    private __asQRelCadFileFile;
    private __asQMappingConfig;
    private __asQtest_product;
    private __asQtest_product001;
    private __asQTest_ProductModel;
    private __asQTest_ProductModel111;
    private __asQTest_ProductModel222;
    private __asQTest_ProductModel333;
    private __asQTest_ProductModel444;
}
export declare const qPlt0ApplicationObject: QPlt0ApplicationObject;
export declare class QPlt0ApplicationObjectId extends QId<Plt0ApplicationObjectId> {
    private readonly params;
    getParams(): QStringParam<string>[];
}
export declare class Plt0ApplicationObject_QGenerateCode extends QAction<Plt0ApplicationObject_GenerateCodeParams> {
    private readonly params;
    constructor();
    getParams(): QStringParam<string>[];
}
export declare class QPlt0MasterObject extends QPlt0ApplicationObjectBaseType {
}
export declare const qPlt0MasterObject: QPlt0MasterObject;
export declare class QPlt0RevisionObject extends QPlt0ApplicationObjectBaseType {
    readonly revisionCode: QStringPath<string>;
    readonly revisionOrder: QNumberPath<number>;
    readonly isLatestRevision: QBooleanPath<boolean>;
    readonly isLocked: QBooleanPath<boolean>;
    readonly lockedAt: QDateTimeOffsetPath<string>;
    readonly preRevision: QEntityPath<QPlt0RevisionObject>;
    readonly master: QEntityPath<QPlt0MasterObject>;
    readonly lockedBy: QEntityPath<QPlt0ApplicationObject>;
}
export declare const qPlt0RevisionObject: QPlt0RevisionObject;
export declare class QPlt0VersionObject extends QPlt0ApplicationObjectBaseType {
    readonly versionNumber: QNumberPath<number>;
    readonly isLatestVersion: QBooleanPath<boolean>;
    readonly revision: QEntityPath<QPlt0RevisionObject>;
}
export declare const qPlt0VersionObject: QPlt0VersionObject;
export declare class QPlt0LifecycleStatus extends QPlt0ApplicationObjectBaseType {
    readonly statusCode: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
}
export declare const qPlt0LifecycleStatus: QPlt0LifecycleStatus;
export declare class QPlt0LifecycleStatusStrategy extends QPlt0ApplicationObjectBaseType {
    readonly strategyCode: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
}
export declare const qPlt0LifecycleStatusStrategy: QPlt0LifecycleStatusStrategy;
export declare class QPlt0MrvObjectBaseType extends QPlt0ApplicationObjectBaseType {
    readonly code: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly version: QEntityPath<QPlt0VersionObject>;
    readonly owner: QEntityPath<QPlt0ApplicationObject>;
    readonly lifecycleStatus: QEntityPath<QPlt0LifecycleStatus>;
}
export declare class QPlt0MrvObject extends QPlt0MrvObjectBaseType {
    protected readonly __subtypeMapping: {
        "neue.CadFile": string;
        "neue.CadPart": string;
        "neue.NeueCadPart": string;
        "neue.NeueCadAsm": string;
        "neue.TwoDimensionDrawing": string;
        "neue.NeueTwoDimensionDrawing": string;
    };
    get QCadFile_submitDescription(): QStringPath<string>;
    get QCadPart_partType(): QEnumPath<typeof PartType>;
    get QCadPart_volume(): QNumberPath<number>;
    get QCadPart_mass(): QNumberPath<number>;
    get QCadPart_material(): QStringPath<string>;
    get QCadPart_openSurfaceArea(): QNumberPath<number>;
    get QCadPart_solidSurfaceArea(): QNumberPath<number>;
    get QCadPart_gravityCenter(): QStringPath<string>;
    get QCadPart_thumbnail(): QEntityPath<QPlt0File>;
    get QTwoDimensionDrawing_thumbnail(): QEntityPath<QPlt0File>;
    private __asQCadFile;
    private __asQCadPart;
    private __asQNeueCadPart;
    private __asQNeueCadAsm;
    private __asQTwoDimensionDrawing;
    private __asQNeueTwoDimensionDrawing;
}
export declare const qPlt0MrvObject: QPlt0MrvObject;
export declare class Plt0MrvObject_QNextRevision extends QAction {
    private readonly params;
    constructor();
    getParams(): [];
}
export declare class Plt0MrvObject_QLock extends QAction {
    private readonly params;
    constructor();
    getParams(): [];
}
export declare class Plt0MrvObject_QUnlock extends QAction {
    private readonly params;
    constructor();
    getParams(): [];
}
export declare class QPlt0FileBaseType extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly softwareRev: QStringPath<string>;
    readonly storageItem: QEntityPath<QPlt0ApplicationObject>;
}
export declare class QPlt0File extends QPlt0FileBaseType {
    protected readonly __subtypeMapping: {
        "BuiltInModel.Plt0IdiFile": string;
    };
    get QPlt0IdiFile_idiLightModelId(): QStringPath<string>;
    get QPlt0IdiFile_convertState(): QEnumPath<typeof IdiConvertState>;
    get QPlt0IdiFile_triggerTime(): QDateTimeOffsetPath<string>;
    private __asQPlt0IdiFile;
}
export declare const qPlt0File: QPlt0File;
export declare class QPlt0IdiFile extends QPlt0FileBaseType {
    readonly idiLightModelId: QStringPath<string>;
    readonly convertState: QEnumPath<typeof IdiConvertState>;
    readonly triggerTime: QDateTimeOffsetPath<string>;
}
export declare const qPlt0IdiFile: QPlt0IdiFile;
export declare class QPlt0RootRelationTypeBaseType extends QPlt0ApplicationObjectBaseType {
    readonly from: QEntityPath<QPlt0ApplicationObject>;
    readonly to: QEntityPath<QPlt0ApplicationObject>;
}
export declare class QPlt0RootRelationType extends QPlt0RootRelationTypeBaseType {
    protected readonly __subtypeMapping: {
        "neue.RelCadBom": string;
        "neue.RelCadPartTwoDimensionDrawing": string;
        "neue.RelCadFileFile": string;
    };
    get QRelCadBom_transformationMatrix(): QStringPath<string>;
    get QRelCadBom_configuration(): QStringPath<string>;
    get QRelCadBom_quantity(): QNumberPath<number>;
    get QRelCadBom_suppressed(): QBooleanPath<boolean>;
    get QRelCadBom_bomExcluded(): QBooleanPath<boolean>;
    get QRelCadBom_instanceName(): QStringPath<string>;
    get QRelCadFileFile_linkType(): QEnumPath<typeof RelCadFileFileLinkTypeEnum>;
    get QRelCadFileFile_usageType(): QEnumPath<typeof RelCadFileFileUsageTypeEnum>;
    private __asQRelCadBom;
    private __asQRelCadPartTwoDimensionDrawing;
    private __asQRelCadFileFile;
}
export declare const qPlt0RootRelationType: QPlt0RootRelationType;
export declare class QPlt0RelationQuery extends QPlt0ApplicationObjectBaseType {
    readonly toRelations: QEntityCollectionPath<QPlt0RootRelationType>;
}
export declare const qPlt0RelationQuery: QPlt0RelationQuery;
export declare class QPlt0RootRelationTypeConfig extends QPlt0ApplicationObjectBaseType {
    readonly relationClassify: QEnumPath<typeof RelationClassify>;
    readonly sourceRelationConstrict: QEnumPath<typeof RelationConstrictEnum>;
    readonly targetRelationConstrict: QEnumPath<typeof RelationConstrictEnum>;
    readonly sourceRelationVersionUpgradeAction: QEnumPath<typeof RelationVersionUpgradeActionEnum>;
    readonly targetRelationVersionUpgradeAction: QEnumPath<typeof RelationVersionUpgradeActionEnum>;
    readonly relationStatus: QEnumPath<typeof RelationStatus>;
    readonly description: QStringPath<string>;
    readonly applyRelationType: QEntityPath<QPlt0RootRelationType>;
}
export declare const qPlt0RootRelationTypeConfig: QPlt0RootRelationTypeConfig;
export declare class QPlt0RelationFromOrToType extends QPlt0ApplicationObjectBaseType {
    readonly sideType: QEnumPath<typeof RelationSourceTypeEnum>;
    readonly relationType: QEntityPath<QPlt0EntityType>;
    readonly constrictType: QEntityPath<QPlt0EntityType>;
}
export declare const qPlt0RelationFromOrToType: QPlt0RelationFromOrToType;
export declare class QPlt0Schema extends QPlt0ApplicationObjectBaseType {
    readonly namespace: QStringPath<string>;
    readonly alias: QStringPath<string>;
}
export declare const qPlt0Schema: QPlt0Schema;
export declare class QPlt0BaseTypeBaseType extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly displayName: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly isAbstract: QBooleanPath<boolean>;
    readonly isOpen: QBooleanPath<boolean>;
    readonly schema: QEntityPath<QPlt0Schema>;
}
export declare class QPlt0BaseType extends QPlt0BaseTypeBaseType {
    protected readonly __subtypeMapping: {
        "BuiltInModel.Plt0EntityType": string;
        "BuiltInModel.Plt0ComplexType": string;
    };
    get QPlt0EntityType_entityTypeCode(): QStringPath<string>;
    get QPlt0EntityType_isVirtual(): QBooleanPath<boolean>;
    get QPlt0EntityType_hasStream(): QBooleanPath<boolean>;
    get QPlt0EntityType_mrvStrategy(): QEnumPath<typeof MrvStrategyType>;
    get QPlt0EntityType_isReadOnly(): QBooleanPath<boolean>;
    get QPlt0EntityType_icon(): QEntityPath<QPlt0File>;
    get QPlt0EntityType_baseType(): QEntityPath<QPlt0EntityType>;
    get QPlt0EntityType_lifecycleStatusStrategy(): QEntityPath<QPlt0LifecycleStatusStrategy>;
    get QPlt0EntityType_revisionNumberRule(): QEntityPath<QPlt0BusinessObjectRevisionNumberRule>;
    get QPlt0ComplexType_complexTypeCode(): QStringPath<string>;
    get QPlt0ComplexType_baseType(): QEntityPath<QPlt0ComplexType>;
    private __asQPlt0EntityType;
    private __asQPlt0ComplexType;
}
export declare const qPlt0BaseType: QPlt0BaseType;
export declare class QPlt0EntityType extends QPlt0BaseTypeBaseType {
    readonly entityTypeCode: QStringPath<string>;
    readonly isVirtual: QBooleanPath<boolean>;
    readonly hasStream: QBooleanPath<boolean>;
    readonly mrvStrategy: QEnumPath<typeof MrvStrategyType>;
    readonly isReadOnly: QBooleanPath<boolean>;
    readonly icon: QEntityPath<QPlt0File>;
    readonly baseType: QEntityPath<QPlt0EntityType>;
    readonly lifecycleStatusStrategy: QEntityPath<QPlt0LifecycleStatusStrategy>;
    readonly revisionNumberRule: QEntityPath<QPlt0BusinessObjectRevisionNumberRule>;
}
export declare const qPlt0EntityType: QPlt0EntityType;
export declare class QPlt0EntityTypeUnionUniq extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly ownerType: QEntityPath<QPlt0EntityType>;
}
export declare const qPlt0EntityTypeUnionUniq: QPlt0EntityTypeUnionUniq;
export declare class QPlt0EntityTypeUnionUniqProperty extends QPlt0ApplicationObjectBaseType {
    readonly ownerUnionUniq: QEntityPath<QPlt0EntityTypeUnionUniq>;
    readonly structuralProperty: QEntityPath<QPlt0StructuralProperty>;
    readonly navigationProperty: QEntityPath<QPlt0NavigationProperty>;
}
export declare const qPlt0EntityTypeUnionUniqProperty: QPlt0EntityTypeUnionUniqProperty;
export declare class QPlt0EntityTypePropertyOverride extends QPlt0ApplicationObjectBaseType {
    readonly isAutoGenerateCode: QBooleanPath<boolean>;
    readonly ownerType: QEntityPath<QPlt0EntityType>;
    readonly structuralProperty: QEntityPath<QPlt0StructuralProperty>;
}
export declare const qPlt0EntityTypePropertyOverride: QPlt0EntityTypePropertyOverride;
export declare class QPlt0ComplexType extends QPlt0BaseTypeBaseType {
    readonly complexTypeCode: QStringPath<string>;
    readonly baseType: QEntityPath<QPlt0ComplexType>;
}
export declare const qPlt0ComplexType: QPlt0ComplexType;
export declare class QPlt0EnumType extends QPlt0ApplicationObjectBaseType {
    readonly enumTypeCode: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly underlyingType: QStringPath<string>;
    readonly isFlags: QBooleanPath<boolean>;
    readonly members: QCollectionPath<QStringCollection<any>>;
    readonly schema: QEntityPath<QPlt0Schema>;
}
export declare const qPlt0EnumType: QPlt0EnumType;
export declare class QPlt0TypeDefinition extends QPlt0ApplicationObjectBaseType {
    readonly typeDefinitionCode: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly underlyingType: QStringPath<string>;
    readonly maxLength: QNumberPath<number>;
    readonly isUnicode: QBooleanPath<boolean>;
    readonly srid: QStringPath<string>;
    readonly precision: QNumberPath<number>;
    readonly scale: QNumberPath<number>;
    readonly schema: QEntityPath<QPlt0Schema>;
}
export declare const qPlt0TypeDefinition: QPlt0TypeDefinition;
export declare class QPlt0StructuralProperty extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly displayName: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly type: QStringPath<string>;
    readonly isCollection: QBooleanPath<boolean>;
    readonly isNullable: QBooleanPath<boolean>;
    readonly maxLength: QNumberPath<number>;
    readonly isUnicode: QBooleanPath<boolean>;
    readonly srid: QStringPath<string>;
    readonly precision: QNumberPath<number>;
    readonly scale: QNumberPath<number>;
    readonly defaultValue: QStringPath<string>;
    readonly isAutoGenerateCode: QBooleanPath<boolean>;
    readonly isReadOnly: QBooleanPath<boolean>;
    readonly isUniq: QBooleanPath<boolean>;
    readonly ownerType: QEntityPath<QPlt0BaseType>;
}
export declare const qPlt0StructuralProperty: QPlt0StructuralProperty;
export declare class QPlt0NavigationProperty extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly displayName: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly isCollection: QBooleanPath<boolean>;
    readonly isNullable: QBooleanPath<boolean>;
    readonly isContainsTarget: QBooleanPath<boolean>;
    readonly referentialConstraint: QStringPath<string>;
    readonly onDelete: QEnumPath<typeof OnDelete>;
    readonly isReadOnly: QBooleanPath<boolean>;
    readonly isUniq: QBooleanPath<boolean>;
    readonly ownerType: QEntityPath<QPlt0BaseType>;
    readonly type: QEntityPath<QPlt0EntityType>;
    readonly partner: QEntityPath<QPlt0NavigationProperty>;
}
export declare const qPlt0NavigationProperty: QPlt0NavigationProperty;
export declare class QPlt0Action extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly schema: QStringPath<string>;
    readonly isBound: QBooleanPath<boolean>;
    readonly parameter: QStringPath<string>;
    readonly returnType: QStringPath<string>;
    readonly entitySetPath: QStringPath<string>;
}
export declare const qPlt0Action: QPlt0Action;
export declare class QPlt0Function extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly schema: QStringPath<string>;
    readonly isBound: QBooleanPath<boolean>;
    readonly isComposable: QBooleanPath<boolean>;
    readonly parameter: QStringPath<string>;
    readonly returnType: QStringPath<string>;
    readonly entitySetPath: QStringPath<string>;
}
export declare const qPlt0Function: QPlt0Function;
export declare class QPlt0EntityContainer extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly extend: QStringPath<string>;
    readonly schema: QEntityPath<QPlt0Schema>;
}
export declare const qPlt0EntityContainer: QPlt0EntityContainer;
export declare class QPlt0EntitySet extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly isIncludeInServiceDocument: QBooleanPath<boolean>;
    readonly entityContainer: QEntityPath<QPlt0EntityContainer>;
    readonly entityType: QEntityPath<QPlt0EntityType>;
}
export declare const qPlt0EntitySet: QPlt0EntitySet;
export declare class QPlt0Singleton extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly entityContainer: QEntityPath<QPlt0EntityContainer>;
    readonly entityType: QEntityPath<QPlt0EntityType>;
}
export declare const qPlt0Singleton: QPlt0Singleton;
export declare class QPlt0NavigationPropertyBinding extends QPlt0ApplicationObjectBaseType {
    readonly entitySet: QEntityPath<QPlt0EntitySet>;
    readonly path: QEntityPath<QPlt0NavigationProperty>;
    readonly target: QEntityPath<QPlt0EntitySet>;
}
export declare const qPlt0NavigationPropertyBinding: QPlt0NavigationPropertyBinding;
export declare class QPlt0ActionImport extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly entityContainer: QEntityPath<QPlt0EntityContainer>;
    readonly action: QEntityPath<QPlt0Action>;
    readonly entitySet: QEntityPath<QPlt0EntitySet>;
}
export declare const qPlt0ActionImport: QPlt0ActionImport;
export declare class QPlt0FunctionImport extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly description: QStringPath<string>;
    readonly isIncludeInServiceDocument: QBooleanPath<boolean>;
    readonly entityContainer: QEntityPath<QPlt0EntityContainer>;
    readonly function: QEntityPath<QPlt0Function>;
    readonly entitySet: QEntityPath<QPlt0EntitySet>;
}
export declare const qPlt0FunctionImport: QPlt0FunctionImport;
export declare class QPlt0BusinessObjectRevisionNumberRule extends QPlt0ApplicationObjectBaseType {
    readonly ruleCode: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly isActive: QBooleanPath<boolean>;
    readonly isEmployed: QBooleanPath<boolean>;
    readonly isLocked: QBooleanPath<boolean>;
    readonly description: QStringPath<string>;
}
export declare const qPlt0BusinessObjectRevisionNumberRule: QPlt0BusinessObjectRevisionNumberRule;
export declare class QPlt0BusinessObjectRevisionNumberCode extends QPlt0ApplicationObjectBaseType {
    readonly revisionOrder: QNumberPath<number>;
    readonly revisionCode: QStringPath<string>;
    readonly ruleCodeRef: QEntityPath<QPlt0BusinessObjectRevisionNumberRule>;
}
export declare const qPlt0BusinessObjectRevisionNumberCode: QPlt0BusinessObjectRevisionNumberCode;
export declare class QCadFileBaseType extends QPlt0MrvObjectBaseType {
    readonly submitDescription: QStringPath<string>;
}
export declare class QCadFile extends QCadFileBaseType {
    protected readonly __subtypeMapping: {
        "neue.CadPart": string;
        "neue.NeueCadPart": string;
        "neue.NeueCadAsm": string;
        "neue.TwoDimensionDrawing": string;
        "neue.NeueTwoDimensionDrawing": string;
    };
    get QCadPart_partType(): QEnumPath<typeof PartType>;
    get QCadPart_volume(): QNumberPath<number>;
    get QCadPart_mass(): QNumberPath<number>;
    get QCadPart_material(): QStringPath<string>;
    get QCadPart_openSurfaceArea(): QNumberPath<number>;
    get QCadPart_solidSurfaceArea(): QNumberPath<number>;
    get QCadPart_gravityCenter(): QStringPath<string>;
    get QCadPart_thumbnail(): QEntityPath<QPlt0File>;
    get QTwoDimensionDrawing_thumbnail(): QEntityPath<QPlt0File>;
    private __asQCadPart;
    private __asQNeueCadPart;
    private __asQNeueCadAsm;
    private __asQTwoDimensionDrawing;
    private __asQNeueTwoDimensionDrawing;
}
export declare const qCadFile: QCadFile;
export declare class QCadPartBaseType extends QCadFileBaseType {
    readonly partType: QEnumPath<typeof PartType>;
    readonly volume: QNumberPath<number>;
    readonly mass: QNumberPath<number>;
    readonly material: QStringPath<string>;
    readonly openSurfaceArea: QNumberPath<number>;
    readonly solidSurfaceArea: QNumberPath<number>;
    readonly gravityCenter: QStringPath<string>;
    readonly thumbnail: QEntityPath<QPlt0File>;
}
export declare class QCadPart extends QCadPartBaseType {
    protected readonly __subtypeMapping: {
        "neue.NeueCadPart": string;
        "neue.NeueCadAsm": string;
    };
    private __asQNeueCadPart;
    private __asQNeueCadAsm;
}
export declare const qCadPart: QCadPart;
export declare class QNeueCadPart extends QCadPartBaseType {
}
export declare const qNeueCadPart: QNeueCadPart;
export declare class QNeueCadAsm extends QCadPartBaseType {
}
export declare const qNeueCadAsm: QNeueCadAsm;
export declare class QTwoDimensionDrawingBaseType extends QCadFileBaseType {
    readonly thumbnail: QEntityPath<QPlt0File>;
}
export declare class QTwoDimensionDrawing extends QTwoDimensionDrawingBaseType {
    protected readonly __subtypeMapping: {
        "neue.NeueTwoDimensionDrawing": string;
    };
    private __asQNeueTwoDimensionDrawing;
}
export declare const qTwoDimensionDrawing: QTwoDimensionDrawing;
export declare class QNeueTwoDimensionDrawing extends QTwoDimensionDrawingBaseType {
}
export declare const qNeueTwoDimensionDrawing: QNeueTwoDimensionDrawing;
export declare class QPlt0File2BaseType extends QPlt0ApplicationObjectBaseType {
    readonly description: QStringPath<string>;
    readonly softwareRev: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly storageItem: QEntityPath<QPlt0ApplicationObject>;
}
export declare class QPlt0File2 extends QPlt0File2BaseType {
    protected readonly __subtypeMapping: {
        "neue.Plt0IdiFile": string;
    };
    get QPlt0IdiFile2_convertState(): QEnumPath<typeof IdiConvertState>;
    get QPlt0IdiFile2_triggerTime(): QDateTimeOffsetPath<string>;
    get QPlt0IdiFile2_idiLightModelId(): QStringPath<string>;
    private __asQPlt0IdiFile2;
}
export declare const qPlt0File2: QPlt0File2;
export declare class QPlt0IdiFile2 extends QPlt0File2BaseType {
    readonly convertState: QEnumPath<typeof IdiConvertState>;
    readonly triggerTime: QDateTimeOffsetPath<string>;
    readonly idiLightModelId: QStringPath<string>;
}
export declare const qPlt0IdiFile2: QPlt0IdiFile2;
export declare class QRelCadBom extends QPlt0RootRelationTypeBaseType {
    readonly transformationMatrix: QStringPath<string>;
    readonly configuration: QStringPath<string>;
    readonly quantity: QNumberPath<number>;
    readonly suppressed: QBooleanPath<boolean>;
    readonly bomExcluded: QBooleanPath<boolean>;
    readonly instanceName: QStringPath<string>;
}
export declare const qRelCadBom: QRelCadBom;
export declare class QRelCadPartTwoDimensionDrawing extends QPlt0RootRelationTypeBaseType {
}
export declare const qRelCadPartTwoDimensionDrawing: QRelCadPartTwoDimensionDrawing;
export declare class QRelCadFileFile extends QPlt0RootRelationTypeBaseType {
    readonly linkType: QEnumPath<typeof RelCadFileFileLinkTypeEnum>;
    readonly usageType: QEnumPath<typeof RelCadFileFileUsageTypeEnum>;
}
export declare const qRelCadFileFile: QRelCadFileFile;
export declare class QMappingConfig extends QPlt0ApplicationObjectBaseType {
    readonly tool: QEnumPath<typeof MappingConfigToolEnum>;
    readonly caxProperty: QStringPath<string>;
    readonly name: QStringPath<string>;
    readonly drawingSheetArea: QStringPath<string>;
    readonly partType: QEnumPath<typeof MappingConfigPartTypeEnum>;
    readonly cdpProperty: QStringPath<string>;
    readonly onCax: QBooleanPath<boolean>;
    readonly direction: QEnumPath<typeof MappingConfigDirectionEnum>;
}
export declare const qMappingConfig: QMappingConfig;
export declare class Qtest_product extends QPlt0ApplicationObjectBaseType {
}
export declare const qtest_product: Qtest_product;
export declare class Qtest_product001 extends QPlt0ApplicationObjectBaseType {
}
export declare const qtest_product001: Qtest_product001;
export declare class QTest_ProductModel extends QPlt0ApplicationObjectBaseType {
}
export declare const qTest_ProductModel: QTest_ProductModel;
export declare class QTest_ProductModel111 extends QPlt0ApplicationObjectBaseType {
    readonly Productname: QStringPath<string>;
}
export declare const qTest_ProductModel111: QTest_ProductModel111;
export declare class QTest_ProductModel222 extends QPlt0ApplicationObjectBaseType {
}
export declare const qTest_ProductModel222: QTest_ProductModel222;
export declare class QTest_ProductModel333 extends QPlt0ApplicationObjectBaseType {
}
export declare const qTest_ProductModel333: QTest_ProductModel333;
export declare class QTest_ProductModel444 extends QPlt0ApplicationObjectBaseType {
    readonly name: QStringPath<string>;
    readonly prince: QNumberPath<number>;
}
export declare const qTest_ProductModel444: QTest_ProductModel444;
export declare class QFileSignatureUrl extends QueryObject {
    readonly storageItemId: QStringPath<string>;
    readonly fileId: QStringPath<string>;
    readonly signatureUrl: QStringPath<string>;
}
export declare const qFileSignatureUrl: QFileSignatureUrl;
export declare class QGetRelationTreeByNcid extends QFunction<GetRelationTreeByNcidParams> {
    private readonly params;
    constructor();
    getParams(): (QStringParam<string> | QBooleanParam<boolean>)[];
}
export declare class QDeploy extends QAction<DeployParams> {
    private readonly params;
    constructor();
    getParams(): QStringParam<string>[];
}
export declare class QBatchGetFileSignatureUrl extends QAction<BatchGetFileSignatureUrlParams> {
    private readonly params;
    constructor();
    getParams(): (QStringParam<string> | QEnumParam<import("@odata2ts/odata-query-objects").StringEnumLike>)[];
}
