import {
  ElAlert,
  ElAside,
  ElAutocomplete,
  ElAvatar,
  ElBadge,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElCard,
  ElCascader,
  ElCheckbox,
  ElCheckboxGroup,
  ElCol,
  ElCollapse,
  ElCollapseItem,
  ElColorPicker,
  ElContainer,
  ElDatePicker,
  ElDialog,
  ElDivider,
  ElDrawer,
  ElEmpty,
  ElFooter,
  ElForm,
  ElFormItem,
  ElHeader,
  ElImage,
  ElInput,
  ElLoading,
  ElMain,
  ElMessage,
  ElNotification,
  ElOption,
  ElPagination,
  ElPopconfirm,
  ElPopover,
  ElProgress,
  ElRadio,
  ElRadioGroup,
  ElRate,
  ElResult,
  ElRow,
  ElSelect,
  ElSkeleton,
  ElSkeletonItem,
  ElSlider,
  ElSpace,
  ElStep,
  ElSteps,
  ElSwitch,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElTabs,
  ElTag,
  ElTimePicker,
  ElTooltip,
  ElTransfer,
  ElTree,
  ElUpload,
} from 'element-plus'

import {
  WidgetBasicDetail,
  WidgetBasicForm,
  WidgetBomPanel,
  WidgetBomTable,
  WidgetBusinessType,
  WidgetDialogForm,
  WidgetPanelForm,
} from '@neue-plus/widgets'
import WidgetTable from '@neue-plus/widgets/table'
import NeAvatar from '../../../avatar'
import NeTag from '../../../tag'
import NeImage from '../../../image'
import NeSwitch from '../../../switch'
import NeSlider from '../../../slider'
import NeButton from '../../../button'
import NeDescriptions from '../../../descriptions'
import NeCard from '../../../card'
import NeCheckboxGroup from '../../../checkbox-group'
import NeConfigProvider from '../../../config-provider'
import NeDrawer from '../../../drawer'
import NeProForm from '../../../pro-form'
import NeProTab from '../../../pro-tab'
import NeProTable from '../../../pro-table'
import NeRadioGroup from '../../../radio-group'
import NeTable from '../../../table'
import NeWidgetContainer from '../../../widget-container'
import NeWidgetWrapper from '../../../widget-wrapper'
import NeSelect from '../../../select'
import NeTreeTable from '../../../tree-table'

// 组件映射表
const elComponentMap: Record<string, any> = {
  // Element Plus 组件
  'el-button': ElButton,
  'el-input': ElInput,
  'el-select': ElSelect,
  'el-option': ElOption,
  'el-form': ElForm,
  'el-form-item': ElFormItem,
  'el-card': ElCard,
  'el-table': ElTable,
  'el-tree-table': ElTable,
  'el-table-column': ElTableColumn,
  'el-pagination': ElPagination,
  'el-dialog': ElDialog,
  'el-drawer': ElDrawer,
  'el-tabs': ElTabs,
  'el-tab-pane': ElTabPane,
  'el-collapse': ElCollapse,
  'el-collapse-item': ElCollapseItem,
  'el-breadcrumb': ElBreadcrumb,
  'el-breadcrumb-item': ElBreadcrumbItem,
  'el-steps': ElSteps,
  'el-step': ElStep,
  'el-radio': ElRadio,
  'el-radio-group': ElRadioGroup,
  'el-checkbox': ElCheckbox,
  'el-checkbox-group': ElCheckboxGroup,
  'el-switch': ElSwitch,
  'el-slider': ElSlider,
  'el-time-picker': ElTimePicker,
  'el-date-picker': ElDatePicker,
  'el-upload': ElUpload,
  'el-rate': ElRate,
  'el-color-picker': ElColorPicker,
  'el-transfer': ElTransfer,
  'el-tree': ElTree,
  'el-cascader': ElCascader,
  'el-autocomplete': ElAutocomplete,
  'el-tooltip': ElTooltip,
  'el-popover': ElPopover,
  'el-popconfirm': ElPopconfirm,
  'el-alert': ElAlert,
  'el-loading': ElLoading,
  'el-message': ElMessage,
  'el-notification': ElNotification,
  'el-progress': ElProgress,
  'el-skeleton': ElSkeleton,
  'el-skeleton-item': ElSkeletonItem,
  'el-empty': ElEmpty,
  'el-result': ElResult,
  'el-image': ElImage,
  'el-avatar': ElAvatar,
  'el-badge': ElBadge,
  'el-tag': ElTag,
  'el-divider': ElDivider,
  'el-space': ElSpace,
  'el-row': ElRow,
  'el-col': ElCol,
  'el-container': ElContainer,
  'el-header': ElHeader,
  'el-aside': ElAside,
  'el-main': ElMain,
  'el-footer': ElFooter,
}
// 组件映射表
const neComponentMap: Record<string, any> = {
  // Neue Plus 组件
  'ne-card': NeCard,
  'ne-button': NeButton,
  'ne-select': NeSelect,
  'ne-table': NeTable,
  'ne-pro-table': NeProTable,
  'ne-tree-table': NeTreeTable,
  'ne-pro-form': NeProForm,
  'ne-pro-tab': NeProTab,
  'ne-radio-group': NeRadioGroup,
  'ne-checkbox': NeCheckboxGroup,
  'ne-drawer': NeDrawer,
  'ne-config-provider': NeConfigProvider,
  'ne-widget-container': NeWidgetContainer,
  'ne-widget-wrapper': NeWidgetWrapper,
  'ne-tag': NeTag,
  'ne-switch': NeSwitch,
  'ne-slider': NeSlider,
  'ne-descriptions': NeDescriptions,
  'ne-avatar': NeAvatar,
  'ne-image': NeImage,
}
const widgetsComponentMap: Record<string, any> = {
  'wg-bom-table': WidgetBomTable,
  'wg-bom-panel': WidgetBomPanel,
  'wg-table': WidgetTable,
  'wg-basic-form': WidgetBasicForm,
  'wg-business-type': WidgetBusinessType,
  'wg-dialog-form': WidgetDialogForm,
  'wg-panel-form': WidgetPanelForm,
  'wg-basic-detail': WidgetBasicDetail,
}
/**
 * 根据组件名称获取组件
 * @param componentName 组件名称
 * @returns 组件构造函数或组件名称
 */
export function getComponentByName(componentName: string) {
  try {
    // 首先尝试从组件映射表中获取
    if (neComponentMap[`ne-${componentName}`]) {
      return neComponentMap[`ne-${componentName}`]
    } else if (elComponentMap[`el-${componentName}`]) {
      return elComponentMap[`el-${componentName}`]
    } else if (widgetsComponentMap[`${componentName}`]) {
      return widgetsComponentMap[`${componentName}`]
    }
    return componentName
  } catch {
    console.warn(`Component "${componentName}" not found, using as tag name`)
    return componentName
  }
}

/**
 * 获取所有已注册的组件名称
 * @returns 组件名称数组
 */
export function getRegisteredComponentNames(): string[] {
  return Object.keys({ ...elComponentMap, ...neComponentMap })
}

/**
 * 检查组件是否已注册
 * @param name 组件名称
 * @returns 是否已注册
 */
export function isComponentRegistered(name: string): boolean {
  return name in { ...elComponentMap, ...neComponentMap }
}
