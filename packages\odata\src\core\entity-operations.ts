/**
 * OData 实体操作类
 */

import { OlingoWrapper } from './olingo-wrapper'
import { QueryBuilder } from './query-builder'
import type {
  EntityOperations as IEntityOperations,
  QueryBuilder as IQueryBuilder,
  ODataQueryOptions,
  ODataResponse,
} from '../types'

/**
 * 实体操作实现类
 */
export class EntityOperations<T = any> implements IEntityOperations<T> {
  private entitySetName: string
  private client: OlingoWrapper
  private keyProperties: string[]

  constructor(
    entitySetName: string,
    client: OlingoWrapper,
    keyProperties: string[] = ['id']
  ) {
    this.entitySetName = entitySetName
    this.client = client
    this.keyProperties = keyProperties
  }

  /**
   * 获取所有实体
   */
  async getAll(options?: ODataQueryOptions): Promise<ODataResponse<T>> {
    const response = await this.client.get<T>(this.entitySetName, options)
    return response.data
  }

  /**
   * 根据 ID 获取单个实体
   */
  async getById(id: string | number, options?: ODataQueryOptions): Promise<T> {
    const entityPath = this.buildEntityPath(id)
    const response = await this.client.get<T>(entityPath, options)

    // 如果返回的是 OData 响应格式，提取实际数据
    const data = response.data as any
    if (data && typeof data === 'object' && 'value' in data) {
      return data.value[0] || data
    }

    return data
  }

  /**
   * 创建查询构建器
   */
  query(): IQueryBuilder<T> {
    return new QueryBuilder<T>(this.entitySetName, this.client)
  }

  /**
   * 创建新实体
   */
  async create(entity: Partial<T>): Promise<T> {
    const response = await this.client.post<T>(this.entitySetName, entity)
    return response.data
  }

  /**
   * 更新实体（完整更新）
   */
  async update(id: string | number, entity: Partial<T>): Promise<T> {
    const entityPath = this.buildEntityPath(id)
    const response = await this.client.put<T>(entityPath, entity)
    return response.data
  }

  /**
   * 部分更新实体
   */
  async patch(id: string | number, entity: Partial<T>): Promise<T> {
    const entityPath = this.buildEntityPath(id)
    const response = await this.client.patch<T>(entityPath, entity)
    return response.data
  }

  /**
   * 删除实体
   */
  async delete(id: string | number): Promise<void> {
    const entityPath = this.buildEntityPath(id)
    await this.client.delete(entityPath)
  }

  /**
   * 批量创建实体
   */
  async createBatch(entities: Partial<T>[]): Promise<T[]> {
    const results: T[] = []

    // 简单实现：逐个创建（实际应该使用 OData 批量操作）
    for (const entity of entities) {
      const created = await this.create(entity)
      results.push(created)
    }

    return results
  }

  /**
   * 批量更新实体
   */
  async updateBatch(
    updates: Array<{ id: string | number; entity: Partial<T> }>
  ): Promise<T[]> {
    const results: T[] = []

    // 简单实现：逐个更新（实际应该使用 OData 批量操作）
    for (const update of updates) {
      const updated = await this.update(update.id, update.entity)
      results.push(updated)
    }

    return results
  }

  /**
   * 批量删除实体
   */
  async deleteBatch(ids: Array<string | number>): Promise<void> {
    // 简单实现：逐个删除（实际应该使用 OData 批量操作）
    for (const id of ids) {
      await this.delete(id)
    }
  }

  /**
   * 构建实体路径
   */
  private buildEntityPath(id: string | number): string {
    // 处理复合键的情况
    if (this.keyProperties.length === 1) {
      const keyValue = typeof id === 'string' ? `'${id}'` : id
      return `${this.entitySetName}(${keyValue})`
    } else {
      // 复合键需要特殊处理
      // 这里假设 id 是一个包含所有键值的对象
      if (typeof id === 'object') {
        const keyPairs = this.keyProperties.map((key) => {
          const value = (id as any)[key]
          const formattedValue =
            typeof value === 'string' ? `'${value}'` : value
          return `${key}=${formattedValue}`
        })
        return `${this.entitySetName}(${keyPairs.join(',')})`
      } else {
        throw new TypeError('复合键实体需要提供包含所有键值的对象')
      }
    }
  }

  /**
   * 获取实体集名称
   */
  getEntitySetName(): string {
    return this.entitySetName
  }

  /**
   * 获取键属性
   */
  getKeyProperties(): string[] {
    return [...this.keyProperties]
  }

  /**
   * 设置键属性
   */
  setKeyProperties(keyProperties: string[]): void {
    this.keyProperties = [...keyProperties]
  }
}
