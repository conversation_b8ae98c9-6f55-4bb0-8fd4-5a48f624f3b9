/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { IdiFiles } from './IdiFiles';

/**
 * Request builder class for operations supported on the {@link IdiFiles} entity.
 */
export class IdiFilesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<IdiFiles<T>, T> {
  /**
   * Returns a request builder for querying all `IdiFiles` entities.
   * @returns A request builder for creating requests to retrieve all `IdiFiles` entities.
   */
  getAll(): GetAllRequestBuilder<IdiFiles<T>, T> {
    return new GetAllRequestBuilder<IdiFiles<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `IdiFiles` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `IdiFiles`.
   */
  create(entity: IdiFiles<T>): CreateRequestBuilder<IdiFiles<T>, T> {
    return new CreateRequestBuilder<IdiFiles<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `IdiFiles` entity based on its keys.
   * @param ncid Key property. See {@link IdiFiles.ncid}.
   * @returns A request builder for creating requests to retrieve one `IdiFiles` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<IdiFiles<T>, T> {
    return new GetByKeyRequestBuilder<IdiFiles<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `IdiFiles`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `IdiFiles`.
   */
  update(entity: IdiFiles<T>): UpdateRequestBuilder<IdiFiles<T>, T> {
    return new UpdateRequestBuilder<IdiFiles<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `IdiFiles`.
   * @param ncid Key property. See {@link IdiFiles.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `IdiFiles`.
   */
  delete(ncid: string): DeleteRequestBuilder<IdiFiles<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `IdiFiles`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `IdiFiles` by taking the entity as a parameter.
   */
  delete(entity: IdiFiles<T>): DeleteRequestBuilder<IdiFiles<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<IdiFiles<T>, T> {
    return new DeleteRequestBuilder<IdiFiles<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof IdiFiles ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
