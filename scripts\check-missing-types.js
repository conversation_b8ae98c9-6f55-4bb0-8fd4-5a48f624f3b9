const fs = require('fs')
const path = require('path')

const vdmDir = path.join(__dirname, '../play/src/vdm/neue')

// 获取所有实体文件（排除 API 和 RequestBuilder 文件）
const entityFiles = fs
  .readdirSync(vdmDir)
  .filter((file) => file.endsWith('.ts'))
  .filter((file) => !file.includes('Api') && !file.includes('RequestBuilder'))
  .filter(
    (file) => !['index.ts', 'service.ts', 'BatchRequest.ts'].includes(file)
  )
  .filter(
    (file) =>
      !file.endsWith('Enum.ts') &&
      !file.endsWith('State.ts') &&
      !file.endsWith('Type.ts')
  )

console.log('Checking entity files for missing type interfaces...\n')

const missingTypes = []

entityFiles.forEach((file) => {
  const filePath = path.join(vdmDir, file)
  const content = fs.readFileSync(filePath, 'utf-8')

  // 检查是否有 implements XxxType<T> 但没有 export interface XxxType
  const implementsMatch = content.match(/implements\s+(\w+Type)<T>/)
  const hasExportInterface = content.includes('export interface')

  if (implementsMatch && !hasExportInterface) {
    const typeName = implementsMatch[1]
    missingTypes.push({
      file,
      typeName,
      className: file.replace('.ts', ''),
    })
    console.log(`❌ ${file}: Missing export interface ${typeName}`)
  } else if (implementsMatch && hasExportInterface) {
    console.log(`✅ ${file}: Has type interface`)
  } else {
    console.log(`ℹ️  ${file}: No type interface needed`)
  }
})

if (missingTypes.length > 0) {
  console.log(
    `\n🔧 Found ${missingTypes.length} files with missing type interfaces:`
  )
  missingTypes.forEach(({ file, typeName }) => {
    console.log(`   - ${file}: ${typeName}`)
  })
} else {
  console.log('\n✅ All files have proper type interfaces!')
}
