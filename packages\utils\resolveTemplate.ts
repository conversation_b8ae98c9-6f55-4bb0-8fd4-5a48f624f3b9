export function resolveTemplateAdvanced(
  obj: any,
  data: Record<string, any>
): any {
  if (typeof obj === 'string') {
    const match = obj.match(/^{{\s*([^{}]+)\s*}}$/)
    if (match) {
      try {
        const fn = new Function(
          'data',
          `
          with(data) {
            return ${match[1]}
          }
        `
        )
        return fn(data) // 保留原始类型
      } catch (e) {
        console.warn('Template evaluation error:', match[1], e)
        return undefined
      }
    }
    return obj // 普通字符串不变
  } else if (Array.isArray(obj)) {
    return obj.map((item) => resolveTemplateAdvanced(item, data))
  } else if (typeof obj === 'object' && obj !== null) {
    const res: Record<string, any> = {}
    for (const key in obj) {
      res[key] = resolveTemplateAdvanced(obj[key], data)
    }
    return res
  }
  return obj
}
