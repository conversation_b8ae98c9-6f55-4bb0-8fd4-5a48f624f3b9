import type { ODataModelResponseV4, ODataValueResponseV4, ODataCollectionResponseV4 } from "@odata2ts/odata-core";
import type { StringCollection } from "@odata2ts/odata-query-objects";
import { QStringCollection } from "@odata2ts/odata-query-objects";
import type { ODataHttpClient, ODataHttpClientConfig, HttpResponseModel } from "@odata2ts/http-client-api";
import { ODataService, EntityTypeServiceV4, ODataServiceOptionsInternal, EntitySetServiceV4, CollectionServiceV4 } from "@odata2ts/odata-service";
import type { Plt0ApplicationObjectId, Plt0RelationQuery, GetRelationTreeByNcidParams, DeployParams, FileSignatureUrl, BatchGetFileSignatureUrlParams, Plt0ApplicationObject, EditablePlt0ApplicationObject, Plt0ApplicationObject_GenerateCodeParams, Plt0MasterObject, EditablePlt0MasterObject, Plt0RevisionObject, EditablePlt0RevisionObject, Plt0VersionObject, EditablePlt0VersionObject, Plt0LifecycleStatus, EditablePlt0LifecycleStatus, Plt0LifecycleStatusStrategy, EditablePlt0LifecycleStatusStrategy, Plt0MrvObject, EditablePlt0MrvObject, Plt0File, EditablePlt0File, Plt0IdiFile, EditablePlt0IdiFile, Plt0RootRelationType, EditablePlt0RootRelationType, EditablePlt0RelationQuery, Plt0RootRelationTypeConfig, EditablePlt0RootRelationTypeConfig, Plt0RelationFromOrToType, EditablePlt0RelationFromOrToType, Plt0Schema, EditablePlt0Schema, Plt0BaseType, EditablePlt0BaseType, Plt0EntityType, EditablePlt0EntityType, Plt0EntityTypeUnionUniq, EditablePlt0EntityTypeUnionUniq, Plt0EntityTypeUnionUniqProperty, EditablePlt0EntityTypeUnionUniqProperty, Plt0EntityTypePropertyOverride, EditablePlt0EntityTypePropertyOverride, Plt0ComplexType, EditablePlt0ComplexType, Plt0EnumType, EditablePlt0EnumType, Plt0TypeDefinition, EditablePlt0TypeDefinition, Plt0StructuralProperty, EditablePlt0StructuralProperty, Plt0NavigationProperty, EditablePlt0NavigationProperty, Plt0Action, EditablePlt0Action, Plt0Function, EditablePlt0Function, Plt0EntityContainer, EditablePlt0EntityContainer, Plt0EntitySet, EditablePlt0EntitySet, Plt0Singleton, EditablePlt0Singleton, Plt0NavigationPropertyBinding, EditablePlt0NavigationPropertyBinding, Plt0ActionImport, EditablePlt0ActionImport, Plt0FunctionImport, EditablePlt0FunctionImport, Plt0BusinessObjectRevisionNumberRule, EditablePlt0BusinessObjectRevisionNumberRule, Plt0BusinessObjectRevisionNumberCode, EditablePlt0BusinessObjectRevisionNumberCode, CadFile, EditableCadFile, CadPart, EditableCadPart, NeueCadPart, EditableNeueCadPart, NeueCadAsm, EditableNeueCadAsm, TwoDimensionDrawing, EditableTwoDimensionDrawing, NeueTwoDimensionDrawing, EditableNeueTwoDimensionDrawing, Plt0File2, EditablePlt0File2, Plt0IdiFile2, EditablePlt0IdiFile2, RelCadBom, EditableRelCadBom, RelCadPartTwoDimensionDrawing, EditableRelCadPartTwoDimensionDrawing, RelCadFileFile, EditableRelCadFileFile, MappingConfig, EditableMappingConfig, test_product, Editabletest_product, test_product001, Editabletest_product001, Test_ProductModel, EditableTest_ProductModel, Test_ProductModel111, EditableTest_ProductModel111, Test_ProductModel222, EditableTest_ProductModel222, Test_ProductModel333, EditableTest_ProductModel333, Test_ProductModel444, EditableTest_ProductModel444 } from "./PaaSModel";
import type { QPlt0ApplicationObject, QPlt0MasterObject, QPlt0RevisionObject, QPlt0VersionObject, QPlt0LifecycleStatus, QPlt0LifecycleStatusStrategy, QPlt0MrvObject, QPlt0File, QPlt0IdiFile, QPlt0RootRelationType, QPlt0RelationQuery, QPlt0RootRelationTypeConfig, QPlt0RelationFromOrToType, QPlt0Schema, QPlt0BaseType, QPlt0EntityType, QPlt0EntityTypeUnionUniq, QPlt0EntityTypeUnionUniqProperty, QPlt0EntityTypePropertyOverride, QPlt0ComplexType, QPlt0EnumType, QPlt0TypeDefinition, QPlt0StructuralProperty, QPlt0NavigationProperty, QPlt0Action, QPlt0Function, QPlt0EntityContainer, QPlt0EntitySet, QPlt0Singleton, QPlt0NavigationPropertyBinding, QPlt0ActionImport, QPlt0FunctionImport, QPlt0BusinessObjectRevisionNumberRule, QPlt0BusinessObjectRevisionNumberCode, QCadFile, QCadPart, QNeueCadPart, QNeueCadAsm, QTwoDimensionDrawing, QNeueTwoDimensionDrawing, QPlt0File2, QPlt0IdiFile2, QRelCadBom, QRelCadPartTwoDimensionDrawing, QRelCadFileFile, QMappingConfig, Qtest_product, Qtest_product001, QTest_ProductModel, QTest_ProductModel111, QTest_ProductModel222, QTest_ProductModel333, QTest_ProductModel444 } from "./QPaaS";
export declare class PaaSService<in out ClientType extends ODataHttpClient> extends ODataService<ClientType> {
    private _QGetRelationTreeByNcid?;
    private _QDeploy?;
    private _QBatchGetFileSignatureUrl?;
    Schemas(): Plt0SchemaCollectionService<ClientType>;
    Schemas(id: Plt0ApplicationObjectId): Plt0SchemaService<ClientType>;
    EntityTypes(): Plt0EntityTypeCollectionService<ClientType>;
    EntityTypes(id: Plt0ApplicationObjectId): Plt0EntityTypeService<ClientType>;
    ComplexTypes(): Plt0ComplexTypeCollectionService<ClientType>;
    ComplexTypes(id: Plt0ApplicationObjectId): Plt0ComplexTypeService<ClientType>;
    EnumTypes(): Plt0EnumTypeCollectionService<ClientType>;
    EnumTypes(id: Plt0ApplicationObjectId): Plt0EnumTypeService<ClientType>;
    TypeDefinitions(): Plt0TypeDefinitionCollectionService<ClientType>;
    TypeDefinitions(id: Plt0ApplicationObjectId): Plt0TypeDefinitionService<ClientType>;
    StructuralProperties(): Plt0StructuralPropertyCollectionService<ClientType>;
    StructuralProperties(id: Plt0ApplicationObjectId): Plt0StructuralPropertyService<ClientType>;
    NavigationProperties(): Plt0NavigationPropertyCollectionService<ClientType>;
    NavigationProperties(id: Plt0ApplicationObjectId): Plt0NavigationPropertyService<ClientType>;
    Actions(): Plt0ActionCollectionService<ClientType>;
    Actions(id: Plt0ApplicationObjectId): Plt0ActionService<ClientType>;
    Functions(): Plt0FunctionCollectionService<ClientType>;
    Functions(id: Plt0ApplicationObjectId): Plt0FunctionService<ClientType>;
    EntityContainers(): Plt0EntityContainerCollectionService<ClientType>;
    EntityContainers(id: Plt0ApplicationObjectId): Plt0EntityContainerService<ClientType>;
    EntitySets(): Plt0EntitySetCollectionService<ClientType>;
    EntitySets(id: Plt0ApplicationObjectId): Plt0EntitySetService<ClientType>;
    Singletons(): Plt0SingletonCollectionService<ClientType>;
    Singletons(id: Plt0ApplicationObjectId): Plt0SingletonService<ClientType>;
    NavigationPropertyBindings(): Plt0NavigationPropertyBindingCollectionService<ClientType>;
    NavigationPropertyBindings(id: Plt0ApplicationObjectId): Plt0NavigationPropertyBindingService<ClientType>;
    ActionImports(): Plt0ActionImportCollectionService<ClientType>;
    ActionImports(id: Plt0ApplicationObjectId): Plt0ActionImportService<ClientType>;
    FunctionImports(): Plt0FunctionImportCollectionService<ClientType>;
    FunctionImports(id: Plt0ApplicationObjectId): Plt0FunctionImportService<ClientType>;
    BusinessObjectRevisionNumberRules(): Plt0BusinessObjectRevisionNumberRuleCollectionService<ClientType>;
    BusinessObjectRevisionNumberRules(id: Plt0ApplicationObjectId): Plt0BusinessObjectRevisionNumberRuleService<ClientType>;
    BusinessObjectRevisionNumberCodes(): Plt0BusinessObjectRevisionNumberCodeCollectionService<ClientType>;
    BusinessObjectRevisionNumberCodes(id: Plt0ApplicationObjectId): Plt0BusinessObjectRevisionNumberCodeService<ClientType>;
    LifecycleStatuses(): Plt0LifecycleStatusCollectionService<ClientType>;
    LifecycleStatuses(id: Plt0ApplicationObjectId): Plt0LifecycleStatusService<ClientType>;
    LifecycleStatusStrategies(): Plt0LifecycleStatusStrategyCollectionService<ClientType>;
    LifecycleStatusStrategies(id: Plt0ApplicationObjectId): Plt0LifecycleStatusStrategyService<ClientType>;
    FromOrToTypes(): Plt0RelationFromOrToTypeCollectionService<ClientType>;
    FromOrToTypes(id: Plt0ApplicationObjectId): Plt0RelationFromOrToTypeService<ClientType>;
    RootRelationTypes(): Plt0RootRelationTypeCollectionService<ClientType>;
    RootRelationTypes(id: Plt0ApplicationObjectId): Plt0RootRelationTypeService<ClientType>;
    EntityTypeUnionUniqs(): Plt0EntityTypeUnionUniqCollectionService<ClientType>;
    EntityTypeUnionUniqs(id: Plt0ApplicationObjectId): Plt0EntityTypeUnionUniqService<ClientType>;
    EntityTypeUnionUniqProperties(): Plt0EntityTypeUnionUniqPropertyCollectionService<ClientType>;
    EntityTypeUnionUniqProperties(id: Plt0ApplicationObjectId): Plt0EntityTypeUnionUniqPropertyService<ClientType>;
    Files(): Plt0FileCollectionService<ClientType>;
    Files(id: Plt0ApplicationObjectId): Plt0FileService<ClientType>;
    IdiFiles(): Plt0IdiFileCollectionService<ClientType>;
    IdiFiles(id: Plt0ApplicationObjectId): Plt0IdiFileService<ClientType>;
    EntityTypePropertyOverrides(): Plt0EntityTypePropertyOverrideCollectionService<ClientType>;
    EntityTypePropertyOverrides(id: Plt0ApplicationObjectId): Plt0EntityTypePropertyOverrideService<ClientType>;
    CadParts(): CadPartCollectionService<ClientType>;
    CadParts(id: Plt0ApplicationObjectId): CadPartService<ClientType>;
    NeueCadParts(): NeueCadPartCollectionService<ClientType>;
    NeueCadParts(id: Plt0ApplicationObjectId): NeueCadPartService<ClientType>;
    NeueCadAsms(): NeueCadAsmCollectionService<ClientType>;
    NeueCadAsms(id: Plt0ApplicationObjectId): NeueCadAsmService<ClientType>;
    NeueTwoDimensionDrawings(): NeueTwoDimensionDrawingCollectionService<ClientType>;
    NeueTwoDimensionDrawings(id: Plt0ApplicationObjectId): NeueTwoDimensionDrawingService<ClientType>;
    RelCadBomRelations(): RelCadBomCollectionService<ClientType>;
    RelCadBomRelations(id: Plt0ApplicationObjectId): RelCadBomService<ClientType>;
    RelCadPartTwoDimensionDrawingRelations(): RelCadPartTwoDimensionDrawingCollectionService<ClientType>;
    RelCadPartTwoDimensionDrawingRelations(id: Plt0ApplicationObjectId): RelCadPartTwoDimensionDrawingService<ClientType>;
    RelCadFileFileRelations(): RelCadFileFileCollectionService<ClientType>;
    RelCadFileFileRelations(id: Plt0ApplicationObjectId): RelCadFileFileService<ClientType>;
    MappingConfigs(): MappingConfigCollectionService<ClientType>;
    MappingConfigs(id: Plt0ApplicationObjectId): MappingConfigService<ClientType>;
    Test_CollectionProducts(): Test_ProductModelCollectionService<ClientType>;
    Test_CollectionProducts(id: Plt0ApplicationObjectId): Test_ProductModelService<ClientType>;
    Test_CollectionProduct1113(): Test_ProductModel222CollectionService<ClientType>;
    Test_CollectionProduct1113(id: Plt0ApplicationObjectId): Test_ProductModel222Service<ClientType>;
    Test_CollectionProduct222(): Test_ProductModel222CollectionService<ClientType>;
    Test_CollectionProduct222(id: Plt0ApplicationObjectId): Test_ProductModel222Service<ClientType>;
    Test_CollectionProduct333(): Test_ProductModel333CollectionService<ClientType>;
    Test_CollectionProduct333(id: Plt0ApplicationObjectId): Test_ProductModel333Service<ClientType>;
    Test_CollectionProduct444(): Test_ProductModel444CollectionService<ClientType>;
    Test_CollectionProduct444(id: Plt0ApplicationObjectId): Test_ProductModel444Service<ClientType>;
    GetRelationTreeByNcid(params: GetRelationTreeByNcidParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<Plt0RelationQuery>>>;
    Deploy(params: DeployParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<boolean>>>;
    BatchGetFileSignatureUrl(params: BatchGetFileSignatureUrlParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataCollectionResponseV4<FileSignatureUrl>>>;
}
export declare class Plt0ApplicationObjectService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0ApplicationObject, EditablePlt0ApplicationObject, QPlt0ApplicationObject> {
    private _createdBy?;
    private _modifiedBy?;
    private _Plt0ApplicationObject_QGenerateCode?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    GenerateCode(params: Plt0ApplicationObject_GenerateCodeParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<string>>>;
    asPlt0MasterObjectService(): Plt0MasterObjectService<ClientType>;
    asPlt0RevisionObjectService(): Plt0RevisionObjectService<ClientType>;
    asPlt0VersionObjectService(): Plt0VersionObjectService<ClientType>;
    asPlt0LifecycleStatusService(): Plt0LifecycleStatusService<ClientType>;
    asPlt0LifecycleStatusStrategyService(): Plt0LifecycleStatusStrategyService<ClientType>;
    asPlt0MrvObjectService(): Plt0MrvObjectService<ClientType>;
    asPlt0FileService(): Plt0FileService<ClientType>;
    asPlt0IdiFileService(): Plt0IdiFileService<ClientType>;
    asPlt0RootRelationTypeService(): Plt0RootRelationTypeService<ClientType>;
    asPlt0RelationQueryService(): Plt0RelationQueryService<ClientType>;
    asPlt0RootRelationTypeConfigService(): Plt0RootRelationTypeConfigService<ClientType>;
    asPlt0RelationFromOrToTypeService(): Plt0RelationFromOrToTypeService<ClientType>;
    asPlt0SchemaService(): Plt0SchemaService<ClientType>;
    asPlt0BaseTypeService(): Plt0BaseTypeService<ClientType>;
    asPlt0EntityTypeService(): Plt0EntityTypeService<ClientType>;
    asPlt0EntityTypeUnionUniqService(): Plt0EntityTypeUnionUniqService<ClientType>;
    asPlt0EntityTypeUnionUniqPropertyService(): Plt0EntityTypeUnionUniqPropertyService<ClientType>;
    asPlt0EntityTypePropertyOverrideService(): Plt0EntityTypePropertyOverrideService<ClientType>;
    asPlt0ComplexTypeService(): Plt0ComplexTypeService<ClientType>;
    asPlt0EnumTypeService(): Plt0EnumTypeService<ClientType>;
    asPlt0TypeDefinitionService(): Plt0TypeDefinitionService<ClientType>;
    asPlt0StructuralPropertyService(): Plt0StructuralPropertyService<ClientType>;
    asPlt0NavigationPropertyService(): Plt0NavigationPropertyService<ClientType>;
    asPlt0ActionService(): Plt0ActionService<ClientType>;
    asPlt0FunctionService(): Plt0FunctionService<ClientType>;
    asPlt0EntityContainerService(): Plt0EntityContainerService<ClientType>;
    asPlt0EntitySetService(): Plt0EntitySetService<ClientType>;
    asPlt0SingletonService(): Plt0SingletonService<ClientType>;
    asPlt0NavigationPropertyBindingService(): Plt0NavigationPropertyBindingService<ClientType>;
    asPlt0ActionImportService(): Plt0ActionImportService<ClientType>;
    asPlt0FunctionImportService(): Plt0FunctionImportService<ClientType>;
    asPlt0BusinessObjectRevisionNumberRuleService(): Plt0BusinessObjectRevisionNumberRuleService<ClientType>;
    asPlt0BusinessObjectRevisionNumberCodeService(): Plt0BusinessObjectRevisionNumberCodeService<ClientType>;
    asCadFileService(): CadFileService<ClientType>;
    asCadPartService(): CadPartService<ClientType>;
    asNeueCadPartService(): NeueCadPartService<ClientType>;
    asNeueCadAsmService(): NeueCadAsmService<ClientType>;
    asTwoDimensionDrawingService(): TwoDimensionDrawingService<ClientType>;
    asNeueTwoDimensionDrawingService(): NeueTwoDimensionDrawingService<ClientType>;
    asPlt0File2Service(): Plt0File2Service<ClientType>;
    asPlt0IdiFile2Service(): Plt0IdiFile2Service<ClientType>;
    asRelCadBomService(): RelCadBomService<ClientType>;
    asRelCadPartTwoDimensionDrawingService(): RelCadPartTwoDimensionDrawingService<ClientType>;
    asRelCadFileFileService(): RelCadFileFileService<ClientType>;
    asMappingConfigService(): MappingConfigService<ClientType>;
    asTest_productService(): test_productService<ClientType>;
    asTest_product001Service(): test_product001Service<ClientType>;
    asTest_ProductModelService(): Test_ProductModelService<ClientType>;
    asTest_ProductModel111Service(): Test_ProductModel111Service<ClientType>;
    asTest_ProductModel222Service(): Test_ProductModel222Service<ClientType>;
    asTest_ProductModel333Service(): Test_ProductModel333Service<ClientType>;
    asTest_ProductModel444Service(): Test_ProductModel444Service<ClientType>;
}
export declare class Plt0ApplicationObjectCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0ApplicationObject, EditablePlt0ApplicationObject, QPlt0ApplicationObject, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asPlt0MasterObjectCollectionService(): Plt0MasterObjectCollectionService<ClientType>;
    asPlt0RevisionObjectCollectionService(): Plt0RevisionObjectCollectionService<ClientType>;
    asPlt0VersionObjectCollectionService(): Plt0VersionObjectCollectionService<ClientType>;
    asPlt0LifecycleStatusCollectionService(): Plt0LifecycleStatusCollectionService<ClientType>;
    asPlt0LifecycleStatusStrategyCollectionService(): Plt0LifecycleStatusStrategyCollectionService<ClientType>;
    asPlt0MrvObjectCollectionService(): Plt0MrvObjectCollectionService<ClientType>;
    asPlt0FileCollectionService(): Plt0FileCollectionService<ClientType>;
    asPlt0IdiFileCollectionService(): Plt0IdiFileCollectionService<ClientType>;
    asPlt0RootRelationTypeCollectionService(): Plt0RootRelationTypeCollectionService<ClientType>;
    asPlt0RelationQueryCollectionService(): Plt0RelationQueryCollectionService<ClientType>;
    asPlt0RootRelationTypeConfigCollectionService(): Plt0RootRelationTypeConfigCollectionService<ClientType>;
    asPlt0RelationFromOrToTypeCollectionService(): Plt0RelationFromOrToTypeCollectionService<ClientType>;
    asPlt0SchemaCollectionService(): Plt0SchemaCollectionService<ClientType>;
    asPlt0BaseTypeCollectionService(): Plt0BaseTypeCollectionService<ClientType>;
    asPlt0EntityTypeCollectionService(): Plt0EntityTypeCollectionService<ClientType>;
    asPlt0EntityTypeUnionUniqCollectionService(): Plt0EntityTypeUnionUniqCollectionService<ClientType>;
    asPlt0EntityTypeUnionUniqPropertyCollectionService(): Plt0EntityTypeUnionUniqPropertyCollectionService<ClientType>;
    asPlt0EntityTypePropertyOverrideCollectionService(): Plt0EntityTypePropertyOverrideCollectionService<ClientType>;
    asPlt0ComplexTypeCollectionService(): Plt0ComplexTypeCollectionService<ClientType>;
    asPlt0EnumTypeCollectionService(): Plt0EnumTypeCollectionService<ClientType>;
    asPlt0TypeDefinitionCollectionService(): Plt0TypeDefinitionCollectionService<ClientType>;
    asPlt0StructuralPropertyCollectionService(): Plt0StructuralPropertyCollectionService<ClientType>;
    asPlt0NavigationPropertyCollectionService(): Plt0NavigationPropertyCollectionService<ClientType>;
    asPlt0ActionCollectionService(): Plt0ActionCollectionService<ClientType>;
    asPlt0FunctionCollectionService(): Plt0FunctionCollectionService<ClientType>;
    asPlt0EntityContainerCollectionService(): Plt0EntityContainerCollectionService<ClientType>;
    asPlt0EntitySetCollectionService(): Plt0EntitySetCollectionService<ClientType>;
    asPlt0SingletonCollectionService(): Plt0SingletonCollectionService<ClientType>;
    asPlt0NavigationPropertyBindingCollectionService(): Plt0NavigationPropertyBindingCollectionService<ClientType>;
    asPlt0ActionImportCollectionService(): Plt0ActionImportCollectionService<ClientType>;
    asPlt0FunctionImportCollectionService(): Plt0FunctionImportCollectionService<ClientType>;
    asPlt0BusinessObjectRevisionNumberRuleCollectionService(): Plt0BusinessObjectRevisionNumberRuleCollectionService<ClientType>;
    asPlt0BusinessObjectRevisionNumberCodeCollectionService(): Plt0BusinessObjectRevisionNumberCodeCollectionService<ClientType>;
    asCadFileCollectionService(): CadFileCollectionService<ClientType>;
    asCadPartCollectionService(): CadPartCollectionService<ClientType>;
    asNeueCadPartCollectionService(): NeueCadPartCollectionService<ClientType>;
    asNeueCadAsmCollectionService(): NeueCadAsmCollectionService<ClientType>;
    asTwoDimensionDrawingCollectionService(): TwoDimensionDrawingCollectionService<ClientType>;
    asNeueTwoDimensionDrawingCollectionService(): NeueTwoDimensionDrawingCollectionService<ClientType>;
    asPlt0File2CollectionService(): Plt0File2CollectionService<ClientType>;
    asPlt0IdiFile2CollectionService(): Plt0IdiFile2CollectionService<ClientType>;
    asRelCadBomCollectionService(): RelCadBomCollectionService<ClientType>;
    asRelCadPartTwoDimensionDrawingCollectionService(): RelCadPartTwoDimensionDrawingCollectionService<ClientType>;
    asRelCadFileFileCollectionService(): RelCadFileFileCollectionService<ClientType>;
    asMappingConfigCollectionService(): MappingConfigCollectionService<ClientType>;
    asTest_productCollectionService(): test_productCollectionService<ClientType>;
    asTest_product001CollectionService(): test_product001CollectionService<ClientType>;
    asTest_ProductModelCollectionService(): Test_ProductModelCollectionService<ClientType>;
    asTest_ProductModel111CollectionService(): Test_ProductModel111CollectionService<ClientType>;
    asTest_ProductModel222CollectionService(): Test_ProductModel222CollectionService<ClientType>;
    asTest_ProductModel333CollectionService(): Test_ProductModel333CollectionService<ClientType>;
    asTest_ProductModel444CollectionService(): Test_ProductModel444CollectionService<ClientType>;
}
export declare class Plt0MasterObjectService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0MasterObject, EditablePlt0MasterObject, QPlt0MasterObject> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0MasterObjectCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0MasterObject, EditablePlt0MasterObject, QPlt0MasterObject, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0RevisionObjectService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0RevisionObject, EditablePlt0RevisionObject, QPlt0RevisionObject> {
    private _createdBy?;
    private _modifiedBy?;
    private _preRevision?;
    private _master?;
    private _lockedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    preRevision(): Plt0RevisionObjectService<ClientType>;
    master(): Plt0MasterObjectService<ClientType>;
    lockedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0RevisionObjectCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0RevisionObject, EditablePlt0RevisionObject, QPlt0RevisionObject, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0VersionObjectService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0VersionObject, EditablePlt0VersionObject, QPlt0VersionObject> {
    private _createdBy?;
    private _modifiedBy?;
    private _revision?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    revision(): Plt0RevisionObjectService<ClientType>;
}
export declare class Plt0VersionObjectCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0VersionObject, EditablePlt0VersionObject, QPlt0VersionObject, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0LifecycleStatusService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0LifecycleStatus, EditablePlt0LifecycleStatus, QPlt0LifecycleStatus> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0LifecycleStatusCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0LifecycleStatus, EditablePlt0LifecycleStatus, QPlt0LifecycleStatus, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0LifecycleStatusStrategyService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0LifecycleStatusStrategy, EditablePlt0LifecycleStatusStrategy, QPlt0LifecycleStatusStrategy> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0LifecycleStatusStrategyCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0LifecycleStatusStrategy, EditablePlt0LifecycleStatusStrategy, QPlt0LifecycleStatusStrategy, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0MrvObjectService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0MrvObject, EditablePlt0MrvObject, QPlt0MrvObject> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _Plt0MrvObject_QNextRevision?;
    private _Plt0MrvObject_QLock?;
    private _Plt0MrvObject_QUnlock?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    NextRevision(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<string>>>;
    Lock(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<boolean>>>;
    Unlock(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<boolean>>>;
    asCadFileService(): CadFileService<ClientType>;
    asCadPartService(): CadPartService<ClientType>;
    asNeueCadPartService(): NeueCadPartService<ClientType>;
    asNeueCadAsmService(): NeueCadAsmService<ClientType>;
    asTwoDimensionDrawingService(): TwoDimensionDrawingService<ClientType>;
    asNeueTwoDimensionDrawingService(): NeueTwoDimensionDrawingService<ClientType>;
}
export declare class Plt0MrvObjectCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0MrvObject, EditablePlt0MrvObject, QPlt0MrvObject, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asCadFileCollectionService(): CadFileCollectionService<ClientType>;
    asCadPartCollectionService(): CadPartCollectionService<ClientType>;
    asNeueCadPartCollectionService(): NeueCadPartCollectionService<ClientType>;
    asNeueCadAsmCollectionService(): NeueCadAsmCollectionService<ClientType>;
    asTwoDimensionDrawingCollectionService(): TwoDimensionDrawingCollectionService<ClientType>;
    asNeueTwoDimensionDrawingCollectionService(): NeueTwoDimensionDrawingCollectionService<ClientType>;
}
export declare class Plt0FileService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0File, EditablePlt0File, QPlt0File> {
    private _createdBy?;
    private _modifiedBy?;
    private _storageItem?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    storageItem(): Plt0ApplicationObjectService<ClientType>;
    asPlt0IdiFileService(): Plt0IdiFileService<ClientType>;
}
export declare class Plt0FileCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0File, EditablePlt0File, QPlt0File, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asPlt0IdiFileCollectionService(): Plt0IdiFileCollectionService<ClientType>;
}
export declare class Plt0IdiFileService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0IdiFile, EditablePlt0IdiFile, QPlt0IdiFile> {
    private _createdBy?;
    private _modifiedBy?;
    private _storageItem?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    storageItem(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0IdiFileCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0IdiFile, EditablePlt0IdiFile, QPlt0IdiFile, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0RootRelationTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0RootRelationType, EditablePlt0RootRelationType, QPlt0RootRelationType> {
    private _createdBy?;
    private _modifiedBy?;
    private _from?;
    private _to?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    from(): Plt0ApplicationObjectService<ClientType>;
    to(): Plt0ApplicationObjectService<ClientType>;
    asRelCadBomService(): RelCadBomService<ClientType>;
    asRelCadPartTwoDimensionDrawingService(): RelCadPartTwoDimensionDrawingService<ClientType>;
    asRelCadFileFileService(): RelCadFileFileService<ClientType>;
}
export declare class Plt0RootRelationTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0RootRelationType, EditablePlt0RootRelationType, QPlt0RootRelationType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asRelCadBomCollectionService(): RelCadBomCollectionService<ClientType>;
    asRelCadPartTwoDimensionDrawingCollectionService(): RelCadPartTwoDimensionDrawingCollectionService<ClientType>;
    asRelCadFileFileCollectionService(): RelCadFileFileCollectionService<ClientType>;
}
export declare class Plt0RelationQueryService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0RelationQuery, EditablePlt0RelationQuery, QPlt0RelationQuery> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    toRelations(): Plt0RootRelationTypeCollectionService<ClientType>;
    toRelations(id: Plt0ApplicationObjectId): Plt0RootRelationTypeService<ClientType>;
}
export declare class Plt0RelationQueryCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0RelationQuery, EditablePlt0RelationQuery, QPlt0RelationQuery, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0RootRelationTypeConfigService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0RootRelationTypeConfig, EditablePlt0RootRelationTypeConfig, QPlt0RootRelationTypeConfig> {
    private _createdBy?;
    private _modifiedBy?;
    private _applyRelationType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    applyRelationType(): Plt0RootRelationTypeService<ClientType>;
}
export declare class Plt0RootRelationTypeConfigCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0RootRelationTypeConfig, EditablePlt0RootRelationTypeConfig, QPlt0RootRelationTypeConfig, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0RelationFromOrToTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0RelationFromOrToType, EditablePlt0RelationFromOrToType, QPlt0RelationFromOrToType> {
    private _createdBy?;
    private _modifiedBy?;
    private _relationType?;
    private _constrictType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    relationType(): Plt0EntityTypeService<ClientType>;
    constrictType(): Plt0EntityTypeService<ClientType>;
}
export declare class Plt0RelationFromOrToTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0RelationFromOrToType, EditablePlt0RelationFromOrToType, QPlt0RelationFromOrToType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0SchemaService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0Schema, EditablePlt0Schema, QPlt0Schema> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0SchemaCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0Schema, EditablePlt0Schema, QPlt0Schema, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0BaseTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0BaseType, EditablePlt0BaseType, QPlt0BaseType> {
    private _createdBy?;
    private _modifiedBy?;
    private _schema?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    schema(): Plt0SchemaService<ClientType>;
    asPlt0EntityTypeService(): Plt0EntityTypeService<ClientType>;
    asPlt0ComplexTypeService(): Plt0ComplexTypeService<ClientType>;
}
export declare class Plt0BaseTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0BaseType, EditablePlt0BaseType, QPlt0BaseType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asPlt0EntityTypeCollectionService(): Plt0EntityTypeCollectionService<ClientType>;
    asPlt0ComplexTypeCollectionService(): Plt0ComplexTypeCollectionService<ClientType>;
}
export declare class Plt0EntityTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntityType, EditablePlt0EntityType, QPlt0EntityType> {
    private _createdBy?;
    private _modifiedBy?;
    private _schema?;
    private _icon?;
    private _baseType?;
    private _lifecycleStatusStrategy?;
    private _revisionNumberRule?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    schema(): Plt0SchemaService<ClientType>;
    icon(): Plt0FileService<ClientType>;
    baseType(): Plt0EntityTypeService<ClientType>;
    lifecycleStatusStrategy(): Plt0LifecycleStatusStrategyService<ClientType>;
    revisionNumberRule(): Plt0BusinessObjectRevisionNumberRuleService<ClientType>;
}
export declare class Plt0EntityTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntityType, EditablePlt0EntityType, QPlt0EntityType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EntityTypeUnionUniqService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntityTypeUnionUniq, EditablePlt0EntityTypeUnionUniq, QPlt0EntityTypeUnionUniq> {
    private _createdBy?;
    private _modifiedBy?;
    private _ownerType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ownerType(): Plt0EntityTypeService<ClientType>;
}
export declare class Plt0EntityTypeUnionUniqCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntityTypeUnionUniq, EditablePlt0EntityTypeUnionUniq, QPlt0EntityTypeUnionUniq, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EntityTypeUnionUniqPropertyService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntityTypeUnionUniqProperty, EditablePlt0EntityTypeUnionUniqProperty, QPlt0EntityTypeUnionUniqProperty> {
    private _createdBy?;
    private _modifiedBy?;
    private _ownerUnionUniq?;
    private _structuralProperty?;
    private _navigationProperty?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ownerUnionUniq(): Plt0EntityTypeUnionUniqService<ClientType>;
    structuralProperty(): Plt0StructuralPropertyService<ClientType>;
    navigationProperty(): Plt0NavigationPropertyService<ClientType>;
}
export declare class Plt0EntityTypeUnionUniqPropertyCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntityTypeUnionUniqProperty, EditablePlt0EntityTypeUnionUniqProperty, QPlt0EntityTypeUnionUniqProperty, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EntityTypePropertyOverrideService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntityTypePropertyOverride, EditablePlt0EntityTypePropertyOverride, QPlt0EntityTypePropertyOverride> {
    private _createdBy?;
    private _modifiedBy?;
    private _ownerType?;
    private _structuralProperty?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ownerType(): Plt0EntityTypeService<ClientType>;
    structuralProperty(): Plt0StructuralPropertyService<ClientType>;
}
export declare class Plt0EntityTypePropertyOverrideCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntityTypePropertyOverride, EditablePlt0EntityTypePropertyOverride, QPlt0EntityTypePropertyOverride, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0ComplexTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0ComplexType, EditablePlt0ComplexType, QPlt0ComplexType> {
    private _createdBy?;
    private _modifiedBy?;
    private _schema?;
    private _baseType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    schema(): Plt0SchemaService<ClientType>;
    baseType(): Plt0ComplexTypeService<ClientType>;
}
export declare class Plt0ComplexTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0ComplexType, EditablePlt0ComplexType, QPlt0ComplexType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EnumTypeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EnumType, EditablePlt0EnumType, QPlt0EnumType> {
    private _createdBy?;
    private _modifiedBy?;
    private _members?;
    private _schema?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    members(): CollectionServiceV4<ClientType, StringCollection, QStringCollection<string>, string>;
    schema(): Plt0SchemaService<ClientType>;
}
export declare class Plt0EnumTypeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EnumType, EditablePlt0EnumType, QPlt0EnumType, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0TypeDefinitionService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0TypeDefinition, EditablePlt0TypeDefinition, QPlt0TypeDefinition> {
    private _createdBy?;
    private _modifiedBy?;
    private _schema?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    schema(): Plt0SchemaService<ClientType>;
}
export declare class Plt0TypeDefinitionCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0TypeDefinition, EditablePlt0TypeDefinition, QPlt0TypeDefinition, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0StructuralPropertyService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0StructuralProperty, EditablePlt0StructuralProperty, QPlt0StructuralProperty> {
    private _createdBy?;
    private _modifiedBy?;
    private _ownerType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ownerType(): Plt0BaseTypeService<ClientType>;
}
export declare class Plt0StructuralPropertyCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0StructuralProperty, EditablePlt0StructuralProperty, QPlt0StructuralProperty, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0NavigationPropertyService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0NavigationProperty, EditablePlt0NavigationProperty, QPlt0NavigationProperty> {
    private _createdBy?;
    private _modifiedBy?;
    private _ownerType?;
    private _type?;
    private _partner?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ownerType(): Plt0BaseTypeService<ClientType>;
    type(): Plt0EntityTypeService<ClientType>;
    partner(): Plt0NavigationPropertyService<ClientType>;
}
export declare class Plt0NavigationPropertyCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0NavigationProperty, EditablePlt0NavigationProperty, QPlt0NavigationProperty, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0ActionService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0Action, EditablePlt0Action, QPlt0Action> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0ActionCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0Action, EditablePlt0Action, QPlt0Action, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0FunctionService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0Function, EditablePlt0Function, QPlt0Function> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0FunctionCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0Function, EditablePlt0Function, QPlt0Function, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EntityContainerService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntityContainer, EditablePlt0EntityContainer, QPlt0EntityContainer> {
    private _createdBy?;
    private _modifiedBy?;
    private _schema?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    schema(): Plt0SchemaService<ClientType>;
}
export declare class Plt0EntityContainerCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntityContainer, EditablePlt0EntityContainer, QPlt0EntityContainer, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0EntitySetService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0EntitySet, EditablePlt0EntitySet, QPlt0EntitySet> {
    private _createdBy?;
    private _modifiedBy?;
    private _entityContainer?;
    private _entityType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    entityContainer(): Plt0EntityContainerService<ClientType>;
    entityType(): Plt0EntityTypeService<ClientType>;
}
export declare class Plt0EntitySetCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0EntitySet, EditablePlt0EntitySet, QPlt0EntitySet, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0SingletonService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0Singleton, EditablePlt0Singleton, QPlt0Singleton> {
    private _createdBy?;
    private _modifiedBy?;
    private _entityContainer?;
    private _entityType?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    entityContainer(): Plt0EntityContainerService<ClientType>;
    entityType(): Plt0EntityTypeService<ClientType>;
}
export declare class Plt0SingletonCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0Singleton, EditablePlt0Singleton, QPlt0Singleton, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0NavigationPropertyBindingService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0NavigationPropertyBinding, EditablePlt0NavigationPropertyBinding, QPlt0NavigationPropertyBinding> {
    private _createdBy?;
    private _modifiedBy?;
    private _entitySet?;
    private _path?;
    private _target?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    entitySet(): Plt0EntitySetService<ClientType>;
    path(): Plt0NavigationPropertyService<ClientType>;
    target(): Plt0EntitySetService<ClientType>;
}
export declare class Plt0NavigationPropertyBindingCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0NavigationPropertyBinding, EditablePlt0NavigationPropertyBinding, QPlt0NavigationPropertyBinding, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0ActionImportService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0ActionImport, EditablePlt0ActionImport, QPlt0ActionImport> {
    private _createdBy?;
    private _modifiedBy?;
    private _entityContainer?;
    private _action?;
    private _entitySet?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    entityContainer(): Plt0EntityContainerService<ClientType>;
    action(): Plt0ActionService<ClientType>;
    entitySet(): Plt0EntitySetService<ClientType>;
}
export declare class Plt0ActionImportCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0ActionImport, EditablePlt0ActionImport, QPlt0ActionImport, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0FunctionImportService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0FunctionImport, EditablePlt0FunctionImport, QPlt0FunctionImport> {
    private _createdBy?;
    private _modifiedBy?;
    private _entityContainer?;
    private _function?;
    private _entitySet?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    entityContainer(): Plt0EntityContainerService<ClientType>;
    function(): Plt0FunctionService<ClientType>;
    entitySet(): Plt0EntitySetService<ClientType>;
}
export declare class Plt0FunctionImportCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0FunctionImport, EditablePlt0FunctionImport, QPlt0FunctionImport, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0BusinessObjectRevisionNumberRuleService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0BusinessObjectRevisionNumberRule, EditablePlt0BusinessObjectRevisionNumberRule, QPlt0BusinessObjectRevisionNumberRule> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0BusinessObjectRevisionNumberRuleCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0BusinessObjectRevisionNumberRule, EditablePlt0BusinessObjectRevisionNumberRule, QPlt0BusinessObjectRevisionNumberRule, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0BusinessObjectRevisionNumberCodeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0BusinessObjectRevisionNumberCode, EditablePlt0BusinessObjectRevisionNumberCode, QPlt0BusinessObjectRevisionNumberCode> {
    private _createdBy?;
    private _modifiedBy?;
    private _ruleCodeRef?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    ruleCodeRef(): Plt0BusinessObjectRevisionNumberRuleService<ClientType>;
}
export declare class Plt0BusinessObjectRevisionNumberCodeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0BusinessObjectRevisionNumberCode, EditablePlt0BusinessObjectRevisionNumberCode, QPlt0BusinessObjectRevisionNumberCode, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class CadFileService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, CadFile, EditableCadFile, QCadFile> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    asCadPartService(): CadPartService<ClientType>;
    asNeueCadPartService(): NeueCadPartService<ClientType>;
    asNeueCadAsmService(): NeueCadAsmService<ClientType>;
    asTwoDimensionDrawingService(): TwoDimensionDrawingService<ClientType>;
    asNeueTwoDimensionDrawingService(): NeueTwoDimensionDrawingService<ClientType>;
}
export declare class CadFileCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, CadFile, EditableCadFile, QCadFile, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asCadPartCollectionService(): CadPartCollectionService<ClientType>;
    asNeueCadPartCollectionService(): NeueCadPartCollectionService<ClientType>;
    asNeueCadAsmCollectionService(): NeueCadAsmCollectionService<ClientType>;
    asTwoDimensionDrawingCollectionService(): TwoDimensionDrawingCollectionService<ClientType>;
    asNeueTwoDimensionDrawingCollectionService(): NeueTwoDimensionDrawingCollectionService<ClientType>;
}
export declare class CadPartService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, CadPart, EditableCadPart, QCadPart> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _thumbnail?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    thumbnail(): Plt0File2Service<ClientType>;
    asNeueCadPartService(): NeueCadPartService<ClientType>;
    asNeueCadAsmService(): NeueCadAsmService<ClientType>;
}
export declare class CadPartCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, CadPart, EditableCadPart, QCadPart, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asNeueCadPartCollectionService(): NeueCadPartCollectionService<ClientType>;
    asNeueCadAsmCollectionService(): NeueCadAsmCollectionService<ClientType>;
}
export declare class NeueCadPartService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, NeueCadPart, EditableNeueCadPart, QNeueCadPart> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _thumbnail?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    thumbnail(): Plt0File2Service<ClientType>;
}
export declare class NeueCadPartCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, NeueCadPart, EditableNeueCadPart, QNeueCadPart, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class NeueCadAsmService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, NeueCadAsm, EditableNeueCadAsm, QNeueCadAsm> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _thumbnail?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    thumbnail(): Plt0File2Service<ClientType>;
}
export declare class NeueCadAsmCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, NeueCadAsm, EditableNeueCadAsm, QNeueCadAsm, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class TwoDimensionDrawingService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, TwoDimensionDrawing, EditableTwoDimensionDrawing, QTwoDimensionDrawing> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _thumbnail?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    thumbnail(): Plt0File2Service<ClientType>;
    asNeueTwoDimensionDrawingService(): NeueTwoDimensionDrawingService<ClientType>;
}
export declare class TwoDimensionDrawingCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, TwoDimensionDrawing, EditableTwoDimensionDrawing, QTwoDimensionDrawing, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asNeueTwoDimensionDrawingCollectionService(): NeueTwoDimensionDrawingCollectionService<ClientType>;
}
export declare class NeueTwoDimensionDrawingService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, NeueTwoDimensionDrawing, EditableNeueTwoDimensionDrawing, QNeueTwoDimensionDrawing> {
    private _createdBy?;
    private _modifiedBy?;
    private _version?;
    private _owner?;
    private _lifecycleStatus?;
    private _thumbnail?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    version(): Plt0VersionObjectService<ClientType>;
    owner(): Plt0ApplicationObjectService<ClientType>;
    lifecycleStatus(): Plt0LifecycleStatusService<ClientType>;
    thumbnail(): Plt0File2Service<ClientType>;
}
export declare class NeueTwoDimensionDrawingCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, NeueTwoDimensionDrawing, EditableNeueTwoDimensionDrawing, QNeueTwoDimensionDrawing, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Plt0File2Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0File2, EditablePlt0File2, QPlt0File2> {
    private _createdBy?;
    private _modifiedBy?;
    private _storageItem?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    storageItem(): Plt0ApplicationObjectService<ClientType>;
    asPlt0IdiFile2Service(): Plt0IdiFile2Service<ClientType>;
}
export declare class Plt0File2CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0File2, EditablePlt0File2, QPlt0File2, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asPlt0IdiFile2CollectionService(): Plt0IdiFile2CollectionService<ClientType>;
}
export declare class Plt0IdiFile2Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Plt0IdiFile2, EditablePlt0IdiFile2, QPlt0IdiFile2> {
    private _createdBy?;
    private _modifiedBy?;
    private _storageItem?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    storageItem(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Plt0IdiFile2CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Plt0IdiFile2, EditablePlt0IdiFile2, QPlt0IdiFile2, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class RelCadBomService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, RelCadBom, EditableRelCadBom, QRelCadBom> {
    private _createdBy?;
    private _modifiedBy?;
    private _from?;
    private _to?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    from(): Plt0ApplicationObjectService<ClientType>;
    to(): Plt0ApplicationObjectService<ClientType>;
}
export declare class RelCadBomCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, RelCadBom, EditableRelCadBom, QRelCadBom, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class RelCadPartTwoDimensionDrawingService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, RelCadPartTwoDimensionDrawing, EditableRelCadPartTwoDimensionDrawing, QRelCadPartTwoDimensionDrawing> {
    private _createdBy?;
    private _modifiedBy?;
    private _from?;
    private _to?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    from(): Plt0ApplicationObjectService<ClientType>;
    to(): Plt0ApplicationObjectService<ClientType>;
}
export declare class RelCadPartTwoDimensionDrawingCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, RelCadPartTwoDimensionDrawing, EditableRelCadPartTwoDimensionDrawing, QRelCadPartTwoDimensionDrawing, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class RelCadFileFileService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, RelCadFileFile, EditableRelCadFileFile, QRelCadFileFile> {
    private _createdBy?;
    private _modifiedBy?;
    private _from?;
    private _to?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
    from(): Plt0ApplicationObjectService<ClientType>;
    to(): Plt0ApplicationObjectService<ClientType>;
}
export declare class RelCadFileFileCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, RelCadFileFile, EditableRelCadFileFile, QRelCadFileFile, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class MappingConfigService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, MappingConfig, EditableMappingConfig, QMappingConfig> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class MappingConfigCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, MappingConfig, EditableMappingConfig, QMappingConfig, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class test_productService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, test_product, Editabletest_product, Qtest_product> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class test_productCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, test_product, Editabletest_product, Qtest_product, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class test_product001Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, test_product001, Editabletest_product001, Qtest_product001> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class test_product001CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, test_product001, Editabletest_product001, Qtest_product001, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Test_ProductModelService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Test_ProductModel, EditableTest_ProductModel, QTest_ProductModel> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Test_ProductModelCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Test_ProductModel, EditableTest_ProductModel, QTest_ProductModel, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Test_ProductModel111Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Test_ProductModel111, EditableTest_ProductModel111, QTest_ProductModel111> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Test_ProductModel111CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Test_ProductModel111, EditableTest_ProductModel111, QTest_ProductModel111, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Test_ProductModel222Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Test_ProductModel222, EditableTest_ProductModel222, QTest_ProductModel222> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Test_ProductModel222CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Test_ProductModel222, EditableTest_ProductModel222, QTest_ProductModel222, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Test_ProductModel333Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Test_ProductModel333, EditableTest_ProductModel333, QTest_ProductModel333> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Test_ProductModel333CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Test_ProductModel333, EditableTest_ProductModel333, QTest_ProductModel333, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class Test_ProductModel444Service<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Test_ProductModel444, EditableTest_ProductModel444, QTest_ProductModel444> {
    private _createdBy?;
    private _modifiedBy?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    createdBy(): Plt0ApplicationObjectService<ClientType>;
    modifiedBy(): Plt0ApplicationObjectService<ClientType>;
}
export declare class Test_ProductModel444CollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Test_ProductModel444, EditableTest_ProductModel444, QTest_ProductModel444, Plt0ApplicationObjectId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
