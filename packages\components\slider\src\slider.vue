<template>
  <el-slider
    v-bind="props"
    @update:model-value="(value:any) => emit('update:modelValue', value)"
    @change="(value:any) => emit('change', value)"
    @input="(value:any) => emit('input', value)"
  />
</template>

<script lang="ts" setup>
import { neSliderProps } from './slider'

// 定义 props 和 emits
const props = defineProps(neSliderProps)
defineOptions({
  name: 'NeSlider',
})

const emit = defineEmits(['update:modelValue', 'change', 'input'])
</script>
