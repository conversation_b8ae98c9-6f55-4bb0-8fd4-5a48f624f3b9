<template>
  <div>
    <h3>dialog-form测试</h3>
    <NeCard>
      <el-button type="primary" @click="openDialogForm">打开对话框</el-button>
      <WidgetDialogForm
        ref="dialogFormRef"
        :config="dialogFormConfig.config"
        :form-items="dialogFormItems"
      ></WidgetDialogForm>
    </NeCard>

    <NeMaterialRender v-bind="modalFormConfig" />
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import {
  basicFormConfig,
  dialogFormItems,
  dialogFormConfig,
  modalFormConfig,
} from '../../constant'
import { WidgetDialogForm, WidgetBasicForm } from '@neue-plus/widgets'

const dialogFormRef = ref<InstanceType<typeof WidgetDialogForm>>()
const openDialogForm = () => {
  // dialogFormVisible.value = true
  dialogFormRef.value?.open()
  // console.log('打开对话框表单')
}
</script>
