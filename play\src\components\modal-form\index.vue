<template>
  <div>
    <h3>form测试</h3>
    <!-- 工程图详情 已更新字段-->
    <!-- <NeMaterialRender v-bind="edDetailConfig" /> -->
    <!-- 创建part -->
    <!-- <NeMaterialRender v-bind="modalFormConfig" /> -->
    <!-- 修订part -->
    <!-- <NeMaterialRender v-bind="editPartConfig" /> -->
    <!-- 编辑关系 -->
    <!-- <NeMaterialRender v-bind="editRelationConfig" /> -->
    <!-- 零部件属性信息 -->
    <!-- <NeMaterialRender v-bind="panelFormConfig" /> -->

    <!-- <NeMaterialRender v-bind="basicDetailConfig" /> -->
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import {
  // basicFormConfig,
  // dialogFormItems,
  // dialogFormConfig,
  modalFormConfig,
  panelFormConfig,
  editPartConfig,
  edDetailConfig,
  editRelationConfig,
  basicDetailConfig,
} from '../../constant'
import { panelFormItems } from './constant'
import { WidgetDialogForm, WidgetPanelForm } from '@neue-plus/widgets'

const dialogFormRef = ref<InstanceType<typeof WidgetDialogForm>>()
const openDialogForm = () => {
  // dialogFormVisible.value = true
  dialogFormRef.value?.open()
  // console.log('打开对话框表单')
}

const groupFormItems = ref(panelFormItems)
</script>
