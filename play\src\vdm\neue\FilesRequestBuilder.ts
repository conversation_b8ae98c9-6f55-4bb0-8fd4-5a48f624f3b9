/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { Files } from './Files';

/**
 * Request builder class for operations supported on the {@link Files} entity.
 */
export class FilesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<Files<T>, T> {
  /**
   * Returns a request builder for querying all `Files` entities.
   * @returns A request builder for creating requests to retrieve all `Files` entities.
   */
  getAll(): GetAllRequestBuilder<Files<T>, T> {
    return new GetAllRequestBuilder<Files<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `Files` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `Files`.
   */
  create(entity: Files<T>): CreateRequestBuilder<Files<T>, T> {
    return new CreateRequestBuilder<Files<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `Files` entity based on its keys.
   * @param ncid Key property. See {@link Files.ncid}.
   * @returns A request builder for creating requests to retrieve one `Files` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<Files<T>, T> {
    return new GetByKeyRequestBuilder<Files<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `Files`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `Files`.
   */
  update(entity: Files<T>): UpdateRequestBuilder<Files<T>, T> {
    return new UpdateRequestBuilder<Files<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `Files`.
   * @param ncid Key property. See {@link Files.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `Files`.
   */
  delete(ncid: string): DeleteRequestBuilder<Files<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `Files`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `Files` by taking the entity as a parameter.
   */
  delete(entity: Files<T>): DeleteRequestBuilder<Files<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<Files<T>, T> {
    return new DeleteRequestBuilder<Files<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof Files ? ncidOrEntity : { ncid: ncidOrEntity! }
    );
  }
}
