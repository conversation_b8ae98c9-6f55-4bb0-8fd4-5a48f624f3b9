// scripts/find-missing-return-types.js
import fs from 'fs'
import path from 'path'
import { XMLParser } from 'fast-xml-parser'

const metadataFile = path.resolve('resource', '$metadata.xml')
const outputFile = path.resolve('options-per-service.json')

// 读取 $metadata.xml
const xmlData = fs.readFileSync(metadataFile, 'utf-8')
const parser = new XMLParser({
  ignoreAttributes: false,
  attributeNamePrefix: '',
})
const edmx = parser.parse(xmlData)

const schema = edmx['edmx:Edmx']['edmx:DataServices'].Schema
const schemas = Array.isArray(schema) ? schema : [schema]

// 收集所有已定义类型
const definedTypes = new Set()
schemas.forEach((s) => {
  const ns = s.Namespace
  ;['EntityType', 'ComplexType', 'EnumType', 'TypeDefinition'].forEach((t) => {
    const items = s[t]
    if (items) {
      const arr = Array.isArray(items) ? items : [items]
      arr.forEach((it) => definedTypes.add(`${ns}.${it.Name}`))
    }
  })
})

// 找到 Function 和 Action 返回类型缺失的操作
const missing = []
schemas.forEach((s) => {
  const funcs = [].concat(s.Function || [], s.Action || []).filter(Boolean)
  funcs.forEach((f) => {
    const returnType = f.ReturnType?.Type
    if (
      returnType &&
      !definedTypes.has(returnType) &&
      !returnType.startsWith('Edm.')
    ) {
      missing.push(f.Name)
    }
  })
})

// 生成 options-per-service.json
const optionsPerService = {
  neue: {
    operations: {
      exclude: missing,
    },
  },
}

fs.writeFileSync(
  outputFile,
  JSON.stringify(optionsPerService, null, 2),
  'utf-8'
)
console.log(
  `✅ Generated options-per-service.json with ${missing.length} excluded operations.`
)
console.log(`File: ${outputFile}`)
