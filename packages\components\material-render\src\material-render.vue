<template>
  <el-skeleton v-if="loading" :rows="5" animated />
  <NeConfigProvider v-else v-bind="configProviderConfig">
    <template v-if="elements && elements.length > 0">
      <NeRenderCore
        v-for="element in elements"
        :key="element.id"
        v-bind="element"
        @before-render="handleBeforeRender"
        @after-render="handleAfterRender"
      />
    </template>
  </NeConfigProvider>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, ref } from 'vue'
// import { AxiosClient } from '@odata2ts/http-client-axios'
import { parseODataMetadata } from '@neue-plus/utils'
import NeConfigProvider from '../../config-provider/src/config-provider.vue'
import NeRenderCore from './render-core/render-core'
import { neMaterialRenderProps } from './types'
import { provideContext } from './context/event-flow'
import { getODataMetadataJson } from './utils/metadata'
import { loadPaaSModules } from './utils/commonjs-compat'

// 使用专门的 CommonJS 兼容性工具加载模块
const loadPaaSModel = async () => {
  try {
    const paasModules = await loadPaaSModules()
    console.log('PaaS Modules loaded successfully:', paasModules)
    console.log('Available enums:', {
      LifecycleState: paasModules.LifecycleState,
      OnDelete: paasModules.OnDelete,
      PartType: paasModules.PartType,
    })
    return paasModules
  } catch (error) {
    console.error('Failed to load PaaS modules:', error)
  }
}
defineOptions({
  name: 'NeMaterialRender',
  inheritAttrs: false,
})
const props = defineProps(neMaterialRenderProps)
const { metadataRef } = provideContext(props.apis)
const loading = ref(false)
const configProviderConfig = computed(() => props.config?.configProvider)
// const events = computed(() => props.events)
const elements = computed(() => {
  return props.elements
})
const emit = defineEmits(['beforeRender', 'afterRender'])
const handleBeforeRender = (node: any) => {
  emit('beforeRender', node)
}
const handleAfterRender = (vnode: any, node: any) => {
  emit('afterRender', vnode, node)
}
const getODataMetadata = async () => {
  loading.value = true
  const metadataUrl = `${props.config?.apiBaseConfig?.baseUrl || ''}${
    props.apis?.metadata?.url
  }`

  const metadataJson = await getODataMetadataJson(metadataUrl)
  metadataRef.value = parseODataMetadata(metadataJson)
  console.log('metadataRef', metadataJson, metadataRef.value)
  loading.value = false
}
onBeforeMount(async () => {
  await getODataMetadata()
  await loadPaaSModel()
})
</script>
