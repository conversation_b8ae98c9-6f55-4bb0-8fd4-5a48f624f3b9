<template>
  <div class="ne-material-render">
    <el-skeleton v-if="loading" :rows="5" animated />
    <NeConfigProvider v-else v-bind="configProviderConfig">
      <template v-if="elements && elements.length > 0">
        <NeRenderCore
          v-for="element in elements"
          :key="element.id"
          v-bind="element"
          @before-render="handleBeforeRender"
          @after-render="handleAfterRender"
        />
      </template>
    </NeConfigProvider>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, ref } from 'vue'
// import { AxiosClient } from '@odata2ts/http-client-axios'
import { keys, merge } from 'lodash-unified'
import { parseODataMetadata } from '@neue-plus/utils'
import NeConfigProvider from '../../config-provider/src/config-provider.vue'
import NeRenderCore from './render-core/render-core'
import { neMaterialRenderProps } from './types'
import { provideContext } from './context/event-flow'
import { getODataMetadataJson } from './utils/metadata'
import service from './utils/service'

defineOptions({
  name: 'NeMaterialRender',
  inheritAttrs: false,
})
const props = defineProps(neMaterialRenderProps)
const { metadataRef } = provideContext(props.apis)
const loading = ref(false)
const configProviderConfig = computed(() => props.config?.configProvider)
// const events = computed(() => props.events)
const elements = computed(() => {
  return props.elements
})
const emit = defineEmits(['beforeRender', 'afterRender'])
const handleBeforeRender = (node: any) => {
  emit('beforeRender', node)
}
const handleAfterRender = (vnode: any, node: any) => {
  emit('afterRender', vnode, node)
}
const getODataMetadata = async () => {
  const metadataUrl = `${props.apis?.metadata?.url}`
  const metadataJson = await getODataMetadataJson(metadataUrl)
  const result = parseODataMetadata(metadataJson)
  metadataRef.value = merge({}, result, {
    enums: {
      cdpUserEmu: {
        name: 'cdp',
        fullName: 'cdp.User',
        members: keys(userList.value).map((item: string) => ({
          label: userList.value[item].text,
          value: item,
        })),
        valueEnum: userList.value,
      },
    },
  })
  console.log('metadataJson', metadataJson, metadataRef.value)
  loading.value = false
}
const userList = ref()
const getUserList = async () => {
  const res = await service({
    url: '/businessobject/202512/plt0User/actions/list',
    method: 'post',
    data: {
      pageParams: {
        limit: 9999,
        page: 0,
      },
    },
  })
  userList.value = res.data.reduce(
    (pre: { [x: string]: any }, item: { ncid: string | number; name: any }) => {
      pre[item.ncid] = { text: item.name }
      return pre
    },
    {}
  )
}
onBeforeMount(async () => {
  loading.value = true
  await getUserList()
  await getODataMetadata()
})
</script>
