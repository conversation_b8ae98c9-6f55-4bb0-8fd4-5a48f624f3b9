export const basicFormConfig = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {},
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'basic-form_60spo02i6g', // 组件唯一身份
      type: 'widget-container',
      name: '基础表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'basic-form_60spo02i5g', // 组件唯一身份
          type: 'wg-basic-form',
          name: '基础表单',
          props: {
            formItems: [
              {
                prop: 'ncid',
                fieldName: '编码',
                fieldType: 'text',
                required: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'name',
                fieldName: '名称1',
                fieldType: 'text',
                required: true,
                minLength: 6,
                maxLength: 18,
                showWordLimit: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'version',
                fieldName: '版本',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'version1',
                fieldName: '版次',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'age',
                fieldName: '年龄',
                fieldType: 'number',
                ruleId: 'RULE_AGE',
              },
              {
                prop: 'btype',
                fieldName: '类型',
                fieldType: 'businessType',
                options: [
                  { label: '北京', value: 'beijing' },
                  { label: '上海', value: 'shanghai' },
                  { label: '广州', value: 'guangzhou' },
                ],
              },
              {
                prop: 'city',
                fieldName: '城市',
                fieldType: 'select',
                filterable: true,
                options: [
                  { label: '北京', value: 'beijing' },
                  { label: '上海', value: 'shanghai' },
                  { label: '广州', value: 'guangzhou' },
                ],
              },
              {
                prop: 'gender',
                fieldName: '性别',
                fieldType: 'radio',
                options: [
                  { label: '男', value: 'male' },
                  { label: '女', value: 'female' },
                ],
              },
              {
                prop: 'hobbies',
                fieldName: '爱好',
                fieldType: 'checkbox',
                options: [
                  { label: '阅读', value: 'reading' },
                  { label: '运动', value: 'sports' },
                ],
              },
              {
                prop: 'birthday',
                fieldName: '生日',
                fieldType: 'date',
                dateFormat: 'YYYY-MM-DD',
              },
              {
                prop: 'agreement',
                fieldName: '同意协议',
                fieldType: 'switch',
              },
              {
                prop: 'remark',
                fieldName: '备注',
                fieldType: 'textarea',
                showWordLimit: true,
                maxLength: 200,
              },
            ],
            data: [],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'bom-panel_60spo02i5g',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
      ],
    },
  ],
}

export const dialogFormConfig = {
  config: {
    title: '新建',
    width: '520px',
    height: '578px',
    labelWidth: '100',
    formWidth: '420',
  },
}
export const dialogFormItems = [
  {
    prop: 'ncid',
    fieldName: '编码',
    fieldType: 'text',
    required: true,
    minLength: 1,
    maxLength: 32,
    ruleId: 'RULE_101',
  },
  {
    prop: 'name',
    fieldName: '名称',
    fieldType: 'text',
    required: true,
    minLength: 6,
    maxLength: 18,
    showWordLimit: true,
    ruleId: 'RULE_101',
  },
  {
    prop: 'btype',
    fieldName: '类型',
    fieldType: 'businessType',
    required: true,
    options: [
      { label: '北京', value: 'beijing' },
      { label: '上海', value: 'shanghai' },
      { label: '广州', value: 'guangzhou' },
    ],
  },
  {
    prop: 'version',
    fieldName: '版本',
    fieldType: 'text',
    disabled: true,
    required: true,
  },
  {
    prop: 'version1',
    fieldName: '版次',
    fieldType: 'text',
    disabled: true,
    required: true,
  },
  {
    prop: 'remark',
    fieldName: '备注',
    fieldType: 'textarea',
    showWordLimit: true,
    maxLength: 255,
  },
]

export const panelFormItems = [
  {
    name: '基本属性1',
    key: 'base',
    viewImg: 'https://example.com/image1.png',
    formItems: [
      {
        prop: 'code',
        fieldName: '编码',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
      },
      {
        prop: 'name',
        fieldName: '名称',
        fieldType: 'text',
        disabled: false,
        minLength: 6,
        maxLength: 18,
        showWordLimit: true,
        ruleId: 'RULE_101',
      },
      {
        prop: 'revision',
        fieldName: '版本',
        fieldType: 'text',
        disabled: true,
        ruleId: 'RULE_EMAIL',
      },
      {
        prop: 'version',
        fieldName: '版次',
        fieldType: 'text',
        disabled: true,
        ruleId: 'RULE_EMAIL',
      },
      {
        label: ' 责任人 ',
        prop: 'responsiblePerson',
        fieldName: ' 责任人 ',
        fieldType: 'text',
        disabled: false,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入责任人 ',
        value: ' 张无珊 ',
      },
      {
        label: ' 状态 ',
        prop: 'lifecycleStatus.name',
        fieldName: ' 状态 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入状态 ',
        value: ' 工作中 ',
        valueEnum: {
          IN_WORK: '工作中',
        },
      },
      {
        label: ' 锁定人 ',
        prop: 'lockPerson',
        fieldName: ' 锁定人 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入锁定人 ',
        value: ' 张无珊 ',
      },
      {
        label: ' 锁定时间 ',
        prop: 'lockTime',
        fieldName: ' 锁定时间 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入锁定时间 ',
        value: '2025-07-14 09:55:10',
      },
    ],
  },
  {
    name: '设计属性',
    key: 'design',
    formItems: [
      {
        label: '实体曲面面积',
        prop: 'entitySurfaceArea',
        fieldName: '实体曲面面积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入实体曲面面积',
        disabled: true,
        value: '10663.5',
      },
      {
        label: '开放曲面面积',
        prop: 'openSurfaceArea',
        fieldName: '开放曲面面积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入开放曲面面积',
        disabled: true,
        value: '0',
      },
      {
        label: '体积',
        prop: 'volume',
        fieldName: '体积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入体积',
        disabled: true,
        value: '84464.4',
      },
      {
        label: '质量',
        prop: 'mass',
        fieldName: '质量',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入质量',
        disabled: true,
        value: '0.660512',
      },
      {
        label: '材料',
        prop: 'material',
        fieldName: '材料',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入材料',
        disabled: true,
        value: 'SHEETMETAL DEFAULT .125IN K=-33',
      },
      {
        label: '重心',
        prop: 'centerOfGravity',
        fieldName: '重心',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入重心',
        disabled: true,
        value: '52.3446, 6.85254, 25',
      },
    ],
  },
]

export const panelFormItemsMock = {
  ncid: '',
  name: '',
  revision: '',
  version: '',
  responsiblePerson: ' 张无珊 ',
  status: ' 工作中 ',
  lockPerson: ' 张无珊 ',
  lockTime: '2025-07-14 09:55:10',

  // 设计属性
  entitySurfaceArea: '10663.5',
  openSurfaceArea: '0',
  volume: '84464.4',
  mass: '0.660512',
  material: 'SHEETMETAL DEFAULT .125IN K=-33',
  centerOfGravity: '52.3446, 6.85254, 25',
}
// 零部件详情配置
export const partDetailConfig = [
  {
    name: '基础属性',
    key: 'base',
    viewImg: 'https://example.com/image1.png',
    formItems: [
      { prop: 'ncid', fieldName: '零部件编号', value: 'F1005-A01-0' },
      { prop: 'name', fieldName: '零部件名称', value: 'XXx螺旋钉' },
      { prop: 'version', fieldName: '版本', value: 'V00.03' },
      { prop: 'revision', fieldName: '版次', value: 'V00.03' },
      { prop: 'responsiblePerson', fieldName: '责任人', value: '张珊' },
      { prop: 'status', fieldName: '状态', value: '工作中' },
      { prop: 'lockPerson', fieldName: '锁定人', value: '张珊' },
      { prop: 'lockTime', fieldName: '锁定时间', value: '2025-04-01' },
      { prop: 'desc', fieldName: '描述', value: '一零件自身属性' },
    ],
  },
  {
    name: '设计属性',
    key: 'design',
    formItems: [
      {
        prop: 'entitySurfaceArea',
        fieldName: '实体曲面面积 (mm²)',
        value: '10663.5',
      },
      { prop: 'mass', fieldName: '质量 (kg)', value: '0.660512' },
      { prop: 'openSurfaceArea', fieldName: '开放曲面面积 (mm²)', value: '0' },
      { prop: 'material', fieldName: '材料', value: 'SHEETMETAL DEFAULT' },
      { prop: 'volume', fieldName: '体积 (mm³)', value: '84464.4' },
      {
        prop: 'centerOfGravity',
        fieldName: '重心',
        value: '52.3446, 6.85254, 25',
      },
    ],
  },
]
