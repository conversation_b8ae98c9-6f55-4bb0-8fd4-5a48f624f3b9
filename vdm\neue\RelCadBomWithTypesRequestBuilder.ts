/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadBomWithTypes } from './RelCadBomWithTypes';

/**
 * Request builder class for operations supported on the {@link RelCadBomWithTypes} entity.
 */
export class RelCadBomWithTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadBomWithTypes<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadBomWithTypes` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadBomWithTypes` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadBomWithTypes<T>, T> {
    return new GetAllRequestBuilder<RelCadBomWithTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `RelCadBomWithTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadBomWithTypes`.
   */
  create(
    entity: RelCadBomWithTypes<T>
  ): CreateRequestBuilder<RelCadBomWithTypes<T>, T> {
    return new CreateRequestBuilder<RelCadBomWithTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `RelCadBomWithTypes` entity based on its keys.
   * @param ncid Key property. See {@link RelCadBomWithTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadBomWithTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadBomWithTypes<T>, T> {
    return new GetByKeyRequestBuilder<RelCadBomWithTypes<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadBomWithTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadBomWithTypes`.
   */
  update(
    entity: RelCadBomWithTypes<T>
  ): UpdateRequestBuilder<RelCadBomWithTypes<T>, T> {
    return new UpdateRequestBuilder<RelCadBomWithTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadBomWithTypes`.
   * @param ncid Key property. See {@link RelCadBomWithTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadBomWithTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<RelCadBomWithTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadBomWithTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadBomWithTypes` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadBomWithTypes<T>
  ): DeleteRequestBuilder<RelCadBomWithTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<RelCadBomWithTypes<T>, T> {
    return new DeleteRequestBuilder<RelCadBomWithTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof RelCadBomWithTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
