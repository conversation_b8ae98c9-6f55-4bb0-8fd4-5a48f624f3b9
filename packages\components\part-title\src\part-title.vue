<template>
  <div :class="`ne-part-title ${titleClass} ${isHover}`">
    <i
      v-if="partType?.icon"
      :class="`iconfont ${partType?.icon}`"
      :style="partType?.style"
    />
    {{ title }}
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { nePartTitleProps } from './types'

defineOptions({
  name: 'NePartTitle',
})
const props = defineProps(nePartTitleProps)
const partTypeMap: any = {
  NeueCadPart: {
    icon: 'icon-a-lingjian1',
    style: {
      fontSize: `${props.size}px`,
      color: '#92A6D4',
    },
  },
  NeueCadAsm: {
    icon: 'icon-a-zhuangpei1',
    style: {
      fontSize: `${props.size}px`,
      color: '#B370FF',
    },
  },
  NeueTwoDimensionDrawing: {
    icon: 'icon-tree-drawing',
    style: {
      fontSize: `${props.size}px`,
      color: '#EDB581',
    },
  },
}
console.log(props, 'props123')
const titleClass = computed(() => {
  return props.isTitle ? 'ne-part-title__text' : ''
})
const isHover = computed(() => {
  return props.hover ? 'is-hover' : ''
})

const partType = computed(() => {
  return props.type ? partTypeMap[props.type] : undefined
})
</script>
