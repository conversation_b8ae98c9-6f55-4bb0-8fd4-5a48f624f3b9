<template>
  <div :class="`ne-part-title ${titleClass}`">
    <i :class="`iconfont ${partType?.icon}`" :style="partType?.style" />
    {{ title }}
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { PartTypeEmu, nePartTitleProps } from './types'

defineOptions({
  name: 'NePartTitle',
})
const partTypeMap = {
  [PartTypeEmu.NEUEPART]: {
    icon: 'icon-a-lingjian1',
    style: {
      color: '#92A6D4',
    },
  },
  [PartTypeEmu.NEUEASM]: {
    icon: 'icon-a-zhuangpei1',
    style: {
      color: '#B370FF',
    },
  },
}
const props = defineProps(nePartTitleProps)
const titleClass = computed(() => {
  return props.isTitle ? 'ne-part-title__text' : ''
})

const partType = computed(() => {
  console.log(props.type, 123456)
  return partTypeMap[props.type ?? PartTypeEmu.NEUEPART]
})
</script>
