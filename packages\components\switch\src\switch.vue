<template>
  <el-switch
    v-bind="props"
    ref="switchRef"
    :before-change="beforeChange"
    @update:model-value="(value:any) => emit('update:modelValue', value)"
    @change="(value:any) => emit('change', value)"
  >
    <template #active-action>
      <slot name="active-action" />
    </template>
    <template #inactive-action>
      <slot name="inactive-action" />
    </template>
  </el-switch>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { neSwitchProps } from './switch'

const switchRef = ref<InstanceType<
  typeof import('element-plus')['ElSwitch']
> | null>(null)
// 定义 props 和 emits
const props = defineProps(neSwitchProps)

defineOptions({
  name: 'NeSwitch',
})

const focus = () => {
  // 聚焦事件处理
  switchRef.value?.focus()
}

defineExpose({
  focus,
})
const emit = defineEmits(['update:modelValue', 'change'])
</script>
