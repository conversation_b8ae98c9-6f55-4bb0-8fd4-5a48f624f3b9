/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { FromOrToTypes } from './FromOrToTypes';

/**
 * Request builder class for operations supported on the {@link FromOrToTypes} entity.
 */
export class FromOrToTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<FromOrToTypes<T>, T> {
  /**
   * Returns a request builder for querying all `FromOrToTypes` entities.
   * @returns A request builder for creating requests to retrieve all `FromOrToTypes` entities.
   */
  getAll(): GetAllRequestBuilder<FromOrToTypes<T>, T> {
    return new GetAllRequestBuilder<FromOrToTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `FromOrToTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `FromOrToTypes`.
   */
  create(entity: FromOrToTypes<T>): CreateRequestBuilder<FromOrToTypes<T>, T> {
    return new CreateRequestBuilder<FromOrToTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `FromOrToTypes` entity based on its keys.
   * @param ncid Key property. See {@link FromOrToTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `FromOrToTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<FromOrToTypes<T>, T> {
    return new GetByKeyRequestBuilder<FromOrToTypes<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `FromOrToTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `FromOrToTypes`.
   */
  update(entity: FromOrToTypes<T>): UpdateRequestBuilder<FromOrToTypes<T>, T> {
    return new UpdateRequestBuilder<FromOrToTypes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `FromOrToTypes`.
   * @param ncid Key property. See {@link FromOrToTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `FromOrToTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<FromOrToTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `FromOrToTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `FromOrToTypes` by taking the entity as a parameter.
   */
  delete(entity: FromOrToTypes<T>): DeleteRequestBuilder<FromOrToTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<FromOrToTypes<T>, T> {
    return new DeleteRequestBuilder<FromOrToTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof FromOrToTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
