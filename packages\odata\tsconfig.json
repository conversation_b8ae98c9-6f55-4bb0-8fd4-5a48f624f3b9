{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noEmit": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}