{"compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2020", "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "verbatimModuleSyntax": false, "lib": ["ES2020", "DOM"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}