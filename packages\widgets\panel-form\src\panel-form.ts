import { FieldType } from '../../form-item/src/form-item'

interface panelDetail {
  key: string
  name: string
  viewImg?: string
  formItems: FieldType[]
}

// 表单API映射; TODO: 接口需要根据选择的业务类型来匹配到对应的接口名称
// 引擎不支持拼接Url渲染
const formApiMap: Record<
  string,
  { key: string; editApi?: string; updateApi?: string }
> = {
  NeueCadPart: {
    key: 'Part',
    editApi: 'panelFormEditRequestPart',
    updateApi: 'panelFormUpdatePart',
  },
  NeueCadAsm: {
    key: 'Asm',
    editApi: 'panelFormEditRequestAsm',
    updateApi: 'panelFormUpdateAsm',
  },
  drawing: {
    key: 'Draw',
    editApi: 'panelFormEditRequestDraw',
  },
}

export { panelDetail, formApiMap }
