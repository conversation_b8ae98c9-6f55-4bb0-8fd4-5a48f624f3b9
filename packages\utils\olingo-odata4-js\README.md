Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.

-------------------------
## Olingo OData Client for JavaScript
The Olingo OData Client for JavaScript (odatajs) is a library written in JavaScript that enables browser based frontend applications to easily use the OData protocol for communication with application servers.

This library "odatajs-4.0.0.min.js" supports only the OData V4 protocol.

For using the OData protocols V1-V3 please refer to the [datajs library](http://datajs.codeplex.com/)

The odatajs library can be included in any html page with the script tag (for example)
```
<script type="text/javascript" src="./sources/odatajs-4.0.0.min.js"></script>
```
and its features can be used through the `odatajs` namespace (or `window.odatajs`). The odatajs library can be used together with the datajs library which uses the `window.OData` namespace.

For API documentation please see [ODatajs API documentation](http://olingo.apache.org/doc/javascript/apidoc/)

You may also use the documentation and the samples from the [datajs library](http://datajs.codeplex.com/documentation) because the features and API are similar.

## Contribute to Olingo OData Client for JavaScript
If you are interested to contribute to this library please have a look into [Project setup](http://olingo.apache.org/doc/javascript/project-setup.html) and [Build instructions](http://olingo.apache.org/doc/javascript/project-build.html) where you find a manual how you can download the source code and build the odatajs library.

If you intend so please also join the [Olingo developers group](http://olingo.apache.org/support.html) for discussion.
