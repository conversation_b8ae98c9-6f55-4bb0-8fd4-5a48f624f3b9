@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include neb(pro-tab) {
  @include set-component-css-var('pro-tab', $pro-tab);
  height: 100%;
  @include e(header-toolbar) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  @include e(content) {
  }
}
@include b(tabs) {
  @include e(header-vertical) {
    height: 100%;
  }
  @include e(item) {
    height: 44px;
    width: 180px;
    color: var(--el-pro-tab-color);
    @include when(active) {
      background-color: #ccd5ea;
      border-radius: 8px;
      color: var(--el-pro-tab-color);
    }
    span {
      padding-left: 12px;
    }
  }
  @include e(active-bar) {
    display: none;
  }
  @include e(nav-wrap) {
    &::after {
      display: none;
    }
  }
  @include m(left) {
    @include e(item) {
      @include when(left) {
        justify-content: flex-start;
      }
    }
  }
}
