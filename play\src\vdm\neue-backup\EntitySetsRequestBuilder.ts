/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EntitySets } from './EntitySets';

/**
 * Request builder class for operations supported on the {@link EntitySets} entity.
 */
export class EntitySetsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntitySets<T>, T> {
  /**
   * Returns a request builder for querying all `EntitySets` entities.
   * @returns A request builder for creating requests to retrieve all `EntitySets` entities.
   */
  getAll(): GetAllRequestBuilder<EntitySets<T>, T> {
    return new GetAllRequestBuilder<EntitySets<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `EntitySets` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntitySets`.
   */
  create(entity: EntitySets<T>): CreateRequestBuilder<EntitySets<T>, T> {
    return new CreateRequestBuilder<EntitySets<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `EntitySets` entity based on its keys.
   * @param ncid Key property. See {@link EntitySets.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntitySets` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntitySets<T>, T> {
    return new GetByKeyRequestBuilder<EntitySets<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `EntitySets`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntitySets`.
   */
  update(entity: EntitySets<T>): UpdateRequestBuilder<EntitySets<T>, T> {
    return new UpdateRequestBuilder<EntitySets<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `EntitySets`.
   * @param ncid Key property. See {@link EntitySets.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntitySets`.
   */
  delete(ncid: string): DeleteRequestBuilder<EntitySets<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntitySets`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntitySets` by taking the entity as a parameter.
   */
  delete(entity: EntitySets<T>): DeleteRequestBuilder<EntitySets<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<EntitySets<T>, T> {
    return new DeleteRequestBuilder<EntitySets<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntitySets
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
