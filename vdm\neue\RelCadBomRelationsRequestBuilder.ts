/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadBomRelations } from './RelCadBomRelations';

/**
 * Request builder class for operations supported on the {@link RelCadBomRelations} entity.
 */
export class RelCadBomRelationsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadBomRelations<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadBomRelations` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadBomRelations` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadBomRelations<T>, T> {
    return new GetAllRequestBuilder<RelCadBomRelations<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `RelCadBomRelations` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadBomRelations`.
   */
  create(
    entity: RelCadBomRelations<T>
  ): CreateRequestBuilder<RelCadBomRelations<T>, T> {
    return new CreateRequestBuilder<RelCadBomRelations<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `RelCadBomRelations` entity based on its keys.
   * @param ncid Key property. See {@link RelCadBomRelations.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadBomRelations` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadBomRelations<T>, T> {
    return new GetByKeyRequestBuilder<RelCadBomRelations<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadBomRelations`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadBomRelations`.
   */
  update(
    entity: RelCadBomRelations<T>
  ): UpdateRequestBuilder<RelCadBomRelations<T>, T> {
    return new UpdateRequestBuilder<RelCadBomRelations<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadBomRelations`.
   * @param ncid Key property. See {@link RelCadBomRelations.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadBomRelations`.
   */
  delete(ncid: string): DeleteRequestBuilder<RelCadBomRelations<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadBomRelations`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadBomRelations` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadBomRelations<T>
  ): DeleteRequestBuilder<RelCadBomRelations<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<RelCadBomRelations<T>, T> {
    return new DeleteRequestBuilder<RelCadBomRelations<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof RelCadBomRelations
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
