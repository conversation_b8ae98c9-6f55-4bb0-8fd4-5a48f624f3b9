/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { MappingConfigs } from './MappingConfigs';
import { MappingConfigsRequestBuilder } from './MappingConfigsRequestBuilder';
import { MappingConfigDirectionEnum } from './MappingConfigDirectionEnum';
import { MappingConfigPartTypeEnum } from './MappingConfigPartTypeEnum';
import { MappingConfigToolEnum } from './MappingConfigToolEnum';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  EnumField,
  OrderableEdmTypeField
} from '@sap-cloud-sdk/odata-v4';
export class MappingConfigsApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<MappingConfigs<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): MappingConfigsApi<DeSerializersT> {
    return new MappingConfigsApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = MappingConfigs;

  requestBuilder(): MappingConfigsRequestBuilder<DeSerializersT> {
    return new MappingConfigsRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<
    MappingConfigs<DeSerializersT>,
    DeSerializersT
  > {
    return entityBuilder<MappingConfigs<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<MappingConfigs<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof MappingConfigs, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(MappingConfigs, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    DIRECTION: EnumField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      MappingConfigDirectionEnum,
      false,
      true
    >;
    ON_CAX: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.Boolean',
      true,
      true
    >;
    CDP_PROPERTY: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    PART_TYPE: EnumField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      MappingConfigPartTypeEnum,
      false,
      true
    >;
    DRAWING_SHEET_AREA: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    NAME: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CAX_PROPERTY: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    TOOL: EnumField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      MappingConfigToolEnum,
      false,
      true
    >;
    NCID: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      MappingConfigs<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<MappingConfigs<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link direction} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DIRECTION: fieldBuilder.buildEnumField(
          'direction',
          MappingConfigDirectionEnum,
          false
        ),
        /**
         * Static representation of the {@link onCax} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        ON_CAX: fieldBuilder.buildEdmTypeField('onCax', 'Edm.Boolean', true),
        /**
         * Static representation of the {@link cdpProperty} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CDP_PROPERTY: fieldBuilder.buildEdmTypeField(
          'cdpProperty',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link partType} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        PART_TYPE: fieldBuilder.buildEnumField(
          'partType',
          MappingConfigPartTypeEnum,
          false
        ),
        /**
         * Static representation of the {@link drawingSheetArea} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DRAWING_SHEET_AREA: fieldBuilder.buildEdmTypeField(
          'drawingSheetArea',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', false),
        /**
         * Static representation of the {@link caxProperty} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CAX_PROPERTY: fieldBuilder.buildEdmTypeField(
          'caxProperty',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link tool} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        TOOL: fieldBuilder.buildEnumField('tool', MappingConfigToolEnum, false),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', MappingConfigs)
      };
    }

    return this._schema;
  }
}
