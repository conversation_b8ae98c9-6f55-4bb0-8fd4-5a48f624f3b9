/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { TypeDefinitionsApi } from './TypeDefinitionsApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "TypeDefinitions" of service "neue".
 */
export class TypeDefinitions<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements TypeDefinitionsType<T>
{
  /**
   * Technical entity name for TypeDefinitions.
   */
  static override _entityName = 'TypeDefinitions';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the TypeDefinitions entity.
   */
  static _keys = ['ncid'];
  /**
   * Type Definition Code.
   */
  declare typeDefinitionCode: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   */
  declare name: DeserializedType<T, 'Edm.String'>;
  /**
   * Description.
   */
  declare description: DeserializedType<T, 'Edm.String'>;
  /**
   * Underlying Type.
   * @nullable
   */
  declare underlyingType?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Max Length.
   * @nullable
   */
  declare maxLength?: DeserializedType<T, 'Edm.Int32'> | null;
  /**
   * Is Unicode.
   * @nullable
   */
  declare isUnicode?: DeserializedType<T, 'Edm.Boolean'> | null;
  /**
   * Srid.
   * @nullable
   */
  declare srid?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Precision.
   * @nullable
   */
  declare precision?: DeserializedType<T, 'Edm.Int32'> | null;
  /**
   * Scale.
   * @nullable
   */
  declare scale?: DeserializedType<T, 'Edm.Int32'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: TypeDefinitionsApi<T>) {
    super(_entityApi);
  }
}

export interface TypeDefinitionsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  typeDefinitionCode: DeserializedType<T, 'Edm.String'>;
  name: DeserializedType<T, 'Edm.String'>;
  description: DeserializedType<T, 'Edm.String'>;
  underlyingType?: DeserializedType<T, 'Edm.String'> | null;
  maxLength?: DeserializedType<T, 'Edm.Int32'> | null;
  isUnicode?: DeserializedType<T, 'Edm.Boolean'> | null;
  srid?: DeserializedType<T, 'Edm.String'> | null;
  precision?: DeserializedType<T, 'Edm.Int32'> | null;
  scale?: DeserializedType<T, 'Edm.Int32'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
