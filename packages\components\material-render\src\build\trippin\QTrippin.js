"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QResetDataSource = exports.QGetNearestAirport = exports.QGetPersonWithMostFriends = exports.qEventLocation = exports.QEventLocation = exports.qAirportLocation = exports.QAirportLocation = exports.qCity = exports.QCity = exports.qLocation = exports.QLocation = exports.QLocationBaseType = exports.qManager = exports.QManager = exports.qEmployee = exports.QEmployee = exports.qFlight = exports.QFlight = exports.qPublicTransportation = exports.QPublicTransportation = exports.QPublicTransportationBaseType = exports.qEvent = exports.QEvent = exports.QPlanItemId = exports.qPlanItem = exports.QPlanItem = exports.QPlanItemBaseType = exports.Trip_QGetInvolvedPeople = exports.QTripId = exports.qTrip = exports.QTrip = exports.QAirportId = exports.qAirport = exports.QAirport = exports.QAirlineId = exports.qAirline = exports.QAirline = exports.Person_QShareTrip = exports.Person_QUpdateLastName = exports.Person_QGetFriendsTrips = exports.Person_QGetFavoriteAirline = exports.QPersonId = exports.qPerson = exports.QPerson = exports.QPersonBaseType = void 0;
// @ts-nocheck
var odata_query_objects_1 = require("@odata2ts/odata-query-objects");
var TrippinModel_1 = require("./TrippinModel");
var QPersonBaseType = /** @class */ (function (_super) {
    __extends(QPersonBaseType, _super);
    function QPersonBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.UserName = new odata_query_objects_1.QStringPath(_this.withPrefix("UserName"));
        _this.FirstName = new odata_query_objects_1.QStringPath(_this.withPrefix("FirstName"));
        _this.LastName = new odata_query_objects_1.QStringPath(_this.withPrefix("LastName"));
        _this.MiddleName = new odata_query_objects_1.QStringPath(_this.withPrefix("MiddleName"));
        _this.Gender = new odata_query_objects_1.QEnumPath(_this.withPrefix("Gender"), TrippinModel_1.PersonGender);
        _this.Age = new odata_query_objects_1.QNumberPath(_this.withPrefix("Age"));
        _this.Emails = new odata_query_objects_1.QCollectionPath(_this.withPrefix("Emails"), function () { return odata_query_objects_1.QStringCollection; });
        _this.AddressInfo = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("AddressInfo"), function () { return QLocation; });
        _this.HomeAddress = new odata_query_objects_1.QEntityPath(_this.withPrefix("HomeAddress"), function () { return QLocation; });
        _this.FavoriteFeature = new odata_query_objects_1.QEnumPath(_this.withPrefix("FavoriteFeature"), TrippinModel_1.Feature);
        _this.Features = new odata_query_objects_1.QEnumCollectionPath(_this.withPrefix("Features"), TrippinModel_1.Feature);
        _this.Friends = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("Friends"), function () { return QPerson; });
        _this.BestFriend = new odata_query_objects_1.QEntityPath(_this.withPrefix("BestFriend"), function () { return QPerson; });
        _this.Trips = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("Trips"), function () { return QTrip; });
        return _this;
    }
    return QPersonBaseType;
}(odata_query_objects_1.QueryObject));
exports.QPersonBaseType = QPersonBaseType;
var QPerson = /** @class */ (function (_super) {
    __extends(QPerson, _super);
    function QPerson() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "Trippin.Employee": "QEmployee", "Trippin.Manager": "QManager" };
        return _this;
    }
    Object.defineProperty(QPerson.prototype, "QEmployee_Cost", {
        get: function () {
            return this.__asQEmployee().Cost;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPerson.prototype, "QEmployee_Peers", {
        get: function () {
            return this.__asQEmployee().Peers;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPerson.prototype, "QManager_Budget", {
        get: function () {
            return this.__asQManager().Budget;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPerson.prototype, "QManager_BossOffice", {
        get: function () {
            return this.__asQManager().BossOffice;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPerson.prototype, "QManager_DirectReports", {
        get: function () {
            return this.__asQManager().DirectReports;
        },
        enumerable: false,
        configurable: true
    });
    QPerson.prototype.__asQEmployee = function () {
        return new QEmployee(this.withPrefix("Trippin.Employee"));
    };
    QPerson.prototype.__asQManager = function () {
        return new QManager(this.withPrefix("Trippin.Manager"));
    };
    return QPerson;
}(QPersonBaseType));
exports.QPerson = QPerson;
Object.defineProperties(QPerson.prototype, { QEmployee_Cost: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QEmployee_Peers: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QManager_Budget: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QManager_BossOffice: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QManager_DirectReports: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPerson = new QPerson();
var QPersonId = /** @class */ (function (_super) {
    __extends(QPersonId, _super);
    function QPersonId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QStringParam("UserName")];
        return _this;
    }
    QPersonId.prototype.getParams = function () {
        return this.params;
    };
    return QPersonId;
}(odata_query_objects_1.QId));
exports.QPersonId = QPersonId;
var Person_QGetFavoriteAirline = /** @class */ (function (_super) {
    __extends(Person_QGetFavoriteAirline, _super);
    function Person_QGetFavoriteAirline() {
        var _this = _super.call(this, "Trippin.GetFavoriteAirline", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX, new odata_query_objects_1.QComplexParam("NONE", new QAirline))) || this;
        _this.params = [];
        return _this;
    }
    Person_QGetFavoriteAirline.prototype.getParams = function () {
        return this.params;
    };
    Person_QGetFavoriteAirline.prototype.buildUrl = function (notEncoded) {
        if (notEncoded === void 0) { notEncoded = false; }
        return _super.prototype.buildUrl.call(this, undefined, notEncoded);
    };
    return Person_QGetFavoriteAirline;
}(odata_query_objects_1.QFunction));
exports.Person_QGetFavoriteAirline = Person_QGetFavoriteAirline;
var Person_QGetFriendsTrips = /** @class */ (function (_super) {
    __extends(Person_QGetFriendsTrips, _super);
    function Person_QGetFriendsTrips() {
        var _this = _super.call(this, "Trippin.GetFriendsTrips", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX_COLLECTION, new odata_query_objects_1.QComplexParam("NONE", new QTrip))) || this;
        _this.params = [new odata_query_objects_1.QStringParam("userName")];
        return _this;
    }
    Person_QGetFriendsTrips.prototype.getParams = function () {
        return this.params;
    };
    return Person_QGetFriendsTrips;
}(odata_query_objects_1.QFunction));
exports.Person_QGetFriendsTrips = Person_QGetFriendsTrips;
var Person_QUpdateLastName = /** @class */ (function (_super) {
    __extends(Person_QUpdateLastName, _super);
    function Person_QUpdateLastName() {
        var _this = _super.call(this, "Trippin.UpdateLastName") || this;
        _this.params = [new odata_query_objects_1.QStringParam("lastName")];
        return _this;
    }
    Person_QUpdateLastName.prototype.getParams = function () {
        return this.params;
    };
    return Person_QUpdateLastName;
}(odata_query_objects_1.QAction));
exports.Person_QUpdateLastName = Person_QUpdateLastName;
var Person_QShareTrip = /** @class */ (function (_super) {
    __extends(Person_QShareTrip, _super);
    function Person_QShareTrip() {
        var _this = _super.call(this, "Trippin.ShareTrip") || this;
        _this.params = [new odata_query_objects_1.QStringParam("userName"), new odata_query_objects_1.QNumberParam("tripId")];
        return _this;
    }
    Person_QShareTrip.prototype.getParams = function () {
        return this.params;
    };
    return Person_QShareTrip;
}(odata_query_objects_1.QAction));
exports.Person_QShareTrip = Person_QShareTrip;
var QAirline = /** @class */ (function (_super) {
    __extends(QAirline, _super);
    function QAirline() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.AirlineCode = new odata_query_objects_1.QStringPath(_this.withPrefix("AirlineCode"));
        _this.Name = new odata_query_objects_1.QStringPath(_this.withPrefix("Name"));
        return _this;
    }
    return QAirline;
}(odata_query_objects_1.QueryObject));
exports.QAirline = QAirline;
exports.qAirline = new QAirline();
var QAirlineId = /** @class */ (function (_super) {
    __extends(QAirlineId, _super);
    function QAirlineId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QStringParam("AirlineCode")];
        return _this;
    }
    QAirlineId.prototype.getParams = function () {
        return this.params;
    };
    return QAirlineId;
}(odata_query_objects_1.QId));
exports.QAirlineId = QAirlineId;
var QAirport = /** @class */ (function (_super) {
    __extends(QAirport, _super);
    function QAirport() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Name = new odata_query_objects_1.QStringPath(_this.withPrefix("Name"));
        _this.IcaoCode = new odata_query_objects_1.QStringPath(_this.withPrefix("IcaoCode"));
        _this.IataCode = new odata_query_objects_1.QStringPath(_this.withPrefix("IataCode"));
        _this.Location = new odata_query_objects_1.QEntityPath(_this.withPrefix("Location"), function () { return QAirportLocation; });
        return _this;
    }
    return QAirport;
}(odata_query_objects_1.QueryObject));
exports.QAirport = QAirport;
exports.qAirport = new QAirport();
var QAirportId = /** @class */ (function (_super) {
    __extends(QAirportId, _super);
    function QAirportId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QStringParam("IcaoCode")];
        return _this;
    }
    QAirportId.prototype.getParams = function () {
        return this.params;
    };
    return QAirportId;
}(odata_query_objects_1.QId));
exports.QAirportId = QAirportId;
var QTrip = /** @class */ (function (_super) {
    __extends(QTrip, _super);
    function QTrip() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.TripId = new odata_query_objects_1.QNumberPath(_this.withPrefix("TripId"));
        _this.ShareId = new odata_query_objects_1.QGuidPath(_this.withPrefix("ShareId"));
        _this.Name = new odata_query_objects_1.QStringPath(_this.withPrefix("Name"));
        _this.Budget = new odata_query_objects_1.QNumberPath(_this.withPrefix("Budget"));
        _this.Description = new odata_query_objects_1.QStringPath(_this.withPrefix("Description"));
        _this.Tags = new odata_query_objects_1.QCollectionPath(_this.withPrefix("Tags"), function () { return odata_query_objects_1.QStringCollection; });
        _this.StartsAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("StartsAt"));
        _this.EndsAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("EndsAt"));
        _this.PlanItems = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("PlanItems"), function () { return QPlanItem; });
        return _this;
    }
    return QTrip;
}(odata_query_objects_1.QueryObject));
exports.QTrip = QTrip;
exports.qTrip = new QTrip();
var QTripId = /** @class */ (function (_super) {
    __extends(QTripId, _super);
    function QTripId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QNumberParam("TripId")];
        return _this;
    }
    QTripId.prototype.getParams = function () {
        return this.params;
    };
    return QTripId;
}(odata_query_objects_1.QId));
exports.QTripId = QTripId;
var Trip_QGetInvolvedPeople = /** @class */ (function (_super) {
    __extends(Trip_QGetInvolvedPeople, _super);
    function Trip_QGetInvolvedPeople() {
        var _this = _super.call(this, "Trippin.GetInvolvedPeople", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX_COLLECTION, new odata_query_objects_1.QComplexParam("NONE", new QPerson))) || this;
        _this.params = [];
        return _this;
    }
    Trip_QGetInvolvedPeople.prototype.getParams = function () {
        return this.params;
    };
    Trip_QGetInvolvedPeople.prototype.buildUrl = function (notEncoded) {
        if (notEncoded === void 0) { notEncoded = false; }
        return _super.prototype.buildUrl.call(this, undefined, notEncoded);
    };
    return Trip_QGetInvolvedPeople;
}(odata_query_objects_1.QFunction));
exports.Trip_QGetInvolvedPeople = Trip_QGetInvolvedPeople;
var QPlanItemBaseType = /** @class */ (function (_super) {
    __extends(QPlanItemBaseType, _super);
    function QPlanItemBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.PlanItemId = new odata_query_objects_1.QNumberPath(_this.withPrefix("PlanItemId"));
        _this.ConfirmationCode = new odata_query_objects_1.QStringPath(_this.withPrefix("ConfirmationCode"));
        _this.StartsAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("StartsAt"));
        _this.EndsAt = new odata_query_objects_1.QDateTimeOffsetPath(_this.withPrefix("EndsAt"));
        _this.Duration = new odata_query_objects_1.QStringPath(_this.withPrefix("Duration"));
        return _this;
    }
    return QPlanItemBaseType;
}(odata_query_objects_1.QueryObject));
exports.QPlanItemBaseType = QPlanItemBaseType;
var QPlanItem = /** @class */ (function (_super) {
    __extends(QPlanItem, _super);
    function QPlanItem() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "Trippin.Event": "QEvent", "Trippin.PublicTransportation": "QPublicTransportation", "Trippin.Flight": "QFlight" };
        return _this;
    }
    Object.defineProperty(QPlanItem.prototype, "QEvent_OccursAt", {
        get: function () {
            return this.__asQEvent().OccursAt;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QEvent_Description", {
        get: function () {
            return this.__asQEvent().Description;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QPublicTransportation_SeatNumber", {
        get: function () {
            return this.__asQPublicTransportation().SeatNumber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QFlight_FlightNumber", {
        get: function () {
            return this.__asQFlight().FlightNumber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QFlight_Airline", {
        get: function () {
            return this.__asQFlight().Airline;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QFlight_From", {
        get: function () {
            return this.__asQFlight().From;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPlanItem.prototype, "QFlight_To", {
        get: function () {
            return this.__asQFlight().To;
        },
        enumerable: false,
        configurable: true
    });
    QPlanItem.prototype.__asQEvent = function () {
        return new QEvent(this.withPrefix("Trippin.Event"));
    };
    QPlanItem.prototype.__asQPublicTransportation = function () {
        return new QPublicTransportation(this.withPrefix("Trippin.PublicTransportation"));
    };
    QPlanItem.prototype.__asQFlight = function () {
        return new QFlight(this.withPrefix("Trippin.Flight"));
    };
    return QPlanItem;
}(QPlanItemBaseType));
exports.QPlanItem = QPlanItem;
Object.defineProperties(QPlanItem.prototype, { QEvent_OccursAt: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QEvent_Description: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QPublicTransportation_SeatNumber: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_FlightNumber: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_Airline: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_From: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_To: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPlanItem = new QPlanItem();
var QPlanItemId = /** @class */ (function (_super) {
    __extends(QPlanItemId, _super);
    function QPlanItemId() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.params = [new odata_query_objects_1.QNumberParam("PlanItemId")];
        return _this;
    }
    QPlanItemId.prototype.getParams = function () {
        return this.params;
    };
    return QPlanItemId;
}(odata_query_objects_1.QId));
exports.QPlanItemId = QPlanItemId;
var QEvent = /** @class */ (function (_super) {
    __extends(QEvent, _super);
    function QEvent() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.OccursAt = new odata_query_objects_1.QEntityPath(_this.withPrefix("OccursAt"), function () { return QEventLocation; });
        _this.Description = new odata_query_objects_1.QStringPath(_this.withPrefix("Description"));
        return _this;
    }
    return QEvent;
}(QPlanItemBaseType));
exports.QEvent = QEvent;
exports.qEvent = new QEvent();
var QPublicTransportationBaseType = /** @class */ (function (_super) {
    __extends(QPublicTransportationBaseType, _super);
    function QPublicTransportationBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.SeatNumber = new odata_query_objects_1.QStringPath(_this.withPrefix("SeatNumber"));
        return _this;
    }
    return QPublicTransportationBaseType;
}(QPlanItemBaseType));
exports.QPublicTransportationBaseType = QPublicTransportationBaseType;
var QPublicTransportation = /** @class */ (function (_super) {
    __extends(QPublicTransportation, _super);
    function QPublicTransportation() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "Trippin.Flight": "QFlight" };
        return _this;
    }
    Object.defineProperty(QPublicTransportation.prototype, "QFlight_FlightNumber", {
        get: function () {
            return this.__asQFlight().FlightNumber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPublicTransportation.prototype, "QFlight_Airline", {
        get: function () {
            return this.__asQFlight().Airline;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPublicTransportation.prototype, "QFlight_From", {
        get: function () {
            return this.__asQFlight().From;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QPublicTransportation.prototype, "QFlight_To", {
        get: function () {
            return this.__asQFlight().To;
        },
        enumerable: false,
        configurable: true
    });
    QPublicTransportation.prototype.__asQFlight = function () {
        return new QFlight(this.withPrefix("Trippin.Flight"));
    };
    return QPublicTransportation;
}(QPublicTransportationBaseType));
exports.QPublicTransportation = QPublicTransportation;
Object.defineProperties(QPublicTransportation.prototype, { QFlight_FlightNumber: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_Airline: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_From: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QFlight_To: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qPublicTransportation = new QPublicTransportation();
var QFlight = /** @class */ (function (_super) {
    __extends(QFlight, _super);
    function QFlight() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.FlightNumber = new odata_query_objects_1.QStringPath(_this.withPrefix("FlightNumber"));
        _this.Airline = new odata_query_objects_1.QEntityPath(_this.withPrefix("Airline"), function () { return QAirline; });
        _this.From = new odata_query_objects_1.QEntityPath(_this.withPrefix("From"), function () { return QAirport; });
        _this.To = new odata_query_objects_1.QEntityPath(_this.withPrefix("To"), function () { return QAirport; });
        return _this;
    }
    return QFlight;
}(QPublicTransportationBaseType));
exports.QFlight = QFlight;
exports.qFlight = new QFlight();
var QEmployee = /** @class */ (function (_super) {
    __extends(QEmployee, _super);
    function QEmployee() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Cost = new odata_query_objects_1.QNumberPath(_this.withPrefix("Cost"));
        _this.Peers = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("Peers"), function () { return QPerson; });
        return _this;
    }
    return QEmployee;
}(QPersonBaseType));
exports.QEmployee = QEmployee;
exports.qEmployee = new QEmployee();
var QManager = /** @class */ (function (_super) {
    __extends(QManager, _super);
    function QManager() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Budget = new odata_query_objects_1.QNumberPath(_this.withPrefix("Budget"));
        _this.BossOffice = new odata_query_objects_1.QEntityPath(_this.withPrefix("BossOffice"), function () { return QLocation; });
        _this.DirectReports = new odata_query_objects_1.QEntityCollectionPath(_this.withPrefix("DirectReports"), function () { return QPerson; });
        return _this;
    }
    return QManager;
}(QPersonBaseType));
exports.QManager = QManager;
exports.qManager = new QManager();
var QLocationBaseType = /** @class */ (function (_super) {
    __extends(QLocationBaseType, _super);
    function QLocationBaseType() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Address = new odata_query_objects_1.QStringPath(_this.withPrefix("Address"));
        _this.City = new odata_query_objects_1.QEntityPath(_this.withPrefix("City"), function () { return QCity; });
        return _this;
    }
    return QLocationBaseType;
}(odata_query_objects_1.QueryObject));
exports.QLocationBaseType = QLocationBaseType;
var QLocation = /** @class */ (function (_super) {
    __extends(QLocation, _super);
    function QLocation() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__subtypeMapping = { "Trippin.AirportLocation": "QAirportLocation", "Trippin.EventLocation": "QEventLocation" };
        return _this;
    }
    Object.defineProperty(QLocation.prototype, "QAirportLocation_Loc", {
        get: function () {
            return this.__asQAirportLocation().Loc;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(QLocation.prototype, "QEventLocation_BuildingInfo", {
        get: function () {
            return this.__asQEventLocation().BuildingInfo;
        },
        enumerable: false,
        configurable: true
    });
    QLocation.prototype.__asQAirportLocation = function () {
        return new QAirportLocation(this.withPrefix("Trippin.AirportLocation"));
    };
    QLocation.prototype.__asQEventLocation = function () {
        return new QEventLocation(this.withPrefix("Trippin.EventLocation"));
    };
    return QLocation;
}(QLocationBaseType));
exports.QLocation = QLocation;
Object.defineProperties(QLocation.prototype, { QAirportLocation_Loc: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION, QEventLocation_BuildingInfo: odata_query_objects_1.ENUMERABLE_PROP_DEFINITION });
exports.qLocation = new QLocation();
var QCity = /** @class */ (function (_super) {
    __extends(QCity, _super);
    function QCity() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Name = new odata_query_objects_1.QStringPath(_this.withPrefix("Name"));
        _this.CountryRegion = new odata_query_objects_1.QStringPath(_this.withPrefix("CountryRegion"));
        _this.Region = new odata_query_objects_1.QStringPath(_this.withPrefix("Region"));
        return _this;
    }
    return QCity;
}(odata_query_objects_1.QueryObject));
exports.QCity = QCity;
exports.qCity = new QCity();
var QAirportLocation = /** @class */ (function (_super) {
    __extends(QAirportLocation, _super);
    function QAirportLocation() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.Loc = new odata_query_objects_1.QStringPath(_this.withPrefix("Loc"));
        return _this;
    }
    return QAirportLocation;
}(QLocationBaseType));
exports.QAirportLocation = QAirportLocation;
exports.qAirportLocation = new QAirportLocation();
var QEventLocation = /** @class */ (function (_super) {
    __extends(QEventLocation, _super);
    function QEventLocation() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.BuildingInfo = new odata_query_objects_1.QStringPath(_this.withPrefix("BuildingInfo"));
        return _this;
    }
    return QEventLocation;
}(QLocationBaseType));
exports.QEventLocation = QEventLocation;
exports.qEventLocation = new QEventLocation();
var QGetPersonWithMostFriends = /** @class */ (function (_super) {
    __extends(QGetPersonWithMostFriends, _super);
    function QGetPersonWithMostFriends() {
        var _this = _super.call(this, "GetPersonWithMostFriends", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX, new odata_query_objects_1.QComplexParam("NONE", new QPerson))) || this;
        _this.params = [];
        return _this;
    }
    QGetPersonWithMostFriends.prototype.getParams = function () {
        return this.params;
    };
    QGetPersonWithMostFriends.prototype.buildUrl = function (notEncoded) {
        if (notEncoded === void 0) { notEncoded = false; }
        return _super.prototype.buildUrl.call(this, undefined, notEncoded);
    };
    return QGetPersonWithMostFriends;
}(odata_query_objects_1.QFunction));
exports.QGetPersonWithMostFriends = QGetPersonWithMostFriends;
var QGetNearestAirport = /** @class */ (function (_super) {
    __extends(QGetNearestAirport, _super);
    function QGetNearestAirport() {
        var _this = _super.call(this, "GetNearestAirport", new odata_query_objects_1.OperationReturnType(odata_query_objects_1.ReturnTypes.COMPLEX, new odata_query_objects_1.QComplexParam("NONE", new QAirport))) || this;
        _this.params = [new odata_query_objects_1.QNumberParam("lat"), new odata_query_objects_1.QNumberParam("lon")];
        return _this;
    }
    QGetNearestAirport.prototype.getParams = function () {
        return this.params;
    };
    return QGetNearestAirport;
}(odata_query_objects_1.QFunction));
exports.QGetNearestAirport = QGetNearestAirport;
var QResetDataSource = /** @class */ (function (_super) {
    __extends(QResetDataSource, _super);
    function QResetDataSource() {
        var _this = _super.call(this, "ResetDataSource") || this;
        _this.params = [];
        return _this;
    }
    QResetDataSource.prototype.getParams = function () {
        return this.params;
    };
    return QResetDataSource;
}(odata_query_objects_1.QAction));
exports.QResetDataSource = QResetDataSource;
