// registry.ts
// ⬇️ Glob 引入所有组件
import { Component } from 'vue'
import bomTable from './bom-table'
import basicForm from './basic-form'
import businessType from './business-type'
import dialogForm from './dialog-form'
import PanelForm from './panel-form'
import basicDetail from './basic-detail'

const modules = {
  bomTable,
  basicForm,
  businessType,
  dialogForm,
  PanelForm,
  basicDetail,
}
const componentMap: Record<string, Component> = {}

for (const path in modules) {
  const match = path.match(/\/widgets\/(.+)\.vue$/)
  if (match) {
    const raw = match[1] // 如 form/Input
    const name = raw.split('/').pop()!.toLowerCase() // → input
    componentMap[name] = modules[path as keyof typeof modules]
  }
}

/**
 * 获取懒加载组件的函数
 * @param type 组件类型，比如 'table', 'input'
 */
export function getComponentLoader(type: string) {
  return componentMap[type.toLowerCase()] || null
}
