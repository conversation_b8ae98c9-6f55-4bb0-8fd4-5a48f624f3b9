/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadPartTwoDimensionDrawingWithTypes } from './RelCadPartTwoDimensionDrawingWithTypes';

/**
 * Request builder class for operations supported on the {@link RelCadPartTwoDimensionDrawingWithTypes} entity.
 */
export class RelCadPartTwoDimensionDrawingWithTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadPartTwoDimensionDrawingWithTypes` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadPartTwoDimensionDrawingWithTypes` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
    return new GetAllRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<T>,
      T
    >(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `RelCadPartTwoDimensionDrawingWithTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   */
  create(
    entity: RelCadPartTwoDimensionDrawingWithTypes<T>
  ): CreateRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
    return new CreateRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<T>,
      T
    >(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `RelCadPartTwoDimensionDrawingWithTypes` entity based on its keys.
   * @param ncid Key property. See {@link RelCadPartTwoDimensionDrawingWithTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadPartTwoDimensionDrawingWithTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
    return new GetByKeyRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<T>,
      T
    >(this.entityApi, { ncid: ncid });
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   */
  update(
    entity: RelCadPartTwoDimensionDrawingWithTypes<T>
  ): UpdateRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
    return new UpdateRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<T>,
      T
    >(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   * @param ncid Key property. See {@link RelCadPartTwoDimensionDrawingWithTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   */
  delete(
    ncid: string
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadPartTwoDimensionDrawingWithTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadPartTwoDimensionDrawingWithTypes` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadPartTwoDimensionDrawingWithTypes<T>
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingWithTypes<T>, T> {
    return new DeleteRequestBuilder<
      RelCadPartTwoDimensionDrawingWithTypes<T>,
      T
    >(
      this.entityApi,
      ncidOrEntity instanceof RelCadPartTwoDimensionDrawingWithTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
