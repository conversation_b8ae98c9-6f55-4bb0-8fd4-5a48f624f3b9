<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
-->

<%@ ServiceHost Language="C#" Factory="Microsoft.OData.Service.DataServiceHostFactory, Microsoft.OData.Service, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Service="DataJS.Tests.EpmDataService" %>


namespace DataJS.Tests
{
    using System;
    using System.Collections.Generic;
    using Microsoft.OData.Service;
    using System.Linq;
    using System.ServiceModel.Web;

    /// <summary>
    /// A data service that uses EPM
    /// </summary>
    [System.ServiceModel.ServiceBehavior(IncludeExceptionDetailInFaults = true)]
    public class EpmDataService : DataService<EpmDataSource>
    {
        // This method is called only once to initialize service-wide policies.
        public static void InitializeService(DataServiceConfiguration config)
        {
            config.SetEntitySetAccessRule("*", EntitySetRights.All);
            config.SetServiceOperationAccessRule("*", ServiceOperationRights.All);
            config.DataServiceBehavior.MaxProtocolVersion = Microsoft.OData.Client.ODataProtocolVersion.V4;
            config.UseVerboseErrors = true;

        }
        
        [WebInvoke]
        public void ResetData()
        {
            this.CurrentDataSource.ResetData();
        }
    }

    public class EpmDataSource : ReflectionDataContext, IUpdatable
    {
        private static bool dataInitialized;

        public IQueryable<MappedEntry> MappedEntries
        {
            get { return this.GetResourceSetEntities<MappedEntry>("MappedEntries").AsQueryable(); }
        }

        public IQueryable<ReplicatedEntry> ReplicatedEntries
        {
            get { return this.GetResourceSetEntities<ReplicatedEntry>("ReplicatedEntries").AsQueryable(); }
        }

        public IQueryable<BaseEntry> HierarchicalEntries
        {
            get { return this.GetResourceSetEntities<BaseEntry>("HierarchicalEntries").AsQueryable(); }
        }

        public void ResetData()
        {
            this.ClearData();

            MappedEntry[] mappedEntries = new MappedEntry[]
            {
                new MappedEntry
                {
                    ID = 0,
                    UnmappedField = "Unmapped0",
                    Author = new Author
                    {
                        Email = "AuthorEmail0",
                        Name = "AuthorName0",
                        Uri = "http://www.example.com/AuthorUri",
                        Contributor = new Contributor
                        {
                            Email = "ContributorEmail0",
                            Name = "ContributorName0",
                            Uri = "http://www.example.com/ContributorUri",
                        },
                    },
                    Published = "2000-01-01T00:00:00-08:00",
                    Rights = "Rights0",
                    Summary = "<xmlElement xmlns=\"http://www.example.org/dummy\" attr=\"value0\">Summary0</xmlElement>",
                    Title = "Title<b>0</b>",
                    Updated = "2000-01-01T00:00:00-08:00",
                    CustomElement = "CustomElement0",
                    CustomAttribute = "CustomAttribute0",
                    NestedElement1 = "NestedElement1_0",
                    NestedElement2 = "NestedElement2_0",
                    CommonAttribute1 = "CommonAttribute1_0",
                    CommonAttribute2 = "CommonAttribute2_0",
                    Location = new Location
                    {
                        Lat = 3.14f,
                        Long = 2.72f
                    }
                },
                
                new MappedEntry
                {
                    ID = 1,
                    UnmappedField = null,
                    Author = new Author
                    {
                        Email = null,
                        Name = string.Empty,
                        Uri = "http://www.example.com/AuthorUri1",
                        Contributor = new Contributor
                        {
                            Email = null,
                            Name = string.Empty,
                            Uri = "http://www.example.com/ContributorUri1",
                        },
                    },
                    Published = "2000-01-01T00:00:00-08:00",
                    Rights = null,
                    Summary = "",
                    Title = "Title<i>1</i>",
                    Updated = "2111-01-01T00:00:00-08:00",
                    CustomElement = null,
                    NestedElement1 = string.Empty,
                    NestedElement2 = "NestedElement2_1",
                    CustomAttribute = null,
                    CommonAttribute1 = string.Empty,
                    CommonAttribute2 = "CommonAttribute2_1",
                    Location = new Location
                    {
                        Lat = float.MaxValue,
                        Long = float.MinValue
                    }
                },
                
                new MappedEntry
                {
                    ID = 2,
                    UnmappedField = "Unmapped2",
                    Author = new Author
                    {
                        Email = "AuthorEmail2",
                        Name = "AuthorName2",
                        Uri = "http://www.example.com/AuthorUri2",
                        Contributor = null
                    },
                    Published = "2000-01-01T00:00:00-08:00",
                    Rights = "Rights2",
                    Summary = "Summary2",
                    Title = "Title2",
                    Updated = "2000-01-01T00:00:00-08:00",
                    CustomElement = "CustomElement2",
                    CustomAttribute = "CustomAttribute2",
                    NestedElement1 = "NestedElement1_2",
                    NestedElement2 = "NestedElement2_2",
                    CommonAttribute1 = "CommonAttribute1_2",
                    CommonAttribute2 = "CommonAttribute2_2",
                    Location = null
                },
            };
            Array.ForEach(mappedEntries, (item) => this.GetResourceSetEntities<MappedEntry>("MappedEntries").Add(item));

            Array.ForEach(mappedEntries, (item) => this.GetResourceSetEntities<ReplicatedEntry>("ReplicatedEntries").Add(new ReplicatedEntry
            {
                ID = item.ID,
                UnmappedField = item.UnmappedField,
                Author = item.Author == null ? null : new Author2
                {
                    Email = item.Author.Email,
                    Name = item.Author.Name,
                    Uri = item.Author.Uri,
                    Contributor = item.Author.Contributor == null ? null : new Contributor2
                    {
                        Name = item.Author.Contributor.Name,
                        Email = item.Author.Contributor.Email,
                        Uri = item.Author.Contributor.Uri
                    },
                },
                Published = item.Published,
                Rights = item.Rights,
                Summary = item.Summary,
                Title = item.Title,
                Updated = item.Updated,
                CustomElement = item.CustomElement,
                CustomAttribute = item.CustomAttribute,
                NestedElement1 = item.NestedElement1,
                NestedElement2 = item.NestedElement2,
                CommonAttribute1 = item.CommonAttribute1,
                CommonAttribute2 = item.CommonAttribute2,
                Location = item.Location == null ? null : new Location2
                {
                    Lat = item.Location.Lat,
                    Long = item.Location.Long
                }
            }));

            BaseEntry[] hierarchicalEntries = new BaseEntry[]
            {
                new BaseEntry
                {
                    ID = 0,
                    MappedField = "MappedField0",
                    MappedInDerivedField = "MappedInDerivedField0",
                    UnmappedField = "UnmappedField0"
                },
                new DerivedEntry
                {
                    ID = 1,
                    MappedField = "MappedField1",
                    MappedInDerivedField = "MappedInDerivedField1",
                    UnmappedField = "UnmappedField1",
                    MappedConcreteField = "MappedConcreteField1",
                    UnmappedConcreteField = "UnmappedConcreteField1"
                },
            };
            Array.ForEach(hierarchicalEntries, (item) => this.GetResourceSetEntities<BaseEntry>("HierarchicalEntries").Add(item));
        }

        protected override void EnsureDataIsInitialized()
        {
            if (!dataInitialized)
            {
                this.ResetData();
                dataInitialized = true;
            }
        }
    }

    public class Author
    {
        public string Email { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
        public Contributor Contributor { get; set; }
    }

    public class Contributor
    {
        public string Email { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
    }
    
    public class Location
    {
        public float Lat { get; set; }
        public float Long { get; set; }
    }

    public class Author2
    {
        public string Email { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
        public Contributor2 Contributor { get; set; }
    }

    public class Contributor2
    {
        public string Email { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
    }

    public class Location2
    {
        public float Lat { get; set; }
        public float Long { get; set; }
    }
    
    public class MappedEntry
    {
        public int ID { get; set; }
        public string UnmappedField { get; set; }
        public Author Author { get; set; }
        public string Published { get; set; }
        public string Rights { get; set; }
        public string Summary { get; set; }
        public string Title { get; set; }
        public string Updated { get; set; }
        public string CustomElement { get; set; }
        public string CustomAttribute { get; set; }
        public string NestedElement1 { get; set; }
        public string NestedElement2 { get; set; }
        public string CommonAttribute1 { get; set; }
        public string CommonAttribute2 { get; set; }
        public Location Location { get; set; }
    }

    public class ReplicatedEntry
    {
        public int ID { get; set; }
        public string UnmappedField { get; set; }
        public Author2 Author { get; set; }
        public string Published { get; set; }
        public string Rights { get; set; }
        public string Summary { get; set; }
        public string Title { get; set; }
        public string Updated { get; set; }
        public string CustomElement { get; set; }
        public string CustomAttribute { get; set; }
        public string NestedElement1 { get; set; }
        public string NestedElement2 { get; set; }
        public string CommonAttribute1 { get; set; }
        public string CommonAttribute2 { get; set; }
        public Location2 Location { get; set; }
    }
    
    public class BaseEntry
    {
        public int ID { get; set; }
        public string UnmappedField { get; set; }
        public string MappedInDerivedField { get; set; }
        public string MappedField { get; set; }
    }
    
    public class DerivedEntry : BaseEntry
    {
        public string UnmappedConcreteField { get; set; }
        public string MappedConcreteField { get; set; }
    }
}