<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElDialog, ElMessage } from 'element-plus'
import service from '@neue-plus/components/material-render/src/utils/service'
import proTable from '../../../components/pro-table'
import { request } from '../../../components/material-render/src/handlers/handleRequest'
import { useSysParamsRef } from '../../../components/material-render/src/context/event-flow'
import { neBomDialogProps } from './types'
import type {
  ColumnType,
  ValueEnum,
  ValueType,
} from '../../../components/pro-table'

const props = defineProps(neBomDialogProps)
const emit = defineEmits(['refresh'])

const visible = ref(false)
const rowData = ref()

// 暴露方法给父组件调用
defineExpose({
  open: (row: any) => {
    visible.value = true
    rowData.value = row
  },
  close: () => {
    visible.value = false
  },
})
const { submit, request: propRequest, ...otherProps } = props
const userList = ref()
const getUserList = async () => {
  const res = await service({
    url: '/businessobject/202512/plt0User/actions/list',
    method: 'post',
    data: {
      pageParams: {
        limit: 9999,
        page: 0,
      },
    },
  })
  userList.value = res.data.reduce(
    (pre: { [x: string]: any }, item: { ncid: string | number; name: any }) => {
      pre[item.ncid] = { text: item.name }
      return pre
    },
    {}
  )
}
onMounted(() => {
  getUserList()
})
const columns = computed(() => [
  {
    type: 'selection' as ColumnType,
    prop: 'selection',
    width: 40,
    hideInForm: true,
  },
  {
    prop: 'code',
    label: '编号',
  },
  {
    prop: 'name',
    label: '名称',
  },
  {
    prop: 'partType',
    label: '子类别',
    valueEnum: {
      "BuiltInModel.PartType'NEUE_PRT'": {
        text: 'NEUE_PRT',
      },
      "BuiltInModel.PartType'NEUE_ASM'": {
        text: 'NEUE_ASM',
      },
    },
    entityType: 'BuiltInModel.PartType',
  },
  {
    prop: 'version/versionNumber',
    label: '版本号',
    render: '{{version.revision.revisionCode}}.{{version.versionNumber}}',
    hideInForm: true,
  },
  {
    prop: 'lifecycleStatus/name',
    label: '状态',
    hideInForm: true,
    valueEnum: {
      IN_WORK: {
        text: '工作中',
      },
    } as ValueEnum,
  },
  {
    prop: 'version/revision/lockedBy/ncid',
    label: '锁定人',
    render: '{{version.revision.lockedBy.name}}',
    valueType: 'avatar' as ValueType,
    entityType: 'Edm.Enum',
    valueEnum: userList.value,
  },
  {
    prop: 'owner/ncid',
    label: '责任人',
    valueType: 'avatar' as ValueType,
    entityType: 'Edm.Enum',
    valueEnum: userList.value,
  },
  {
    prop: 'description',
    label: '描述',
  },
])

const sendData = ref()
const sysParamsRef = useSysParamsRef()
const handleSelectionChange = async (val: any[]) => {
  sendData.value = val
}
const handleClose = async () => {
  const ids = sendData.value.map((item: any) => item.ncid)
  if (ids.length > 1) {
    ElMessage({
      message: '当前版本仅支持选择一个零部件添加',
      type: 'warning',
    })
    return
  }
  if (ids.length === 0) {
    ElMessage({
      message: '请选择零部件',
      type: 'warning',
    })
    return
  }
  if (submit) {
    await request({
      ...submit,
      replaceData: sysParamsRef.value,
      data: {
        from: {
          ncid: rowData.value.newNcid,
        },
        to: {
          ncid: ids[0],
        },
      },
    })
    emit('refresh')
    visible.value = false
    ElMessage({
      message: '添加成功',
      type: 'success',
    })
  }
}
</script>

<template>
  <el-dialog
    v-bind="otherProps"
    v-model="visible"
    style="min-width: 720px"
    title="查询添加"
    :width="'70%'"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <div style="padding-top: 2px">
      <pro-table
        :request="propRequest"
        row-key="ncid"
        :max-height="500"
        :columns="columns"
        :search-config="{
          labelWidth: 60,
        }"
        @selection-change="handleSelectionChange"
      />
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleClose()"> 确定 </el-button>
    </template>
  </el-dialog>
</template>
