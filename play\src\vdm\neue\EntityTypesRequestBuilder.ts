/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { EntityTypes } from './EntityTypes';

/**
 * Request builder class for operations supported on the {@link EntityTypes} entity.
 */
export class EntityTypesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntityTypes<T>, T> {
  /**
   * Returns a request builder for querying all `EntityTypes` entities.
   * @returns A request builder for creating requests to retrieve all `EntityTypes` entities.
   */
  getAll(): GetAllRequestBuilder<EntityTypes<T>, T> {
    return new GetAllRequestBuilder<EntityTypes<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `EntityTypes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntityTypes`.
   */
  create(entity: EntityTypes<T>): CreateRequestBuilder<EntityTypes<T>, T> {
    return new CreateRequestBuilder<EntityTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `EntityTypes` entity based on its keys.
   * @param ncid Key property. See {@link EntityTypes.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntityTypes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntityTypes<T>, T> {
    return new GetByKeyRequestBuilder<EntityTypes<T>, T>(this.entityApi, {
      ncid: ncid
    });
  }

  /**
   * Returns a request builder for updating an entity of type `EntityTypes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntityTypes`.
   */
  update(entity: EntityTypes<T>): UpdateRequestBuilder<EntityTypes<T>, T> {
    return new UpdateRequestBuilder<EntityTypes<T>, T>(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `EntityTypes`.
   * @param ncid Key property. See {@link EntityTypes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypes`.
   */
  delete(ncid: string): DeleteRequestBuilder<EntityTypes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntityTypes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypes` by taking the entity as a parameter.
   */
  delete(entity: EntityTypes<T>): DeleteRequestBuilder<EntityTypes<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<EntityTypes<T>, T> {
    return new DeleteRequestBuilder<EntityTypes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntityTypes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
