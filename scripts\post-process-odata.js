#!/usr/bin/env node
/**
 * OData 生成后处理脚本
 * 修复生成代码中的常见问题
 */

const fs = require('fs')
const path = require('path')

const CONFIG = {
  VDM_DIR: path.resolve(__dirname, '../play/src/vdm/neue'),
  BACKUP_DIR: path.resolve(__dirname, '../play/src/vdm/neue-backup'),
}

/**
 * 修复 moment.js 导入问题
 */
function fixMomentImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf-8')
  let modified = false

  // 修复 moment Duration 导入
  if (content.includes("import { Moment, Duration } from 'moment'")) {
    content = content.replace(
      "import { Moment, Duration } from 'moment'",
      `import moment from 'moment'
import type { Moment } from 'moment'

type Duration = moment.Duration`
    )
    modified = true
  }

  // 修复其他 moment 导入问题
  if (content.includes("import { Moment } from 'moment'") && !content.includes('import type')) {
    content = content.replace(
      "import { Moment } from 'moment'",
      "import type { Moment } from 'moment'"
    )
    modified = true
  }

  return { content, modified }
}

/**
 * 修复 SAP Cloud SDK 类型导入
 */
function fixSapCloudSdkImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf-8')
  let modified = false

  // 修复 DeSerializers 等类型导入
  const typeImports = ['DeSerializers', 'DefaultDeSerializers', 'Time']
  
  typeImports.forEach(typeImport => {
    const regex = new RegExp(`import\\s*{([^}]*${typeImport}[^}]*)}\\s*from\\s*'@sap-cloud-sdk/odata-v4'`, 'g')
    const matches = content.match(regex)
    
    if (matches) {
      matches.forEach(match => {
        const imports = match.match(/{([^}]*)}/)[1]
        const importList = imports.split(',').map(s => s.trim())
        
        const valueImports = importList.filter(imp => !typeImports.includes(imp))
        const typeImportsList = importList.filter(imp => typeImports.includes(imp))
        
        if (typeImportsList.length > 0) {
          let replacement = ''
          
          if (valueImports.length > 0) {
            replacement += `import {\n  ${valueImports.join(',\n  ')}\n} from '@sap-cloud-sdk/odata-v4'\n`
          }
          
          replacement += `import type {\n  ${typeImportsList.join(',\n  ')}\n} from '@sap-cloud-sdk/odata-v4'`
          
          content = content.replace(match, replacement)
          modified = true
        }
      })
    }
  })

  return { content, modified }
}

/**
 * 添加缺失的类型接口
 */
function addMissingTypeInterfaces(filePath) {
  let content = fs.readFileSync(filePath, 'utf-8')
  let modified = false

  // 检查是否有 implements XxxType<T> 但没有对应的接口定义
  const implementsMatch = content.match(/implements\s+(\w+Type)<T>/)
  const hasExportInterface = content.includes('export interface')

  if (implementsMatch && !hasExportInterface) {
    const typeName = implementsMatch[1]
    const className = path.basename(filePath, '.ts')
    
    // 提取类中的属性定义
    const propertyMatches = content.match(/declare\s+(\w+)(\?)?:\s*([^;]+);/g)
    
    if (propertyMatches) {
      const interfaceProperties = propertyMatches.map(prop => {
        return '  ' + prop.replace('declare ', '')
      }).join('\n')

      const interfaceDefinition = `
export interface ${typeName}<
  T extends DeSerializers = DefaultDeSerializers
> {
${interfaceProperties}
}
`

      content += interfaceDefinition
      modified = true
    }
  }

  return { content, modified }
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  console.log(`🔧 处理文件: ${path.relative(CONFIG.VDM_DIR, filePath)}`)
  
  let totalModifications = 0
  let content = fs.readFileSync(filePath, 'utf-8')

  // 应用各种修复
  const fixes = [
    fixMomentImports,
    fixSapCloudSdkImports,
    addMissingTypeInterfaces
  ]

  for (const fix of fixes) {
    const result = fix(filePath)
    if (result.modified) {
      content = result.content
      totalModifications++
    }
  }

  // 如果有修改，写回文件
  if (totalModifications > 0) {
    fs.writeFileSync(filePath, content, 'utf-8')
    console.log(`   ✅ 应用了 ${totalModifications} 个修复`)
  } else {
    console.log(`   ℹ️  无需修复`)
  }

  return totalModifications
}

/**
 * 处理所有 TypeScript 文件
 */
function processAllFiles() {
  console.log('🚀 开始后处理 OData 生成的代码...')
  
  if (!fs.existsSync(CONFIG.VDM_DIR)) {
    console.error(`❌ VDM 目录不存在: ${CONFIG.VDM_DIR}`)
    process.exit(1)
  }

  const files = fs.readdirSync(CONFIG.VDM_DIR)
    .filter(file => file.endsWith('.ts'))
    .map(file => path.join(CONFIG.VDM_DIR, file))

  console.log(`📁 找到 ${files.length} 个 TypeScript 文件`)

  let totalFiles = 0
  let modifiedFiles = 0

  files.forEach(filePath => {
    totalFiles++
    const modifications = processFile(filePath)
    if (modifications > 0) {
      modifiedFiles++
    }
  })

  console.log(`\n📊 处理完成:`)
  console.log(`   - 总文件数: ${totalFiles}`)
  console.log(`   - 修改文件数: ${modifiedFiles}`)
  console.log(`   - 未修改文件数: ${totalFiles - modifiedFiles}`)
}

/**
 * 创建备份
 */
function createBackup() {
  if (fs.existsSync(CONFIG.VDM_DIR)) {
    console.log('💾 创建备份...')
    
    if (fs.existsSync(CONFIG.BACKUP_DIR)) {
      fs.rmSync(CONFIG.BACKUP_DIR, { recursive: true, force: true })
    }
    
    fs.cpSync(CONFIG.VDM_DIR, CONFIG.BACKUP_DIR, { recursive: true })
    console.log(`✅ 备份创建完成: ${CONFIG.BACKUP_DIR}`)
  }
}

// 主执行函数
if (require.main === module) {
  try {
    createBackup()
    processAllFiles()
    console.log('\n🎉 后处理完成!')
  } catch (error) {
    console.error('❌ 后处理失败:', error.message)
    process.exit(1)
  }
}
