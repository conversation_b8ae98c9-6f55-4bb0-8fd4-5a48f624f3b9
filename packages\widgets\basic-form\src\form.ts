interface FormConfig {
  layout?: 'horizontal' | 'vertical'
  labelWidth?: string
  formWidth?: string // 表单内容宽度
  gutter?: number
  showSubmit?: boolean
  showReset?: boolean
  showCancel?: boolean
  submitText?: string
  resetText?: string
  cancelText?: string
  buttonAlign?: 'left' | 'right' | 'center'
  clearOnClose?: boolean // 关闭弹窗时是否清空表单, 默认 true
}

// 定义选项接口，用于 select、enum 和 array 类型的字段
interface Option {
  label: string
  value: string
}

// 定义基础字段接口
interface BaseField {
  // 字段英文名称
  prop: string
  // 字段名称
  fieldName: string
  readonly?: boolean // 是否只读, 默认否
  disabled?: boolean // 是否禁用, 默认否
  // text、email、number、select、radio、checkbox、date、switch、textarea
  fieldType?: string
  ruleId?: string
  required?: boolean
  filterable?: boolean
  options?: Option[]
  minLength?: number
  maxLength?: number
}

// 定义特定字段类型接口，用于扩展基础字段接口
interface TextField extends BaseField {
  fieldType: 'text'
  minLength?: number
  maxLength?: number
  showWordLimit?: boolean
}

interface TextAreaField extends BaseField {
  fieldType: 'textarea'
  minLength?: number
  maxLength?: number
  showWordLimit?: boolean
  rows?: number // 可选的行数
  disabled?: boolean // 是否禁用, 默认否
}

interface EmailField extends BaseField {
  fieldType: 'email'
}

interface NumberField extends BaseField {
  fieldType: 'number'
  min?: number
  max?: number
  size?: number
  step?: number
}

interface SelectField extends BaseField {
  fieldType: 'select'
  filterable: boolean
  options: Option[]
}

interface RadioField extends BaseField {
  fieldType: 'radio'
  options: Option[]
}

interface CheckboxField extends BaseField {
  fieldType: 'checkbox'
  options: Option[]
}

interface DateField extends BaseField {
  fieldType: 'date'
  dateFormat?: string // 可选的日期格式
  startPlaceholder?: string // 可选的开始日期占位符
  endPlaceholder?: string // 可选的结束日期占位符
  type?: 'date' | 'datetime' | 'year' | 'month' // 可选的日期选择类型
  format?: string // 可选的日期格式化字符串
  valueFormat?: string // 可选的值格式化字符串
  clearable?: boolean // 是否可清除日期选择
  placeholder?: string // 可选的占位符
}

interface SwitchField extends BaseField {
  fieldType: 'switch'
}

// 定义字段类型
type FieldType =
  | TextField
  | EmailField
  | NumberField
  | SelectField
  | RadioField
  | CheckboxField
  | DateField
  | SwitchField
  | TextAreaField

// 定义字段数组接口
interface FormField {
  formItems: FieldType[]
}

// 1. 先准备一个默认值映射
type DefaultValue<T extends FieldType> = T extends SwitchField
  ? boolean
  : T extends NumberField
  ? number
  : T extends CheckboxField
  ? string[]
  : string

// 根据 FieldType[] 生成 { prop : DefaultValue<T> }
type FormModelType<T extends readonly FieldType[]> = {
  [K in T[number] as K['prop']]: DefaultValue<K>
}

type FormModelDefault = Record<string, unknown>

export type {
  FormConfig,
  FormField,
  FieldType,
  Option,
  FormModelType,
  FormModelDefault,
}

// 示例：定义一个具体的表单字段数组
// const formFields: FormField = {
//   formItems: [
//     {
//       fieldName: "姓名",
//       prop: "name",
//       fieldType: "string",
//       ruleId: "RULE_001",
//       required: true,
//     }
//   ]
// };
