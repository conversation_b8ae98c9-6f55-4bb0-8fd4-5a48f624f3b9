import { RequestOptions, request } from './handleRequest'

interface ActionProps {
  config: {
    apiList: RequestOptions[]
  }
}
export async function handleFileDown(action: ActionProps, params: any) {
  const { config } = action
  try {
    let resParams = params
    for (const item of config.apiList) {
      resParams = await request({ ...item, replaceData: resParams })
      console.log(item.url)
      if (
        '/modeling/202512/OdataService/BatchGetFileSignatureUrl' === item.url
      ) {
        await Promise.all(
          resParams.data.value.forEach(async (item: any) => {
            const res = await fetch(item.signatureUrl) // 接口返回文件数据
            const blob = await res.blob()
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `downloaded_${Date.now()}.idi`
            a.click()
            URL.revokeObjectURL(url) // 释放内存
          })
        )
      }
    }
    return 'success'
  } catch {
    return 'fail'
  }
}
