// NeDescription.tsx

import { defineComponent, h } from 'vue'
import { ElDescriptions, ElDescriptionsItem } from 'element-plus' // 引入 Element Plus 的 DescriptionsItem 组件
import { neDescriptionProps } from './type'

export default defineComponent({
  name: 'NeDescription',
  inheritAttrs: false,
  props: neDescriptionProps,
  // 核心逻辑都在 setup 函数中
  setup(props, { slots }) {
    // 渲染函数
    const render = () => {
      // 循环遍历 options 数组，为每个 option 创建一个 DescriptionsItem
      const items = props.options?.map((option) => {
        // 判断是否存在 labelRender 函数
        const labelNode = option.labelRender
          ? option.labelRender()
          : h('span', { class: 'custom-label' }, option.label)

        // 处理 valueRender
        const valueNode = option.valueRender
          ? option.valueRender()
          : h('span', { class: 'custom-value' }, option.value)

        return h(
          ElDescriptionsItem,
          {
            ...option,
          },
          {
            // 具名插槽：'label'
            label: () => labelNode,
            // 默认插槽：'default'
            default: () => valueNode,
          }
        )
      })

      return h(
        ElDescriptions,
        {
          ...props,
        },
        {
          // 具名插槽：title
          title: () => slots.title?.() || props.title,
          // 具名插槽：extra
          extra: () => slots.extra?.() || props.extra,
          // 默认插槽：所有 DescriptionsItem
          default: () => items,
        }
      )
    }

    // setup 函数返回一个 render 函数
    return render
  },
})
