/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EntityTypePropertyOverrides } from './EntityTypePropertyOverrides';

/**
 * Request builder class for operations supported on the {@link EntityTypePropertyOverrides} entity.
 */
export class EntityTypePropertyOverridesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntityTypePropertyOverrides<T>, T> {
  /**
   * Returns a request builder for querying all `EntityTypePropertyOverrides` entities.
   * @returns A request builder for creating requests to retrieve all `EntityTypePropertyOverrides` entities.
   */
  getAll(): GetAllRequestBuilder<EntityTypePropertyOverrides<T>, T> {
    return new GetAllRequestBuilder<EntityTypePropertyOverrides<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `EntityTypePropertyOverrides` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntityTypePropertyOverrides`.
   */
  create(
    entity: EntityTypePropertyOverrides<T>
  ): CreateRequestBuilder<EntityTypePropertyOverrides<T>, T> {
    return new CreateRequestBuilder<EntityTypePropertyOverrides<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `EntityTypePropertyOverrides` entity based on its keys.
   * @param ncid Key property. See {@link EntityTypePropertyOverrides.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntityTypePropertyOverrides` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntityTypePropertyOverrides<T>, T> {
    return new GetByKeyRequestBuilder<EntityTypePropertyOverrides<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `EntityTypePropertyOverrides`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntityTypePropertyOverrides`.
   */
  update(
    entity: EntityTypePropertyOverrides<T>
  ): UpdateRequestBuilder<EntityTypePropertyOverrides<T>, T> {
    return new UpdateRequestBuilder<EntityTypePropertyOverrides<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `EntityTypePropertyOverrides`.
   * @param ncid Key property. See {@link EntityTypePropertyOverrides.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypePropertyOverrides`.
   */
  delete(ncid: string): DeleteRequestBuilder<EntityTypePropertyOverrides<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntityTypePropertyOverrides`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypePropertyOverrides` by taking the entity as a parameter.
   */
  delete(
    entity: EntityTypePropertyOverrides<T>
  ): DeleteRequestBuilder<EntityTypePropertyOverrides<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<EntityTypePropertyOverrides<T>, T> {
    return new DeleteRequestBuilder<EntityTypePropertyOverrides<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntityTypePropertyOverrides
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
