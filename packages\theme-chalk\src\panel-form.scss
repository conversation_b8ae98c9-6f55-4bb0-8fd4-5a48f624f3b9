@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@include neb(panel-form) {
  .form-banner-title {
    font-size: 16px;
    font-weight: 500;
  }
  .custom-collapse {
    margin-top: 16px;

    .el-collapse-item {
      border: 0 none;
      &:nth-child(n+2) {
        margin-top: 4px;
      }

    }
    .el-collapse-item__header {
      background: #F0F3FA;
      height: 48px;
      padding-right: 0;
      gap: 4px;
      margin-bottom: 20px;
    }
    .el-collapse-item__wrap {
      border-bottom: 0 none;
    }
    .el-collapse-item__content {
      gap: 16px;
      display: flex;
      width: 100%;
    }

    .el-collapse-item__arrow {
      order: -1;
      margin-left: 20px;
      font-family: "iconfont" !important;
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: rgba(0, 0, 0, 0.85);
      &:before {
        content: "\e724";
      }
      .collapse-content {
      }
    }
  }
  .banner-wrapper {
    order: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .el-collapse-item__header {
    border-bottom: 0 none;
  }
  .el-collapse-item__wrap {
    display: flex;
  }
  .collapse-vertical .el-collapse-item__content{
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .collapse-horizontal .el-collapse-item__content {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
  .collapse-grid .el-collapse-item__content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }
  .img-wrapper {
    text-align: center;
    margin-bottom: 16px;
    width: 200px;
    height: 200px;
    background: #F7F8FA;
    img {
      max-width: 100%;
    }
  }
  .el-row {
    flex: 1;
  }
  // .panel-form-content .el-form-item__label {
  //   width: 120px !important;
  // }
}
