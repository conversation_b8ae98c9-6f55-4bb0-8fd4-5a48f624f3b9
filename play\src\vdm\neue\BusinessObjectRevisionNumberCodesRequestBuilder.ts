/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4'
import type {
  DeSerializers,
  DefaultDeSerializers
} from '@sap-cloud-sdk/odata-v4';
import { BusinessObjectRevisionNumberCodes } from './BusinessObjectRevisionNumberCodes';

/**
 * Request builder class for operations supported on the {@link BusinessObjectRevisionNumberCodes} entity.
 */
export class BusinessObjectRevisionNumberCodesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
  /**
   * Returns a request builder for querying all `BusinessObjectRevisionNumberCodes` entities.
   * @returns A request builder for creating requests to retrieve all `BusinessObjectRevisionNumberCodes` entities.
   */
  getAll(): GetAllRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
    return new GetAllRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `BusinessObjectRevisionNumberCodes` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `BusinessObjectRevisionNumberCodes`.
   */
  create(
    entity: BusinessObjectRevisionNumberCodes<T>
  ): CreateRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
    return new CreateRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `BusinessObjectRevisionNumberCodes` entity based on its keys.
   * @param ncid Key property. See {@link BusinessObjectRevisionNumberCodes.ncid}.
   * @returns A request builder for creating requests to retrieve one `BusinessObjectRevisionNumberCodes` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
    return new GetByKeyRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `BusinessObjectRevisionNumberCodes`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `BusinessObjectRevisionNumberCodes`.
   */
  update(
    entity: BusinessObjectRevisionNumberCodes<T>
  ): UpdateRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
    return new UpdateRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `BusinessObjectRevisionNumberCodes`.
   * @param ncid Key property. See {@link BusinessObjectRevisionNumberCodes.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `BusinessObjectRevisionNumberCodes`.
   */
  delete(
    ncid: string
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `BusinessObjectRevisionNumberCodes`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `BusinessObjectRevisionNumberCodes` by taking the entity as a parameter.
   */
  delete(
    entity: BusinessObjectRevisionNumberCodes<T>
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T> {
    return new DeleteRequestBuilder<BusinessObjectRevisionNumberCodes<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof BusinessObjectRevisionNumberCodes
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
