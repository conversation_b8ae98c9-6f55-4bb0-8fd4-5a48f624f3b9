<template>
  <el-tag v-bind="props" @click="emit('click', $event)" @close="emit('close')">
    <div v-if="props.value">
      {{ props.value }}
    </div>
    <div v-else>
      <slot />
    </div>
  </el-tag>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue'
import { netagProps } from './tag'

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
  (e: 'close'): void
}>()

defineOptions({
  name: 'NeTag',
})

const props = defineProps(netagProps)
</script>
