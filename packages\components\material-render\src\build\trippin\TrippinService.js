"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventLocationService = exports.AirportLocationService = exports.CityService = exports.LocationService = exports.ManagerCollectionService = exports.ManagerService = exports.EmployeeCollectionService = exports.EmployeeService = exports.FlightCollectionService = exports.FlightService = exports.PublicTransportationCollectionService = exports.PublicTransportationService = exports.EventCollectionService = exports.EventService = exports.PlanItemCollectionService = exports.PlanItemService = exports.TripCollectionService = exports.TripService = exports.AirportCollectionService = exports.AirportService = exports.AirlineCollectionService = exports.AirlineService = exports.PersonCollectionService = exports.PersonService = exports.TrippinService = void 0;
var odata_query_objects_1 = require("@odata2ts/odata-query-objects");
var odata_service_1 = require("@odata2ts/odata-service");
var QTrippin_1 = require("./QTrippin");
var TrippinModel_1 = require("./TrippinModel");
var TrippinService = /** @class */ (function (_super) {
    __extends(TrippinService, _super);
    function TrippinService() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TrippinService.prototype.People = function (id) {
        var fieldName = "People";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    TrippinService.prototype.Airlines = function (id) {
        var fieldName = "Airlines";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new AirlineCollectionService(client, path, fieldName, options)
            : new AirlineService(client, path, new QTrippin_1.QAirlineId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    TrippinService.prototype.Airports = function (id) {
        var fieldName = "Airports";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new AirportCollectionService(client, path, fieldName, options)
            : new AirportService(client, path, new QTrippin_1.QAirportId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    TrippinService.prototype.Me = function () {
        if (!this._Me) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Me = new PersonService(client, path, "Me", options);
        }
        return this._Me;
    };
    TrippinService.prototype.GetPersonWithMostFriends = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._QGetPersonWithMostFriends) {
                            this._QGetPersonWithMostFriends = new QTrippin_1.QGetPersonWithMostFriends();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._QGetPersonWithMostFriends.buildUrl(isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._QGetPersonWithMostFriends.convertResponse(response)];
                }
            });
        });
    };
    TrippinService.prototype.GetNearestAirport = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._QGetNearestAirport) {
                            this._QGetNearestAirport = new QTrippin_1.QGetNearestAirport();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._QGetNearestAirport.buildUrl(params, isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._QGetNearestAirport.convertResponse(response)];
                }
            });
        });
    };
    TrippinService.prototype.ResetDataSource = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url;
            return __generator(this, function (_b) {
                if (!this._QResetDataSource) {
                    this._QResetDataSource = new QTrippin_1.QResetDataSource();
                }
                _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                url = addFullPath(this._QResetDataSource.buildUrl());
                return [2 /*return*/, client.post(url, {}, requestConfig, getDefaultHeaders())];
            });
        });
    };
    return TrippinService;
}(odata_service_1.ODataService));
exports.TrippinService = TrippinService;
var PersonService = /** @class */ (function (_super) {
    __extends(PersonService, _super);
    function PersonService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPerson, options) || this;
    }
    PersonService.prototype.Emails = function () {
        if (!this._Emails) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Emails = new odata_service_1.CollectionServiceV4(client, path, "Emails", new odata_query_objects_1.QStringCollection(), options);
        }
        return this._Emails;
    };
    PersonService.prototype.AddressInfo = function () {
        if (!this._AddressInfo) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._AddressInfo = new odata_service_1.CollectionServiceV4(client, path, "AddressInfo", QTrippin_1.qLocation, options);
        }
        return this._AddressInfo;
    };
    PersonService.prototype.HomeAddress = function () {
        if (!this._HomeAddress) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._HomeAddress = new LocationService(client, path, "HomeAddress", options);
        }
        return this._HomeAddress;
    };
    PersonService.prototype.Features = function () {
        if (!this._Features) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Features = new odata_service_1.CollectionServiceV4(client, path, "Features", new odata_query_objects_1.QEnumCollection(TrippinModel_1.Feature), options);
        }
        return this._Features;
    };
    PersonService.prototype.Friends = function (id) {
        var fieldName = "Friends";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PersonService.prototype.BestFriend = function () {
        if (!this._BestFriend) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._BestFriend = new PersonService(client, path, "BestFriend", options);
        }
        return this._BestFriend;
    };
    PersonService.prototype.Trips = function (id) {
        var fieldName = "Trips";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new TripCollectionService(client, path, fieldName, options)
            : new TripService(client, path, new QTrippin_1.QTripId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    PersonService.prototype.GetFavoriteAirline = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Person_QGetFavoriteAirline) {
                            this._Person_QGetFavoriteAirline = new QTrippin_1.Person_QGetFavoriteAirline();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Person_QGetFavoriteAirline.buildUrl(isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Person_QGetFavoriteAirline.convertResponse(response)];
                }
            });
        });
    };
    PersonService.prototype.GetFriendsTrips = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Person_QGetFriendsTrips) {
                            this._Person_QGetFriendsTrips = new QTrippin_1.Person_QGetFriendsTrips();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Person_QGetFriendsTrips.buildUrl(params, isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Person_QGetFriendsTrips.convertResponse(response)];
                }
            });
        });
    };
    PersonService.prototype.UpdateLastName = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Person_QUpdateLastName) {
                            this._Person_QUpdateLastName = new QTrippin_1.Person_QUpdateLastName();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Person_QUpdateLastName.buildUrl());
                        return [4 /*yield*/, client.post(url, this._Person_QUpdateLastName.convertUserParams(params), requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Person_QUpdateLastName.convertResponse(response)];
                }
            });
        });
    };
    PersonService.prototype.ShareTrip = function (params, requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url;
            return __generator(this, function (_b) {
                if (!this._Person_QShareTrip) {
                    this._Person_QShareTrip = new QTrippin_1.Person_QShareTrip();
                }
                _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                url = addFullPath(this._Person_QShareTrip.buildUrl());
                return [2 /*return*/, client.post(url, this._Person_QShareTrip.convertUserParams(params), requestConfig, getDefaultHeaders())];
            });
        });
    };
    PersonService.prototype.asEmployeeService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new EmployeeService(client, path, "Trippin.Employee", __assign(__assign({}, options), { subtype: true }));
    };
    PersonService.prototype.asManagerService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new ManagerService(client, path, "Trippin.Manager", __assign(__assign({}, options), { subtype: true }));
    };
    return PersonService;
}(odata_service_1.EntityTypeServiceV4));
exports.PersonService = PersonService;
var PersonCollectionService = /** @class */ (function (_super) {
    __extends(PersonCollectionService, _super);
    function PersonCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPerson, new QTrippin_1.QPersonId(name), options) || this;
    }
    PersonCollectionService.prototype.asEmployeeCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new EmployeeCollectionService(client, path, "Trippin.Employee", __assign(__assign({}, options), { subtype: true }));
    };
    PersonCollectionService.prototype.asManagerCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new ManagerCollectionService(client, path, "Trippin.Manager", __assign(__assign({}, options), { subtype: true }));
    };
    return PersonCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.PersonCollectionService = PersonCollectionService;
var AirlineService = /** @class */ (function (_super) {
    __extends(AirlineService, _super);
    function AirlineService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qAirline, options) || this;
    }
    return AirlineService;
}(odata_service_1.EntityTypeServiceV4));
exports.AirlineService = AirlineService;
var AirlineCollectionService = /** @class */ (function (_super) {
    __extends(AirlineCollectionService, _super);
    function AirlineCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qAirline, new QTrippin_1.QAirlineId(name), options) || this;
    }
    return AirlineCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.AirlineCollectionService = AirlineCollectionService;
var AirportService = /** @class */ (function (_super) {
    __extends(AirportService, _super);
    function AirportService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qAirport, options) || this;
    }
    AirportService.prototype.Location = function () {
        if (!this._Location) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Location = new AirportLocationService(client, path, "Location", options);
        }
        return this._Location;
    };
    return AirportService;
}(odata_service_1.EntityTypeServiceV4));
exports.AirportService = AirportService;
var AirportCollectionService = /** @class */ (function (_super) {
    __extends(AirportCollectionService, _super);
    function AirportCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qAirport, new QTrippin_1.QAirportId(name), options) || this;
    }
    return AirportCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.AirportCollectionService = AirportCollectionService;
var TripService = /** @class */ (function (_super) {
    __extends(TripService, _super);
    function TripService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qTrip, options) || this;
    }
    TripService.prototype.Tags = function () {
        if (!this._Tags) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Tags = new odata_service_1.CollectionServiceV4(client, path, "Tags", new odata_query_objects_1.QStringCollection(), options);
        }
        return this._Tags;
    };
    TripService.prototype.PlanItems = function (id) {
        var fieldName = "PlanItems";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PlanItemCollectionService(client, path, fieldName, options)
            : new PlanItemService(client, path, new QTrippin_1.QPlanItemId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    TripService.prototype.GetInvolvedPeople = function (requestConfig) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, addFullPath, client, getDefaultHeaders, isUrlNotEncoded, url, response;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this._Trip_QGetInvolvedPeople) {
                            this._Trip_QGetInvolvedPeople = new QTrippin_1.Trip_QGetInvolvedPeople();
                        }
                        _a = this.__base, addFullPath = _a.addFullPath, client = _a.client, getDefaultHeaders = _a.getDefaultHeaders, isUrlNotEncoded = _a.isUrlNotEncoded;
                        url = addFullPath(this._Trip_QGetInvolvedPeople.buildUrl(isUrlNotEncoded()));
                        return [4 /*yield*/, client.get(url, requestConfig, getDefaultHeaders())];
                    case 1:
                        response = _b.sent();
                        return [2 /*return*/, this._Trip_QGetInvolvedPeople.convertResponse(response)];
                }
            });
        });
    };
    return TripService;
}(odata_service_1.EntityTypeServiceV4));
exports.TripService = TripService;
var TripCollectionService = /** @class */ (function (_super) {
    __extends(TripCollectionService, _super);
    function TripCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qTrip, new QTrippin_1.QTripId(name), options) || this;
    }
    return TripCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.TripCollectionService = TripCollectionService;
var PlanItemService = /** @class */ (function (_super) {
    __extends(PlanItemService, _super);
    function PlanItemService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPlanItem, options) || this;
    }
    PlanItemService.prototype.asEventService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new EventService(client, path, "Trippin.Event", __assign(__assign({}, options), { subtype: true }));
    };
    PlanItemService.prototype.asPublicTransportationService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new PublicTransportationService(client, path, "Trippin.PublicTransportation", __assign(__assign({}, options), { subtype: true }));
    };
    PlanItemService.prototype.asFlightService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new FlightService(client, path, "Trippin.Flight", __assign(__assign({}, options), { subtype: true }));
    };
    return PlanItemService;
}(odata_service_1.EntityTypeServiceV4));
exports.PlanItemService = PlanItemService;
var PlanItemCollectionService = /** @class */ (function (_super) {
    __extends(PlanItemCollectionService, _super);
    function PlanItemCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPlanItem, new QTrippin_1.QPlanItemId(name), options) || this;
    }
    PlanItemCollectionService.prototype.asEventCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new EventCollectionService(client, path, "Trippin.Event", __assign(__assign({}, options), { subtype: true }));
    };
    PlanItemCollectionService.prototype.asPublicTransportationCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new PublicTransportationCollectionService(client, path, "Trippin.PublicTransportation", __assign(__assign({}, options), { subtype: true }));
    };
    PlanItemCollectionService.prototype.asFlightCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new FlightCollectionService(client, path, "Trippin.Flight", __assign(__assign({}, options), { subtype: true }));
    };
    return PlanItemCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.PlanItemCollectionService = PlanItemCollectionService;
var EventService = /** @class */ (function (_super) {
    __extends(EventService, _super);
    function EventService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qEvent, options) || this;
    }
    EventService.prototype.OccursAt = function () {
        if (!this._OccursAt) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._OccursAt = new EventLocationService(client, path, "OccursAt", options);
        }
        return this._OccursAt;
    };
    return EventService;
}(odata_service_1.EntityTypeServiceV4));
exports.EventService = EventService;
var EventCollectionService = /** @class */ (function (_super) {
    __extends(EventCollectionService, _super);
    function EventCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qEvent, new QTrippin_1.QPlanItemId(name), options) || this;
    }
    return EventCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.EventCollectionService = EventCollectionService;
var PublicTransportationService = /** @class */ (function (_super) {
    __extends(PublicTransportationService, _super);
    function PublicTransportationService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPublicTransportation, options) || this;
    }
    PublicTransportationService.prototype.asFlightService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new FlightService(client, path, "Trippin.Flight", __assign(__assign({}, options), { subtype: true }));
    };
    return PublicTransportationService;
}(odata_service_1.EntityTypeServiceV4));
exports.PublicTransportationService = PublicTransportationService;
var PublicTransportationCollectionService = /** @class */ (function (_super) {
    __extends(PublicTransportationCollectionService, _super);
    function PublicTransportationCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qPublicTransportation, new QTrippin_1.QPlanItemId(name), options) || this;
    }
    PublicTransportationCollectionService.prototype.asFlightCollectionService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new FlightCollectionService(client, path, "Trippin.Flight", __assign(__assign({}, options), { subtype: true }));
    };
    return PublicTransportationCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.PublicTransportationCollectionService = PublicTransportationCollectionService;
var FlightService = /** @class */ (function (_super) {
    __extends(FlightService, _super);
    function FlightService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qFlight, options) || this;
    }
    FlightService.prototype.Airline = function () {
        if (!this._Airline) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Airline = new AirlineService(client, path, "Airline", options);
        }
        return this._Airline;
    };
    FlightService.prototype.From = function () {
        if (!this._From) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._From = new AirportService(client, path, "From", options);
        }
        return this._From;
    };
    FlightService.prototype.To = function () {
        if (!this._To) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._To = new AirportService(client, path, "To", options);
        }
        return this._To;
    };
    return FlightService;
}(odata_service_1.EntityTypeServiceV4));
exports.FlightService = FlightService;
var FlightCollectionService = /** @class */ (function (_super) {
    __extends(FlightCollectionService, _super);
    function FlightCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qFlight, new QTrippin_1.QPlanItemId(name), options) || this;
    }
    return FlightCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.FlightCollectionService = FlightCollectionService;
var EmployeeService = /** @class */ (function (_super) {
    __extends(EmployeeService, _super);
    function EmployeeService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qEmployee, options) || this;
    }
    EmployeeService.prototype.Emails = function () {
        if (!this._Emails) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Emails = new odata_service_1.CollectionServiceV4(client, path, "Emails", new odata_query_objects_1.QStringCollection(), options);
        }
        return this._Emails;
    };
    EmployeeService.prototype.AddressInfo = function () {
        if (!this._AddressInfo) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._AddressInfo = new odata_service_1.CollectionServiceV4(client, path, "AddressInfo", QTrippin_1.qLocation, options);
        }
        return this._AddressInfo;
    };
    EmployeeService.prototype.HomeAddress = function () {
        if (!this._HomeAddress) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._HomeAddress = new LocationService(client, path, "HomeAddress", options);
        }
        return this._HomeAddress;
    };
    EmployeeService.prototype.Features = function () {
        if (!this._Features) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Features = new odata_service_1.CollectionServiceV4(client, path, "Features", new odata_query_objects_1.QEnumCollection(TrippinModel_1.Feature), options);
        }
        return this._Features;
    };
    EmployeeService.prototype.Friends = function (id) {
        var fieldName = "Friends";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    EmployeeService.prototype.BestFriend = function () {
        if (!this._BestFriend) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._BestFriend = new PersonService(client, path, "BestFriend", options);
        }
        return this._BestFriend;
    };
    EmployeeService.prototype.Trips = function (id) {
        var fieldName = "Trips";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new TripCollectionService(client, path, fieldName, options)
            : new TripService(client, path, new QTrippin_1.QTripId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    EmployeeService.prototype.Peers = function (id) {
        var fieldName = "Peers";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    return EmployeeService;
}(odata_service_1.EntityTypeServiceV4));
exports.EmployeeService = EmployeeService;
var EmployeeCollectionService = /** @class */ (function (_super) {
    __extends(EmployeeCollectionService, _super);
    function EmployeeCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qEmployee, new QTrippin_1.QPersonId(name), options) || this;
    }
    return EmployeeCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.EmployeeCollectionService = EmployeeCollectionService;
var ManagerService = /** @class */ (function (_super) {
    __extends(ManagerService, _super);
    function ManagerService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qManager, options) || this;
    }
    ManagerService.prototype.Emails = function () {
        if (!this._Emails) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Emails = new odata_service_1.CollectionServiceV4(client, path, "Emails", new odata_query_objects_1.QStringCollection(), options);
        }
        return this._Emails;
    };
    ManagerService.prototype.AddressInfo = function () {
        if (!this._AddressInfo) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._AddressInfo = new odata_service_1.CollectionServiceV4(client, path, "AddressInfo", QTrippin_1.qLocation, options);
        }
        return this._AddressInfo;
    };
    ManagerService.prototype.HomeAddress = function () {
        if (!this._HomeAddress) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._HomeAddress = new LocationService(client, path, "HomeAddress", options);
        }
        return this._HomeAddress;
    };
    ManagerService.prototype.Features = function () {
        if (!this._Features) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._Features = new odata_service_1.CollectionServiceV4(client, path, "Features", new odata_query_objects_1.QEnumCollection(TrippinModel_1.Feature), options);
        }
        return this._Features;
    };
    ManagerService.prototype.Friends = function (id) {
        var fieldName = "Friends";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    ManagerService.prototype.BestFriend = function () {
        if (!this._BestFriend) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._BestFriend = new PersonService(client, path, "BestFriend", options);
        }
        return this._BestFriend;
    };
    ManagerService.prototype.Trips = function (id) {
        var fieldName = "Trips";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new TripCollectionService(client, path, fieldName, options)
            : new TripService(client, path, new QTrippin_1.QTripId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    ManagerService.prototype.BossOffice = function () {
        if (!this._BossOffice) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._BossOffice = new LocationService(client, path, "BossOffice", options);
        }
        return this._BossOffice;
    };
    ManagerService.prototype.DirectReports = function (id) {
        var fieldName = "DirectReports";
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options, isUrlNotEncoded = _a.isUrlNotEncoded;
        return typeof id === "undefined" || id === null
            ? new PersonCollectionService(client, path, fieldName, options)
            : new PersonService(client, path, new QTrippin_1.QPersonId(fieldName).buildUrl(id, isUrlNotEncoded()), options);
    };
    return ManagerService;
}(odata_service_1.EntityTypeServiceV4));
exports.ManagerService = ManagerService;
var ManagerCollectionService = /** @class */ (function (_super) {
    __extends(ManagerCollectionService, _super);
    function ManagerCollectionService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qManager, new QTrippin_1.QPersonId(name), options) || this;
    }
    return ManagerCollectionService;
}(odata_service_1.EntitySetServiceV4));
exports.ManagerCollectionService = ManagerCollectionService;
var LocationService = /** @class */ (function (_super) {
    __extends(LocationService, _super);
    function LocationService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qLocation, options) || this;
    }
    LocationService.prototype.City = function () {
        if (!this._City) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._City = new CityService(client, path, "City", options);
        }
        return this._City;
    };
    LocationService.prototype.asAirportLocationService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new AirportLocationService(client, path, "Trippin.AirportLocation", __assign(__assign({}, options), { subtype: true }));
    };
    LocationService.prototype.asEventLocationService = function () {
        var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
        return new EventLocationService(client, path, "Trippin.EventLocation", __assign(__assign({}, options), { subtype: true }));
    };
    return LocationService;
}(odata_service_1.EntityTypeServiceV4));
exports.LocationService = LocationService;
var CityService = /** @class */ (function (_super) {
    __extends(CityService, _super);
    function CityService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qCity, options) || this;
    }
    return CityService;
}(odata_service_1.EntityTypeServiceV4));
exports.CityService = CityService;
var AirportLocationService = /** @class */ (function (_super) {
    __extends(AirportLocationService, _super);
    function AirportLocationService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qAirportLocation, options) || this;
    }
    AirportLocationService.prototype.City = function () {
        if (!this._City) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._City = new CityService(client, path, "City", options);
        }
        return this._City;
    };
    return AirportLocationService;
}(odata_service_1.EntityTypeServiceV4));
exports.AirportLocationService = AirportLocationService;
var EventLocationService = /** @class */ (function (_super) {
    __extends(EventLocationService, _super);
    function EventLocationService(client, basePath, name, options) {
        return _super.call(this, client, basePath, name, QTrippin_1.qEventLocation, options) || this;
    }
    EventLocationService.prototype.City = function () {
        if (!this._City) {
            var _a = this.__base, client = _a.client, path = _a.path, options = _a.options;
            this._City = new CityService(client, path, "City", options);
        }
        return this._City;
    };
    return EventLocationService;
}(odata_service_1.EntityTypeServiceV4));
exports.EventLocationService = EventLocationService;
