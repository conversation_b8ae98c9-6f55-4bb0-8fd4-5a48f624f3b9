/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/** @module odata/handler */

import { assigned, extend, trimString } from '../utils'
import { maxVersion } from './odatautils'

export const MAX_DATA_SERVICE_VERSION = '4.0'

export interface ContentType {
  mediaType: string
  properties: Record<string, string>
}

/** Parses a string into an object with media type and properties. */
export function contentType(str?: string): ContentType | null {
  if (!str) return null

  const contentTypeParts = str.split(';')
  const properties: Record<string, string> = {}

  for (let i = 1; i < contentTypeParts.length; i++) {
    const [key, value] = contentTypeParts[i].split('=')
    properties[trimString(key)] = value
  }

  return { mediaType: trimString(contentTypeParts[0]), properties }
}

/** Serializes a content type object into a string. */
export function contentTypeToString(
  contentType?: ContentType
): string | undefined {
  if (!contentType) return undefined

  let result = contentType.mediaType
  for (const property in contentType.properties) {
    result += `;${property}=${contentType.properties[property]}`
  }
  return result
}

/** Creates a read/write context for handlers. */
export function createReadWriteContext(
  contentType: ContentType | null,
  dataServiceVersion: string,
  context: Record<string, any>,
  handler: any
): Record<string, any> {
  const rwContext: Record<string, any> = {}
  extend(rwContext, context)
  extend(rwContext, { contentType, dataServiceVersion, handler })
  return rwContext
}

/** Sets a request header if not already set. */
export function fixRequestHeader(request: any, name: string, value: string) {
  if (!request) return
  const headers = request.headers
  if (!headers[name]) {
    headers[name] = value
  }
}

/** Sets the DataServiceVersion header if not set or lower than given version. */
export function fixDataServiceVersionHeader(request: any, version: string) {
  if (request) {
    const headers = request.headers
    const dsv = headers['OData-Version']
    headers['OData-Version'] = dsv ? maxVersion(dsv, version) : version
  }
}

/** Gets a request/response header value. */
export function getRequestOrResponseHeader(
  requestOrResponse: any,
  name: string
): string | undefined {
  return requestOrResponse?.headers?.[name]
}

/** Gets the Content-Type header object from request/response. */
export function getContentType(requestOrResponse: any): ContentType | null {
  return contentType(
    getRequestOrResponseHeader(requestOrResponse, 'Content-Type')
  )
}

const versionRE = /^\s?(\d+\.\d+);?.*$/

/** Gets the DataServiceVersion header value from request/response. */
export function getDataServiceVersion(
  requestOrResponse: any
): string | undefined {
  const value = getRequestOrResponseHeader(requestOrResponse, 'OData-Version')
  if (value) {
    const matches = versionRE.exec(value)
    if (matches) return matches[1]
  }
}

/** Checks if a handler accepts a given content type. */
export function handlerAccepts(
  handler: any,
  cType: ContentType | null
): boolean {
  return cType ? handler.accept.includes(cType.mediaType) : false
}

/** Reads response payload using a handler. */
export function handlerRead(
  handler: any,
  parseCallback: Function,
  response: any,
  context: Record<string, any>
): boolean {
  if (!response?.headers) return false

  const cType = getContentType(response)
  const version = getDataServiceVersion(response) || ''
  const body = response.body

  if (!assigned(body)) return false

  if (handlerAccepts(handler, cType)) {
    const readContext = createReadWriteContext(cType, version, context, handler)
    readContext.response = response
    response.data = parseCallback(handler, body, readContext)
    return response.data !== undefined
  }

  return false
}

/** Serializes request payload using a handler. */
export function handlerWrite(
  handler: any,
  serializeCallback: Function,
  request: any,
  context: Record<string, any>
): boolean {
  if (!request?.headers) return false

  const cType = getContentType(request)
  const version = getDataServiceVersion(request)

  if (!cType || handlerAccepts(handler, cType)) {
    const writeContext = createReadWriteContext(
      cType,
      version,
      context,
      handler
    )
    writeContext.request = request

    request.body = serializeCallback(handler, request.data, writeContext)

    if (request.body !== undefined) {
      fixDataServiceVersionHeader(
        request,
        writeContext.dataServiceVersion || '4.0'
      )
      fixRequestHeader(
        request,
        'Content-Type',
        contentTypeToString(writeContext.contentType)!
      )
      fixRequestHeader(
        request,
        'OData-MaxVersion',
        handler.maxDataServiceVersion
      )
      return true
    }
  }

  return false
}

/** Creates a handler object for requests/responses. */
export function handler(
  parseCallback: Function,
  serializeCallback: Function,
  accept: string,
  maxDataServiceVersion: string
) {
  return {
    accept,
    maxDataServiceVersion,
    read(response: any, context: Record<string, any>) {
      return handlerRead(this, parseCallback, response, context)
    },
    write(request: any, context: Record<string, any>) {
      return handlerWrite(this, serializeCallback, request, context)
    },
  }
}

/** Default text/plain handler. */
function textParse(_handler: any, body: any) {
  return body
}

function textSerialize(_handler: any, data: any) {
  return assigned(data) ? data.toString() : undefined
}

export const textHandler = handler(
  textParse,
  textSerialize,
  'text/plain',
  MAX_DATA_SERVICE_VERSION
)
