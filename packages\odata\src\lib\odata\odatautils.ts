/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/** @module odata/utils */

import {
  assigned,
  contains,
  find,
  isArray,
  isDate,
  isObject,
  parseInt10,
} from '../utils.js'

// Type definitions
interface Metadata {
  type?: string
  [key: string]: any
}

interface DataItem {
  __metadata?: Metadata
  [key: string]: any
}

interface Schema {
  namespace?: string
  dataServices?: { schema: Schema | Schema[] }
  entityContainer?: any
  [key: string]: any
}

interface DateTimeValue extends Date {
  __edmType?: string
  __offset?: string
  __ns?: number
}

interface DurationValue {
  ms: number
  ns?: number
  __edmType: string
}

interface TimezoneInfo {
  d: number
  h: number
  m: number
}

interface TimeOfDayValue {
  h: number
  m: number
  s: number
  ms: number
}

interface RequestObject {
  method?: string
  headers?: Record<string, string>
  data?: any
  body?: any
  async?: boolean
  [key: string]: any
}

interface ResponseObject {
  data?: any
  headers?: Record<string, string>
  statusCode?: number
  [key: string]: any
}

interface Handler {
  accept?: string
  maxDataServiceVersion?: string
  read?: (response: ResponseObject, context: any) => void
  write?: (request: RequestObject, context: any) => void
}

interface HttpClient {
  request: (
    request: RequestObject,
    success: (response: ResponseObject) => void,
    error: (err: any) => void
  ) => any
}

/** Gets the type name of a data item value that belongs to a feed, an entry, a complex type property, or a collection property
 * @param value - Value of the data item from which the type name is going to be retrieved.
 * @param metadata - Object containing metadata about the data item.
 * @returns Data item type name; null if the type name cannot be found within the value or the metadata
 * This function will first try to get the type name from the data item's value itself if it is an object with a __metadata property; otherwise
 * it will try to recover it from the metadata.  If both attempts fail, it will return null.
 */
export const dataItemTypeName = (
  value: DataItem,
  metadata?: Metadata
): string | null => {
  const valueTypeName = ((value && value.__metadata) || {}).type
  return valueTypeName || (metadata ? metadata.type : null)
}

// EDM Type Constants
const EDM = 'Edm.'
export const EDM_BOOLEAN = `${EDM}Boolean`
export const EDM_BYTE = `${EDM}Byte`
export const EDM_SBYTE = `${EDM}SByte`
export const EDM_INT16 = `${EDM}Int16`
export const EDM_INT32 = `${EDM}Int32`
export const EDM_INT64 = `${EDM}Int64`
export const EDM_SINGLE = `${EDM}Single`
export const EDM_DOUBLE = `${EDM}Double`
export const EDM_DECIMAL = `${EDM}Decimal`
export const EDM_STRING = `${EDM}String`

export const EDM_BINARY = `${EDM}Binary`
export const EDM_DATE = `${EDM}Date`
export const EDM_DATETIMEOFFSET = `${EDM}DateTimeOffset`
export const EDM_DURATION = `${EDM}Duration`
export const EDM_GUID = `${EDM}Guid`
export const EDM_TIMEOFDAY = `${EDM}Time`

// Geography Types
const GEOGRAPHY = 'Geography'
export const EDM_GEOGRAPHY = EDM + GEOGRAPHY
export const EDM_GEOGRAPHY_POINT = `${EDM_GEOGRAPHY}Point`
export const EDM_GEOGRAPHY_LINESTRING = `${EDM_GEOGRAPHY}LineString`
export const EDM_GEOGRAPHY_POLYGON = `${EDM_GEOGRAPHY}Polygon`
export const EDM_GEOGRAPHY_COLLECTION = `${EDM_GEOGRAPHY}Collection`
export const EDM_GEOGRAPHY_MULTIPOLYGON = `${EDM_GEOGRAPHY}MultiPolygon`
export const EDM_GEOGRAPHY_MULTILINESTRING = `${EDM_GEOGRAPHY}MultiLineString`
export const EDM_GEOGRAPHY_MULTIPOINT = `${EDM_GEOGRAPHY}MultiPoint`

const GEOGRAPHY_POINT = `${GEOGRAPHY}Point`
const GEOGRAPHY_LINESTRING = `${GEOGRAPHY}LineString`
const GEOGRAPHY_POLYGON = `${GEOGRAPHY}Polygon`
const GEOGRAPHY_COLLECTION = `${GEOGRAPHY}Collection`
const GEOGRAPHY_MULTIPOLYGON = `${GEOGRAPHY}MultiPolygon`
const GEOGRAPHY_MULTILINESTRING = `${GEOGRAPHY}MultiLineString`
const GEOGRAPHY_MULTIPOINT = `${GEOGRAPHY}MultiPoint`

// Geometry Types
const GEOMETRY = 'Geometry'
export const EDM_GEOMETRY = EDM + GEOMETRY
export const EDM_GEOMETRY_POINT = `${EDM_GEOMETRY}Point`
export const EDM_GEOMETRY_LINESTRING = `${EDM_GEOMETRY}LineString`
export const EDM_GEOMETRY_POLYGON = `${EDM_GEOMETRY}Polygon`
export const EDM_GEOMETRY_COLLECTION = `${EDM_GEOMETRY}Collection`
export const EDM_GEOMETRY_MULTIPOLYGON = `${EDM_GEOMETRY}MultiPolygon`
export const EDM_GEOMETRY_MULTILINESTRING = `${EDM_GEOMETRY}MultiLineString`
export const EDM_GEOMETRY_MULTIPOINT = `${EDM_GEOMETRY}MultiPoint`

const GEOMETRY_POINT = `${GEOMETRY}Point`
const GEOMETRY_LINESTRING = `${GEOMETRY}LineString`
const GEOMETRY_POLYGON = `${GEOMETRY}Polygon`
const GEOMETRY_COLLECTION = `${GEOMETRY}Collection`
const GEOMETRY_MULTIPOLYGON = `${GEOMETRY}MultiPolygon`
const GEOMETRY_MULTILINESTRING = `${GEOMETRY}MultiLineString`
const GEOMETRY_MULTIPOINT = `${GEOMETRY}MultiPoint`

// GeoJSON Types
export const GEOJSON_POINT = 'Point'
export const GEOJSON_LINESTRING = 'LineString'
export const GEOJSON_POLYGON = 'Polygon'
export const GEOJSON_MULTIPOINT = 'MultiPoint'
export const GEOJSON_MULTILINESTRING = 'MultiLineString'
export const GEOJSON_MULTIPOLYGON = 'MultiPolygon'
export const GEOJSON_GEOMETRYCOLLECTION = 'GeometryCollection'

// Type Arrays
const primitiveEdmTypes = [
  EDM_STRING,
  EDM_INT32,
  EDM_INT64,
  EDM_BOOLEAN,
  EDM_DOUBLE,
  EDM_SINGLE,
  EDM_DATE,
  EDM_DATETIMEOFFSET,
  EDM_DURATION,
  EDM_TIMEOFDAY,
  EDM_DECIMAL,
  EDM_GUID,
  EDM_BYTE,
  EDM_INT16,
  EDM_SBYTE,
  EDM_BINARY,
]

const geometryEdmTypes = [
  EDM_GEOMETRY,
  EDM_GEOMETRY_POINT,
  EDM_GEOMETRY_LINESTRING,
  EDM_GEOMETRY_POLYGON,
  EDM_GEOMETRY_COLLECTION,
  EDM_GEOMETRY_MULTIPOLYGON,
  EDM_GEOMETRY_MULTILINESTRING,
  EDM_GEOMETRY_MULTIPOINT,
]

const geometryTypes = [
  GEOMETRY,
  GEOMETRY_POINT,
  GEOMETRY_LINESTRING,
  GEOMETRY_POLYGON,
  GEOMETRY_COLLECTION,
  GEOMETRY_MULTIPOLYGON,
  GEOMETRY_MULTILINESTRING,
  GEOMETRY_MULTIPOINT,
]

const geographyEdmTypes = [
  EDM_GEOGRAPHY,
  EDM_GEOGRAPHY_POINT,
  EDM_GEOGRAPHY_LINESTRING,
  EDM_GEOGRAPHY_POLYGON,
  EDM_GEOGRAPHY_COLLECTION,
  EDM_GEOGRAPHY_MULTIPOLYGON,
  EDM_GEOGRAPHY_MULTILINESTRING,
  EDM_GEOGRAPHY_MULTIPOINT,
]

const geographyTypes = [
  GEOGRAPHY,
  GEOGRAPHY_POINT,
  GEOGRAPHY_LINESTRING,
  GEOGRAPHY_POLYGON,
  GEOGRAPHY_COLLECTION,
  GEOGRAPHY_MULTIPOLYGON,
  GEOGRAPHY_MULTILINESTRING,
  GEOGRAPHY_MULTIPOINT,
]

/** Invokes a function once per schema in metadata.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @param callback - Callback function to invoke once per schema.
 * @returns The first truthy value to be returned from the callback; null or the last falsy value otherwise.
 */
export function forEachSchema(
  metadata: Schema | Schema[] | null,
  callback: (schema: Schema) => any
): any {
  if (!metadata) {
    return null
  }

  if (isArray(metadata)) {
    for (let i = 0, len = metadata.length; i < len; i++) {
      const result = forEachSchema(metadata[i], callback)
      if (result) {
        return result
      }
    }
    return null
  } else {
    if (metadata.dataServices) {
      return forEachSchema(metadata.dataServices.schema, callback)
    }
    return callback(metadata)
  }
}

/** Formats a millisecond and a nanosecond value into a single string.
 * @param ms - Number of milliseconds to format.
 * @param ns - Number of nanoseconds to format.
 * @returns Formatted text.
 * If the value is already as string it's returned as-is.
 */
export function formatMilliseconds(ms: number, ns: number = 0): string {
  // Avoid generating milliseconds if not necessary.
  let msStr = ''
  if (ms === 0) {
    msStr = ''
  } else {
    msStr = `.${formatNumberWidth(ms.toString(), 3, false)}`
  }
  if (ns > 0) {
    if (msStr === '') {
      msStr = '.000'
    }
    msStr += formatNumberWidth(ns.toString(), 4, false)
  }
  return msStr
}

/** Formats a DateTime or DateTimeOffset value as JSON string.
 * @param value - Value to format
 * @returns Formatted text.
 */
export function formatDateTimeOffsetJSON(value: Date): string {
  return `/Date(${value.getTime()})/`
}

/** Formats a DateTime or DateTimeOffset value as string.
 * @param value - Value to format
 * @returns Formatted text.
 * If the value is already as string it's returned as-is
 */
export function formatDateTimeOffset(value: string | DateTimeValue): string {
  if (typeof value === 'string') {
    return value
  }

  const hasOffset = isDateTimeOffset(value)
  const offset = getCanonicalTimezone(value.__offset)

  if (hasOffset && offset !== 'Z') {
    // We're about to change the value, so make a copy.
    value = new Date(value.valueOf()) as DateTimeValue
    value.__offset = (value as any).__offset
    value.__ns = (value as any).__ns

    const timezone = parseTimezone(offset)
    const hours = value.getUTCHours() + timezone.d * timezone.h
    const minutes = value.getUTCMinutes() + timezone.d * timezone.m

    value.setUTCHours(hours, minutes)
  } else if (!hasOffset) {
    // Don't suffix a 'Z' for Edm.DateTime values.
    const finalOffset = ''
  }

  const year = value.getUTCFullYear()
  const month = value.getUTCMonth() + 1
  let sign = ''
  let finalYear = year

  if (year <= 0) {
    finalYear = -(year - 1)
    sign = '-'
  }

  const ms = formatMilliseconds(value.getUTCMilliseconds(), value.__ns || 0)

  return `${
    sign + formatNumberWidth(finalYear.toString(), 4, false)
  }-${formatNumberWidth(month.toString(), 2, false)}-${formatNumberWidth(
    value.getUTCDate().toString(),
    2,
    false
  )}T${formatNumberWidth(
    value.getUTCHours().toString(),
    2,
    false
  )}:${formatNumberWidth(
    value.getUTCMinutes().toString(),
    2,
    false
  )}:${formatNumberWidth(value.getUTCSeconds().toString(), 2, false)}${ms}${
    hasOffset ? offset : ''
  }`
}

/** Converts a duration to a string in xsd:duration format.
 * @param value - Object with ms and __edmType properties.
 * @returns String representation of the time object in xsd:duration format.
 */
export function formatDuration(value: DurationValue): string {
  let ms = value.ms

  let sign = ''
  if (ms < 0) {
    sign = '-'
    ms = -ms
  }

  const days = Math.floor(ms / 86400000)
  ms -= 86400000 * days
  const hours = Math.floor(ms / 3600000)
  ms -= 3600000 * hours
  const minutes = Math.floor(ms / 60000)
  ms -= 60000 * minutes
  const seconds = Math.floor(ms / 1000)
  ms -= seconds * 1000

  return `${sign}P${formatNumberWidth(
    days.toString(),
    2,
    false
  )}DT${formatNumberWidth(hours.toString(), 2, false)}H${formatNumberWidth(
    minutes.toString(),
    2,
    false
  )}M${formatNumberWidth(seconds.toString(), 2, false)}${formatMilliseconds(
    ms,
    value.ns || 0
  )}S`
}

/** Formats the specified value to the given width.
 * @param value - Number to format (non-negative).
 * @param width - Minimum width for number.
 * @param append - Flag indicating if the value is padded at the beginning (false) or at the end (true).
 * @returns Text representation.
 */
export function formatNumberWidth(
  value: string,
  width: number,
  append: boolean = false
): string {
  let result = value
  while (result.length < width) {
    if (append) {
      result += '0'
    } else {
      result = `0${result}`
    }
  }
  return result
}

/** Gets the canonical timezone representation.
 * @param timezone - Timezone representation.
 * @returns An 'Z' string if the timezone is absent or 0; the timezone otherwise.
 */
export function getCanonicalTimezone(timezone?: string): string {
  return !timezone ||
    timezone === 'Z' ||
    timezone === '+00:00' ||
    timezone === '-00:00'
    ? 'Z'
    : timezone
}

/** Checks whether a Date object is DateTimeOffset value
 * @param value - Value to check
 * @returns true if the value is a DateTimeOffset, false otherwise.
 */
export function isDateTimeOffset(value: DateTimeValue): boolean {
  return (
    value.__edmType === 'Edm.DateTimeOffset' ||
    (!value.__edmType && !!value.__offset)
  )
}

/** Parses a timezone description in (+|-)nn:nn format.
 * @param timezone - Timezone offset.
 * @returns An object with a (d)irection property of 1 for + and -1 for -, offset (h)ours and offset (m)inutes.
 */
export function parseTimezone(timezone: string): TimezoneInfo {
  const direction = timezone.slice(0, 1)
  const d = direction === '+' ? 1 : -1

  const offsetHours = parseInt10(timezone.slice(1))
  const offsetMinutes = parseInt10(
    timezone.slice(Math.max(0, timezone.indexOf(':') + 1))
  )
  return { d, h: offsetHours, m: offsetMinutes }
}

/** Gets the type of a collection type name.
 * @param typeName - Type name of the collection.
 * @returns Type of the collection; null if the type name is not a collection type.
 */
export function getCollectionType(typeName: string): string | null {
  if (typeof typeName === 'string') {
    const end = typeName.indexOf(')', 10)
    if (typeName.indexOf('Collection(') === 0 && end > 0) {
      return typeName.slice(11, end)
    }
  }
  return null
}

/** Tests whether a value is a batch object in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is a batch object; false otherwise.
 */
export function isBatch(value: any): boolean {
  return isComplex(value) && isArray(value.__batchRequests)
}

// Regular expression used for testing and parsing for a collection type.
const collectionTypeRE = /Collection\((.*)\)/

/** Tests whether a value is a collection value in the library's internal representation.
 * @param value - Value to test.
 * @param typeName - Type name of the value. This is used to disambiguate from a collection property value.
 * @returns True is the value is a feed value; false otherwise.
 */
export function isCollection(value: any, typeName?: string): boolean {
  const colData = (value && value.results) || value
  return (
    !!colData &&
    (isCollectionType(typeName) ||
      (!typeName && isArray(colData) && !isComplex(colData[0])))
  )
}

/** Checks whether the specified type name is a collection type.
 * @param typeName - Name of type to check.
 * @returns True if the type is the name of a collection type; false otherwise.
 */
export function isCollectionType(typeName?: string): boolean {
  return typeName ? collectionTypeRE.test(typeName) : false
}

/** Tests whether a value is a complex type value in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is a complex type value; false otherwise.
 */
export function isComplex(value: any): boolean {
  return !!value && isObject(value) && !isArray(value) && !isDate(value)
}

/** Tests whether a value is a deferred navigation property in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is a deferred navigation property; false otherwise.
 */
export function isDeferred(value: any): boolean {
  if (!value && !isComplex(value)) {
    return false
  }
  const metadata = value.__metadata || {}
  const deferred = value.__deferred || {}
  return !metadata.type && !!deferred.uri
}

/** Tests whether a value is an entry object in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is an entry object; false otherwise.
 */
export function isEntry(value: any): boolean {
  return isComplex(value) && value.__metadata && 'uri' in value.__metadata
}

/** Tests whether a value is a feed value in the library's internal representation.
 * @param value - Value to test.
 * @param typeName - Type name of the value. This is used to disambiguate from a collection property value.
 * @returns True is the value is a feed value; false otherwise.
 */
export function isFeed(value: any, typeName?: string): boolean {
  const feedData = (value && value.results) || value
  return (
    isArray(feedData) && !isCollectionType(typeName) && isComplex(feedData[0])
  )
}

/** Checks whether the specified type name is a geography EDM type.
 * @param typeName - Name of type to check.
 * @returns True if the type is a geography EDM type; false otherwise.
 */
export function isGeographyEdmType(typeName: string): boolean {
  return (
    contains(geographyEdmTypes, typeName) ||
    (!typeName.includes('.') && contains(geographyTypes, typeName))
  )
}

/** Checks whether the specified type name is a geometry EDM type.
 * @param typeName - Name of type to check.
 * @returns True if the type is a geometry EDM type; false otherwise.
 */
export function isGeometryEdmType(typeName: string): boolean {
  return (
    contains(geometryEdmTypes, typeName) ||
    (!typeName.includes('.') && contains(geometryTypes, typeName))
  )
}

/** Tests whether a value is a named stream value in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is a named stream; false otherwise.
 */
export function isNamedStream(value: any): boolean {
  if (!value && !isComplex(value)) {
    return false
  }
  const metadata = value.__metadata
  const mediaResource = value.__mediaresource
  return !metadata && !!mediaResource && !!mediaResource.media_src
}

/** Tests whether a value is a primitive type value in the library's internal representation.
 * @param value - Value to test.
 * @returns True is the value is a primitive type value.
 * Date objects are considered primitive types by the library.
 */
export function isPrimitive(value: any): boolean {
  return (
    isDate(value) ||
    typeof value === 'string' ||
    typeof value === 'number' ||
    typeof value === 'boolean'
  )
}

/** Checks whether the specified type name is a primitive EDM type.
 * @param typeName - Name of type to check.
 * @returns True if the type is a primitive EDM type; false otherwise.
 */
export function isPrimitiveEdmType(typeName: string): boolean {
  return contains(primitiveEdmTypes, typeName)
}

/** Gets the kind of a navigation property value.
 * @param value - Value of the navigation property.
 * @param propertyModel - Object that describes the navigation property in an OData conceptual schema.
 * @returns String value describing the kind of the navigation property; null if the kind cannot be determined.
 */
export function navigationPropertyKind(
  value: any,
  propertyModel?: any
): string | null {
  if (isDeferred(value)) {
    return 'deferred'
  }
  if (isEntry(value)) {
    return 'entry'
  }
  if (isFeed(value)) {
    return 'feed'
  }
  if (propertyModel && propertyModel.relationship) {
    if (value === null || value === undefined || !isFeed(value)) {
      return 'entry'
    }
    return 'feed'
  }
  return null
}

/** Looks up a property by name.
 * @param properties - Array of property objects as per EDM metadata (may be null)
 * @param name - Name to look for.
 * @returns The property object; null if not found.
 */
export function lookupProperty(
  properties: any[] | null,
  name: string
): any | null {
  return find(properties, (property: any) => property.name === name)
}

/** Looks up a type object by name.
 * @param name - Name, possibly null or empty.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @param kind - Kind of object to look for as per EDM metadata.
 * @returns An type description if the name is found; null otherwise
 */
export function lookupInMetadata(
  name: string | null,
  metadata: any,
  kind: string
): any | null {
  return name
    ? forEachSchema(metadata, (schema: Schema) =>
        lookupInSchema(name, schema, kind)
      )
    : null
}

/** Looks up a entity set by name.
 * @param entitySets - Array of entity set objects as per EDM metadata( may be null)
 * @param name - Name to look for.
 * @returns The entity set object; null if not found.
 */
export function lookupEntitySet(
  entitySets: any[] | null,
  name: string
): any | null {
  return find(entitySets, (entitySet: any) => entitySet.name === name)
}

/** Looks up a singleton by name.
 * @param singletons - Array of singleton objects as per EDM metadata (may be null)
 * @param name - Name to look for.
 * @returns The singleton object; null if not found.
 */
export function lookupSingleton(
  singletons: any[] | null,
  name: string
): any | null {
  return find(singletons, (singleton: any) => singleton.name === name)
}

/** Looks up a complex type object by name.
 * @param name - Name, possibly null or empty.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @returns A complex type description if the name is found; null otherwise.
 */
export function lookupComplexType(
  name: string | null,
  metadata: any
): any | null {
  return lookupInMetadata(name, metadata, 'complexType')
}

/** Looks up an entity type object by name.
 * @param name - Name, possibly null or empty.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @returns An entity type description if the name is found; null otherwise.
 */
export function lookupEntityType(
  name: string | null,
  metadata: any
): any | null {
  return lookupInMetadata(name, metadata, 'entityType')
}

/** Looks up the default entity container.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @returns An entity container description if found; null otherwise.
 */
export function lookupDefaultEntityContainer(metadata: any): any | null {
  return forEachSchema(metadata, (schema: Schema) => {
    if (isObject(schema.entityContainer)) {
      return schema.entityContainer
    }
    return null
  })
}

/** Looks up an entity container object by name.
 * @param name - Name, possibly null or empty.
 * @param metadata - Metadata store; one of edmx, schema, or an array of any of them.
 * @returns An entity container description if the name is found; null otherwise.
 */
export function lookupEntityContainer(
  name: string | null,
  metadata: any
): any | null {
  return lookupInMetadata(name, metadata, 'entityContainer')
}

/** Looks up a schema object by name.
 * @param name - Name (assigned).
 * @param schema - Schema object as per EDM metadata.
 * @param kind - Kind of object to look for as per EDM metadata.
 * @returns An entity type description if the name is found; null otherwise.
 */
export function lookupInSchema(
  name: string,
  schema: Schema,
  kind: string
): any | null {
  if (name && schema) {
    // The name should be the namespace qualified name in 'ns'.'type' format.
    const nameOnly = removeNamespace(schema.namespace, name)
    if (nameOnly) {
      return find(schema[kind], (item: any) => item.name === nameOnly)
    }
  }
  return null
}

/** Given an expected namespace prefix, removes it from a full name.
 * @param ns - Expected namespace.
 * @param fullName - Full name in 'ns'.'name' form.
 * @returns The local name, null if it isn't found in the expected namespace.
 */
export function removeNamespace(
  ns: string | undefined,
  fullName: string
): string | null {
  if (ns && fullName.indexOf(ns) === 0 && fullName.charAt(ns.length) === '.') {
    return fullName.slice(ns.length + 1)
  }
  return null
}
