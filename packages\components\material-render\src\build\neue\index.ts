// ES6 模块包装器，使用动态导入来处理 CommonJS 模块

// 创建一个异步函数来加载模块
export const loadPaaSModules = async () => {
  try {
    const [PaaSModelModule, PaaSServiceModule, QPaaSModule] = await Promise.all(
      [import('./PaaSModel'), import('./PaaSService'), import('./QPaaS')]
    )

    return {
      PaaSModelModule,
      PaaSServiceModule,
      QPaaSModule,
      // 直接导出常用的枚举
      LifecycleState: PaaSModelModule.LifecycleState,
      OnDelete: PaaSModelModule.OnDelete,
      MrvStrategyType: PaaSModelModule.MrvStrategyType,
      RelationSourceTypeEnum: PaaSModelModule.RelationSourceTypeEnum,
      RelationConstrictEnum: PaaSModelModule.RelationConstrictEnum,
      RelationStatus: PaaSModelModule.RelationStatus,
      RelationClassify: PaaSModelModule.RelationClassify,
      RelationVersionUpgradeActionEnum:
        PaaSModelModule.RelationVersionUpgradeActionEnum,
      IdiConvertState: PaaSModelModule.IdiConvertState,
      PartType: PaaSModelModule.PartType,
      PaaSService: PaaSServiceModule.PaaSService,
    }
  } catch (error) {
    console.error('Failed to load PaaS modules:', error)
    throw error
  }
}

// 为了保持同步导入的体验，我们可以创建一个懒加载的代理
let cachedModules: any = null

export const getPaaSModules = async () => {
  if (!cachedModules) {
    cachedModules = await loadPaaSModules()
  }
  return cachedModules
}
