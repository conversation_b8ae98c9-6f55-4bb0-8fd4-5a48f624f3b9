// ES6 模块包装器，用于重新导出 CommonJS 模块
import * as PaaSModelModule from './PaaSModel'
import * as PaaSServiceModule from './PaaSService'
import * as QPaaSModule from './QPaaS'

// 重新导出所有枚举类型
export const {
  LifecycleState,
  OnDelete,
  MrvStrategyType,
  RelationSourceTypeEnum,
  RelationConstrictEnum,
  RelationStatus,
  RelationClassify,
  RelationVersionUpgradeActionEnum,
  IdiConvertState,
  PartType,
} = PaaSModelModule

// 重新导出服务类
export const {
  PaaSService,
} = PaaSServiceModule

// 重新导出查询对象（如果有的话）
export * from './QPaaS'

// 默认导出常用的内容
export default {
  ...PaaSModelModule,
  ...PaaSServiceModule,
  ...QPaaSModule,
}

// 为了向后兼容，也导出整个模块
export { PaaSModelModule, PaaSServiceModule, QPaaSModule }
