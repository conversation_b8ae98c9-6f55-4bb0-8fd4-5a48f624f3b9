import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Inspect from 'vite-plugin-inspect'
import VueMacros from 'unplugin-vue-macros/vite'
import { defineConfig } from 'vite'
// import { resolve } from 'path'
import Components from 'unplugin-vue-components/vite'
import { NeuePlusResolver } from '@neue-plus/resolver'
export default defineConfig({
  build: {
    sourcemap: true,
    commonjsOptions: {
      transformMixedEsModules: true, // 转换 CJS -> ESM
    },
  },
  plugins: [
    VueMacros({
      setupComponent: false,
      setupSFC: false,
      plugins: {
        vue: vue(),
        vueJsx: vueJsx(),
      },
    }),
    // AutoImport({
    //   resolvers: [ElementPlusResolver()],
    // }),
    Components({
      include: `${__dirname}/**`,
      resolvers: [
        NeuePlusResolver({
          version: '1.0.0',
          importStyle: 'sass',
        }),
      ],
      dts: false,
    }),
    Inspect(),
  ],
  resolve: {
    alias: {
      vue: 'vue/dist/vue.esm-bundler.js', // ✅ 关键配置
      // 'neue-plus': resolve(__dirname, '../packages/neue-plus/index.ts'),
    },
  },
  server: {
    open: true, // 设置服务启动时是否自动打开浏览器
    cors: true, // 允许跨域
    // 设置代理，根据我们项目实际情况配置
    proxy: {
      '/modeling': {
        target: 'https://api-cn-hangzhou-2.cloud.neuetech.cn/',
        changeOrigin: true,
      },
      '/account': {
        target: 'https://api-cn-hangzhou-2.cloud.neuetech.cn/',
        changeOrigin: true,
      },
    },
  },
})
