/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { EntityTypeUnionUniqPropertiesApi } from './EntityTypeUnionUniqPropertiesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "EntityTypeUnionUniqProperties" of service "neue".
 */
export class EntityTypeUnionUniqProperties<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements EntityTypeUnionUniqPropertiesType<T>
{
  /**
   * Technical entity name for EntityTypeUnionUniqProperties.
   */
  static override _entityName = 'EntityTypeUnionUniqProperties';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the EntityTypeUnionUniqProperties entity.
   */
  static _keys = ['ncid'];
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: EntityTypeUnionUniqPropertiesApi<T>) {
    super(_entityApi);
  }
}

export interface EntityTypeUnionUniqPropertiesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
