import { ExtractPropTypes, PropType } from 'vue'
import { ElTooltipProps } from 'element-plus'
import { buildProp } from '@neue-plus/utils'
import { ActionNode, SchemaElement } from '../../../components/material-render'

interface Config {
  href?: string
  dataSource: 'query' | 'tableRow'
  blank: '_self' | '_blank'
  actionType: string
  params?: Record<string, any>
  target: string
}
type BtnType = 'icon' | 'button' | 'element'
export interface ActionsProps {
  type: BtnType
  elements: SchemaElement[]
  text: string
  icon: string
  //用于区分当前操作的身份，比如：新增、修改、删除
  id: any
  //type 对应的属性
  props: Record<string, any>
  config?: Config
  actions?: ActionNode[]
  tooltip?: ElTooltipProps
}
export const neProButtonProps = {
  record: buildProp({
    type: Object as PropType<{
      row: any
    }>,
  }),
  query: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  items: buildProp({
    type: Array as () => Array<ActionsProps>,
    default: () => [],
  }),
}

export type NeProButtonProps = ExtractPropTypes<typeof neProButtonProps>
