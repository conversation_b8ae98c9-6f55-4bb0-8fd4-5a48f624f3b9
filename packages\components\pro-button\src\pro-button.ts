import { ExtractPropTypes, PropType } from 'vue'
import { buildProp } from '@neue-plus/utils'
import { ActionNode } from '../../../components/material-render'
import { RequestOptions } from '../../material-render/src/handlers/handleRequest'

interface Config {
  href?: string
  actionType: string
  params?: Record<string, any>
  target: string
}
export interface ActionsProps {
  text: string
  icon: string
  //用于区分当前操作的身份，比如：新增、修改、删除
  id: any
  //type 对应的属性
  props: Record<string, any>
  config?: Config
  actions?: ActionNode[]
  requestMap?: Record<string, RequestOptions>
}
export const neProButtonProps = {
  record: buildProp({
    type: Object as PropType<{
      row: any
    }>,
  }),
  items: buildProp({
    type: Array as () => Array<ActionsProps>,
    default: () => [],
  }),
}

export type NeProButtonProps = ExtractPropTypes<typeof neProButtonProps>
