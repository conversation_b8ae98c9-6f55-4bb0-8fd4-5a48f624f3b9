/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { RelCadPartTwoDimensionDrawingRelations } from './RelCadPartTwoDimensionDrawingRelations';

/**
 * Request builder class for operations supported on the {@link RelCadPartTwoDimensionDrawingRelations} entity.
 */
export class RelCadPartTwoDimensionDrawingRelationsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
  /**
   * Returns a request builder for querying all `RelCadPartTwoDimensionDrawingRelations` entities.
   * @returns A request builder for creating requests to retrieve all `RelCadPartTwoDimensionDrawingRelations` entities.
   */
  getAll(): GetAllRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
    return new GetAllRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<T>,
      T
    >(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `RelCadPartTwoDimensionDrawingRelations` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   */
  create(
    entity: RelCadPartTwoDimensionDrawingRelations<T>
  ): CreateRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
    return new CreateRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<T>,
      T
    >(this.entityApi, entity);
  }

  /**
   * Returns a request builder for retrieving one `RelCadPartTwoDimensionDrawingRelations` entity based on its keys.
   * @param ncid Key property. See {@link RelCadPartTwoDimensionDrawingRelations.ncid}.
   * @returns A request builder for creating requests to retrieve one `RelCadPartTwoDimensionDrawingRelations` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
    return new GetByKeyRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<T>,
      T
    >(this.entityApi, { ncid: ncid });
  }

  /**
   * Returns a request builder for updating an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   */
  update(
    entity: RelCadPartTwoDimensionDrawingRelations<T>
  ): UpdateRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
    return new UpdateRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<T>,
      T
    >(this.entityApi, entity);
  }

  /**
   * Returns a request builder for deleting an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   * @param ncid Key property. See {@link RelCadPartTwoDimensionDrawingRelations.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   */
  delete(
    ncid: string
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `RelCadPartTwoDimensionDrawingRelations`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `RelCadPartTwoDimensionDrawingRelations` by taking the entity as a parameter.
   */
  delete(
    entity: RelCadPartTwoDimensionDrawingRelations<T>
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<RelCadPartTwoDimensionDrawingRelations<T>, T> {
    return new DeleteRequestBuilder<
      RelCadPartTwoDimensionDrawingRelations<T>,
      T
    >(
      this.entityApi,
      ncidOrEntity instanceof RelCadPartTwoDimensionDrawingRelations
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
