{"config": {"apiBaseConfig": {"baseURL": "https://api-cn-hangzhou-2.cloud.neuetech.cn"}}, "apis": {"getUserList": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "submit": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/CadParts", "method": "get", "query": {"filter": {"version/isLatestVersion": {"eq": true}, "version/revision/isLatestRevision": {"eq": true}}, "expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision($expand=lockedBy,master))"}}, "dialogFormSubmitAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms", "method": "post"}, "dialogFormSubmitPart": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "dialogFormEditRequestAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "get"}, "dialogFormEditRequestPart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "get"}, "dialogFormUpdateAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms", "method": "post"}, "dialogFormUpdatePart": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "businessTypeRequest": {"url": "/modeling/202512/OdataService/EntityTypes", "method": "get"}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}}, "elements": [{"id": "div123123", "type": "div", "name": "OData表格", "props": {"style": "padding:16px;background:#ECEEF3;box-sizing:border-box;height:100%;"}, "elements": [{"id": "odata-card", "type": "card", "name": "OData表格", "props": {"header": "设计零部件"}, "elements": [{"id": "cad-part-table", "type": "pro-table", "name": "OData表格", "props": {"maxHeight": "800px", "entityType": "<PERSON><PERSON><PERSON><PERSON>", "rowKey": "ncid", "columns": [{"prop": "code", "label": "零部件编号"}, {"prop": "thumbnail", "label": "缩略图", "valueType": "image", "hideInForm": true, "hideInTable": true}, {"prop": "name", "label": "零部件名称"}, {"prop": "partType", "label": "子类别"}, {"prop": "version/versionNumber", "label": "版本号", "render": "{{version.revision.revisionCode}}.{{version.versionNumber}}", "hideInForm": true}, {"prop": "lifecycleStatus/name", "label": "状态", "hideInForm": true, "valueEnum": {"IN_WORK": {"text": "工作中"}}}, {"prop": "version/revision/lockedBy/ncid", "label": "锁定人", "render": "{{version.revision.lockedBy.name}}", "valueType": "avatar", "entityType": "<PERSON><PERSON><PERSON>", "valueEnum": "cdpUserEmu"}, {"prop": "modifiedAt", "label": "修改时间", "valueType": "time"}, {"prop": "owner/ncid", "label": "责任人", "valueType": "avatar", "entityType": "<PERSON><PERSON><PERSON>", "valueEnum": "cdpUserEmu"}, {"prop": "operation", "label": "操作", "minWidth": 80, "actions": [{"text": "浏览", "id": "edit", "props": {"link": true, "type": "primary"}, "config": {"blank": "_blank", "href": "/#/new/part-props?ncid={{ncid}}&odataType={{@odata.type}}&masterNcid={{version.revision.master.ncid}}&partType={{@odata.type}}&code={{code}}"}}, {"text": "删除", "id": "delete", "props": {"link": true, "type": "primary", "disabled": "{{version.revision.isLocked}}===true"}, "tooltip": {"content": "锁定状态不能删除"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "删除", "content": "是否确认删除？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "afterConfirm", "cancel": "end"}}}, {"id": "afterConfirm", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/CadParts('{{ncid}}')/BuiltInModel.DeleteRevision", "method": "post"}, "branches": {"success": "refreshTable", "fail": "refreshTable"}}}, {"id": "refreshTable", "type": "normal", "title": "刷新表格", "config": {"actionType": "refresh", "target": "cad-part-table", "next": "end"}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "toolbar": {"header": [{"text": "新建", "props": {"type": "primary"}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk123456"}}], "footer": []}}, "events": [], "api": {}}]}]}, {"id": "DialogForm_er4mn6jbtk123456", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "新建设计零部件", "width": "520px", "height": "578px", "labelWidth": "80", "formWidth": "460", "layout": "horizontal", "submitText": "保存", "resetText": "重置2", "cancelText": "取消", "showLabel": true, "showSubmit": true, "showReset": false, "showCancel": true, "noRequest": true, "buttonAlign": "right"}, "formItems": [{"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 1, "maxLength": 64, "showWordLimit": true, "placeholder": "请输入"}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "placeholder": "请选择", "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "placeholder": "请选择", "required": true}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "placeholder": "请输入", "maxLength": 255}]}, "elements": [], "events": [{"nickName": "行点击事件", "eventName": "onRefresh", "actions": [{"id": "start", "type": "start", "title": "开始"}, {"id": "56132221", "type": "normal", "title": "节点7690", "content": "打开弹框", "config": {"actionType": "refresh", "actionName": "打开drawer", "target": "cad-part-table"}}, {"id": "end", "type": "end", "title": "结束"}]}]}, {"id": "DialogForm_edit_er4mn6jbtk1234567", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "修订设计零部件", "width": "520px", "height": "578px", "labelWidth": "100", "formWidth": "420", "layout": "horizontal", "submitText": "确定", "cancelText": "取消", "showSubmit": true, "showCancel": true, "showReset": false, "buttonAlign": "right"}, "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "required": true, "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 6, "maxLength": 18, "showWordLimit": true}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "disabled": true, "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "required": true, "disabled": true}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "required": true}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "required": true}, {"prop": "mass", "fieldName": "质量", "fieldType": "text"}, {"prop": "meterial", "fieldName": "材料", "fieldType": "text"}, {"prop": "gravityCenter", "fieldName": "重心", "fieldType": "text"}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, "elements": [], "events": []}]}