<template>
  <widget-wrapper>
    <div style="padding: 10px 20px" class="ne-panel-form">
      <div class="banner-wrapper">
        <div class="banner-title">
          {{ props.config.title }}
        </div>
        <div class="banner-button">
          <template v-if="isEdit">
            <NeButton type="primary" @click="submitForm">
              {{ props.config.submitText || '保存' }}
            </NeButton>
            <NeButton @click="cancelForm">
              {{ props.config.cancelText || '取消' }}
            </NeButton>
          </template>
          <template v-else>
            <NeButton
              v-show="props.config.showSubmit"
              type="primary"
              :disabled="editDisabled"
              @click="editForm"
            >
              {{ props.config.editText || '编辑' }}
            </NeButton>
          </template>
        </div>
      </div>
      <!-- 整张表单 -->
      <el-form
        ref="formRef"
        :model="formModel"
        :label-width="`${props.config.labelWidth || 120}px`"
        class="panel-form-content"
      >
        <!-- 栏目：基础属性 -->
        <el-collapse
          v-model="activeNames"
          :class="`custom-collapse collapse-${props.config.layout}`"
        >
          <el-collapse-item
            v-for="panel in props.formItems"
            :key="panel.key"
            :title="panel.name"
            :name="panel.key"
          >
            <div v-if="panel.viewImg" class="img-wrapper">
              <img :src="panel.viewImg" alt="" style="max-width: 100%" />
            </div>
            <el-row :gutter="16">
              <el-col
                v-for="(item, idx) in panel.formItems"
                :key="idx"
                :span="props.config.span"
              >
                <el-form-item
                  :prop="item.prop"
                  :label="item.fieldName"
                  :rules="getRules(item)"
                  :required="isEdit && item.required"
                  :label-width="props.config.labelWidth || 120"
                >
                  <FormItem
                    v-model="formModel"
                    :item="item"
                    :is-edit="isEdit"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </div>
  </widget-wrapper>
</template>

<script setup lang="ts">
// import { config } from 'process'
import { computed, ref, watch, watchEffect } from 'vue'
import { useRequest } from 'vue-request'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import {
  useApisRef,
  useSysParamsRef,
} from '@neue-plus/components/material-render/src/context/event-flow'
import NeButton from '@neue-plus/components/button'
import {
  buildQuery,
  filterFlat,
  flattenFormItems,
  getOdataTypeName,
  pickMatchedFieldsDbg,
  unflatten,
} from '@neue-plus/utils/form'
import FormItem from '../../form-item/src/form-item.vue'
import { getRules } from '../../form-item/src/validation'
import { formApiMap } from './panel-form'
import type { PropType } from 'vue'
import type { panelDetail } from './panel-form'
import type { FormConfig } from '../../form-item/src/form-item'

const apiRefs = useApisRef()
const sysParamsRef = useSysParamsRef()

defineOptions({
  name: 'PanelForm',
  inheritAttrs: false,
})
const props = defineProps({
  config: {
    type: Object as () => FormConfig,
    default: () => ({
      // 默认展示编辑按钮
      showSubmit: true,
    }),
  },
  formItems: {
    type: Array as () => panelDetail[],
    default: () => [],
  },
  // 2D工程图详情查询
  panelFormEditRequestDraw: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  panelFormEditRequestAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  panelFormEditRequestPart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  panelFormUpdateAsm: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
  panelFormUpdatePart: {
    type: Function as PropType<(params: any) => Promise<any>>,
    default: () => ({}),
  },
})

const emit = defineEmits(['formSubmit', 'formCancel', 'update:visible'])

const isEdit = ref(false)
const formRef = ref()
// const panelList = ref<panelDetail[]>({ ...props.formItems })
// const formData = ref<any>({})
const editNcid = ref<string>('')
const editApiMap = ref<any>({})
interface FormModel {
  [key: string]: any
}
const formModelInited = ref(false) // 表单是否inited
const formItemsData = ref<any>([])
const editDisabled = ref(false)
const odataType = computed(() => {
  return getOdataTypeName(
    props.config.odataType || sysParamsRef.value?.odataType || 'NeueCadPart'
  )
})

const activeNames = computed(() => {
  return props.formItems.map((panel) => panel.key)
})

const formModel = ref<any>({
  name: '1',
})
const formModelMain = ref<any>({
  name: '1',
})

const editForm = () => {
  console.log('编辑表单')
  isEdit.value = true
}

const submitForm = async () => {
  console.log('提交表单', formModel.value, formItemsData.value)
  await formRef.value?.validate(async (valid: any) => {
    if (valid) {
      if (typeof apiRefs[editApiMap.value.updateApi] !== 'function') {
        throw new TypeError(
          `apiRefs.${editApiMap.value.updateApi} is not a function`
        )
      }
      const formData = unflatten(
        filterFlat(
          formModel.value,
          flattenFormItems(formItemsData.value)
        ) as any
      )

      console.log(
        'formData====>',
        formData,
        filterFlat(formModel.value, formItemsData.value)
      )
      delete formData.ncid
      const res = await apiRefs[editApiMap.value.updateApi]({
        replaceData: {
          ncid: `'${editNcid.value}'`,
        },
        data: formData,
      })
      console.log('submitForm:res', res)
      const rData = res.data || {}
      if (!rData.ncid) {
        return
      }
      location.href = buildQuery({
        ncid: rData.ncid,
        odataType: odataType.value,
      })
      // isEdit.value = false
      // emit('formSubmit', formModel.value)
    }
  })
}
const cancelForm = () => {
  console.log('取消表单', formModelMain.value)
  isEdit.value = false
  formModel.value = { ...formModelMain.value }
  emit('formCancel', formModel.value)
}

/**
 * 通过NCID获取表单数据
 * @param {Object} params - 包含NCID的对象
 * @param {string} params.ncid - NCID
 * @returns {Promise<any>} - 表单数据
 */
const fetchForm = async (params: { ncid: string }) => {
  console.log('fetchForm:params', params, editApiMap.value)
  if (typeof apiRefs[editApiMap.value.editApi] !== 'function') {
    throw new TypeError(`apiRefs.${editApiMap.value} is not a function`)
  }
  // console.log('fetchForm:apiRefs', apiRefs)
  // 这里直接返回接口原始数据
  return apiRefs[editApiMap.value.editApi]({
    replaceData: {
      ncid: `'${params.ncid}'`,
    },
    // params: {
    //   $expand: 'version($expand=revision),thumbnail,lifecycleStatus',
    // },
  })
}

const { data: remoteData, run: loadForm } = useRequest(fetchForm, {
  manual: true,
  initialData: {},
})

const initModel = () => {
  const model: FormModel = {}

  // 双层 flatMap：先展开外层，再展开里层 formItems
  formItemsData.value
    .flatMap((group: any) => group.formItems || [])
    .forEach((item: any) => {
      model[item.prop] = item.value ?? ''
    })

  formModel.value = model
  console.log('initModel formModel===========', formModel.value)
  formModelInited.value = true
}
// 设置按钮是否禁用
const setEditButton = ({ isDisabled }: { isDisabled: boolean }) => {
  console.log('setEditButton===>', isDisabled)
  editDisabled.value = isDisabled
  initModel()
  // 更新‘编辑’按钮状态，此时该信息锁定状态有更新，需要重新加载详情
  loadForm({ ncid: editNcid.value })
}

watch(
  () => props.formItems,
  (newItems) => {
    formItemsData.value = [...newItems]
    initModel()
  },
  { immediate: true, deep: true }
)

watch(
  () => editNcid.value,
  (newVal) => {
    console.log('watch:editNcid已更新:', newVal)
    if (newVal) {
      loadForm({ ncid: newVal })
    }
  },
  { immediate: true }
)

const initEdit = (eData: any) => {
  console.log('initEdit==>0', eData)
  editNcid.value = eData.ncid
  console.log('initEdit==>', eData, editNcid.value)
}

// watchEffect(() => {
//   if (formModelInited.value && editNcid.value && remoteData.value) {
//     console.log('===============watch remoteData==', remoteData.value)
//     if (!remoteData.value.data) {
//       return
//     }
//     const rData = remoteData.value.data
//     const newModel = pickMatchedFieldsDbg(formModel.value, rData)
//     console.log('===============watch newModel==', newModel)
//     formModel.value = {
//       ...newModel,
//     }
//   }
// })

watch(
  [() => formModelInited.value, () => editNcid.value, () => remoteData.value],
  ([inited, ncid, remote]) => {
    if (!inited || !ncid || !remote?.data) return
    console.log(
      '===============watch remoteData==',
      formModel.value,
      remote.data
    )
    const rData = remote.data
    const newModel = pickMatchedFieldsDbg(formModel.value, rData)

    console.log('===============watch newModel==', newModel)
    formModel.value = { ...newModel }
    formModelMain.value = { ...newModel }
  },
  { flush: 'post', deep: true }
)

watchEffect(() => {
  console.log('sysParamsRef changed:', sysParamsRef)
  console.log(
    'sysParamsRef changed:',
    sysParamsRef,
    getOdataTypeName(props.config.odataType || sysParamsRef.value.odataType) ||
      'NeueCadPart'
  )
  if (sysParamsRef.value && sysParamsRef.value.ncid) {
    // editApiMap.value =
    //   formApiMap[
    //     getOdataTypeName(
    //       props.config.odataType || sysParamsRef.value.odataType
    //     ) || 'NeueCadPart'
    //   ]
    // odataType.value
    editApiMap.value =
      formApiMap[getOdataTypeName(odataType.value) || 'NeueCadPart']
    initEdit({
      ncid: sysParamsRef.value.ncid,
    })
  }
})

// watch(
//   // 监听源：只要 ncid 变化就触发
//   () => sysParamsRef.value?.ncid,
//   (newNcid) => {
//     if (newNcid) {
//       editApiMap.value =
//         formApiMap[
//           getOdataTypeName(
//             props.config.odataType || sysParamsRef.value!.odataType
//           ) || 'NeueCadPart'
//         ]

//       initEdit({ ncid: newNcid })
//     }
//   },
//   { immediate: true }
// )

defineExpose({
  setEditButton,
})
</script>

<style scoped></style>
