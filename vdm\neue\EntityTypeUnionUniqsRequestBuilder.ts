/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { EntityTypeUnionUniqs } from './EntityTypeUnionUniqs';

/**
 * Request builder class for operations supported on the {@link EntityTypeUnionUniqs} entity.
 */
export class EntityTypeUnionUniqsRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<EntityTypeUnionUniqs<T>, T> {
  /**
   * Returns a request builder for querying all `EntityTypeUnionUniqs` entities.
   * @returns A request builder for creating requests to retrieve all `EntityTypeUnionUniqs` entities.
   */
  getAll(): GetAllRequestBuilder<EntityTypeUnionUniqs<T>, T> {
    return new GetAllRequestBuilder<EntityTypeUnionUniqs<T>, T>(this.entityApi);
  }

  /**
   * Returns a request builder for creating a `EntityTypeUnionUniqs` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `EntityTypeUnionUniqs`.
   */
  create(
    entity: EntityTypeUnionUniqs<T>
  ): CreateRequestBuilder<EntityTypeUnionUniqs<T>, T> {
    return new CreateRequestBuilder<EntityTypeUnionUniqs<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `EntityTypeUnionUniqs` entity based on its keys.
   * @param ncid Key property. See {@link EntityTypeUnionUniqs.ncid}.
   * @returns A request builder for creating requests to retrieve one `EntityTypeUnionUniqs` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<EntityTypeUnionUniqs<T>, T> {
    return new GetByKeyRequestBuilder<EntityTypeUnionUniqs<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `EntityTypeUnionUniqs`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `EntityTypeUnionUniqs`.
   */
  update(
    entity: EntityTypeUnionUniqs<T>
  ): UpdateRequestBuilder<EntityTypeUnionUniqs<T>, T> {
    return new UpdateRequestBuilder<EntityTypeUnionUniqs<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `EntityTypeUnionUniqs`.
   * @param ncid Key property. See {@link EntityTypeUnionUniqs.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypeUnionUniqs`.
   */
  delete(ncid: string): DeleteRequestBuilder<EntityTypeUnionUniqs<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `EntityTypeUnionUniqs`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `EntityTypeUnionUniqs` by taking the entity as a parameter.
   */
  delete(
    entity: EntityTypeUnionUniqs<T>
  ): DeleteRequestBuilder<EntityTypeUnionUniqs<T>, T>;
  delete(ncidOrEntity: any): DeleteRequestBuilder<EntityTypeUnionUniqs<T>, T> {
    return new DeleteRequestBuilder<EntityTypeUnionUniqs<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof EntityTypeUnionUniqs
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
