// ES6 模块包装器，用于重新导出 CommonJS 模块
import * as QTrippinModule from './QTrippin'
import * as TrippinModelModule from './TrippinModel'
import * as TrippinServiceModule from './TrippinService'

// 重新导出所有查询对象
export const {
  qPerson,
  QPerson,
  QPersonId,
  qAirline,
  QAirline,
  QAirlineId,
  qAirport,
  QAirport,
  QAirportId,
  qTrip,
  QTrip,
  QTripId,
  qPlanItem,
  QPlanItem,
  QPlanItemId,
  qEvent,
  QEvent,
  qPublicTransportation,
  QPublicTransportation,
  qFlight,
  QFlight,
  qEmployee,
  QEmployee,
  qManager,
  QManager,
  qLocation,
  QLocation,
  qCity,
  QCity,
  qAirportLocation,
  QAirportLocation,
  qEventLocation,
  QEventLocation,
  QGetPersonWithMostFriends,
  QGetNearestAirport,
  QResetDataSource,
} = QTrippinModule

// 重新导出模型类型
export const { PersonGender, Feature } = TrippinModelModule

// 重新导出服务类
export const { TrippinService } = TrippinServiceModule

// 默认导出常用的查询对象
export default {
  qPerson,
  qAirline,
  qAirport,
  qTrip,
  qPlanItem,
  qEvent,
  qPublicTransportation,
  qFlight,
  qEmployee,
  qManager,
  qLocation,
  qCity,
  qAirportLocation,
  qEventLocation,
}
