<template>
  <div v-if="!name">-</div>
  <div v-else class="ne-avatar-wrapper" :style="{ justifyContent: align }">
    <div class="ne-avatar-circle" :style="{ backgroundColor: avatarColor }">
      <span class="ne-avatar-text">{{ avatarText }}</span>
    </div>
    <span
      v-if="name"
      class="ne-avatar-name"
      style="max-width: calc(100% - 32px)"
      >{{ name }}
      <!-- <NyTooltipEllipsis :key="Math.random()">{{ name }}</NyTooltipEllipsis> -->
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// import NyTooltipEllipsis from '../../tooltip-ellipsis'

defineOptions({
  name: 'NeAvatar',
})

const props = defineProps({
  align: { type: String, default: 'left' },
  text: { type: String, default: '' },
  name: { type: String },
  color: { type: String },
})
const avatarText = computed(() => {
  if (props.text) return props.text
  if (props.name) return props?.name?.slice(-1)
  return ''
})
const COLORS = [
  '#08286F',
  '#103589',
  '#14B1BB',
  '#7465E1',
  '#090909',
  '#40A9FF',
  '#40A9FF',
  '#9254DE',
  '#10B981',
  '#3A62BF',
  '#FB923C',
  '#95CE06',
  '#B37FEB',
  '#B51876',
  '#728ECD',
  '#006D75',
]
function strToNumber(str: any) {
  let sum = 0
  for (let i = 0; i < str?.length; i++) {
    sum += str.charCodeAt(i) // 获取每个字符的Unicode编码
  }
  return sum % 16
}
const avatarColor = computed(() => {
  const num = strToNumber(props.name)
  return COLORS[num]
})
const name = computed(() => {
  return props.name
})
</script>
