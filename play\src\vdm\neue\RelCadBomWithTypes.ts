/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { RelCadBomWithTypesApi } from './RelCadBomWithTypesApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "RelCadBomWithTypes" of service "neue".
 */
export class RelCadBomWithTypes<T extends DeSerializers = DefaultDeSerializers>
  extends Entity
  implements RelCadBomWithTypesType<T>
{
  /**
   * Technical entity name for RelCadBomWithTypes.
   */
  static override _entityName = 'RelCadBomWithTypes';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the RelCadBomWithTypes entity.
   */
  static _keys = ['ncid'];
  /**
   * Bom Excluded.
   * Maximum length: 64.
   */
  declare bomExcluded: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Suppressed.
   * Maximum length: 64.
   */
  declare suppressed: DeserializedType<T, 'Edm.Boolean'>;
  /**
   * Quantity.
   * Maximum length: 64.
   */
  declare quantity: DeserializedType<T, 'Edm.Int32'>;
  /**
   * Configuration.
   * Maximum length: 64.
   * @nullable
   */
  declare configuration?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Transformation Matrix.
   * Maximum length: 1000.
   * @nullable
   */
  declare transformationMatrix?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Instance Name.
   * Maximum length: 64.
   */
  declare instanceName: DeserializedType<T, 'Edm.String'>;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: RelCadBomWithTypesApi<T>) {
    super(_entityApi);
  }
}

export interface RelCadBomWithTypesType<
  T extends DeSerializers = DefaultDeSerializers
> {
  bomExcluded: DeserializedType<T, 'Edm.Boolean'>;
  suppressed: DeserializedType<T, 'Edm.Boolean'>;
  quantity: DeserializedType<T, 'Edm.Int32'>;
  configuration?: DeserializedType<T, 'Edm.String'> | null;
  transformationMatrix?: DeserializedType<T, 'Edm.String'> | null;
  instanceName: DeserializedType<T, 'Edm.String'>;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
