/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeSerializers,
  DefaultDeSerializers,
  DeleteRequestBuilder,
  DeserializedType,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  RequestBuilder,
  UpdateRequestBuilder
} from '@sap-cloud-sdk/odata-v4';
import { BusinessObjectRevisionNumberRules } from './BusinessObjectRevisionNumberRules';

/**
 * Request builder class for operations supported on the {@link BusinessObjectRevisionNumberRules} entity.
 */
export class BusinessObjectRevisionNumberRulesRequestBuilder<
  T extends DeSerializers = DefaultDeSerializers
> extends RequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
  /**
   * Returns a request builder for querying all `BusinessObjectRevisionNumberRules` entities.
   * @returns A request builder for creating requests to retrieve all `BusinessObjectRevisionNumberRules` entities.
   */
  getAll(): GetAllRequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
    return new GetAllRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>(
      this.entityApi
    );
  }

  /**
   * Returns a request builder for creating a `BusinessObjectRevisionNumberRules` entity.
   * @param entity The entity to be created
   * @returns A request builder for creating requests that create an entity of type `BusinessObjectRevisionNumberRules`.
   */
  create(
    entity: BusinessObjectRevisionNumberRules<T>
  ): CreateRequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
    return new CreateRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for retrieving one `BusinessObjectRevisionNumberRules` entity based on its keys.
   * @param ncid Key property. See {@link BusinessObjectRevisionNumberRules.ncid}.
   * @returns A request builder for creating requests to retrieve one `BusinessObjectRevisionNumberRules` entity based on its keys.
   */
  getByKey(
    ncid: DeserializedType<T, 'Edm.String'>
  ): GetByKeyRequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
    return new GetByKeyRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>(
      this.entityApi,
      { ncid: ncid }
    );
  }

  /**
   * Returns a request builder for updating an entity of type `BusinessObjectRevisionNumberRules`.
   * @param entity The entity to be updated
   * @returns A request builder for creating requests that update an entity of type `BusinessObjectRevisionNumberRules`.
   */
  update(
    entity: BusinessObjectRevisionNumberRules<T>
  ): UpdateRequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
    return new UpdateRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>(
      this.entityApi,
      entity
    );
  }

  /**
   * Returns a request builder for deleting an entity of type `BusinessObjectRevisionNumberRules`.
   * @param ncid Key property. See {@link BusinessObjectRevisionNumberRules.ncid}.
   * @returns A request builder for creating requests that delete an entity of type `BusinessObjectRevisionNumberRules`.
   */
  delete(
    ncid: string
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>;
  /**
   * Returns a request builder for deleting an entity of type `BusinessObjectRevisionNumberRules`.
   * @param entity Pass the entity to be deleted.
   * @returns A request builder for creating requests that delete an entity of type `BusinessObjectRevisionNumberRules` by taking the entity as a parameter.
   */
  delete(
    entity: BusinessObjectRevisionNumberRules<T>
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>;
  delete(
    ncidOrEntity: any
  ): DeleteRequestBuilder<BusinessObjectRevisionNumberRules<T>, T> {
    return new DeleteRequestBuilder<BusinessObjectRevisionNumberRules<T>, T>(
      this.entityApi,
      ncidOrEntity instanceof BusinessObjectRevisionNumberRules
        ? ncidOrEntity
        : { ncid: ncidOrEntity! }
    );
  }
}
