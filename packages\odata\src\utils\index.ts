/**
 * OData 工具函数
 */

import type { ODataQueryOptions } from '../types'

/**
 * OData 过滤器构建器
 */
export class FilterBuilder {
  private conditions: string[] = []

  /**
   * 等于
   */
  eq(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} eq ${formattedValue}`)
    return this
  }

  /**
   * 不等于
   */
  ne(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} ne ${formattedValue}`)
    return this
  }

  /**
   * 大于
   */
  gt(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} gt ${formattedValue}`)
    return this
  }

  /**
   * 大于等于
   */
  ge(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} ge ${formattedValue}`)
    return this
  }

  /**
   * 小于
   */
  lt(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} lt ${formattedValue}`)
    return this
  }

  /**
   * 小于等于
   */
  le(field: string, value: any): FilterBuilder {
    const formattedValue = this.formatValue(value)
    this.conditions.push(`${field} le ${formattedValue}`)
    return this
  }

  /**
   * 包含（字符串）
   */
  contains(field: string, value: string): FilterBuilder {
    this.conditions.push(`contains(${field}, '${value}')`)
    return this
  }

  /**
   * 开始于（字符串）
   */
  startsWith(field: string, value: string): FilterBuilder {
    this.conditions.push(`startswith(${field}, '${value}')`)
    return this
  }

  /**
   * 结束于（字符串）
   */
  endsWith(field: string, value: string): FilterBuilder {
    this.conditions.push(`endswith(${field}, '${value}')`)
    return this
  }

  /**
   * 在列表中
   */
  in(field: string, values: any[]): FilterBuilder {
    const formattedValues = values.map(v => this.formatValue(v)).join(', ')
    this.conditions.push(`${field} in (${formattedValues})`)
    return this
  }

  /**
   * 为空
   */
  isNull(field: string): FilterBuilder {
    this.conditions.push(`${field} eq null`)
    return this
  }

  /**
   * 不为空
   */
  isNotNull(field: string): FilterBuilder {
    this.conditions.push(`${field} ne null`)
    return this
  }

  /**
   * AND 操作
   */
  and(): FilterBuilder {
    if (this.conditions.length > 0) {
      this.conditions.push('and')
    }
    return this
  }

  /**
   * OR 操作
   */
  or(): FilterBuilder {
    if (this.conditions.length > 0) {
      this.conditions.push('or')
    }
    return this
  }

  /**
   * 分组开始
   */
  groupStart(): FilterBuilder {
    this.conditions.push('(')
    return this
  }

  /**
   * 分组结束
   */
  groupEnd(): FilterBuilder {
    this.conditions.push(')')
    return this
  }

  /**
   * 构建过滤器字符串
   */
  build(): string {
    return this.conditions.join(' ')
  }

  /**
   * 重置构建器
   */
  reset(): FilterBuilder {
    this.conditions = []
    return this
  }

  /**
   * 格式化值
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null'
    }
    
    if (typeof value === 'string') {
      return `'${value.replace(/'/g, "''")}'` // 转义单引号
    }
    
    if (typeof value === 'boolean') {
      return value.toString()
    }
    
    if (value instanceof Date) {
      return value.toISOString()
    }
    
    return value.toString()
  }
}

/**
 * 创建过滤器构建器
 */
export function filter(): FilterBuilder {
  return new FilterBuilder()
}

/**
 * OData 查询选项构建器
 */
export class QueryOptionsBuilder {
  private options: ODataQueryOptions = {}

  /**
   * 选择字段
   */
  select(...fields: string[]): QueryOptionsBuilder {
    this.options.$select = [...(this.options.$select || []), ...fields]
    return this
  }

  /**
   * 展开关联
   */
  expand(...relations: string[]): QueryOptionsBuilder {
    this.options.$expand = [...(this.options.$expand || []), ...relations]
    return this
  }

  /**
   * 过滤条件
   */
  filter(condition: string | FilterBuilder): QueryOptionsBuilder {
    const filterStr = typeof condition === 'string' ? condition : condition.build()
    this.options.$filter = filterStr
    return this
  }

  /**
   * 排序
   */
  orderBy(field: string, direction: 'asc' | 'desc' = 'asc'): QueryOptionsBuilder {
    const orderClause = `${field} ${direction}`
    if (this.options.$orderby) {
      this.options.$orderby = `${this.options.$orderby}, ${orderClause}`
    } else {
      this.options.$orderby = orderClause
    }
    return this
  }

  /**
   * 限制数量
   */
  top(count: number): QueryOptionsBuilder {
    this.options.$top = count
    return this
  }

  /**
   * 跳过数量
   */
  skip(count: number): QueryOptionsBuilder {
    this.options.$skip = count
    return this
  }

  /**
   * 包含总数
   */
  count(include: boolean = true): QueryOptionsBuilder {
    this.options.$count = include
    return this
  }

  /**
   * 搜索
   */
  search(term: string): QueryOptionsBuilder {
    this.options.$search = term
    return this
  }

  /**
   * 格式
   */
  format(format: 'json' | 'xml'): QueryOptionsBuilder {
    this.options.$format = format
    return this
  }

  /**
   * 构建查询选项
   */
  build(): ODataQueryOptions {
    return { ...this.options }
  }

  /**
   * 重置构建器
   */
  reset(): QueryOptionsBuilder {
    this.options = {}
    return this
  }
}

/**
 * 创建查询选项构建器
 */
export function queryOptions(): QueryOptionsBuilder {
  return new QueryOptionsBuilder()
}

/**
 * 转义 OData 字符串值
 */
export function escapeODataString(value: string): string {
  return value.replace(/'/g, "''")
}

/**
 * 格式化 OData 日期
 */
export function formatODataDate(date: Date): string {
  return date.toISOString()
}

/**
 * 构建实体键路径
 */
export function buildEntityKey(keyValues: Record<string, any>): string {
  const keyPairs = Object.entries(keyValues).map(([key, value]) => {
    const formattedValue = typeof value === 'string' ? `'${escapeODataString(value)}'` : value
    return `${key}=${formattedValue}`
  })
  
  return keyPairs.length === 1 && keyPairs[0].includes('=') 
    ? keyPairs[0].split('=')[1] // 单键情况
    : keyPairs.join(',') // 复合键情况
}

/**
 * 解析 OData 错误响应
 */
export function parseODataError(error: any): { code: string; message: string; details?: any[] } {
  if (error?.response?.data?.error) {
    const odataError = error.response.data.error
    return {
      code: odataError.code || 'UNKNOWN_ERROR',
      message: odataError.message || 'An unknown error occurred',
      details: odataError.details
    }
  }
  
  return {
    code: 'NETWORK_ERROR',
    message: error.message || 'Network error occurred'
  }
}
