export function assigned(value: any): boolean {
  return value !== null && value !== undefined
}

export function contains<T>(arr: T[], item: T): boolean {
  for (const element of arr) {
    if (element === item) {
      return true
    }
  }
  return false
}

export function delay(
  callback: (...args: any[]) => void,
  ...args: any[]
): void {
  if (args.length === 0) {
    window.setTimeout(callback, 0)
    return
  }
  window.setTimeout(() => callback(...args), 0)
}

export function djsassert(
  condition: boolean,
  message: string,
  data?: any
): void {
  if (!condition) {
    throw { message: `Assert fired: ${message}`, data }
  }
}

export function extend<T, U>(target: T, values: U): T & U {
  for (const name in values) {
    if (Object.prototype.hasOwnProperty.call(values, name)) {
      ;(target as any)[name] = (values as any)[name]
    }
  }
  return target as T & U
}

export function find<T>(
  arr: T[] | null | undefined,
  callback: (item: T) => boolean
): T | null {
  if (arr) {
    for (const element of arr) {
      if (callback(element)) {
        return element
      }
    }
  }
  return null
}

export function isArray(value: any): value is any[] {
  return Array.isArray(value)
}

export function isDate(value: any): value is Date {
  return Object.prototype.toString.call(value) === '[object Date]'
}

export function isObject(value: any): value is object {
  return typeof value === 'object' && value !== null
}

export function parseInt10(value: string): number {
  return Number.parseInt(value, 10)
}

export function renameProperty<T extends object, K extends keyof T>(
  obj: T,
  oldName: K,
  newName: string
): void {
  if (Object.prototype.hasOwnProperty.call(obj, oldName)) {
    ;(obj as any)[newName] = obj[oldName]
    delete obj[oldName]
  }
}

export function throwErrorCallback(error: any): never {
  throw error
}

export function trimString(str: string): string {
  return str.trim ? str.trim() : str.replace(/^\s+|\s+$/g, '')
}

export function undefinedDefault<T>(value: T | undefined, defaultValue: T): T {
  return value !== undefined ? value : defaultValue
}

// URI Utilities
const uriRegEx = /^([^:\/?#]+:)?(\/\/[^\/?#]*)?([^?#:]+)?(\?[^#]*)?(#.*)?/
const uriPartNames = [
  'scheme',
  'authority',
  'path',
  'query',
  'fragment',
] as const

export interface URIInfo {
  isAbsolute: boolean
  scheme?: string
  authority?: string
  path?: string
  query?: string
  fragment?: string
}

export function getURIInfo(uri: string): URIInfo {
  const result: URIInfo = { isAbsolute: false }

  if (uri) {
    const matches = uriRegEx.exec(uri)
    if (matches) {
      for (const [i, uriPartName] of uriPartNames.entries()) {
        if (matches[i + 1]) {
          result[uriPartName] = matches[i + 1]
        }
      }
    }
    if (result.scheme) {
      result.isAbsolute = true
    }
  }

  return result
}

export function getURIFromInfo(uriInfo: URIInfo): string {
  return ''.concat(
    uriInfo.scheme || '',
    uriInfo.authority || '',
    uriInfo.path || '',
    uriInfo.query || '',
    uriInfo.fragment || ''
  )
}

const uriAuthorityRegEx = /^\/{0,2}(?:([^@]*)@)?([^:]+)(?::{1}(\d+))?/
const pctEncodingRegEx = /%[0-9A-F]{2}/gi

export function normalizeURICase(uri: string): string {
  const uriInfo = getURIInfo(uri)
  const scheme = uriInfo.scheme
  const authority = uriInfo.authority

  if (scheme) {
    uriInfo.scheme = scheme.toLowerCase()
    if (authority) {
      const matches = uriAuthorityRegEx.exec(authority)
      if (matches) {
        uriInfo.authority = `//${
          matches[1] ? `${matches[1]}@` : ''
        }${matches[2].toLowerCase()}${matches[3] ? `:${matches[3]}` : ''}`
      }
    }
  }

  uri = getURIFromInfo(uriInfo)
  return uri.replace(pctEncodingRegEx, (str) => str.toLowerCase())
}

export function mergeUriPathWithBase(
  uriPath: string,
  basePath?: string
): string {
  let path = '/'
  if (basePath) {
    const end = basePath.lastIndexOf('/')
    path = basePath.slice(0, Math.max(0, end))
    if (path.charAt(path.length - 1) !== '/') {
      path += '/'
    }
  }
  return path + uriPath
}

export function removeDotsFromPath(path: string): string {
  let result = ''
  let segment = ''
  let end: number

  while (path) {
    if (path.indexOf('..') === 0 || path.indexOf('.') === 0) {
      path = path.replace(/^\.\.?\/?/g, '')
    } else if (path.indexOf('/..') === 0) {
      path = path.replace(/^\/\..\/?/g, '/')
      end = result.lastIndexOf('/')
      result = end === -1 ? '' : result.slice(0, Math.max(0, end))
    } else if (path.indexOf('/.') === 0) {
      path = path.replace(/^\/\.\/?/g, '/')
    } else {
      segment = path
      end = path.indexOf('/', 1)
      if (end !== -1) segment = path.slice(0, Math.max(0, end))
      result += segment
      path = path.replace(segment, '')
    }
  }
  return result
}

export function normalizeURI(uri: string, base?: string): string {
  if (!base) return uri

  const uriInfo = getURIInfo(uri)
  if (uriInfo.isAbsolute) return uri

  const baseInfo = getURIInfo(base)
  const normInfo: URIInfo = { isAbsolute: false }
  let path: string

  if (uriInfo.authority) {
    normInfo.authority = uriInfo.authority
    path = uriInfo.path || ''
    normInfo.query = uriInfo.query
  } else {
    if (!uriInfo.path) {
      path = baseInfo.path || ''
      normInfo.query = uriInfo.query || baseInfo.query
    } else {
      path =
        uriInfo.path.charAt(0) === '/'
          ? uriInfo.path
          : mergeUriPathWithBase(uriInfo.path, baseInfo.path)
      normInfo.query = uriInfo.query
    }
    normInfo.authority = baseInfo.authority
  }

  normInfo.path = removeDotsFromPath(path)
  normInfo.scheme = baseInfo.scheme
  normInfo.fragment = uriInfo.fragment
  return getURIFromInfo(normInfo)
}

export function getBase64IndexValue(character: string): number | null {
  const asciiCode = character.charCodeAt(0)
  if (asciiCode >= 65 && asciiCode <= 90) return asciiCode - 65
  if (asciiCode >= 97 && asciiCode <= 122) return asciiCode - 71
  if (asciiCode >= 48 && asciiCode <= 57) return asciiCode + 4
  if (character === '+') return 62
  if (character === '/') return 63
  return null
}

export function addBase64Padding(binaryString: string): string {
  while (binaryString.length < 6) binaryString = `0${binaryString}`
  return binaryString
}

// JSON Value Array Utilities
export interface JsonValueArray<T = any> {
  value: T[]
  [key: string]: any
}

export function getJsonValueArraryLength(data?: JsonValueArray): number {
  return data?.value?.length ?? 0
}

export function sliceJsonValueArray<T>(
  data: JsonValueArray<T>,
  start: number,
  end: number
): JsonValueArray<T> {
  if (!data?.value) return data

  start = Math.max(0, start)
  const length = getJsonValueArraryLength(data)
  end = Math.min(end, length)

  const newData: JsonValueArray<T> = {} as any
  for (const prop in data) {
    newData[prop] = prop === 'value' ? data[prop].slice(start, end) : data[prop]
  }
  return newData
}

export function concatJsonValueArray<T>(
  data: JsonValueArray<T>,
  concatData: JsonValueArray<T>
): JsonValueArray<T> {
  if (!concatData?.value) return data
  if (!data || !data.value) return concatData
  data.value = data.value.concat(concatData.value)
  return data
}

// String utilities
export function endsWith(input: string, search: string): boolean {
  return input.endsWith(search)
}

export function startsWith(input: string, search: string): boolean {
  return input.startsWith(search)
}

// Format kind
export function getFormatKind(
  format?: string,
  defaultFormatKind: number = 0
): number {
  if (!assigned(format)) return defaultFormatKind
  switch (format?.toLowerCase()) {
    case 'none':
      return 0
    case 'minimal':
      return 1
    case 'full':
      return 2
    default:
      return defaultFormatKind
  }
}
