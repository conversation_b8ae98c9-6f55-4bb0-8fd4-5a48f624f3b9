/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CreateRequestBuilder,
  DeleteRequestBuilder,
  DeSerializers,
  GetAllRequestBuilder,
  GetByKeyRequestBuilder,
  ODataBatchRequestBuilder,
  UpdateRequestBuilder,
  OperationRequestBuilder,
  BatchChangeSet
} from '@sap-cloud-sdk/odata-v4';
import { transformVariadicArgumentToArray } from '@sap-cloud-sdk/util';
import {
  People,
  Airlines,
  Airports,
  GetPersonWithMostFriendsParameters,
  GetNearestAirportParameters,
  ResetDataSourceParameters
} from './index';

/**
 * Batch builder for operations supported on the $Metadata.
 * @param requests The requests of the batch.
 * @returns A request builder for batch.
 */
export function batch<DeSerializersT extends DeSerializers>(
  ...requests: Array<
    Read$MetadataRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT>;
export function batch<DeSerializersT extends DeSerializers>(
  requests: Array<
    Read$MetadataRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT>;
export function batch<DeSerializersT extends DeSerializers>(
  first:
    | undefined
    | Read$MetadataRequestBuilder<DeSerializersT>
    | BatchChangeSet<DeSerializersT>
    | Array<
        | Read$MetadataRequestBuilder<DeSerializersT>
        | BatchChangeSet<DeSerializersT>
      >,
  ...rest: Array<
    Read$MetadataRequestBuilder<DeSerializersT> | BatchChangeSet<DeSerializersT>
  >
): ODataBatchRequestBuilder<DeSerializersT> {
  return new ODataBatchRequestBuilder(
    default$MetadataPath,
    transformVariadicArgumentToArray(first, rest)
  );
}

/**
 * Change set constructor consists of write operations supported on the $Metadata.
 * @param requests The requests of the change set.
 * @returns A change set for batch.
 */
export function changeset<DeSerializersT extends DeSerializers>(
  ...requests: Array<Write$MetadataRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT>;
export function changeset<DeSerializersT extends DeSerializers>(
  requests: Array<Write$MetadataRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT>;
export function changeset<DeSerializersT extends DeSerializers>(
  first:
    | undefined
    | Write$MetadataRequestBuilder<DeSerializersT>
    | Array<Write$MetadataRequestBuilder<DeSerializersT>>,
  ...rest: Array<Write$MetadataRequestBuilder<DeSerializersT>>
): BatchChangeSet<DeSerializersT> {
  return new BatchChangeSet(transformVariadicArgumentToArray(first, rest));
}

export const default$MetadataPath = '/';
export type Read$MetadataRequestBuilder<DeSerializersT extends DeSerializers> =
  | GetAllRequestBuilder<People<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<Airlines<DeSerializersT>, DeSerializersT>
  | GetAllRequestBuilder<Airports<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<People<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<Airlines<DeSerializersT>, DeSerializersT>
  | GetByKeyRequestBuilder<Airports<DeSerializersT>, DeSerializersT>
  | OperationRequestBuilder<
      DeSerializersT,
      GetPersonWithMostFriendsParameters<DeSerializersT>,
      People | null
    >
  | OperationRequestBuilder<
      DeSerializersT,
      GetNearestAirportParameters<DeSerializersT>,
      Airports | null
    >;
export type Write$MetadataRequestBuilder<DeSerializersT extends DeSerializers> =

    | CreateRequestBuilder<People<DeSerializersT>, DeSerializersT>
    | UpdateRequestBuilder<People<DeSerializersT>, DeSerializersT>
    | DeleteRequestBuilder<People<DeSerializersT>, DeSerializersT>
    | CreateRequestBuilder<Airlines<DeSerializersT>, DeSerializersT>
    | UpdateRequestBuilder<Airlines<DeSerializersT>, DeSerializersT>
    | DeleteRequestBuilder<Airlines<DeSerializersT>, DeSerializersT>
    | CreateRequestBuilder<Airports<DeSerializersT>, DeSerializersT>
    | UpdateRequestBuilder<Airports<DeSerializersT>, DeSerializersT>
    | DeleteRequestBuilder<Airports<DeSerializersT>, DeSerializersT>
    | OperationRequestBuilder<
        DeSerializersT,
        ResetDataSourceParameters<DeSerializersT>,
        undefined
      >;
