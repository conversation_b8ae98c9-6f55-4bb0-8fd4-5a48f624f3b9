@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include neb(pro-table) {
  @include e(filter) {
    overflow: hidden;
  }

  @include e(table) {
  }
    @include e(header-toolbar) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  @include e(footer-toolbar) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  @include e(pagination) {
    text-align: right;
    padding: 16px 0;

    .el-pagination {
      justify-content: flex-end;
    }
  }
}
