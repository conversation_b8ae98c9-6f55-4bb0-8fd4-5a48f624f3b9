<?xml version="1.0" encoding="UTF-8"?>
<edmx:Edmx Version="4.0" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
  <edmx:DataServices>
    <Schema xmlns="http://docs.oasis-open.org/odata/ns/edm" Namespace="PaaS" Alias="PaaS">
      <EnumType Name="LifecycleState" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="CREATING"></Member>
        <Member Name="ACTIVE"></Member>
        <Member Name="INACTIVE"></Member>
        <Member Name="NEEDS_ATTENTION"></Member>
        <Member Name="UPDATING"></Member>
        <Member Name="DELETING"></Member>
        <Member Name="DELETED"></Member>
      </EnumType>
      <EntityType Name="Plt0ApplicationObject" Abstract="true">
        <Key>
          <PropertyRef Name="ncid" />
        </Key>
        <Property Name="ncid" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="createdAt" Type="Edm.DateTimeOffset" Nullable="false"></Property>
        <Property Name="modifiedAt" Type="Edm.DateTimeOffset" Nullable="false"></Property>
        <Property Name="schemaVersion" Type="Edm.String" Nullable="false"></Property>
        <Property Name="lifecycleState" Type="PaaS.LifecycleState" Nullable="false"></Property>
        <Property Name="lifecycleNote" Type="Edm.String" MaxLength="255"></Property>
        <NavigationProperty Name="createdBy" Type="PaaS.Plt0ApplicationObject" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="modifiedBy" Type="PaaS.Plt0ApplicationObject" Nullable="false"></NavigationProperty>
      </EntityType>
    </Schema>
    <Schema xmlns="http://docs.oasis-open.org/odata/ns/edm" Namespace="BuiltInModel"
      Alias="BuiltInModel">
      <EnumType Name="OnDelete" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="CASCADE" Value="0"></Member>
        <Member Name="NONE" Value="1"></Member>
        <Member Name="SET_NULL" Value="2"></Member>
        <Member Name="SET_DEFAULT" Value="3"></Member>
      </EnumType>
      <EnumType Name="MrvStrategyType" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="NONE" Value="0"></Member>
        <Member Name="REVISION" Value="1"></Member>
        <Member Name="REVISION_VERSION" Value="2"></Member>
      </EnumType>
      <EnumType Name="RelationSourceTypeEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="FROM" Value="0"></Member>
        <Member Name="TO" Value="1"></Member>
      </EnumType>
      <EnumType Name="RelationConstrictEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="MANY" Value="0"></Member>
        <Member Name="ONE" Value="1"></Member>
      </EnumType>
      <EnumType Name="RelationStatus" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="ACTIVE" Value="0">
          <Annotation Term="BuiltInModel.Description">
            <String>生效</String>
          </Annotation>
        </Member>
        <Member Name="INACTIVE" Value="1">
          <Annotation Term="BuiltInModel.Description">
            <String>失效</String>
          </Annotation>
        </Member>
      </EnumType>
      <EnumType Name="RelationClassify" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="ASSOCIATION" Value="0">
          <Annotation Term="BuiltInModel.Description">
            <String>水平关系</String>
          </Annotation>
        </Member>
        <Member Name="AGGREGATION" Value="1">
          <Annotation Term="BuiltInModel.Description">
            <String>聚合关系</String>
          </Annotation>
        </Member>
        <Member Name="COMPOSITION" Value="2">
          <Annotation Term="BuiltInModel.Description">
            <String>组合关系</String>
          </Annotation>
        </Member>
      </EnumType>
      <EnumType Name="RelationVersionUpgradeActionEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="NONE" Value="0">
          <Annotation Term="BuiltInModel.Description">
            <String>不变</String>
          </Annotation>
        </Member>
        <Member Name="FLOAT" Value="1">
          <Annotation Term="BuiltInModel.Description">
            <String>浮动</String>
          </Annotation>
        </Member>
        <Member Name="CLONE" Value="2">
          <Annotation Term="BuiltInModel.Description">
            <String>复制</String>
          </Annotation>
        </Member>
      </EnumType>
      <EnumType Name="IdiConvertState" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="INIT" Value="0"></Member>
        <Member Name="WAITING" Value="1"></Member>
        <Member Name="RUNNING" Value="2"></Member>
        <Member Name="FAILURE" Value="3"></Member>
        <Member Name="SUCCESS" Value="4"></Member>
      </EnumType>
      <EnumType Name="PartType" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="NEUE_PRT" Value="0"></Member>
        <Member Name="NEUE_ASM" Value="1"></Member>
      </EnumType>
      <EnumType Name="FileSignatureUrlActionType" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="UPLOAD" Value="0"></Member>
        <Member Name="DOWNLOAD" Value="1"></Member>
      </EnumType>
      <EntityType Name="Plt0MasterObject" BaseType="PaaS.Plt0ApplicationObject">
        <Annotation Term="BuiltInModel.ReadOnly">
          <Bool>true</Bool>
        </Annotation>
      </EntityType>
      <EntityType Name="Plt0User" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="email" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="phone" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="icon" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
      </EntityType>
      <EntityType Name="Plt0RevisionObject" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="revisionCode" Type="Edm.String" Nullable="false"></Property>
        <Property Name="revisionOrder" Type="Edm.Int32" Nullable="false"></Property>
        <Property Name="isLatestRevision" Type="Edm.Boolean" Nullable="false" DefaultValue="true"></Property>
        <Property Name="isLocked" Type="Edm.Boolean" Nullable="false" DefaultValue="false"></Property>
        <Property Name="lockedAt" Type="Edm.DateTimeOffset"></Property>
        <NavigationProperty Name="preRevision" Type="BuiltInModel.Plt0RevisionObject"></NavigationProperty>
        <NavigationProperty Name="master" Type="BuiltInModel.Plt0MasterObject" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="lockedBy" Type="BuiltInModel.Plt0User"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0VersionObject" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="versionNumber" Type="Edm.Int32" Nullable="false"></Property>
        <Property Name="isLatestVersion" Type="Edm.Boolean" Nullable="false" DefaultValue="true"></Property>
        <NavigationProperty Name="revision" Type="BuiltInModel.Plt0RevisionObject" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0LifecycleStatus" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="statusCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="name" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
      </EntityType>
      <EntityType Name="Plt0LifecycleStatusStrategy" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="strategyCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="name" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
      </EntityType>
      <EntityType Name="Plt0MrvObject" BaseType="PaaS.Plt0ApplicationObject" Abstract="true">
        <Property Name="code" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="name" Type="Edm.String"></Property>
        <Property Name="description" Type="Edm.String"></Property>
        <NavigationProperty Name="version" Type="BuiltInModel.Plt0VersionObject">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </NavigationProperty>
        <NavigationProperty Name="owner" Type="BuiltInModel.Plt0User">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </NavigationProperty>
        <NavigationProperty Name="lifecycleStatus" Type="BuiltInModel.Plt0LifecycleStatus">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0File" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String"></Property>
        <Property Name="description" Type="Edm.String"></Property>
        <Property Name="softwareRev" Type="Edm.String"></Property>
        <NavigationProperty Name="storageItem" Type="PaaS.Plt0ApplicationObject"></NavigationProperty>
        <Annotation Term="BuiltInModel.ReadOnly">
          <Bool>true</Bool>
        </Annotation>
      </EntityType>
      <EntityType Name="Plt0IdiFile" BaseType="BuiltInModel.Plt0File">
        <Property Name="idiLightModelId" Type="Edm.String">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="convertState" Type="BuiltInModel.IdiConvertState">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="triggerTime" Type="Edm.DateTimeOffset">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
      </EntityType>
      <EntityType Name="Plt0RootRelationType" BaseType="PaaS.Plt0ApplicationObject">
        <NavigationProperty Name="from" Type="PaaS.Plt0ApplicationObject" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <NavigationProperty Name="to" Type="PaaS.Plt0ApplicationObject" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0RelationQuery" BaseType="PaaS.Plt0ApplicationObject">
        <NavigationProperty Name="toRelations" Type="Collection(BuiltInModel.Plt0RootRelationType)"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0RootRelationTypeConfig" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="relationClassify" Type="BuiltInModel.RelationClassify" Nullable="false"></Property>
        <Property Name="sourceRelationConstrict" Type="BuiltInModel.RelationConstrictEnum"
          Nullable="false"></Property>
        <Property Name="targetRelationConstrict" Type="BuiltInModel.RelationConstrictEnum"
          Nullable="false"></Property>
        <Property Name="sourceRelationVersionUpgradeAction"
          Type="BuiltInModel.RelationVersionUpgradeActionEnum" Nullable="false"></Property>
        <Property Name="targetRelationVersionUpgradeAction"
          Type="BuiltInModel.RelationVersionUpgradeActionEnum" Nullable="false"></Property>
        <Property Name="relationStatus" Type="BuiltInModel.RelationStatus" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" MaxLength="255"></Property>
        <NavigationProperty Name="applyRelationType" Type="BuiltInModel.Plt0RootRelationType"
          Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0RelationFromOrToType" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="sideType" Type="BuiltInModel.RelationSourceTypeEnum" Nullable="false"
          MaxLength="64"></Property>
        <NavigationProperty Name="relationType" Type="BuiltInModel.Plt0EntityType" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <NavigationProperty Name="constrictType" Type="BuiltInModel.Plt0EntityType" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0Schema" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="namespace" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="alias" Type="Edm.String" Nullable="false"></Property>
      </EntityType>
      <EntityType Name="Plt0BaseType" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="displayName" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="isAbstract" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="isOpen" Type="Edm.Boolean" DefaultValue="false"></Property>
        <NavigationProperty Name="schema" Type="BuiltInModel.Plt0Schema" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EntityType" BaseType="BuiltInModel.Plt0BaseType">
        <Property Name="entityTypeCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="isVirtual" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="hasStream" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="mrvStrategy" Type="BuiltInModel.MrvStrategyType"></Property>
        <Property Name="isReadOnly" Type="Edm.Boolean" DefaultValue="false"></Property>
        <NavigationProperty Name="icon" Type="BuiltInModel.Plt0File" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="baseType" Type="BuiltInModel.Plt0EntityType">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </NavigationProperty>
        <NavigationProperty Name="lifecycleStatusStrategy"
          Type="BuiltInModel.Plt0LifecycleStatusStrategy"></NavigationProperty>
        <NavigationProperty Name="revisionNumberRule"
          Type="BuiltInModel.Plt0BusinessObjectRevisionNumberRule"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EntityTypeUnionUniq" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <NavigationProperty Name="ownerType" Type="BuiltInModel.Plt0EntityType" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EntityTypeUnionUniqProperty" BaseType="PaaS.Plt0ApplicationObject">
        <NavigationProperty Name="ownerUnionUniq" Type="BuiltInModel.Plt0EntityTypeUnionUniq"
          Nullable="false"></NavigationProperty>
        <NavigationProperty Name="structuralProperty" Type="BuiltInModel.Plt0StructuralProperty"></NavigationProperty>
        <NavigationProperty Name="navigationProperty" Type="BuiltInModel.Plt0NavigationProperty"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EntityTypePropertyOverride" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="isAutoGenerateCode" Type="Edm.Boolean" DefaultValue="false"></Property>
        <NavigationProperty Name="ownerType" Type="BuiltInModel.Plt0EntityType" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="structuralProperty" Type="BuiltInModel.Plt0StructuralProperty"
          Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0ComplexType" BaseType="BuiltInModel.Plt0BaseType">
        <Property Name="complexTypeCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <NavigationProperty Name="baseType" Type="BuiltInModel.Plt0ComplexType"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EnumType" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="enumTypeCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="underlyingType" Type="Edm.String"></Property>
        <Property Name="isFlags" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="members" Type="Collection(Edm.String)"></Property>
        <NavigationProperty Name="schema" Type="BuiltInModel.Plt0Schema" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0TypeDefinition" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="typeDefinitionCode" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.AutoGenerateCode">
            <Bool>true</Bool>
          </Annotation>
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="underlyingType" Type="Edm.String"></Property>
        <Property Name="maxLength" Type="Edm.Int32"></Property>
        <Property Name="isUnicode" Type="Edm.Boolean"></Property>
        <Property Name="srid" Type="Edm.String"></Property>
        <Property Name="precision" Type="Edm.Int32"></Property>
        <Property Name="scale" Type="Edm.Int32"></Property>
        <NavigationProperty Name="schema" Type="BuiltInModel.Plt0Schema" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0StructuralProperty" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false"></Property>
        <Property Name="displayName" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="type" Type="Edm.String" Nullable="false"></Property>
        <Property Name="isCollection" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="isNullable" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="maxLength" Type="Edm.Int16"></Property>
        <Property Name="isUnicode" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="srid" Type="Edm.String"></Property>
        <Property Name="precision" Type="Edm.Int16"></Property>
        <Property Name="scale" Type="Edm.Int16"></Property>
        <Property Name="defaultValue" Type="Edm.String"></Property>
        <Property Name="isAutoGenerateCode" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="isReadOnly" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="isUniq" Type="Edm.Boolean" DefaultValue="false"></Property>
        <NavigationProperty Name="ownerType" Type="BuiltInModel.Plt0BaseType" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0NavigationProperty" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false"></Property>
        <Property Name="displayName" Type="Edm.String" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="isCollection" Type="Edm.Boolean"></Property>
        <Property Name="isNullable" Type="Edm.Boolean"></Property>
        <Property Name="isContainsTarget" Type="Edm.Boolean"></Property>
        <Property Name="referentialConstraint" Type="Edm.String"></Property>
        <Property Name="onDelete" Type="BuiltInModel.OnDelete"></Property>
        <Property Name="isReadOnly" Type="Edm.Boolean" DefaultValue="false"></Property>
        <Property Name="isUniq" Type="Edm.Boolean" DefaultValue="false"></Property>
        <NavigationProperty Name="ownerType" Type="BuiltInModel.Plt0BaseType" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="type" Type="BuiltInModel.Plt0EntityType" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="partner" Type="BuiltInModel.Plt0NavigationProperty"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0Action" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="schema" Type="Edm.String"></Property>
        <Property Name="isBound" Type="Edm.Boolean"></Property>
        <Property Name="parameter" Type="Edm.String"></Property>
        <Property Name="returnType" Type="Edm.String"></Property>
        <Property Name="entitySetPath" Type="Edm.String"></Property>
      </EntityType>
      <EntityType Name="Plt0Function" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="schema" Type="Edm.String"></Property>
        <Property Name="isBound" Type="Edm.Boolean"></Property>
        <Property Name="isComposable" Type="Edm.Boolean"></Property>
        <Property Name="parameter" Type="Edm.String"></Property>
        <Property Name="returnType" Type="Edm.String"></Property>
        <Property Name="entitySetPath" Type="Edm.String"></Property>
      </EntityType>
      <EntityType Name="Plt0EntityContainer" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="extend" Type="Edm.String"></Property>
        <NavigationProperty Name="schema" Type="BuiltInModel.Plt0Schema" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0EntitySet" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="isIncludeInServiceDocument" Type="Edm.Boolean"></Property>
        <NavigationProperty Name="entityContainer" Type="BuiltInModel.Plt0EntityContainer"
          Nullable="false"></NavigationProperty>
        <NavigationProperty Name="entityType" Type="BuiltInModel.Plt0EntityType" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0Singleton" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <NavigationProperty Name="entityContainer" Type="BuiltInModel.Plt0EntityContainer"
          Nullable="false"></NavigationProperty>
        <NavigationProperty Name="entityType" Type="BuiltInModel.Plt0EntityType" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0NavigationPropertyBinding" BaseType="PaaS.Plt0ApplicationObject">
        <NavigationProperty Name="entitySet" Type="BuiltInModel.Plt0EntitySet" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="path" Type="BuiltInModel.Plt0NavigationProperty" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="target" Type="BuiltInModel.Plt0EntitySet" Nullable="false"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0ActionImport" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <NavigationProperty Name="entityContainer" Type="BuiltInModel.Plt0EntityContainer"
          Nullable="false"></NavigationProperty>
        <NavigationProperty Name="action" Type="BuiltInModel.Plt0Action" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="entitySet" Type="BuiltInModel.Plt0EntitySet"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0FunctionImport" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="name" Type="Edm.String" Nullable="false">
          <Annotation Term="BuiltInModel.Uniq">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="description" Type="Edm.String" Nullable="false"></Property>
        <Property Name="isIncludeInServiceDocument" Type="Edm.Boolean"></Property>
        <NavigationProperty Name="entityContainer" Type="BuiltInModel.Plt0EntityContainer"
          Nullable="false"></NavigationProperty>
        <NavigationProperty Name="function" Type="BuiltInModel.Plt0Function" Nullable="false"></NavigationProperty>
        <NavigationProperty Name="entitySet" Type="BuiltInModel.Plt0EntitySet"></NavigationProperty>
      </EntityType>
      <EntityType Name="Plt0BusinessObjectRevisionNumberRule" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="ruleCode" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="name" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="isActive" Type="Edm.Boolean" Nullable="false"></Property>
        <Property Name="isEmployed" Type="Edm.Boolean" Nullable="false"></Property>
        <Property Name="isLocked" Type="Edm.Boolean" Nullable="false"></Property>
        <Property Name="description" Type="Edm.String" MaxLength="1000"></Property>
      </EntityType>
      <EntityType Name="Plt0BusinessObjectRevisionNumberCode" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="revisionOrder" Type="Edm.Int32" Nullable="false"></Property>
        <Property Name="revisionCode" Type="Edm.String" Nullable="false"></Property>
        <NavigationProperty Name="ruleCodeRef"
          Type="BuiltInModel.Plt0BusinessObjectRevisionNumberRule" Nullable="false"></NavigationProperty>
      </EntityType>
      <ComplexType Name="FileSignatureUrl">
        <Property Name="storageItemId" Type="Edm.String" Nullable="false"></Property>
        <Property Name="fileId" Type="Edm.String" Nullable="false"></Property>
        <Property Name="signatureUrl" Type="Edm.String" Nullable="false"></Property>
      </ComplexType>
      <Action Name="Deploy" IsBound="false">
        <Parameter Name="entityTypeIds" Type="Collection(Edm.String)" Nullable="false"></Parameter>
        <ReturnType Type="Edm.Boolean" Nullable="false" />
      </Action>
      <Action Name="GenerateCode" IsBound="true">
        <Parameter Name="applicationObject" Type="PaaS.Plt0ApplicationObject"></Parameter>
        <Parameter Name="propertyName" Type="Edm.String" Nullable="false"></Parameter>
        <ReturnType Type="Edm.String" Nullable="false" />
      </Action>
      <Action Name="NextRevision" IsBound="true">
        <Parameter Name="mrvObject" Type="BuiltInModel.Plt0MrvObject"></Parameter>
        <ReturnType Type="Edm.String" Nullable="false" />
      </Action>
      <Action Name="Lock" IsBound="true">
        <Parameter Name="mrvObject" Type="BuiltInModel.Plt0MrvObject" Nullable="false"></Parameter>
        <ReturnType Type="Edm.Boolean" Nullable="false" />
      </Action>
      <Action Name="Unlock" IsBound="true">
        <Parameter Name="mrvObject" Type="BuiltInModel.Plt0MrvObject" Nullable="false"></Parameter>
        <ReturnType Type="Edm.Boolean" Nullable="false" />
      </Action>
      <Action Name="DeleteRevision" IsBound="true">
        <Parameter Name="mrvObject" Type="BuiltInModel.Plt0MrvObject" Nullable="false"></Parameter>
        <ReturnType Type="Edm.Int32" Nullable="false" />
      </Action>
      <Action Name="BatchGetFileSignatureUrl" IsBound="false">
        <Parameter Name="fileIds" Type="Collection(Edm.String)" Nullable="false"></Parameter>
        <Parameter Name="actionType" Type="BuiltInModel.FileSignatureUrlActionType" Nullable="false"></Parameter>
        <ReturnType Type="Collection(BuiltInModel.FileSignatureUrl)" Nullable="false" />
      </Action>
      <Action Name="ChangeLifecycleState" IsBound="true">
        <Parameter Name="applicationObject" Type="PaaS.Plt0ApplicationObject" Nullable="false"></Parameter>
        <Parameter Name="lifecycleState" Type="PaaS.LifecycleState" Nullable="false"></Parameter>
        <ReturnType Type="Edm.Boolean" Nullable="false" />
      </Action>
      <Function Name="GetRelationTreeByNcid">
        <Parameter Name="ncid" Type="Edm.String" Nullable="false"></Parameter>
        <Parameter Name="typeNcid" Type="Edm.String" Nullable="false"></Parameter>
        <Parameter Name="isTree" Type="Edm.Boolean" Nullable="false"></Parameter>
        <Parameter Name="isLatest" Type="Edm.Boolean"></Parameter>
        <ReturnType Type="BuiltInModel.Plt0RelationQuery" Nullable="false" />
      </Function>
      <Term Name="Description" Type="Edm.String" Nullable="false"></Term>
      <Term Name="IsVirtual" Type="Edm.Boolean" AppliesTo="EntityType" Nullable="false"
        DefaultValue="false">
        <Annotation Term="BuiltInModel.Description">
          <String>Indicating this entityType is virtual or not,true means this entityType can't be
            instantiated.</String>
        </Annotation>
      </Term>
      <Term Name="MrvStrategy" Type="BuiltInModel.MrvStrategyType" AppliesTo="EntityType"
        Nullable="false" DefaultValue="NONE">
        <Annotation Term="BuiltInModel.Description">
          <String>This allows any sub entityType of BuiltInModel.Plt0MrvObject to override the
            mrvStrategy inherited from parent entityType.</String>
        </Annotation>
      </Term>
      <Term Name="LifecycleStatusStrategy" Type="Edm.String" AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>This allows any sub entityType of BuiltInModel.Plt0MrvObject to override the
            lifecycleStatusStrategy inherited from parent entityType,this value must be any valid
            BuiltInModel.Plt0LifecycleStatusStrategy instance ncid.</String>
        </Annotation>
      </Term>
      <Term Name="RevisionNumberRule" Type="Edm.String" AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>This allows any sub entityType of BuiltInModel.Plt0MrvObject to override the
            revisionNumberRule inherited from parent entityType,this value must be any valid
            BuiltInModel.Plt0BusinessObjectRevisionNumberRule instance ncid.</String>
        </Annotation>
      </Term>
      <Term Name="AutoGenerateCode" Type="Edm.Boolean" AppliesTo="EntityType Property"
        Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>If this values is true and the applied property type is Edm.String,then the value
            of the applied property will be auto generated.</String>
        </Annotation>
      </Term>
      <Term Name="ReadOnly" Type="Edm.Boolean" AppliesTo="EntityType Property" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>Marking this property value or this entityType instance that it can only be
            instantiated once.</String>
        </Annotation>
      </Term>
      <Term Name="Uniq" Type="Edm.Boolean" AppliesTo="Property" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>Marking this property value is uniq index.</String>
        </Annotation>
      </Term>
      <Term Name="UnionUniq" Type="Edm.String" AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>Composing multiple properties as a union uniq index.</String>
        </Annotation>
      </Term>
      <Term Name="RestrictRelationType" Type="Edm.String" AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>Restrict the entity type of the relation model.</String>
        </Annotation>
      </Term>
      <Term Name="RelationConstrictTerm" Type="BuiltInModel.RelationConstrictEnum"
        AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>one or many</String>
        </Annotation>
      </Term>
      <Term Name="RelationVersionUpgradeActionEnumTerm"
        Type="BuiltInModel.RelationVersionUpgradeActionEnum" AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>float or clone or none</String>
        </Annotation>
      </Term>
      <Term Name="RelationClassifyEnumTerm" Type="BuiltInModel.RelationClassify"
        AppliesTo="EntityType" Nullable="false">
        <Annotation Term="BuiltInModel.Description">
          <String>association or aggregation or composition</String>
        </Annotation>
      </Term>
      <EntityContainer Name="Container">
        <EntitySet Name="Schemas" EntityType="BuiltInModel.Plt0Schema"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntityTypes" EntityType="BuiltInModel.Plt0EntityType"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="ComplexTypes" EntityType="BuiltInModel.Plt0ComplexType"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EnumTypes" EntityType="BuiltInModel.Plt0EnumType"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="TypeDefinitions" EntityType="BuiltInModel.Plt0TypeDefinition"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="StructuralProperties" EntityType="BuiltInModel.Plt0StructuralProperty"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="NavigationProperties" EntityType="BuiltInModel.Plt0NavigationProperty"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="Actions" EntityType="BuiltInModel.Plt0Action"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="Functions" EntityType="BuiltInModel.Plt0Function"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntityContainers" EntityType="BuiltInModel.Plt0EntityContainer"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntitySets" EntityType="BuiltInModel.Plt0EntitySet"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="Singletons" EntityType="BuiltInModel.Plt0Singleton"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="NavigationPropertyBindings"
          EntityType="BuiltInModel.Plt0NavigationPropertyBinding" IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="ActionImports" EntityType="BuiltInModel.Plt0ActionImport"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="FunctionImports" EntityType="BuiltInModel.Plt0FunctionImport"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="BusinessObjectRevisionNumberRules"
          EntityType="BuiltInModel.Plt0BusinessObjectRevisionNumberRule"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="BusinessObjectRevisionNumberCodes"
          EntityType="BuiltInModel.Plt0BusinessObjectRevisionNumberCode"
          IncludeInServiceDocument="false">
          <NavigationPropertyBinding Path="ruleCodeRef" Target="BusinessObjectRevisionNumberRules" />
        </EntitySet>
        <EntitySet Name="LifecycleStatuses" EntityType="BuiltInModel.Plt0LifecycleStatus"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="LifecycleStatusStrategies"
          EntityType="BuiltInModel.Plt0LifecycleStatusStrategy" IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="FromOrToTypes" EntityType="BuiltInModel.Plt0RelationFromOrToType"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="RootRelationTypes" EntityType="BuiltInModel.Plt0RootRelationType"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntityTypeUnionUniqs" EntityType="BuiltInModel.Plt0EntityTypeUnionUniq"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntityTypeUnionUniqProperties"
          EntityType="BuiltInModel.Plt0EntityTypeUnionUniqProperty" IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="Files" EntityType="BuiltInModel.Plt0File" IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="IdiFiles" EntityType="BuiltInModel.Plt0IdiFile"
          IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="EntityTypePropertyOverrides"
          EntityType="BuiltInModel.Plt0EntityTypePropertyOverride" IncludeInServiceDocument="false"></EntitySet>
        <EntitySet Name="RelCadBomWithTypes" EntityType="neue.RelCadBomWithType"></EntitySet>
        <EntitySet Name="RelCadPartTwoDimensionDrawingWithTypes"
          EntityType="neue.RelCadPartTwoDimensionDrawingWithType"></EntitySet>
        <EntitySet Name="RelCadFileFileWithTypes" EntityType="neue.RelCadFileFileWithType"></EntitySet>
        <EntitySet Name="MappingConfigs" EntityType="neue.MappingConfig"></EntitySet>
        <EntitySet Name="RelCadFileFileRelations" EntityType="neue.RelCadFileFile"></EntitySet>
        <EntitySet Name="RelCadPartTwoDimensionDrawingRelations"
          EntityType="neue.RelCadPartTwoDimensionDrawing"></EntitySet>
        <EntitySet Name="RelCadBomRelations" EntityType="neue.RelCadBom"></EntitySet>
        <EntitySet Name="CadParts" EntityType="neue.CadPart"></EntitySet>
        <EntitySet Name="NeueCadAsms" EntityType="neue.NeueCadAsm"></EntitySet>
        <EntitySet Name="NeueCadParts" EntityType="neue.NeueCadPart"></EntitySet>
        <EntitySet Name="NeueTwoDimensionDrawings" EntityType="neue.NeueTwoDimensionDrawing"></EntitySet>
        <ActionImport Name="Deploy" Action="BuiltInModel.Deploy"></ActionImport>
        <ActionImport Name="BatchGetFileSignatureUrl" Action="BuiltInModel.BatchGetFileSignatureUrl"></ActionImport>
        <FunctionImport Name="GetRelationTreeByNcid" Function="BuiltInModel.GetRelationTreeByNcid"
          IncludeInServiceDocument="true"></FunctionImport>
      </EntityContainer>
    </Schema>
    <Schema xmlns="http://docs.oasis-open.org/odata/ns/edm" Namespace="neue" Alias="neue">
      <EnumType Name="MappingConfigDirectionEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="CAX_TO_CDP" Value="0"></Member>
        <Member Name="CDP_TO_CAX" Value="1"></Member>
        <Member Name="BIDIRECTIONAL" Value="2"></Member>
      </EnumType>
      <EnumType Name="MappingConfigPartTypeEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="PART" Value="0"></Member>
        <Member Name="DRAWING" Value="1"></Member>
        <Member Name="FE" Value="2"></Member>
        <Member Name="CADBOM" Value="3"></Member>
      </EnumType>
      <EnumType Name="MappingConfigToolEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="CAX" Value="0"></Member>
        <Member Name="NX" Value="1"></Member>
        <Member Name="SOLIDWORKS" Value="2"></Member>
        <Member Name="CATIA" Value="3"></Member>
      </EnumType>
      <EnumType Name="RelCadFileFileLinkTypeEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="MASTER" Value="0"></Member>
        <Member Name="REFERENCE" Value="1"></Member>
      </EnumType>
      <EnumType Name="RelCadFileFileUsageTypeEnum" IsFlags="false" UnderlyingType="Edm.Int32">
        <Member Name="DESIGN_MODEL" Value="0"></Member>
        <Member Name="EXCHANGE_FORMAT" Value="1"></Member>
        <Member Name="THUMBNAIL" Value="2"></Member>
        <Member Name="PDF" Value="3"></Member>
        <Member Name="SIMULATION" Value="4"></Member>
        <Member Name="TWO_DIMENSION_DRAWING" Value="5"></Member>
      </EnumType>
      <EntityType Name="RelCadBomWithType" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="bomExcluded" Type="Edm.Boolean" Nullable="false" DefaultValue="false"
          MaxLength="64"></Property>
        <Property Name="suppressed" Type="Edm.Boolean" Nullable="false" DefaultValue="false"
          MaxLength="64"></Property>
        <Property Name="quantity" Type="Edm.Int32" Nullable="false" DefaultValue="1" MaxLength="64"></Property>
        <Property Name="configuration" Type="Edm.String" MaxLength="64"></Property>
        <Property Name="transformationMatrix" Type="Edm.String" MaxLength="1000"></Property>
        <Property Name="instanceName" Type="Edm.String" Nullable="false" DefaultValue="-"
          MaxLength="64"></Property>
        <NavigationProperty Name="to" Type="neue.CadPart" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <NavigationProperty Name="from" Type="neue.CadPart" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="RelCadPartTwoDimensionDrawingWithType" BaseType="PaaS.Plt0ApplicationObject">
        <NavigationProperty Name="to" Type="neue.NeueTwoDimensionDrawing" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <NavigationProperty Name="from" Type="neue.CadPart" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="RelCadFileFileWithType" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="linkType" Type="neue.RelCadFileFileLinkTypeEnum" Nullable="false"
          MaxLength="64"></Property>
        <Property Name="usageType" Type="neue.RelCadFileFileUsageTypeEnum" Nullable="false"
          MaxLength="64"></Property>
        <NavigationProperty Name="from" Type="neue.CadFile" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <NavigationProperty Name="to" Type="BuiltInModel.Plt0File" Nullable="false">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
      </EntityType>
      <EntityType Name="MappingConfig" BaseType="PaaS.Plt0ApplicationObject">
        <Property Name="direction" Type="neue.MappingConfigDirectionEnum" Nullable="false"></Property>
        <Property Name="onCax" Type="Edm.Boolean"></Property>
        <Property Name="cdpProperty" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="partType" Type="neue.MappingConfigPartTypeEnum" Nullable="false"
          MaxLength="255"></Property>
        <Property Name="drawingSheetArea" Type="Edm.String" MaxLength="255"></Property>
        <Property Name="name" Type="Edm.String" Nullable="false" MaxLength="255"></Property>
        <Property Name="caxProperty" Type="Edm.String" Nullable="false" MaxLength="64"></Property>
        <Property Name="tool" Type="neue.MappingConfigToolEnum" Nullable="false" MaxLength="64"></Property>
        <Annotation Term="BuiltInModel.UnionUniq">
          <Collection>
            <String>partType</String>
            <String>cdpProperty</String>
            <String>caxProperty</String>
            <String>tool</String>
          </Collection>
        </Annotation>
      </EntityType>
      <EntityType Name="RelCadFileFile" BaseType="BuiltInModel.Plt0RootRelationType">
        <Property Name="usageType" Type="neue.RelCadFileFileUsageTypeEnum" Nullable="false"
          MaxLength="64"></Property>
        <Property Name="linkType" Type="neue.RelCadFileFileLinkTypeEnum" Nullable="false"
          MaxLength="64"></Property>
      </EntityType>
      <EntityType Name="RelCadPartTwoDimensionDrawing" BaseType="BuiltInModel.Plt0RootRelationType"></EntityType>
      <EntityType Name="RelCadBom" BaseType="BuiltInModel.Plt0RootRelationType">
        <Property Name="instanceName" Type="Edm.String" Nullable="false"></Property>
        <Property Name="bomExcluded" Type="Edm.Boolean" Nullable="false" DefaultValue="false"
          MaxLength="64"></Property>
        <Property Name="suppressed" Type="Edm.Boolean" Nullable="false" DefaultValue="false"
          MaxLength="64"></Property>
        <Property Name="quantity" Type="Edm.Int32" DefaultValue="1" MaxLength="64"></Property>
        <Property Name="configuration" Type="Edm.String" Nullable="false" MaxLength="64"></Property>
        <Property Name="transformationMatrix" Type="Edm.String" Nullable="false" MaxLength="1000"></Property>
      </EntityType>
      <EntityType Name="CadFile" BaseType="BuiltInModel.Plt0MrvObject">
        <Property Name="submitDescription" Type="Edm.String" MaxLength="1000"></Property>
        <Annotation Term="BuiltInModel.IsVirtual">
          <Bool>true</Bool>
        </Annotation>
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
      </EntityType>
      <EntityType Name="CadPart" BaseType="neue.CadFile">
        <Property Name="gravityCenter" Type="Edm.String"></Property>
        <Property Name="mass" Type="Edm.Decimal" Unicode="false" DefaultValue="0" Precision="20"
          Scale="8"></Property>
        <Property Name="material" Type="Edm.String"></Property>
        <Property Name="openSurfaceArea" Type="Edm.Decimal" Unicode="false" DefaultValue="0"
          Precision="20" Scale="8"></Property>
        <Property Name="partType" Type="BuiltInModel.PartType" Nullable="false" Unicode="false">
          <Annotation Term="BuiltInModel.ReadOnly">
            <Bool>true</Bool>
          </Annotation>
        </Property>
        <Property Name="solidSurfaceArea" Type="Edm.Decimal" Unicode="false" DefaultValue="0"
          Precision="20" Scale="8"></Property>
        <Property Name="volume" Type="Edm.Decimal" Unicode="false" DefaultValue="0" Precision="20"
          Scale="8"></Property>
        <NavigationProperty Name="thumbnail" Type="BuiltInModel.Plt0File">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <Annotation Term="BuiltInModel.IsVirtual">
          <Bool>true</Bool>
        </Annotation>
        <Annotation Term="BuiltInModel.MrvStrategy">
          <EnumMember>BuiltInModel.MrvStrategyType/REVISION_VERSION</EnumMember>
        </Annotation>
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
        <Annotation Term="BuiltInModel.AutoGenerateCode" Qualifier="code">
          <Bool>true</Bool>
        </Annotation>
      </EntityType>
      <EntityType Name="NeueCadAsm" BaseType="neue.CadPart">
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
      </EntityType>
      <EntityType Name="NeueCadPart" BaseType="neue.CadPart">
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
      </EntityType>
      <EntityType Name="NeueTwoDimensionDrawing" BaseType="neue.TwoDimensionDrawing">
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
      </EntityType>
      <EntityType Name="TwoDimensionDrawing" BaseType="neue.CadFile">
        <NavigationProperty Name="thumbnail" Type="BuiltInModel.Plt0File">
          <OnDelete Action="None"></OnDelete>
        </NavigationProperty>
        <Annotation Term="BuiltInModel.IsVirtual">
          <Bool>true</Bool>
        </Annotation>
        <Annotation Term="BuiltInModel.MrvStrategy">
          <EnumMember>BuiltInModel.MrvStrategyType/REVISION_VERSION</EnumMember>
        </Annotation>
        <Annotation Term="BuiltInModel.RevisionNumberRule">
          <String>ncid1.plt0businessobjectrevisionnumberrule.local..revision-rule-english-alphabet</String>
        </Annotation>
        <Annotation Term="BuiltInModel.AutoGenerateCode" Qualifier="code">
          <Bool>true</Bool>
        </Annotation>
      </EntityType>
    </Schema>
  </edmx:DataServices>
</edmx:Edmx>