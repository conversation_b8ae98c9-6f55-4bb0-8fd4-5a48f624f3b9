import { Expand } from '@element-plus/icons-vue'

export const initialData = [
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
]
export const tableData = [
  {
    ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
    createdBy: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    createdAt: '2025-07-08T00:42:31.693Z',
    modifiedBy: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    modifiedAt: '2025-07-08T00:45:08.483Z',
    schemaVersion: '1.0.0',
    partNo: 'P032161',
    partName: '第一层子装配A',
    partDescription: '这是一个子装update',
    mass: 93,
    material: 'est irure Duis non velit',
    gravityCenter: 'deserunt eu',
    volume: 85,
    solidSurfaceArea: 91,
    openSurfaceArea: 39,
    partType: 'NEUE_ASM',
    owner: {
      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
      createdBy: {
        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
      },
      createdAt: '2025-04-11T09:24:58.626Z',
      schemaVersion: '',
      lifecycleState: 'ACTIVE',
      name: 'person3',
      email: '<EMAIL>',
      phone: '***********',
      icon: '',
      account: {
        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
      },
      person: {
        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
      },
    },
    status: 'INWORK',
    lockState: 'UNLOCKED',
    lockedAt: '1970-01-01T00:00:00.000Z',
    version: '2',
    latestVersion: true,
    checkState: 'CHECKED_IN',
    checkedOutAt: '1970-01-01T00:00:00.000Z',
    master: {
      ncid: 'ncid1.plt0neuecadpartmaster.local..1232adb9-b759-481e-b784-0e9146ea7eec',
      createdAt: '2025-07-08T00:42:31.690Z',
      modifiedAt: '2025-07-08T00:42:31.690Z',
      schemaVersion: '1.0.0',
    },
    revision: 'A',
    latestRevision: true,
    cadboms: [
      {
        ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
        createdAt: '2025-07-08T00:42:31.693Z',
        modifiedAt: '2025-07-08T00:45:08.483Z',
        schemaVersion: '1.0.0',
        source: {
          ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
        },
        target: {
          ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
          createdBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
          partNo: 'P032163',
          partName: '装配A的子件C',
          partDescription: '这是一个螺母2update',
          mass: 93,
          material: 'est irure Duis non velit',
          gravityCenter: 'deserunt eu',
          volume: 85,
          solidSurfaceArea: 91,
          openSurfaceArea: 39,
          partType: 'NEUE_PRT',
          owner: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          status: 'INWORK',
          lockState: 'UNLOCKED',
          lockedAt: '1970-01-01T00:00:00.000Z',
          version: '2',
          latestVersion: true,
          checkState: 'CHECKED_IN',
          checkedOutAt: '1970-01-01T00:00:00.000Z',
          master: {
            ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
            createdAt: '2025-07-08T00:42:31.690Z',
            modifiedAt: '2025-07-08T00:42:31.690Z',
            schemaVersion: '1.0.0',
          },
          revision: 'A',
          latestRevision: true,
          idi3DLightModelId:
            'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
          convertStatus: 'SUCCESS',
          cadboms: [],
        },
        instanceName: 'A_C_update',
        transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
        quantity: 1,
        suppressed: false,
        configuration: 'CONFIG1',
        createdBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        modifiedBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        partNo: 'P032163',
        partName: '装配A的子件C',
        partDescription: '这是一个螺母2update',
        mass: 93,
        material: 'est irure Duis non velit',
        gravityCenter: 'deserunt eu',
        volume: 85,
        solidSurfaceArea: 91,
        openSurfaceArea: 39,
        partType: 'NEUE_PRT',
        owner: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        status: 'INWORK',
        lockState: 'UNLOCKED',
        lockedAt: '1970-01-01T00:00:00.000Z',
        version: '2',
        latestVersion: true,
        checkState: 'CHECKED_IN',
        checkedOutAt: '1970-01-01T00:00:00.000Z',
        master: {
          ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
          createdAt: '2025-07-08T00:42:31.690Z',
          modifiedAt: '2025-07-08T00:42:31.690Z',
          schemaVersion: '1.0.0',
        },
        revision: 'A',
        latestRevision: true,
        idi3DLightModelId:
          'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
        convertStatus: 'SUCCESS',
        cadboms: [],
        idPath: '0-0-0',
      },
      {
        ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
        createdAt: '2025-07-08T00:42:31.693Z',
        modifiedAt: '2025-07-08T00:45:08.483Z',
        schemaVersion: '1.0.0',
        source: {
          ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
        },
        target: {
          ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
          createdBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          createdAt: '2025-07-08T00:42:31.693Z',
          modifiedBy: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          modifiedAt: '2025-07-08T00:45:08.483Z',
          schemaVersion: '1.0.0',
          partNo: 'P032162',
          partName: '装配A的子件B',
          partDescription: '这是一个螺栓1update',
          mass: 93,
          material: 'est irure Duis non velit',
          gravityCenter: 'deserunt eu',
          volume: 85,
          solidSurfaceArea: 91,
          openSurfaceArea: 39,
          partType: 'NEUE_PRT',
          owner: {
            ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
            createdBy: {
              ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
            },
            createdAt: '2025-04-11T09:24:58.626Z',
            schemaVersion: '',
            lifecycleState: 'ACTIVE',
            name: 'person3',
            email: '<EMAIL>',
            phone: '***********',
            icon: '',
            account: {
              ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
            },
            person: {
              ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
            },
          },
          status: 'INWORK',
          lockState: 'UNLOCKED',
          lockedAt: '1970-01-01T00:00:00.000Z',
          version: '2',
          latestVersion: true,
          checkState: 'CHECKED_IN',
          checkedOutAt: '1970-01-01T00:00:00.000Z',
          master: {
            ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
            createdAt: '2025-07-08T00:42:31.690Z',
            modifiedAt: '2025-07-08T00:42:31.690Z',
            schemaVersion: '1.0.0',
          },
          revision: 'A',
          latestRevision: true,
          idi3DLightModelId:
            'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
          convertStatus: 'SUCCESS',
          cadboms: [],
        },
        instanceName: 'A_B_update',
        transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
        quantity: 1,
        suppressed: false,
        configuration: 'CONFIG1',
        createdBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        modifiedBy: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        partNo: 'P032162',
        partName: '装配A的子件B',
        partDescription: '这是一个螺栓1update',
        mass: 93,
        material: 'est irure Duis non velit',
        gravityCenter: 'deserunt eu',
        volume: 85,
        solidSurfaceArea: 91,
        openSurfaceArea: 39,
        partType: 'NEUE_PRT',
        owner: {
          ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
          createdBy: {
            ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
          },
          createdAt: '2025-04-11T09:24:58.626Z',
          schemaVersion: '',
          lifecycleState: 'ACTIVE',
          name: 'person3',
          email: '<EMAIL>',
          phone: '***********',
          icon: '',
          account: {
            ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
          },
          person: {
            ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
          },
        },
        status: 'INWORK',
        lockState: 'UNLOCKED',
        lockedAt: '1970-01-01T00:00:00.000Z',
        version: '2',
        latestVersion: true,
        checkState: 'CHECKED_IN',
        checkedOutAt: '1970-01-01T00:00:00.000Z',
        master: {
          ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
          createdAt: '2025-07-08T00:42:31.690Z',
          modifiedAt: '2025-07-08T00:42:31.690Z',
          schemaVersion: '1.0.0',
        },
        revision: 'A',
        latestRevision: true,
        idi3DLightModelId:
          'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
        convertStatus: 'SUCCESS',
        cadboms: [],
        idPath: '0-0-1',
      },
    ],
    idPath: '0',
  },
]

export const pageData = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onClick: {
      name: 'onBeforeMount',
      params: ['e'],
      body: `console.log('clicked', e);\nalert('hi');`,
    },
    onChange: {
      name: 'onMounted',
      params: ['value'],
      body: `console.log('value changed', value);`,
    },
  },
  apis: {
    userList07d5b003: {
      url: '/user/list',
      method: 'get',
      params: {
        deptId: '{{form.deptId}}',
      },
      map: { label: 'name', value: 'id' },
    },
    userInfo07d5b003: {
      url: '/user/list',
      method: 'post',
      body: {
        deptId: '{{form.deptId}}',
      },
      map: { label: 'name', value: 'id' },
    },
  },
  elements: [
    {
      id: 'bom-table_60spo02i5g', // 组件唯一身份
      type: 'widget-container',
      name: '搜索表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'table_60spo02i5g', // 组件唯一身份
          type: 'bom-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            data: tableData,
            request: {
              url: '/api/bom/list',
              method: 'get',
              params: {
                deptId: '{{form.deptId}}',
              },
            },
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'Drawer_er4mn6jbtk',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
        {
          id: 'widget-table_60spo02i5g', // 组件唯一身份
          type: 'widget-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            request: {
              url: '/api/bom/list',
              method: 'get',
              params: {
                deptId: '{{form.deptId}}',
              },
            },
          },
          events: [],
          elements: [],
        },
      ],
    },
    {
      id: 'Drawer_er4mn6jbtk',
      type: 'drawer',
      name: '弹框',
      props: {
        modelValue: false,
        size: '1061px',
      },
      elements: [
        {
          id: 'Tabs_3gx13gh2ht',
          type: 'tabs',
          name: 'tabs',
          props: {},
          elements: [
            {
              id: 'TabPane_3gx13gh2ht',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '属性信息',
              },
              elements: [],
            },
            {
              id: 'TabPane_usage',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
            {
              id: 'TabPane_history',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '历史记录',
              },
              elements: [],
            },
            {
              id: 'TabPane_useBy',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
          ],
        },
      ],
      events: [
        {
          nickName: '弹框关闭',
          eventName: 'onClosed',
          actions: [
            {
              id: 'start',
              type: 'start',
              title: '开始',
            },
            {
              id: '561322123',
              type: 'normal',
              title: '节点561322123',
              content: '弹框关闭回调',
              config: {
                actionType: 'refresh',
                actionName: '刷新表格',
                target: 'table_60spo02i5g',
              },
            },
            {
              id: 'end',
              type: 'end',
              title: '结束',
            },
          ],
        },
      ],
      slots: {
        header: [
          {
            id: 'button_fb19o54fjh',
            type: 'div',
            name: '文本框',
            props: {
              style: 'font-size:30px;font-weight:600;color:#0F172A',
              innerHTML: 'UPX10007248',
            },
          },
        ],
        footer: 'footer',
      },
    },
  ],
}

export const bomTableConfig = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {
    onClick: {
      name: 'onBeforeMount',
      params: ['e'],
      body: `console.log('clicked', e);\nalert('hi');`,
    },
    onChange: {
      name: 'onMounted',
      params: ['value'],
      body: `console.log('value changed', value);`,
    },
  },
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'bom-table_60spo02i5g', // 组件唯一身份
      type: 'widget-container',
      name: '搜索表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'bom-table_60spo02i5g', // 组件唯一身份
          type: 'wg-bom-table',
          name: '搜索表单',
          props: {
            columns: [
              { prop: 'partNo', label: '零部件编号', width: '360px' },
              {
                prop: 'partName',
                label: '零部件名称',
                showOverflowTooltip: true,
              },
              {
                prop: 'instanceName',
                label: '实例名称',
                showOverflowTooltip: true,
              },
              { prop: 'version', label: '版本号' },
              {
                label: '状态',
                prop: 'status',
                valueEnum: {
                  working: { text: '工作中', status: 'success' },
                  INWORK: { text: '工作中', status: 'success' },
                },
              },
              {
                label: '修改人',
                prop: 'partType',
                valueType: 'avatar',
              },
              {
                label: '修改时间',
                prop: 'modifiedAt',
                valueType: 'dateTime',
                timeFormat: 'YYYY-MM-DD',
                sortable: true,
              },
            ],
            data: [
              {
                ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                createdBy: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                createdAt: '2025-07-08T00:42:31.693Z',
                modifiedBy: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                modifiedAt: '2025-07-08T00:45:08.483Z',
                schemaVersion: '1.0.0',
                partNo: 'P032161',
                partName: '第一层子装配A111111',
                partDescription: '这是一个子装update',
                mass: 93,
                material: 'est irure Duis non velit',
                gravityCenter: 'deserunt eu',
                volume: 85,
                solidSurfaceArea: 91,
                openSurfaceArea: 39,
                partType: 'NEUE_ASM',
                owner: {
                  ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                  createdBy: {
                    ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                  },
                  createdAt: '2025-04-11T09:24:58.626Z',
                  schemaVersion: '',
                  lifecycleState: 'ACTIVE',
                  name: 'person3',
                  email: '<EMAIL>',
                  phone: '***********',
                  icon: '',
                  account: {
                    ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                  },
                  person: {
                    ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                  },
                },
                status: 'INWORK',
                lockState: 'UNLOCKED',
                lockedAt: '1970-01-01T00:00:00.000Z',
                version: '2',
                latestVersion: true,
                checkState: 'CHECKED_IN',
                checkedOutAt: '1970-01-01T00:00:00.000Z',
                master: {
                  ncid: 'ncid1.plt0neuecadpartmaster.local..1232adb9-b759-481e-b784-0e9146ea7eec',
                  createdAt: '2025-07-08T00:42:31.690Z',
                  modifiedAt: '2025-07-08T00:42:31.690Z',
                  schemaVersion: '1.0.0',
                },
                revision: 'A',
                latestRevision: true,
                cadboms: [
                  {
                    ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
                    createdAt: '2025-07-08T00:42:31.693Z',
                    modifiedAt: '2025-07-08T00:45:08.483Z',
                    schemaVersion: '1.0.0',
                    source: {
                      ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                    },
                    target: {
                      ncid: 'ncid1.plt0neuecadpart.local..051931a1-33e2-4901-a4cc-52c4d5956b87',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                      partNo: 'P032163',
                      partName: '装配A的子件C',
                      partDescription: '这是一个螺母2update',
                      mass: 93,
                      material: 'est irure Duis non velit',
                      gravityCenter: 'deserunt eu',
                      volume: 85,
                      solidSurfaceArea: 91,
                      openSurfaceArea: 39,
                      partType: 'NEUE_PRT',
                      owner: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      status: 'INWORK',
                      lockState: 'UNLOCKED',
                      lockedAt: '1970-01-01T00:00:00.000Z',
                      version: '2',
                      latestVersion: true,
                      checkState: 'CHECKED_IN',
                      checkedOutAt: '1970-01-01T00:00:00.000Z',
                      master: {
                        ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
                        createdAt: '2025-07-08T00:42:31.690Z',
                        modifiedAt: '2025-07-08T00:42:31.690Z',
                        schemaVersion: '1.0.0',
                      },
                      revision: 'A',
                      latestRevision: true,
                      idi3DLightModelId:
                        'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
                      convertStatus: 'SUCCESS',
                      cadboms: [],
                    },
                    instanceName: 'A_C_update',
                    transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
                    quantity: 1,
                    suppressed: false,
                    configuration: 'CONFIG1',
                    createdBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    modifiedBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    partNo: 'P032163',
                    partName: '装配A的子件C',
                    partDescription: '这是一个螺母2update',
                    mass: 93,
                    material: 'est irure Duis non velit',
                    gravityCenter: 'deserunt eu',
                    volume: 85,
                    solidSurfaceArea: 91,
                    openSurfaceArea: 39,
                    partType: 'NEUE_PRT',
                    owner: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    status: 'INWORK',
                    lockState: 'UNLOCKED',
                    lockedAt: '1970-01-01T00:00:00.000Z',
                    version: '2',
                    latestVersion: true,
                    checkState: 'CHECKED_IN',
                    checkedOutAt: '1970-01-01T00:00:00.000Z',
                    master: {
                      ncid: 'ncid1.plt0neuecadpartmaster.local..296164f4-21ad-467c-956a-ed343eb69b2a',
                      createdAt: '2025-07-08T00:42:31.690Z',
                      modifiedAt: '2025-07-08T00:42:31.690Z',
                      schemaVersion: '1.0.0',
                    },
                    revision: 'A',
                    latestRevision: true,
                    idi3DLightModelId:
                      'ncid1.plt0ufrmeta.local..35f40fa9-8502-465d-84c0-6e440949b258',
                    convertStatus: 'SUCCESS',
                    cadboms: [],
                    idPath: '0-0-0',
                  },
                  {
                    ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
                    createdAt: '2025-07-08T00:42:31.693Z',
                    modifiedAt: '2025-07-08T00:45:08.483Z',
                    schemaVersion: '1.0.0',
                    source: {
                      ncid: 'ncid1.plt0neuecadpart.local..e803e763-b234-4b2a-9749-2ec27d65c47c',
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                    },
                    target: {
                      ncid: 'ncid1.plt0neuecadpart.local..f307be0b-5a79-4382-9695-28150c813d53',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      createdAt: '2025-07-08T00:42:31.693Z',
                      modifiedBy: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      modifiedAt: '2025-07-08T00:45:08.483Z',
                      schemaVersion: '1.0.0',
                      partNo: 'P032162',
                      partName: '装配A的子件B',
                      partDescription: '这是一个螺栓1update',
                      mass: 93,
                      material: 'est irure Duis non velit',
                      gravityCenter: 'deserunt eu',
                      volume: 85,
                      solidSurfaceArea: 91,
                      openSurfaceArea: 39,
                      partType: 'NEUE_PRT',
                      owner: {
                        ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                        createdBy: {
                          ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                        },
                        createdAt: '2025-04-11T09:24:58.626Z',
                        schemaVersion: '',
                        lifecycleState: 'ACTIVE',
                        name: 'person3',
                        email: '<EMAIL>',
                        phone: '***********',
                        icon: '',
                        account: {
                          ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                        },
                        person: {
                          ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                        },
                      },
                      status: 'INWORK',
                      lockState: 'UNLOCKED',
                      lockedAt: '1970-01-01T00:00:00.000Z',
                      version: '2',
                      latestVersion: true,
                      checkState: 'CHECKED_IN',
                      checkedOutAt: '1970-01-01T00:00:00.000Z',
                      master: {
                        ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
                        createdAt: '2025-07-08T00:42:31.690Z',
                        modifiedAt: '2025-07-08T00:42:31.690Z',
                        schemaVersion: '1.0.0',
                      },
                      revision: 'A',
                      latestRevision: true,
                      idi3DLightModelId:
                        'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
                      convertStatus: 'SUCCESS',
                      cadboms: [],
                    },
                    instanceName: 'A_B_update',
                    transformationMatrix: '1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1',
                    quantity: 1,
                    suppressed: false,
                    configuration: 'CONFIG1',
                    createdBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    modifiedBy: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    partNo: 'P032162',
                    partName: '装配A的子件B',
                    partDescription: '这是一个螺栓1update',
                    mass: 93,
                    material: 'est irure Duis non velit',
                    gravityCenter: 'deserunt eu',
                    volume: 85,
                    solidSurfaceArea: 91,
                    openSurfaceArea: 39,
                    partType: 'NEUE_PRT',
                    owner: {
                      ncid: 'ncid1.plt0user.local..e868f35a-8354-453b-a126-6b062cf3aaa3',
                      createdBy: {
                        ncid: 'ncid1.plt0user.local..0003268c-3201-4cf1-9664-801e6856d2bf',
                      },
                      createdAt: '2025-04-11T09:24:58.626Z',
                      schemaVersion: '',
                      lifecycleState: 'ACTIVE',
                      name: 'person3',
                      email: '<EMAIL>',
                      phone: '***********',
                      icon: '',
                      account: {
                        ncid: 'ncid1.plt0account.local..fcb58ab0-2c6c-4732-b641-2f7ad9507116',
                      },
                      person: {
                        ncid: 'ncid1.plt0person.local..2d6a3074-a521-47c4-b7b3-5c963535aaa3',
                      },
                    },
                    status: 'INWORK',
                    lockState: 'UNLOCKED',
                    lockedAt: '1970-01-01T00:00:00.000Z',
                    version: '2',
                    latestVersion: true,
                    checkState: 'CHECKED_IN',
                    checkedOutAt: '1970-01-01T00:00:00.000Z',
                    master: {
                      ncid: 'ncid1.plt0neuecadpartmaster.local..87c0007f-7e18-491c-aa05-675b4263e68c',
                      createdAt: '2025-07-08T00:42:31.690Z',
                      modifiedAt: '2025-07-08T00:42:31.690Z',
                      schemaVersion: '1.0.0',
                    },
                    revision: 'A',
                    latestRevision: true,
                    idi3DLightModelId:
                      'ncid1.plt0ufrmeta.local..bea160ce-8234-402c-b5cb-9c98a1195bad',
                    convertStatus: 'SUCCESS',
                    cadboms: [],
                    idPath: '0-0-1',
                  },
                ],
                idPath: '0',
              },
            ],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'bom-panel_60spo02i5g',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
        {
          id: 'bom-panel_60spo02i5g', // 组件唯一身份
          type: 'bom-panel',
          name: '搜索表单',
          props: {},
          events: [],
          elements: [],
        },
      ],
    },
  ],
}

export const basicFormConfig = {
  config: {
    configProvider: {
      // locale: "locale === 'en' ? enUS : zhCN",
      // size: 'large',
    },
    // 页面的组件属性配置
    // events: [],
    // api: {},
  },
  events: {},
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'basic-form_60spo02i6g', // 组件唯一身份
      type: 'widget-container',
      name: '基础表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'basic-form_60spo02i5g', // 组件唯一身份
          type: 'wg-basic-form',
          name: '基础表单',
          props: {
            formItems: [
              {
                prop: 'ncid',
                fieldName: '编码',
                fieldType: 'text',
                required: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'name',
                fieldName: '名称1',
                fieldType: 'text',
                required: true,
                minLength: 6,
                maxLength: 18,
                showWordLimit: true,
                ruleId: 'RULE_USERNAME',
              },
              {
                prop: 'version',
                fieldName: '版本',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'version1',
                fieldName: '版次',
                fieldType: 'text',
                disabled: true,
                required: true,
                ruleId: 'RULE_EMAIL',
              },
              {
                prop: 'age',
                fieldName: '年龄',
                fieldType: 'number',
                ruleId: 'RULE_AGE',
              },
              {
                prop: 'partType',
                fieldName: '类型',
                fieldType: 'businessType',
                options: [
                  { label: '北京', value: 'beijing' },
                  { label: '上海', value: 'shanghai' },
                  { label: '广州', value: 'guangzhou' },
                ],
              },
              {
                prop: 'city',
                fieldName: '城市',
                fieldType: 'select',
                filterable: true,
                options: [
                  { label: '北京', value: 'beijing' },
                  { label: '上海', value: 'shanghai' },
                  { label: '广州', value: 'guangzhou' },
                ],
              },
              {
                prop: 'gender',
                fieldName: '性别',
                fieldType: 'radio',
                options: [
                  { label: '男', value: 'male' },
                  { label: '女', value: 'female' },
                ],
              },
              {
                prop: 'hobbies',
                fieldName: '爱好',
                fieldType: 'checkbox',
                options: [
                  { label: '阅读', value: 'reading' },
                  { label: '运动', value: 'sports' },
                ],
              },
              {
                prop: 'birthday',
                fieldName: '生日',
                fieldType: 'date',
                dateFormat: 'YYYY-MM-DD',
              },
              {
                prop: 'agreement',
                fieldName: '同意协议',
                fieldType: 'switch',
              },
              {
                prop: 'remark',
                fieldName: '备注',
                fieldType: 'textarea',
                showWordLimit: true,
                maxLength: 200,
              },
            ],
            data: [],
          },
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'open',
                    actionName: '打开drawer',
                    target: 'bom-panel_60spo02i5g',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
          elements: [],
        },
      ],
    },
  ],
}
// dialog-form widget 配置
export const dialogFormConfig = {
  config: {
    //弹窗名
    title: '新建设计零部件demo',
    width: '520px',
    height: '578px',
    // label宽度，统一设置
    labelWidth: '100',
    // 一个form-item的宽度
    formWidth: '420',
    layout: 'horizontal',
    // 按钮配置
    submitText: '保存',
    resetText: '重置2',
    cancelText: '取消',
    // 按钮的显示配置
    showLabel: true,
    showSubmit: true,
    showReset: false,
    showCancel: true,
    // 按钮的对齐方式
    buttonAlign: 'right',
  },
}
export const dialogFormItems = [
  {
    prop: 'ncid',
    fieldName: '编码',
    fieldType: 'text',
    required: true,
    minLength: 1,
    maxLength: 32,
    ruleId: 'RULE_101',
  },
  {
    prop: 'schemaVersion',
    fieldName: '版本',
    fieldType: 'text',
    required: true,
    minLength: 6,
    maxLength: 18,
    showWordLimit: true,
    ruleId: 'RULE_101',
  },
  {
    prop: 'partType',
    fieldName: '类型',
    fieldType: 'businessType',
    required: true,
    options: [
      {
        label: 'CREATING',
        value: 'CREATING',
      },
      {
        label: 'ACTIVE',
        value: 'ACTIVE',
      },
      {
        label: 'INACTIVE',
        value: 'INACTIVE',
      },
      {
        label: 'NEEDS_ATTENTION',
        value: 'NEEDS_ATTENTION',
      },
      {
        label: 'UPDATING',
        value: 'UPDATING',
      },
      {
        label: 'DELETING',
        value: 'DELETING',
      },
      {
        label: 'DELETED',
        value: 'DELETED',
      },
    ],
  },
  {
    prop: 'version',
    fieldName: '版本',
    fieldType: 'text',
    disabled: true,
    required: true,
  },
  {
    prop: 'version1',
    fieldName: '版次',
    fieldType: 'text',
    disabled: true,
    required: true,
  },
  {
    prop: 'remark',
    fieldName: '备注',
    fieldType: 'textarea',
    showWordLimit: true,
    maxLength: 255,
  },
]
// 创建part
export const modalFormConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'odata-card',
      type: 'card',
      name: 'OData表格123',
      elements: [
        {
          id: 'TabPane_button_3gx13gh2ht123',
          type: 'button',
          name: 'button',
          props: {
            type: 'primary',
          },
          elements: [],
          events: [
            {
              nickName: '打开DialogForm',
              eventName: 'onClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '561322123',
                  type: 'normal',
                  title: '节点561322123',
                  content: '弹框关闭回调',
                  config: {
                    actionType: 'open',
                    actionName: '',
                    target: 'DialogForm_er4mn6jbtk123456',
                  },
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],

          slots: {
            default: '创建part',
          },
        },
        {
          id: 'DialogForm_er4mn6jbtk123456',
          type: 'wg-dialog-form',
          name: '弹框',
          props: {
            config: {
              //弹窗名
              title: '新建设计零部件',
              width: '520px',
              height: '578px',
              // label宽度，统一设置
              labelWidth: '100',
              // 一个form-item的宽度
              formWidth: '420',
              layout: 'horizontal',
              // 按钮配置
              submitText: '保存',
              resetText: '重置2',
              cancelText: '取消',
              // 按钮的显示配置
              showLabel: true,
              showSubmit: true,
              showReset: false,
              showCancel: true,
              // 按钮的对齐方式
              buttonAlign: 'right',
            },
            formItems: [
              {
                prop: 'name',
                fieldName: '名称',
                fieldType: 'text',
                required: true,
                minLength: 6,
                maxLength: 18,
                showWordLimit: true,
                ruleId: 'RULE_101',
              },
              {
                prop: 'partType',
                fieldName: '类型',
                fieldType: 'businessType',
                required: true,
              },
              {
                prop: 'revisionCode',
                fieldName: '版本',
                fieldType: 'text',
                disabled: true,
                required: true,
              },
              {
                prop: 'versionNumber',
                fieldName: '版次',
                fieldType: 'text',
                disabled: true,
                required: true,
              },
              {
                prop: 'description',
                fieldName: '备注',
                fieldType: 'textarea',
                showWordLimit: true,
                maxLength: 255,
              },
            ],
          },
          elements: [],
          events: [],
        },
      ],
    },
  ],
  apis: {
    submit: {
      url: '/modeling/202512/OdataService/CadParts',
      method: 'post',
    },
    request: {
      url: '/modeling/202512/OdataService/NeueCadParts',
      method: 'get',
      query: {
        // expand: 'Category',
      },
    },
    businessTypeRequest: {
      url: '/modeling/202512/OdataService/EntityTypes',
      method: 'get',
      // query: {
      //   filter: "baseType/name eq 'CadPart'",
      // },
    },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}
// 修订part
export const editPartConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'odata-card',
      type: 'card',
      name: 'OData表格',
      elements: [
        {
          id: 'TabPane_button_3gx13gh2ht123',
          type: 'button',
          name: 'button',
          props: {
            type: 'primary',
          },
          elements: [],
          events: [
            {
              nickName: '打开DialogForm',
              eventName: 'onClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '561322123',
                  type: 'normal',
                  title: '节点561322123',
                  content: '弹框关闭回调',
                  config: {
                    actionType: 'open',
                    actionName: '',
                    target: 'DialogForm_er4mn6jbtk123456',
                  },
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],

          slots: {
            default: '修订part',
          },
        },
        {
          id: 'DialogForm_er4mn6jbtk123456',
          type: 'wg-dialog-form',
          name: '弹框',
          props: {
            config: {
              //弹窗名
              title: '修订设计零部件',
              width: '520px',
              height: '578px',
              // label宽度，统一设置
              labelWidth: '100',
              // 一个form-item的宽度
              formWidth: '420',
              layout: 'horizontal',
              // 按钮配置
              submitText: '确定',
              cancelText: '取消',
              // 按钮的显示配置
              showSubmit: true,
              showCancel: true,
              showReset: false,
              // 按钮的对齐方式
              buttonAlign: 'right',
            },
            formItems: [
              {
                prop: 'ncid',
                fieldName: '编码',
                fieldType: 'text',
                required: true,
                disabled: true,
                minLength: 1,
                maxLength: 32,
                ruleId: 'RULE_101',
              },
              {
                prop: 'name',
                fieldName: '名称',
                fieldType: 'text',
                required: true,
                minLength: 6,
                maxLength: 18,
                showWordLimit: true,
                ruleId: 'RULE_101',
              },
              {
                prop: 'partType',
                fieldName: '类型',
                fieldType: 'businessType',
                required: true,
                disabled: true,
              },
              {
                prop: 'schemaVersion',
                fieldName: '版本',
                fieldType: 'text',
                disabled: true,
                required: true,
              },
              {
                prop: 'versionNumber',
                fieldName: '版次',
                fieldType: 'text',
                disabled: true,
                required: true,
              },
              {
                prop: 'mass',
                fieldName: '质量',
                fieldType: 'text',
              },
              {
                prop: 'meterial',
                fieldName: '材料',
                fieldType: 'text',
              },
              {
                prop: 'gravityCenter',
                fieldName: '重心',
                fieldType: 'text',
              },
              {
                prop: 'description',
                fieldName: '描述',
                fieldType: 'textarea',
                showWordLimit: true,
                maxLength: 255,
              },
            ],
          },
          elements: [],
          events: [],
        },
      ],
    },
  ],
  apis: {
    // submit: {
    //   url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    //   method: 'post',
    // },
    request: {
      // 调用apiRef 使用query: {ncid: ''}
      url: '/modeling/202512/OdataService/NeueCadParts({{ncid}})',
      method: 'get',
    },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}

export const odataMaterialConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  apis: {
    submit: {
      url: '/cdp/202512/OdataService/CadFile',
      method: 'post',
    },
    request: {
      url: '/modeling/202512/OdataService/NeueCadParts',
      method: 'get',
      query: {
        expand: 'thumbnail,version($expand=revision)',
      },
    },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
    apiJson: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'test1',
      type: 'card',
      name: 'card1',
      props: {
        header: '1111',
      },
    },
    {
      id: 'test2',
      type: 'tag',
      name: 'tag1',
      value: '333',
      slots: {
        default: '打开对话框',
      },
    },
    {
      id: 'test3',
      type: 'avatar',
      name: 'avata1',
      props: {
        text: '好',
        name: '你好',
      },
    },
    {
      id: 'test4',
      type: 'image',
      name: 'image1',
      props: {
        src: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
        style: 'width:40px',
      },
    },
    {
      id: 'odata-card',
      type: 'card',
      name: 'OData表格',
      elements: [
        {
          id: 'odata-table',
          type: 'pro-table',
          name: 'OData表格',
          props: {
            // entityType: 'NeueCadAsm',
            columns: [
              { type: 'selection', with: 55 },
              {
                prop: 'a',
                label: 'ncid',
                valueType: 'avatar',
              },
              {
                prop: 'b',
                label: 'code',
                valueType: 'image',
              },
              {
                prop: 'c',
                label: 'schemaVersion',
                valueType: 'tag',
              },
              {
                prop: 'd',
                label: 'lifecycleState',
                // fieldProps: {
                //   multiple: true,
                // },
              },
              {
                prop: 'e',
                label: 'lifecycleNote',
              },
              {
                prop: 'f',
                label: 'volume',
                // fieldProps: {
                //   controlsPosition: 'right',
                // },
              },
              {
                prop: 'operation',
                label: '操作',
                actions: [
                  {
                    text: '新增',
                    // type: 'el-button',
                    props: {
                      type: 'primary',
                    },
                    config: {
                      actionType: 'open',
                      target: 'DialogForm_er4mn6jbtk',
                    },
                  },
                  {
                    text: '编辑',
                    // type: 'el-button',
                    props: {
                      type: 'primary',
                    },
                    config: {
                      actionType: 'open',
                      target: 'DialogForm_er4mn6jbtk',
                    },
                  },
                  {
                    icon: 'Edit',
                    // type: 'el-icon',
                    config: {
                      actionType: 'open',
                      actionName: '打开drawer',
                      target: 'Drawer_er4mn6jbtk',
                    },
                  },
                ],
              },
            ],
            toolbar: {
              header: [
                {
                  text: '操作一新增',
                  props: {
                    type: 'primary',
                  },
                  config: {
                    actionType: 'open',
                    target: 'DialogForm_er4mn6jbtk',
                  },
                },
                {
                  text: '操作二打开侧边栏',
                  props: {
                    type: 'success',
                  },
                  config: {
                    actionType: 'open',
                    target: 'Drawer_er4mn6jbtk',
                  },
                },
                {
                  text: '操作三',
                  props: {
                    type: 'info',
                  },
                  config: {
                    actionType: 'open',
                    target: 'Drawer_er4mn6jbtk',
                  },
                },
              ],
              footer: [
                {
                  text: '操作一',
                  props: {
                    type: 'primary',
                  },
                  config: {},
                },
                {
                  text: '操作二',
                  props: {
                    type: 'success',
                  },
                  config: {
                    actionType: 'open',
                    target: 'Drawer_er4mn6jbtk',
                  },
                },
                {
                  text: '操作三',
                  props: {
                    type: 'info',
                  },
                  config: {
                    actionType: 'open',
                    target: 'Drawer_er4mn6jbtk',
                  },
                },
              ],
            },
            data: [
              {
                a: { name: '223' },
                b: {
                  src: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
                  style: 'width:60px',
                },
                c: { value: '我是标签', size: 'large' },
                d: '222',
                e: 'xxx',
                f: '2222',
              },
              {
                a: '2',
                b: '222',
                c: { value: '我是标签', size: 'large', type: 'warning' },
                d: '222',
                e: 'xxx',
                f: '2222',
              },
            ],
          },
          events: [],
          api: {},
        },
      ],
    },
    {
      id: 'Drawer_er4mn6jbtk',
      type: 'drawer',
      name: '弹框',
      props: {
        modelValue: false,
        size: '1061px',
      },
      elements: [
        {
          id: 'Tabs_3gx13gh2ht',
          type: 'tabs',
          name: 'tabs',
          props: {},
          elements: [
            {
              id: 'TabPane_3gx13gh2ht',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '属性信息',
              },
              elements: [
                {
                  id: 'TabPane_button_3gx13gh2ht',
                  type: 'button',
                  name: 'button',
                  props: {
                    label: '属性信息',
                  },
                  elements: [],
                  slots: {
                    default: '打开对话框',
                  },
                },
              ],
            },
            {
              id: 'TabPane_usage',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
            {
              id: 'TabPane_history',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '历史记录',
              },
              elements: [],
            },
            {
              id: 'TabPane_useBy',
              type: 'tab-pane',
              name: 'tab-pane',
              props: {
                label: '被用于',
              },
              elements: [],
            },
          ],
        },
      ],
      events: [
        {
          nickName: '弹框关闭',
          eventName: 'onClosed',
          actions: [
            {
              id: 'start',
              type: 'start',
              title: '开始',
            },
            {
              id: '561322123',
              type: 'normal',
              title: '节点561322123',
              content: '弹框关闭回调',
              config: {
                actionType: 'refresh',
                actionName: '刷新表格',
                target: 'table_60spo02i5g',
              },
            },
            {
              id: 'end',
              type: 'end',
              title: '结束',
            },
          ],
        },
      ],
      slots: {
        header: [
          {
            id: 'button_fb19o54fjh',
            type: 'div',
            name: '文本框',
            props: {
              style: 'font-size:30px;font-weight:600;color:#0F172A',
              innerHTML: 'UPX10007248',
            },
          },
        ],
        footer: 'footer',
      },
    },
    {
      id: 'DialogForm_er4mn6jbtk',
      type: 'wg-dialog-form',
      name: '弹框',
      props: {
        config: dialogFormConfig.config,
        formItems: dialogFormItems,
      },
      elements: [],
      events: [
        {
          nickName: '行点击事件',
          eventName: 'onRefresh',
          actions: [
            {
              id: 'start',
              type: 'start',
              title: '开始',
            },
            {
              id: '56132221',
              type: 'normal',
              title: '节点7690',
              content: '打开弹框',
              config: {
                actionType: 'refresh',
                actionName: '打开drawer',
                target: 'odata-table',
              },
              children: [],
            },
            {
              id: 'end',
              type: 'end',
              title: '结束',
            },
          ],
        },
      ],
      // apis: {},
    },
  ],
}

export const panelFormItems = [
  {
    name: '基本属性1',
    key: 'base',
    viewImg: 'https://example.com/image1.png',
    formItems: [
      {
        prop: 'ncid',
        fieldName: '编码',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
      },
      {
        prop: 'name',
        fieldName: '名称',
        fieldType: 'text',
        disabled: false,
        minLength: 6,
        maxLength: 18,
        showWordLimit: true,
        ruleId: 'RULE_101',
      },
      {
        prop: 'revision',
        fieldName: '版本',
        fieldType: 'text',
        disabled: true,
        ruleId: 'RULE_EMAIL',
      },
      {
        prop: 'version',
        fieldName: '版次',
        fieldType: 'text',
        disabled: true,
        ruleId: 'RULE_EMAIL',
      },
      {
        label: ' 责任人 ',
        prop: 'responsiblePerson',
        fieldName: ' 责任人 ',
        fieldType: 'text',
        disabled: false,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入责任人 ',
        value: ' 张无珊 ',
      },
      {
        label: ' 状态 ',
        prop: 'status',
        fieldName: ' 状态 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入状态 ',
        value: ' 工作中 ',
      },
      {
        label: ' 锁定人 ',
        prop: 'lockPerson',
        fieldName: ' 锁定人 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入锁定人 ',
        value: ' 张无珊 ',
      },
      {
        label: ' 锁定时间 ',
        prop: 'lockTime',
        fieldName: ' 锁定时间 ',
        fieldType: 'text',
        disabled: true,
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: ' 请输入锁定时间 ',
        value: '2025-07-14 09:55:10',
      },
    ],
  },
  {
    name: '自身属性',
    key: 'design',
    formItems: [
      {
        label: '实体曲面面积',
        prop: 'entitySurfaceArea',
        fieldName: '实体曲面面积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入实体曲面面积',
        disabled: true,
        value: '10663.5',
      },
      {
        label: '开放曲面面积',
        prop: 'openSurfaceArea',
        fieldName: '开放曲面面积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入开放曲面面积',
        disabled: true,
        value: '0',
      },
      {
        label: '体积',
        prop: 'volume',
        fieldName: '体积',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入体积',
        disabled: true,
        value: '84464.4',
      },
      {
        label: '质量',
        prop: 'mass',
        fieldName: '质量',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入质量',
        disabled: true,
        value: '0.660512',
      },
      {
        label: '材料',
        prop: 'material',
        fieldName: '材料',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入材料',
        disabled: true,
        value: 'SHEETMETAL DEFAULT .125IN K=-33',
      },
      {
        label: '重心',
        prop: 'centerOfGravity',
        fieldName: '重心',
        fieldType: 'text',
        minLength: 1,
        maxLength: 32,
        ruleId: 'RULE_101',
        placeholder: '请输入重心',
        disabled: true,
        value: '52.3446, 6.85254, 25',
      },
    ],
  },
]
// part 零部件属性详情
export const panelFormConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'odata-card222',
      type: 'card',
      name: '标签页',
      elements: [
        {
          id: 'PanelForm_er4mn6jbtk1234562',
          type: 'wg-panel-form',
          name: '面板表单',
          props: {
            config: {
              span: 12, // 每行span，24 为每行1列，12为每行2列，8为每行3列
              gutter: 12, // 列间距
              labelWidth: '100px', // label宽度
              title: '属性信息', // 表单标题
            },
            formItems: [
              {
                name: '基本属性',
                key: 'base',
                // 左侧的缩略图
                viewImg: 'https://example.com/image1.png',
                formItems: [
                  {
                    prop: 'ncid',
                    fieldName: '编码',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                  },
                  {
                    prop: 'name',
                    fieldName: '名称',
                    fieldType: 'text',
                    disabled: false,
                    minLength: 6,
                    maxLength: 18,
                    showWordLimit: true,
                    ruleId: 'RULE_101',
                  },
                  {
                    prop: 'schemaVersion',
                    fieldName: '版本',
                    fieldType: 'text',
                    disabled: true,
                    ruleId: 'RULE_EMAIL',
                  },
                  {
                    prop: 'versionNumber',
                    fieldName: '版次',
                    fieldType: 'text',
                    disabled: true,
                    ruleId: 'RULE_EMAIL',
                  },
                  {
                    prop: 'owner',
                    fieldName: ' 责任人 ',
                    fieldType: 'text',
                    disabled: false,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入 ',
                  },
                  {
                    prop: 'lifecycleState',
                    fieldName: ' 状态 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入状态 ',
                  },
                  {
                    prop: 'lockedBy',
                    fieldName: ' 锁定人 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入锁定人 ',
                    value: ' 张无珊 ',
                  },
                  {
                    prop: 'lockedAt',
                    fieldName: ' 锁定时间 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入',
                  },
                ],
              },
              {
                name: '自身属性',
                key: 'design',
                formItems: [
                  {
                    prop: 'solidSurfaceArea',
                    fieldName: '实体曲面面积',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入',
                    disabled: true,
                  },
                  {
                    prop: 'openSurfaceArea',
                    fieldName: '开放曲面面积',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入开放曲面面积',
                    disabled: true,
                  },
                  {
                    prop: 'volume',
                    fieldName: '体积',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入体积',
                    disabled: true,
                    value: '84464.4',
                  },
                  {
                    label: '质量',
                    prop: 'mass',
                    fieldName: '质量',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入质量',
                    disabled: true,
                    value: '0.660512',
                  },
                  {
                    prop: 'material',
                    fieldName: '材料',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入材料',
                    disabled: true,
                  },
                  {
                    prop: 'centerOfGravity',
                    fieldName: '重心',
                    fieldType: 'text',
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入重心',
                    disabled: true,
                  },
                ],
              },
            ],
          },
          elements: [],
          events: [],
        },
      ],
    },
  ],
  apis: {
    // submit: {
    //   url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    //   method: 'post',
    // },
    // 查询零部件属性信息
    request: {
      url: '/modeling/202512/OdataService/NeueCadParts({{ncid}})',
      method: 'get',
    },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}
// 工程图详情
export const edDetailConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'odata-card222',
      type: 'card',
      name: '标签页',
      props: {
        config: {
          showSubmit: false,
        },
      },
      elements: [
        {
          id: 'PanelForm_er4mn6jbtk1234562',
          type: 'wg-panel-form',
          name: '面板表单',
          props: {
            config: {
              layout: 'horizontal', // horizontal 横向 vertical 纵向
              span: 12, // 每行span
              gutter: 12, // 列间距
              labelWidth: '100px', // label宽度
              title: '属性信息', // 表单标题
            },
            formItems: [
              {
                name: '基本属性',
                key: 'base',
                viewImg: 'https://example.com/image1.png',
                formItems: [
                  {
                    prop: 'ncid',
                    fieldName: '编码',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                  },
                  {
                    prop: 'name',
                    fieldName: '名称',
                    fieldType: 'text',
                    disabled: false,
                    minLength: 6,
                    maxLength: 18,
                    showWordLimit: true,
                    ruleId: 'RULE_101',
                  },
                  {
                    prop: 'version.revision.revisionCode',
                    fieldName: '版本',
                    fieldType: 'text',
                    disabled: true,
                    ruleId: 'RULE_EMAIL',
                  },
                  {
                    prop: 'version.versionNumber',
                    fieldName: '版次',
                    fieldType: 'text',
                    disabled: true,
                    ruleId: 'RULE_EMAIL',
                  },
                  {
                    label: ' 责任人 ',
                    prop: 'responsiblePerson',
                    fieldName: ' 责任人 ',
                    fieldType: 'text',
                    disabled: false,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入责任人 ',
                  },
                  {
                    label: ' 状态 ',
                    prop: 'status',
                    fieldName: ' 状态 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入状态 ',
                    value: ' 工作中 ',
                  },
                  {
                    label: ' 锁定人 ',
                    prop: 'lockPerson',
                    fieldName: ' 锁定人 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入锁定人 ',
                  },
                  {
                    label: ' 锁定时间 ',
                    prop: 'lockTime',
                    fieldName: ' 锁定时间 ',
                    fieldType: 'text',
                    disabled: true,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: ' 请输入锁定时间 ',
                  },
                  {
                    label: '描述',
                    prop: 'description',
                    fieldName: '描述',
                    fieldType: 'textarea',
                    disabled: false,
                    minLength: 1,
                    maxLength: 32,
                    ruleId: 'RULE_101',
                    placeholder: '请输入描述',
                  },
                ],
              },
              {
                name: '自身属性',
                key: 'design',
                formItems: [],
              },
            ],
          },
          elements: [],
          events: [],
        },
      ],
    },
  ],
  apis: {
    submit: {
      url: 'https://services.odata.org/V4/TripPinServiceRW/People',
      method: 'post',
    },
    // 2D
    panelFormEditRequest2D: {
      url: '/modeling/202512/OdataService/NeueTwoDimensionDrawings({{ncid}})',
      method: 'get',
    },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}
// 关系属性编辑
export const editRelationConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'odata-card',
      type: 'card',
      name: 'OData表格',
      elements: [
        {
          id: 'TabPane_button_3gx13gh2ht123',
          type: 'button',
          name: 'button',
          props: {
            type: 'primary',
          },
          elements: [],
          events: [
            {
              nickName: '打开DialogForm',
              eventName: 'onClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '561322123',
                  type: 'normal',
                  title: '节点561322123',
                  content: '弹框关闭回调',
                  config: {
                    actionType: 'open',
                    actionName: '',
                    target: 'DialogForm_er4mn6jbtk123456',
                  },
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],

          slots: {
            default: '关系属性编辑',
          },
        },
        {
          id: 'DialogForm_er4mn6jbtk123456',
          type: 'wg-dialog-form',
          name: '弹框',
          props: {
            config: {
              //弹窗名
              title: '关系属性编辑',
              width: '520px',
              height: '378px',
              // label宽度，统一设置
              labelWidth: '100',
              // 一个form-item的宽度
              formWidth: '420',
              layout: 'horizontal',
              // 按钮配置
              submitText: '保存',
              cancelText: '取消',
              showLabel: true,
              showSubmit: true,
              showReset: false,
              showCancel: true,
              // 按钮的对齐方式
              buttonAlign: 'right',
            },
            formItems: [
              {
                prop: 'number',
                fieldName: '数量',
                fieldType: 'number',
                required: true,
                minLength: 1,
                maxLength: 32,
                ruleId: 'RULE_101',
              },
            ],
          },
          elements: [],
          events: [],
        },
      ],
    },
  ],
  apis: {
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}

// 详情页
export const basicDetailConfig = {
  config: {
    apiBaseConfig: {
      baseUrl: '',
    },
  },
  elements: [
    {
      id: 'basic-detail',
      type: 'wg-basic-detail',
      name: 'OData表格',
      props: {
        config: {
          title: '零部件详情',
        },
        tabItems: [
          {
            name: '属性信息',
            key: 'base',
            elements: [
              {
                id: 'PanelForm_er4mn6jbtk1234562',
                type: 'wg-panel-form',
                name: '面板表单',
                props: {
                  config: {
                    layout: 'horizontal', // horizontal 横向 vertical 纵向
                    span: 12, // 每行span
                    gutter: 12, // 列间距
                    labelWidth: '100px', // label宽度
                    title: '属性信息', // 表单标题
                  },
                  formItems: [
                    {
                      prop: 'name',
                      fieldName: '零部件名称',
                      fieldType: 'text',
                      required: true,
                      minLength: 1,
                      maxLength: 32,
                      ruleId: 'RULE_101',
                      placeholder: '请输入零部件名称',
                      disabled: true,
                      value: 'SHEETMETAL DEFAULT .125IN K=-33',
                    },
                  ],
                },
                elements: [],
                events: [],
              },
            ],
          },
          {
            name: '3D文件',
            key: '3D',
          },
          {
            name: '2D工程图',
            key: '2D',
          },
          {
            name: '版本版次',
            key: 'version',
          },
          {
            name: 'CADBOM',
            key: 'bom',
          },
          {
            name: '历史记录',
            key: 'history',
          },
        ],
      },
    },
  ],
  apis: {
    // submit: {
    //   url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    //   method: 'post',
    // },
    // request: {
    //   url: '/modeling/202512/OdataService/NeueCadParts',
    //   method: 'get',
    //   query: {
    //     // expand: 'Category',
    //   },
    // },
    metadata: {
      url: '/modeling/202512/OdataService/$metadata',
      method: 'get',
    },
  },
}
