<template>
  <el-table
    v-bind="{ ...tableProps, data: dataSource }"
    ref="table"
    v-loading="loading"
    @sort-change="handleSortChange"
  >
    <ne-table-column
      v-for="column in columns"
      v-bind="column"
      :key="column.prop || column.label"
    />
    <slot />
  </el-table>
</template>

<script lang="ts" setup>
import { computed, defineExpose, ref, watch } from 'vue'
import { neTableProps } from './table'
import NeTableColumn from './table-column/index.vue'
import type { NeTableColumnProps } from './table-column/type'

defineOptions({
  name: 'NeTable',
  inheritAttrs: false,
})
const props = defineProps(neTableProps)
console.log(props)
const table = ref<any>()
// 新增：自动请求逻辑
const dataSource = ref<any[]>(props.data)
const expand = ref(true)
const columns = computed(() => {
  return props.columns.map((column: NeTableColumnProps) => {
    return {
      ...column,
    }
  })
})
const tableProps = computed(() => {
  const { bordered } = props
  return { bordered }
})
const toggleExpandAll = (collapse: boolean = expand.value) => {
  expand.value = !collapse
  /**
   * Recursively expands/collapses table rows for nodes and their children.
   * @param {Array} nodes - Array of node objects to expand/collapse
   * @param {boolean} collapse - Expansion flag (true to expand, false to collapse)
   */
  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      table.value.toggleRowExpansion(node, collapse)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  expandRecursive(dataSource.value)
}

defineExpose({
  toggleExpandAll,
})

watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)
const emit = defineEmits(['sort-change'])
// 搜索
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  emit('sort-change', data)
}
</script>
