<template>
  <el-table
    v-bind="tableProps"
    ref="table"
    v-loading="loading"
    style="width: 100%"
    :data="dataSource"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
  >
    <template v-if="selectionColumn">
      <el-table-column v-bind="selectionColumn" />
    </template>
    <ne-table-column
      v-for="column in visibleColumns"
      v-bind="column"
      :key="column.prop || column.label"
    />
    <el-table-column
      v-if="operationColumn"
      v-bind="operationColumn"
      label="操作"
      fixed="right"
    >
      <template #default="scope">
        <pro-button
          :key="scope.row.ncid"
          :record="scope"
          :items="operationColumn.actions"
        />
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { computed, ref, useAttrs, watch } from 'vue'
import proButton from '../../pro-button/src/pro-button.vue'
import { neTableProps } from './table'
import NeTableColumn from './table-column/index.vue'

defineOptions({
  name: 'NeTable',
  inheritAttrs: false,
})
const props = defineProps(neTableProps)
const attrs = useAttrs()
const table = ref<any>()
// 新增：自动请求逻辑
const dataSource = ref<any[]>(props.data || [])
const expand = ref(true)
const loading = ref(props.loading)

const selectionColumn = computed(() => {
  const selection = props.columns.find((column) => column.type === 'selection')
  return selection
})
const visibleColumns = computed(() => {
  return props.columns
    ?.filter(
      (column) =>
        column.hideInTable !== true &&
        column.prop !== 'operation' &&
        column.type !== 'selection'
    )
    .map((column) => ({
      ...column,
    }))
})
const operationColumn = computed(() => {
  const operation = props.columns.find((column) => column.prop === 'operation')
  return operation
})
watch(
  () => props.loading,
  (val) => {
    loading.value = val
  }
)
const tableProps = computed(() => {
  const { bordered } = props
  return { ...attrs, bordered }
})
const toggleExpandAll = (collapse: boolean = expand.value) => {
  expand.value = !collapse
  /**
   * Recursively expands/collapses table rows for nodes and their children.
   * @param {Array} nodes - Array of node objects to expand/collapse
   * @param {boolean} collapse - Expansion flag (true to expand, false to collapse)
   */
  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      table.value.toggleRowExpansion(node, collapse)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  expandRecursive(dataSource.value)
}

defineExpose({
  toggleExpandAll,
})

watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)
const emit = defineEmits(['sort-change', 'selection-change', 'row-click'])
// 搜索
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  emit('sort-change', data)
}
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}
const handleRowClick = (selection: any[]) => {
  emit('row-click', selection)
}
</script>
