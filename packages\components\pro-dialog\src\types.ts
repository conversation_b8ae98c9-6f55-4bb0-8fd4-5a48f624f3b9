import { ExtractPropTypes, PropType } from 'vue'
import { dialogProps } from 'element-plus'
import { ActionsProps } from '../../pro-button'
import { SchemaElement } from '../../material-render'

export const neProDialogProps = {
  ...dialogProps,
  trigger: {
    type: Object as PropType<ActionsProps>,
    required: true,
  },
  elements: {
    type: Array as PropType<SchemaElement[]>,
    default: () => [],
  },
}
export type NeProDialogProps = ExtractPropTypes<typeof neProDialogProps>
