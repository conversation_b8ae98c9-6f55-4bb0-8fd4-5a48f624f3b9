<template>
  <div
    :class="[
      'ne-header-title',
      `ne-header-title--${size}`,
      `ne-header-title--${align}`,
      {
        'ne-header-title--with-divider': divider,
        'ne-header-title--divider-top':
          divider && (dividerPosition === 'top' || dividerPosition === 'both'),
        'ne-header-title--divider-bottom':
          divider &&
          (dividerPosition === 'bottom' || dividerPosition === 'both'),
        'ne-header-title--bold': bold,
      },
    ]"
    @click="handleClick"
  >
    <!-- 顶部分割线 -->
    <div
      v-if="
        divider && (dividerPosition === 'top' || dividerPosition === 'both')
      "
      class="ne-header-title__divider ne-header-title__divider--top"
    />

    <!-- 标题内容区域 -->
    <div class="ne-header-title__content">
      <!-- 返回按钮 -->
      <el-button
        v-if="showBack"
        type="text"
        class="ne-header-title__back"
        @click="handleBack"
      >
        <el-icon class="ne-header-title__back-icon">
          <ArrowLeft />
        </el-icon>
        {{ backText }}
      </el-button>

      <!-- 标题主体 -->
      <div class="ne-header-title__main">
        <!-- 图标 -->
        <el-icon v-if="icon" class="ne-header-title__icon">
          <component :is="icon" v-if="typeof icon === 'object'" />
          <i v-else :class="icon" />
        </el-icon>

        <!-- 标题文本 -->
        <component :is="`h${level}`" class="ne-header-title__title">
          <slot name="title">{{ title }}</slot>
        </component>

        <!-- 副标题 -->
        <div
          v-if="subtitle || $slots.subtitle"
          class="ne-header-title__subtitle"
        >
          <slot name="subtitle">{{ subtitle }}</slot>
        </div>
      </div>

      <!-- 操作区域 -->
      <div v-if="$slots.extra" class="ne-header-title__extra">
        <slot name="extra" />
      </div>
    </div>

    <!-- 底部分割线 -->
    <div
      v-if="
        divider && (dividerPosition === 'bottom' || dividerPosition === 'both')
      "
      class="ne-header-title__divider ne-header-title__divider--bottom"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElButton, ElIcon } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { neHeaderTitleEmits, neHeaderTitleProps } from './header-title'

defineOptions({
  name: 'NeHeaderTitle',
})

const props = defineProps(neHeaderTitleProps)
const emit = defineEmits(neHeaderTitleEmits)

const handleBack = () => {
  emit('back')
}

const handleClick = (evt: MouseEvent) => {
  emit('click', evt)
}
</script>
