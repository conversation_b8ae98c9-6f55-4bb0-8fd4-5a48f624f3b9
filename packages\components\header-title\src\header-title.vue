<template>
  <div class="ne-header-title">
    <!-- 左侧标题 -->
    <div class="ne-header-title__title">
      <slot name="title">{{ title }}</slot>
    </div>

    <!-- 右侧操作区域 -->
    <div v-if="$slots.extra" class="ne-header-title__extra">
      <slot name="extra" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { neHeaderTitleProps } from './header-title'

defineOptions({
  name: 'NeHeaderTitle',
})

defineProps(neHeaderTitleProps)
</script>
