import { computed, defineComponent, ref, watch } from 'vue'
import { ElInput } from 'element-plus'
import { neInputProps } from './types'

const NeInput = defineComponent({
  name: 'NeInput',
  props: neInputProps,
  setup(props, { emit }) {
    const { ...restProps } = props
    const modelValue = ref(props.modelValue)
    const realProps = computed(() => {
      return {
        ...restProps,
        placeholder: props.placeholder || '请输入',
      }
    })
    watch(
      () => props.modelValue,
      (val) => {
        modelValue.value = val
      }
    )
    return () => (
      <ElInput
        {...realProps.value}
        modelValue={modelValue.value}
        onUpdate:modelValue={(val: string) => {
          modelValue.value = val
          emit('update:modelValue', val)
        }}
      />
    )
  },
})

export default NeInput
