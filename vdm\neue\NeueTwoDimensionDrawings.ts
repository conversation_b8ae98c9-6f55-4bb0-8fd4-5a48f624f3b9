/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { NeueTwoDimensionDrawingsApi } from './NeueTwoDimensionDrawingsApi';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "NeueTwoDimensionDrawings" of service "neue".
 */
export class NeueTwoDimensionDrawings<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements NeueTwoDimensionDrawingsType<T>
{
  /**
   * Technical entity name for NeueTwoDimensionDrawings.
   */
  static override _entityName = 'NeueTwoDimensionDrawings';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the NeueTwoDimensionDrawings entity.
   */
  static _keys = ['ncid'];
  /**
   * Submit Description.
   * Maximum length: 1000.
   * @nullable
   */
  declare submitDescription?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Code.
   */
  declare code: DeserializedType<T, 'Edm.String'>;
  /**
   * Name.
   * @nullable
   */
  declare name?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Description.
   * @nullable
   */
  declare description?: DeserializedType<T, 'Edm.String'> | null;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: NeueTwoDimensionDrawingsApi<T>) {
    super(_entityApi);
  }
}

export interface NeueTwoDimensionDrawingsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  submitDescription?: DeserializedType<T, 'Edm.String'> | null;
  code: DeserializedType<T, 'Edm.String'>;
  name?: DeserializedType<T, 'Edm.String'> | null;
  description?: DeserializedType<T, 'Edm.String'> | null;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
