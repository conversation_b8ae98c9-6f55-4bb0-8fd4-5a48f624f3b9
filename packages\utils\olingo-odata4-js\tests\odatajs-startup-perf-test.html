<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 -->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN">
<html>
<!--
Copyright (c) Microsoft Open Technologies, Inc.  All rights reserved.
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation 
files (the "Software"), to deal  in the Software without restriction, including without limitation the rights  to use, copy,
modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the 
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR  IMPLIED, INCLUDING BUT NOT LIMITED TO THE
WARRANTIES OF MERCHANTABILITY,  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, 
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
-->
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <title>datajs startup perf test</title>

    <link rel="stylesheet" href="http://code.jquery.com/qunit/qunit-1.10.0.css" type="text/css" />

    <script type="text/javascript" src="http://cdnjs.cloudflare.com/ajax/libs/json2/20110223/json2.js"></script>
    <script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.min.js"></script>
    <script type="text/javascript" src="http://code.jquery.com/qunit/qunit-1.10.0.js"></script>

    <script type="text/javascript" src="common/TestSynchronizerClient.js"></script>
    <script type="text/javascript">
        window.TestSynchronizer.init(QUnit);
    </script>

    <script type="text/javascript" src="common/djstest.js"></script>
    <script type="text/javascript" src="common/djstest-browser-ext.js"></script>
    <script type="text/javascript" src="common/Instrument.js"></script>

    <script type="text/javascript">
        $(window).load(function () {
            djstest.addTest(function startupTimeAndMemory() {
                var durationLimit = 500;
                var memorySizeDeltaLimit = 5000000;

                var filename = "../_build/lib/odatajs-latest.js";
                var getBrowserMemorySize = Instrument.getBrowserMemorySize;

                $.ajax({
                    url: "../src/" + filename,
                    dataType: "text",
                    success: function (script) {
                        getBrowserMemorySize(function (memorySizeBefore) {
                            var duration = new Date();
                            eval(script);
                            duration = new Date() - duration;
                            getBrowserMemorySize(function (memorySizeAfter) {
                                var memorySizeDelta = memorySizeAfter - memorySizeBefore;
                                djstest.assert(duration < durationLimit, duration + " ms (limit " + durationLimit + " ms)");
                                djstest.assert(memorySizeDelta < memorySizeDeltaLimit,
                                    memorySizeDelta + " bytes (limit " + memorySizeDeltaLimit + " bytes, initial " + memorySizeBefore + " bytes)");

                                djstest.done();
                            });
                        });
                    },
                    error: function () {
                        // See if we are running the dev build
                        $.ajax({
                            url: "../src/odata.js",
                            dataType: "text",
                            success: function () {
                                djstest.pass("Running on dev build, no measurement taken");
                                djstest.done();
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                                djstest.fail("Request failed: " + jqXHR.responseText);
                                djstest.done();
                            }
                        });
                    }
                });
            });
        });
    </script>
</head>
<body>
    <h1 id="qunit-header">datajs startup perf test</h1>
    <h2 id="qunit-banner"></h2>
    <h2 id="qunit-userAgent"></h2>
    <ol id="qunit-tests">
    </ol>
</body>
</html>