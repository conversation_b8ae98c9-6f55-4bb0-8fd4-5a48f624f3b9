import type { ODataModelResponseV4, ODataCollectionResponseV4, ODataValueResponseV4 } from "@odata2ts/odata-core";
import type { StringCollection, EnumCollection } from "@odata2ts/odata-query-objects";
import { QStringCollection, QEnumCollection } from "@odata2ts/odata-query-objects";
import type { ODataHttpClient, ODataHttpClientConfig, HttpResponseModel } from "@odata2ts/http-client-api";
import { ODataService, EntityTypeServiceV4, ODataServiceOptionsInternal, CollectionServiceV4, EntitySetServiceV4 } from "@odata2ts/odata-service";
import type { PersonId, AirlineId, AirportId, Person, Airport, GetNearestAirportParams, EditablePerson, Location, EditableLocation, TripId, Airline, Trip, Person_GetFriendsTripsParams, Person_UpdateLastNameParams, Person_ShareTripParams, EditableAirline, EditableAirport, EditableTrip, PlanItemId, PlanItem, EditablePlanItem, Event, EditableEvent, PublicTransportation, EditablePublicTransportation, Flight, EditableFlight, Employee, EditableEmployee, Manager, EditableManager, City, EditableCity, AirportLocation, EditableAirportLocation, EventLocation, EditableEventLocation } from "./TrippinModel";
import type { QPerson, QLocation, QAirline, QAirport, QTrip, QPlanItem, QEvent, QPublicTransportation, QFlight, QEmployee, QManager, QCity, QAirportLocation, QEventLocation } from "./QTrippin";
import { Feature } from "./TrippinModel";
export declare class TrippinService<in out ClientType extends ODataHttpClient> extends ODataService<ClientType> {
    private _Me?;
    private _QGetPersonWithMostFriends?;
    private _QGetNearestAirport?;
    private _QResetDataSource?;
    People(): PersonCollectionService<ClientType>;
    People(id: PersonId): PersonService<ClientType>;
    Airlines(): AirlineCollectionService<ClientType>;
    Airlines(id: AirlineId): AirlineService<ClientType>;
    Airports(): AirportCollectionService<ClientType>;
    Airports(id: AirportId): AirportService<ClientType>;
    Me(): PersonService<ClientType>;
    GetPersonWithMostFriends(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<Person>>>;
    GetNearestAirport(params: GetNearestAirportParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<Airport>>>;
    ResetDataSource(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<void>>>;
}
export declare class PersonService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Person, EditablePerson, QPerson> {
    private _Emails?;
    private _AddressInfo?;
    private _HomeAddress?;
    private _Features?;
    private _BestFriend?;
    private _Person_QGetFavoriteAirline?;
    private _Person_QGetFriendsTrips?;
    private _Person_QUpdateLastName?;
    private _Person_QShareTrip?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Emails(): CollectionServiceV4<ClientType, StringCollection, QStringCollection<string>, string>;
    AddressInfo(): CollectionServiceV4<ClientType, Location, QLocation, EditableLocation>;
    HomeAddress(): LocationService<ClientType>;
    Features(): CollectionServiceV4<ClientType, EnumCollection<typeof Feature>, QEnumCollection<typeof Feature>, typeof Feature>;
    Friends(): PersonCollectionService<ClientType>;
    Friends(id: PersonId): PersonService<ClientType>;
    BestFriend(): PersonService<ClientType>;
    Trips(): TripCollectionService<ClientType>;
    Trips(id: TripId): TripService<ClientType>;
    GetFavoriteAirline(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<Airline>>>;
    GetFriendsTrips(params: Person_GetFriendsTripsParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataCollectionResponseV4<Trip>>>;
    UpdateLastName(params: Person_UpdateLastNameParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataValueResponseV4<boolean>>>;
    ShareTrip(params: Person_ShareTripParams, requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataModelResponseV4<void>>>;
    asEmployeeService(): EmployeeService<ClientType>;
    asManagerService(): ManagerService<ClientType>;
}
export declare class PersonCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Person, EditablePerson, QPerson, PersonId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asEmployeeCollectionService(): EmployeeCollectionService<ClientType>;
    asManagerCollectionService(): ManagerCollectionService<ClientType>;
}
export declare class AirlineService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Airline, EditableAirline, QAirline> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class AirlineCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Airline, EditableAirline, QAirline, AirlineId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class AirportService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Airport, EditableAirport, QAirport> {
    private _Location?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Location(): AirportLocationService<ClientType>;
}
export declare class AirportCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Airport, EditableAirport, QAirport, AirportId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class TripService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Trip, EditableTrip, QTrip> {
    private _Tags?;
    private _Trip_QGetInvolvedPeople?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Tags(): CollectionServiceV4<ClientType, StringCollection, QStringCollection<string>, string>;
    PlanItems(): PlanItemCollectionService<ClientType>;
    PlanItems(id: PlanItemId): PlanItemService<ClientType>;
    GetInvolvedPeople(requestConfig?: ODataHttpClientConfig<ClientType>): Promise<HttpResponseModel<ODataCollectionResponseV4<Person>>>;
}
export declare class TripCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Trip, EditableTrip, QTrip, TripId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class PlanItemService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, PlanItem, EditablePlanItem, QPlanItem> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asEventService(): EventService<ClientType>;
    asPublicTransportationService(): PublicTransportationService<ClientType>;
    asFlightService(): FlightService<ClientType>;
}
export declare class PlanItemCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, PlanItem, EditablePlanItem, QPlanItem, PlanItemId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asEventCollectionService(): EventCollectionService<ClientType>;
    asPublicTransportationCollectionService(): PublicTransportationCollectionService<ClientType>;
    asFlightCollectionService(): FlightCollectionService<ClientType>;
}
export declare class EventService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Event, EditableEvent, QEvent> {
    private _OccursAt?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    OccursAt(): EventLocationService<ClientType>;
}
export declare class EventCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Event, EditableEvent, QEvent, PlanItemId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class PublicTransportationService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, PublicTransportation, EditablePublicTransportation, QPublicTransportation> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asFlightService(): FlightService<ClientType>;
}
export declare class PublicTransportationCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, PublicTransportation, EditablePublicTransportation, QPublicTransportation, PlanItemId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    asFlightCollectionService(): FlightCollectionService<ClientType>;
}
export declare class FlightService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Flight, EditableFlight, QFlight> {
    private _Airline?;
    private _From?;
    private _To?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Airline(): AirlineService<ClientType>;
    From(): AirportService<ClientType>;
    To(): AirportService<ClientType>;
}
export declare class FlightCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Flight, EditableFlight, QFlight, PlanItemId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class EmployeeService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Employee, EditableEmployee, QEmployee> {
    private _Emails?;
    private _AddressInfo?;
    private _HomeAddress?;
    private _Features?;
    private _BestFriend?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Emails(): CollectionServiceV4<ClientType, StringCollection, QStringCollection<string>, string>;
    AddressInfo(): CollectionServiceV4<ClientType, Location, QLocation, EditableLocation>;
    HomeAddress(): LocationService<ClientType>;
    Features(): CollectionServiceV4<ClientType, EnumCollection<typeof Feature>, QEnumCollection<typeof Feature>, typeof Feature>;
    Friends(): PersonCollectionService<ClientType>;
    Friends(id: PersonId): PersonService<ClientType>;
    BestFriend(): PersonService<ClientType>;
    Trips(): TripCollectionService<ClientType>;
    Trips(id: TripId): TripService<ClientType>;
    Peers(): PersonCollectionService<ClientType>;
    Peers(id: PersonId): PersonService<ClientType>;
}
export declare class EmployeeCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Employee, EditableEmployee, QEmployee, PersonId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class ManagerService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Manager, EditableManager, QManager> {
    private _Emails?;
    private _AddressInfo?;
    private _HomeAddress?;
    private _Features?;
    private _BestFriend?;
    private _BossOffice?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    Emails(): CollectionServiceV4<ClientType, StringCollection, QStringCollection<string>, string>;
    AddressInfo(): CollectionServiceV4<ClientType, Location, QLocation, EditableLocation>;
    HomeAddress(): LocationService<ClientType>;
    Features(): CollectionServiceV4<ClientType, EnumCollection<typeof Feature>, QEnumCollection<typeof Feature>, typeof Feature>;
    Friends(): PersonCollectionService<ClientType>;
    Friends(id: PersonId): PersonService<ClientType>;
    BestFriend(): PersonService<ClientType>;
    Trips(): TripCollectionService<ClientType>;
    Trips(id: TripId): TripService<ClientType>;
    BossOffice(): LocationService<ClientType>;
    DirectReports(): PersonCollectionService<ClientType>;
    DirectReports(id: PersonId): PersonService<ClientType>;
}
export declare class ManagerCollectionService<in out ClientType extends ODataHttpClient> extends EntitySetServiceV4<ClientType, Manager, EditableManager, QManager, PersonId> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class LocationService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, Location, EditableLocation, QLocation> {
    private _City?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    City(): CityService<ClientType>;
    asAirportLocationService(): AirportLocationService<ClientType>;
    asEventLocationService(): EventLocationService<ClientType>;
}
export declare class CityService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, City, EditableCity, QCity> {
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
}
export declare class AirportLocationService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, AirportLocation, EditableAirportLocation, QAirportLocation> {
    private _City?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    City(): CityService<ClientType>;
}
export declare class EventLocationService<in out ClientType extends ODataHttpClient> extends EntityTypeServiceV4<ClientType, EventLocation, EditableEventLocation, QEventLocation> {
    private _City?;
    constructor(client: ClientType, basePath: string, name: string, options?: ODataServiceOptionsInternal);
    City(): CityService<ClientType>;
}
