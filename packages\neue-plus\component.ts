import {
  NeAvatar,
  NeButton,
  NeCard,
  NeCheckboxGroup,
  NeConfigProvider,
  NeDateRange,
  NeDescriptions,
  NeDrawer,
  NeImage,
  NeInput,
  NeMaterialRender,
  NePagination,
  NeProForm,
  NeProTab,
  NeProTable,
  NeRadioGroup,
  NeSelect,
  NeSlider,
  NeSwitch,
  NeTable,
  NeTag,
  NeTooltipEllipsis,
  NeTreeTable,
} from '@neue-plus/components'
import {
  WidgetBasicForm,
  WidgetBomPanel,
  WidgetBomTable,
  WidgetBusinessType,
  WidgetDialogForm,
} from '@neue-plus/widgets'
import type { Plugin } from 'vue'

export default [
  NePagination,
  NeInput,
  NeCard,
  NeSelect,
  NeTable,
  NeProTable,
  NeProForm,
  NeProTab,
  NeDateRange,
  NeButton,
  NeRadioGroup,
  NeCheckboxGroup,
  NeConfigProvider,
  NeTreeTable,
  NeDrawer,
  NeAvatar,
  NeTooltipEllipsis,
  NeMaterialRender,
  NeImage,
  WidgetBomTable,
  WidgetBomPanel,
  WidgetBasicForm,
  WidgetBusinessType,
  NeTag,
  NeSwitch,
  NeSlider,
  NeDescriptions,
  WidgetDialogForm,
] as Plugin[]
