import {
  NeAvatar,
  NeButton,
  NeCard,
  NeCheckboxGroup,
  NeConfigProvider,
  NeDateRange,
  NeDescriptions,
  NeDrawer,
  NeDynamicIcon,
  NeImage,
  NeInput,
  NeMaterialRender,
  NePagination,
  NeProDialog,
  NeProForm,
  NeProTab,
  NeProTable,
  NeRadioGroup,
  NeSelect,
  NeSlider,
  NeSwitch,
  NeTable,
  NeTag,
  NeTooltipEllipsis,
  NeTreeTable,
} from '@neue-plus/components'
import {
  WidgetBasicDetail,
  WidgetBasicForm,
  WidgetBomPanel,
  WidgetBomTable,
  WidgetBusinessType,
  WidgetDialogForm,
  WidgetPanelForm,
} from '@neue-plus/widgets'

export default [
  NeProTab,
  NePagination,
  NeInput,
  NeCard,
  NeSelect,
  NeTable,
  NeProTable,
  NeProForm,
  NeProDialog,
  NeDateRange,
  NeButton,
  NeRadioGroup,
  NeCheckboxGroup,
  NeConfigProvider,
  NeTreeTable,
  NeDrawer,
  NeAvatar,
  NeTooltipEllipsis,
  NeMaterialRender,
  NeImage,
  NeDynamicIcon,
  WidgetBomTable,
  WidgetBomPanel,
  WidgetBasicForm,
  WidgetBusinessType,
  NeTag,
  NeSwitch,
  NeSlider,
  NeDescriptions,
  WidgetDialogForm,
  WidgetPanelForm,
  WidgetBasicDetail,
]
