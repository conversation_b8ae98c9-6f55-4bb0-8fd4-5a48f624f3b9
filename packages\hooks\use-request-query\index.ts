import { computed } from 'vue'
import { keys } from 'lodash-unified'
import { ProTableColumn } from '@neue-plus/components/pro-table'

interface FilterRule {
  [operator: string]: any
}
export const useRequestQuery = (columns: ProTableColumn[]) => {
  const columnTypeMap = computed(() => {
    return columns.reduce((pre, item) => {
      pre[item.prop] = item.entityType
      return pre
    }, {} as any)
  })
  const getFilter = (filters: Record<string, any>) => {
    const odataFilter = Object.entries(filters)
      .map(([key, opObj]) => {
        return Object.entries(opObj)
          .map(([op, value]) => {
            switch (op) {
              case 'contains':
                return `contains(${key}, '${value}')`
              case 'startswith':
                return `startswith(${key}, '${value}')`
              case 'endswith':
                return `endswith(${key}, '${value}')`
              case 'eq':
                return `${key} eq ${value}`
              case 'ge':
                return `${key} ge ${value}`
              case 'le':
                return `${key} le ${value}`
              default:
                return ''
            }
          })
          .join(' and ')
      })
      .join(' and ')
    console.log(odataFilter)
    return odataFilter
  }
  const getOrderBy = (orderBy?: Record<string, any>) => {
    if (keys(orderBy).length != 0) {
      return [`${orderBy?.prop} ${orderBy?.order}`]
    } else {
      return undefined
    }
  }
  const getRequestQuery = (
    filters: Record<string, any>,
    pagination?: { current: number; pageSize: number },
    orderBy?: Record<string, any>
  ) => {
    const query: any = {
      filter: getFilter(filters),
    }
    if (pagination) {
      const { current, pageSize } = pagination
      query['skip'] = (current - 1) * pageSize
      query['top'] = pageSize
      query['count'] = true
    }
    if (orderBy) {
      query['orderBy'] = getOrderBy(orderBy)
    }
    return query
  }
  const buildODataFilterObject = (filters: Record<string, any>) => {
    const result: Record<string, FilterRule> = {}
    Object.entries(filters).forEach(([field, value]) => {
      if (value === undefined || value === null || value === '') return // 忽略空值
      const type = columnTypeMap.value[field] || 'Edm.String'
      const rule: FilterRule = {}
      console.log(111, type, value, field, columnTypeMap.value)
      if (type === 'Edm.String') {
        // 默认使用 contains
        rule.contains = value
      } else if (type === 'Edm.Decimal' || type === 'Edm.DateTimeOffset') {
        if (Array.isArray(value) && value.length === 2) {
          // 范围
          if (type === 'Edm.DateTimeOffset') {
            rule.ge = new Date(value[0]).toISOString()
            rule.le = new Date(value[1]).toISOString()
          } else {
            rule.ge = value[0]
            rule.le = value[1]
          }
        } else {
          rule.eq = value
        }
      } else if (type === 'Edm.Boolean') {
        rule.eq = !!value
      } else {
        rule.eq = value // 默认 eq
      }
      result[field] = rule
    })
    return result
  }
  return { getRequestQuery, columnTypeMap, buildODataFilterObject }
}
