import { find, isArray, keys } from 'lodash-unified'
import { ProTableColumn } from '@neue-plus/components/pro-table'

export const useRequestQuery = (data: ProTableColumn[]) => {
  const getFilter = (filters: Record<string, any>) => {
    return keys(filters).reduce((pre: any, item) => {
      if (!filters[item]) return pre
      const node = find(data, (ite) => ite.prop === item)
      switch (node?.valueType) {
        case 'dateRange':
          filters[item].forEach((item: any, index: number) => {
            pre.push(`${item} ${index === 0 ? 'ge' : 'le'} ${new Date(item)}`)
          })
          break
        case 'select':
          if (isArray(filters[item])) {
            const arrStr = filters[item].reduce((pre, ite: any) => {
              pre.push(`${item} eq ${ite}`)
              return pre
            }, [])
            if (arrStr.length > 1) {
              pre.push(`(${arrStr.join(' or ')})`)
            } else {
              pre.push(`${arrStr[0]}`)
            }
          } else {
            pre.push(`${item} eq ${filters[item]}`)
          }
          break
        case 'digit':
          pre.push(`${item} eq ${filters[item]}`)
          break
        case 'text':
          pre.push(`contains(${item} , '${filters[item]}')`)
          break
      }
      return pre
    }, [])
  }
  const getOrderBy = (orderBy?: Record<string, any>) => {
    if (keys(orderBy).length != 0) {
      return [`${orderBy?.prop} ${orderBy?.order}`]
    } else {
      return undefined
    }
  }
  const getRequestQuery = (
    filters: Record<string, any>,
    pagination: { current: number; pageSize: number },
    orderBy?: Record<string, any>
  ) => {
    const { current, pageSize } = pagination
    console.log(getFilter(filters))
    return {
      filter: getFilter(filters),
      top: pageSize,
      skip: (current - 1) * pageSize,
      orderBy: getOrderBy(orderBy),
      count: true,
      // select: ['Id', 'Name', 'CreatedAt'],
    }
  }
  return { getRequestQuery }
}
