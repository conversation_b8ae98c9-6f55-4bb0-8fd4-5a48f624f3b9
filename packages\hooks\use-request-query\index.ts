import { isArray, isEmpty, keys } from 'lodash-unified'

export const useRequestQuery = (
  data: { valueType?: string; prop: string; valueEnum?: any }[]
) => {
  const getFilter = (filters: Record<string, any>) => {
    const valueTypeMap = data.reduce((pre, item) => {
      pre[item.prop] = item.valueEnum ? 'select' : item.valueType
      return pre
    }, {} as any)
    return keys(filters).reduce((pre: any, item) => {
      switch (valueTypeMap[item]) {
        case 'dateRange':
          if (isArray(filters[item]) && filters[item].length === 2) {
            const [startTime, endTime] = filters[item]
            pre[item] = { ge: new Date(startTime), le: new Date(endTime) }
          }
          break
        case 'select':
          if (isArray(filters[item])) {
            pre['or'] = filters[item].map((filter: any) => ({
              [item]: { eq: filter },
            }))
          } else {
            pre[item] = filters[item] ?? undefined
          }

          break
        default:
          pre[item] = {
            contains: filters[item] ?? undefined,
          }
      }
      return pre
    }, {})
  }
  const getOrderBy = (orderBy: Record<string, any>) => {
    if (keys(orderBy).length != 0) {
      return [`${orderBy.prop} ${orderBy.order}`]
    } else {
      return undefined
    }
  }
  const getRequestQuery = (
    filters: Record<string, any>,
    pagination: { current: number; pageSize: number },
    orderBy: Record<string, any>
  ) => {
    const { current, pageSize } = pagination
    const newFilters = keys(filters).reduce((pre, item) => {
      if (!isEmpty(filters[item])) {
        pre[item] = filters[item]
      }
      return pre
    }, {} as any)
    return {
      filter: getFilter(newFilters),
      top: pageSize,
      skip: (current - 1) * pageSize,
      orderBy: getOrderBy(orderBy),
      count: true,
      // select: ['Id', 'Name', 'CreatedAt'],
    }
  }
  return { getRequestQuery }
}
