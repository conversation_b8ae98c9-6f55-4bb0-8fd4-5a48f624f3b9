import { computed } from 'vue'
import { ProTableColumn } from '@neue-plus/components/pro-table'

interface FilterRule {
  [operator: string]: any
}
export const useRequestQuery = (columns: ProTableColumn[]) => {
  const columnTypeMap = computed(() => {
    return columns.reduce((pre, item) => {
      pre[item.prop] = item.entityType
      return pre
    }, {} as any)
  })

  const buildODataFilterObject = (filters: Record<string, any>) => {
    const result: Record<string, FilterRule> = {}
    Object.entries(filters).forEach(([field, value]) => {
      if (value === undefined || value === null || value === '') return // 忽略空值
      const type = columnTypeMap.value[field] || 'Edm.String'
      const rule: FilterRule = {}
      if (type === 'Edm.String') {
        rule.contains = value
      } else if (type === 'Edm.Decimal' || type === 'Edm.DateTimeOffset') {
        if (Array.isArray(value) && value.length === 2) {
          // 范围
          if (type === 'Edm.DateTimeOffset') {
            rule.ge = new Date(value[0]).toISOString()
            rule.le = new Date(value[1]).toISOString()
          } else {
            rule.ge = value[0]
            rule.le = value[1]
          }
        } else {
          rule.eq = value
        }
      } else if (type === 'Edm.Boolean') {
        rule.eq = !!value
      } else if (type === 'Edm.Enum') {
        rule.eq = `'${value}'`
      } else {
        rule.eq = value
      }
      result[field] = rule
    })
    return result
  }
  return { columnTypeMap, buildODataFilterObject }
}
