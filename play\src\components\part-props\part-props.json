{"config": {"apiBaseConfig": {"baseUrl": ""}}, "apis": {"submit": {"url": "/cdp/202512/OdataService/CadFile", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision)"}}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}, "dialogFormSubmitCadBom": {"url": "/modeling/202512/OdataService/RelCadBomRelations", "method": "post"}, "panelFormEditRequestAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "get", "params": {"$expand": "version($expand=revision($expand=lockedBy)),owner,thumbnail,lifecycleStatus"}}, "panelFormEditRequestPart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "get", "params": {"$expand": "version($expand=revision($expand=lockedBy)),owner,thumbnail,lifecycleStatus"}}, "panelFormUpdateAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "patch"}, "panelFormUpdatePart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "patch"}, "dialogFormEditRequestAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "get"}, "dialogFormEditRequestPart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "get"}, "dialogFormUpdateAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms", "method": "post"}, "dialogFormUpdatePart": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "dialogFormSubmitRelation": {"url": "/modeling/202512/OdataService/RelCadBomWithTypes({{ncid}})", "method": "patch"}, "businessTypeRequest": {"url": "/modeling/202512/OdataService/EntityTypes", "method": "get"}}, "elements": [{"id": "pro-tab", "type": "pro-tab", "name": "OData表格", "props": {"tabProps": {"tabPosition": "left", "style": "width:100%;padding:16px;box-sizing:border-box;background:#ECEEF3"}, "toolbar": {"actions": {"header": [{"text": "锁定", "id": "suoding", "props": {"type": "primary"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "condition"}}, {"id": "condition", "type": "normal", "title": "锁定零部件", "config": {"actionType": "handleCondition", "branches": {"condition1": "#neue.NeueCadPart", "condition2": "#neue.NeueCadAsm"}}}, {"id": "#neue.NeueCadPart", "type": "normal", "title": "锁定", "config": {"request": {"url": "/modeling/202512/OdataService/NeueCadParts('{{ncid}}')/BuiltInModel.Lock", "method": "post"}, "branches": {"success": "message", "fail": "end"}}}, {"id": "#neue.NeueCadAsm", "type": "normal", "title": "锁定", "config": {"request": {"url": "/modeling/202512/OdataService/NeueCadAsms('{{ncid}}')/BuiltInModel.Lock", "method": "post"}, "branches": {"success": "message", "fail": "end"}}}, {"id": "message", "type": "success", "title": "锁定零部件", "content": "锁定成功", "config": {"actionType": "handleMessage", "next": "messageNext"}}, {"id": "messageNext", "config": {"actionType": "setEditButton", "params": {"isDisabled": true}, "target": "PanelForm_er4mn6jbtk1234562"}}, {"id": "end", "type": "end", "title": "结束"}]}, {"text": "解锁", "props": {}, "id": "jiesuo", "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "condition"}}, {"id": "condition", "type": "normal", "title": "解锁零部件", "config": {"actionType": "handleCondition", "branches": {"condition1": "#neue.NeueCadPart", "condition2": "#neue.NeueCadAsm"}}}, {"id": "#neue.NeueCadPart", "type": "normal", "title": "解锁", "config": {"request": {"url": "/modeling/202512/OdataService/NeueCadParts('{{ncid}}')/BuiltInModel.Unlock", "method": "post"}, "branches": {"success": "message", "fail": "end"}}}, {"id": "#neue.NeueCadAsm", "type": "normal", "title": "解锁", "config": {"request": {"url": "/modeling/202512/OdataService/NeueCadAsms('{{ncid}}')/BuiltInModel.Unlock", "method": "post"}, "branches": {"success": "message", "fail": "end"}}}, {"id": "message", "title": "解锁零部件", "content": "解锁成功", "type": "success", "config": {"actionType": "handleMessage", "next": "messageNext"}}, {"id": "messageNext", "config": {"actionType": "setEditButton", "params": {"isDisabled": true}, "target": "PanelForm_er4mn6jbtk1234562"}}, {"id": "end", "type": "end", "title": "结束"}]}, {"text": "修订", "props": {}, "id": "xiuding", "config": {"actionType": "open", "target": "DialogForm_edit_er4mn6jbtk1234567"}}]}}, "tabs": [{"name": "base-info", "label": "基本信息", "icon": "icon-sidebar-character", "elements": [{"id": "PanelForm_er4mn6jbtk1234562", "type": "wg-panel-form", "name": "面板表单", "props": {"config": {"span": 12, "gutter": 12, "labelWidth": "100px", "title": "属性信息", "submitText": "保存", "cancelText": "取消", "editText": "编辑", "isEdit": false, "showSubmit": true, "completeMessage": "保存成功"}, "formItems": [{"name": "基本属性", "key": "base", "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "disabled": true}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "disabled": false, "minLength": 1, "maxLength": 32, "showWordLimit": true, "ruleId": "RULE_101"}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true}, {"prop": "owner.name", "fieldName": "责任人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "lifecycleStatus.name", "fieldName": "状态", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "valueEnum": {"IN_WORK": "工作中"}}, {"prop": "version.revision.lockedBy.name", "fieldName": "锁定人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "version.revision.lockedAt", "fieldName": "锁定时间", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, {"name": "设计属性", "key": "design", "formItems": [{"prop": "solidSurfaceArea", "fieldName": "实体曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "openSurfaceArea", "fieldName": "开放曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "volume", "fieldName": "体积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"label": "质量", "prop": "mass", "fieldName": "质量", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "material", "fieldName": "材料", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "gravityCenter", "fieldName": "重心", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}]}]}, "elements": [], "events": []}]}, {"name": "3D-file", "label": "3D文件", "icon": "icon-a-<PERSON>wen<PERSON><PERSON>-<PERSON><PERSON><PERSON>ing", "elements": [{"id": "3d-card", "type": "card", "name": "OData表格", "slots": {"header": "3D文件"}, "elements": [{"id": "3d-file-table", "type": "pro-table", "name": "3d文件", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "to/storageItem/name", "label": "文件名"}, {"prop": "to/storageItem/mimeType", "label": "格式"}, {"prop": "to/softwareRev", "label": "软件版本"}, {"prop": "usageType", "label": "用途"}, {"prop": "createdAt", "label": "创建时间 ", "valueType": "time"}, {"prop": "modifiedAt", "label": "更新时间", "valueType": "time"}, {"prop": "operation", "label": "操作", "minWidth": 60, "actions": [{"text": "预览", "id": "edit", "props": {"link": true, "type": "primary"}, "config": {"dataSource": "query", "blank": "_blank", "href": "https://cdp-cn-hangzhou-2.cloud.neuetech.cn/#/view-3d?ncid={{ncid}}"}}, {"text": "下载", "id": "download", "props": {"link": true, "type": "primary"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "download"}}, {"id": "download", "type": "normal", "config": {"actionType": "handleFileDown", "apiList": [{"url": "/modeling/202512/OdataService/BatchGetFileSignatureUrl", "method": "post", "data": {"fileIds": ["{{to.ncid}}"], "actionType": "DOWNLOAD"}}], "branches": {"success": "end", "fail": "end"}}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "request": {"url": "/modeling/202512/OdataService/RelCadFileFileWithTypes", "method": "get", "query": {"expand": "to,createdBy,to($expand=storageItem),from", "filter": {"from/ncid": {"eq": "'{{ncid}}'"}}}}}, "events": []}]}]}, {"name": "2D-drawing", "label": "2D工程图", "icon": "icon-a-sidebar-2Ddrawing", "elements": [{"id": "odata-card", "type": "card", "name": "OData表格", "slots": {"header": "2D工程图"}, "elements": [{"id": "2d-file-table", "type": "pro-table", "name": "2D工程图", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "to/code", "label": "编号"}, {"prop": "to/name", "label": "名称"}, {"prop": "to/version/revision/revisionCode", "label": "版本号", "render": "{{to.version.revision.revisionCode}}.{{to.version.versionNumber}}"}, {"prop": "to/description", "label": "描述"}, {"prop": "to/createdBy/name", "label": "创建人 ", "valueType": "avatar"}, {"prop": "modifiedAt", "label": "创建时间", "valueType": "time"}, {"prop": "to/owner/name", "label": "责任人", "valueType": "avatar"}, {"prop": "operation", "label": "操作", "actions": [{"text": "浏览", "id": "2dFile_download", "props": {"link": true, "type": "primary"}, "config": {"dataSource": "tableRow", "blank": "_blank", "href": "https://cdp-cn-hangzhou-2.cloud.neuetech.cn/#/new/part-file-props?ncid={{to.ncid}}&odataType={{@odata.type}}"}}, {"text": "移除", "id": "2dFile_delete", "props": {"link": true, "type": "danger"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "删除2D工程图", "content": "是否确认删除？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "afterConfirm", "cancel": "end"}}}, {"id": "afterConfirm", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/RelCadPartTwoDimensionDrawingRelations('{{ncid}}')", "method": "delete"}, "branches": {"success": "end", "fail": "end"}}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "request": {"url": "/modeling/202512/OdataService/RelCadPartTwoDimensionDrawingWithTypes", "method": "get", "query": {"expand": "to($expand=version($expand=revision),created<PERSON>y,owner),createdBy", "filter": {"from/ncid": {"eq": "'{{ncid}}'"}}}}}, "events": []}]}]}, {"name": "cad-bom", "label": "CADBOM", "icon": "icon-sidebar-CADBOM", "elements": [{"id": "bom-card", "type": "card", "name": "bom表格", "slots": {"header": "CADBOM"}, "elements": [{"id": "bom-tree-table", "type": "tree-table", "name": "bom表格", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "code", "label": "编码", "width": 250, "elements": [{"id": "code-render", "type": "part-title", "props": {"type": "{{partType}}", "title": "{{code}}"}}]}, {"prop": "name", "label": "名称"}, {"prop": "version/revision/revisionCode", "label": "版本"}, {"prop": "version/versionNumber", "label": "版次"}, {"prop": "partType", "label": "类型", "width": 100}, {"prop": "quantity", "label": "数量"}, {"prop": "mass", "label": "质量"}, {"prop": "gravityCenter", "label": "重心"}, {"prop": "owner/name", "label": "责任人", "valueType": "avatar", "width": 160}, {"prop": "createdBy/name", "label": "创建人", "valueType": "avatar", "width": 160}, {"prop": "createdAt", "label": "创建时间", "valueType": "time", "width": 160}, {"prop": "description", "label": "描述"}, {"prop": "suppressed", "label": "抑制", "valueEnum": {"true": {"text": "是"}, "false": {"text": "否"}}}, {"prop": "instanceName", "label": "实例名称"}, {"prop": "bomExcluded", "label": "BOM包含", "width": 100, "valueEnum": {"true": {"text": "是"}, "false": {"text": "否"}}}, {"prop": "transformationMatrix", "label": "变换矩阵", "width": 200}, {"prop": "operation", "label": "操作", "width": 120, "actions": [{"icon": "icon-CADBOM-yulan", "id": "add", "props": {"tooltip": {"content": "预览零部件"}}, "config": {}}, {"icon": "icon-BOM-search", "type": "pro-dialog", "id": "find", "props": {"tooltip": {"content": "查询添加"}}, "config": {"actionType": "open", "target": "bom-dialog_er4mn6jbtk123456"}}, {"icon": "icon-BOM-rmove", "id": "delete", "props": {"hidden": "{{isRoot}}", "tooltip": {"content": "移除"}}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "", "content": "是否确删除关联关系？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "deleteRequest", "cancel": "end"}}}, {"id": "deleteRequest", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/RelCadBomWithTypes('{{ncid}}')", "method": "delete"}, "branches": {"success": "refreshTable", "fail": "refreshTable"}}}, {"id": "refreshTable", "type": "normal", "title": "刷新表格", "config": {"actionType": "refresh", "target": "bom-tree-table", "next": "end"}}, {"id": "end", "type": "end", "title": "结束"}]}, {"icon": "icon-cax-newnode", "id": "edit", "props": {"hidden": "{{isRoot}}", "tooltip": {"content": "编辑子件关系"}}, "config": {"actionType": "open", "params": {"odataType": "bomRelation"}, "target": "DialogForm_relation_er4mn6jbtk123456"}}]}], "request": {"url": "/modeling/202512/OdataService/GetRelationTreeByNcid(typeNcid='ncid1.plt0entitytype.local..cadbomwithtype001',ncid='{{ncid}}',isTree=true,isLatest=true)", "method": "get"}}, "events": []}]}]}, {"name": "version-number", "label": "版本版次", "icon": "icon-sidebar-version", "elements": [{"id": "bom-card", "type": "card", "name": "bom表格", "slots": {"header": "版本版次"}, "elements": [{"id": "cad-part-props-table", "type": "pro-table", "name": "OData表格", "props": {"entityType": "<PERSON><PERSON><PERSON><PERSON>", "rowKey": "ncid", "searchConfig": false, "columns": [{"prop": "code", "label": "编号"}, {"prop": "name", "label": "名称"}, {"prop": "version.revision.revisionCode", "label": "版本"}, {"prop": "version.versionNumber", "label": "版次"}, {"prop": "description", "label": "描述"}, {"prop": "createdBy/name", "label": "创建人", "valueType": "avatar"}, {"prop": "createdAt", "label": "创建时间", "valueType": "time"}, {"prop": "operation", "label": "操作", "minWidth": 80, "actions": [{"text": "预览", "id": "edit", "props": {"link": true, "type": "primary"}, "config": {"blank": "_blank", "href": "https://cdp-cn-hangzhou-2.cloud.neuetech.cn/#/new/part-props?ncid={{ncid}}&odataType={{@odata.type}}&masterNcid={{version.revision.master.ncid}}&partType={{partType}}&code={{code}}"}}, {"text": "清理", "id": "delete", "props": {"link": true, "type": "danger"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "删除此版本", "content": "是否确认删除？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "afterConfirm", "cancel": "end"}}}, {"id": "afterConfirm", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/CadParts('{{ncid}}')", "method": "delete"}, "branches": {"success": "refreshTable", "fail": "refreshTable"}}}, {"id": "refreshTable", "type": "normal", "title": "刷新表格", "config": {"actionType": "refresh", "target": "cad-part-props-table", "next": "end"}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "request": {"url": "/modeling/202512/OdataService/CadParts", "method": "get", "query": {"filter": {"version/revision/master/ncid": {"eq": "'{{masterNcid}}'"}}, "expand": "createdBy,owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision($expand=lockedBy,master))"}}}, "events": [], "api": {}}]}]}]}, "elements": []}, {"id": "DialogForm_er4mn6jbtk123456", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "新建设计零部件", "width": "520px", "height": "578px", "labelWidth": "100", "formWidth": "420", "layout": "horizontal", "submitText": "保存", "resetText": "重置2", "cancelText": "取消", "showLabel": true, "showSubmit": true, "showReset": false, "showCancel": true, "buttonAlign": "right"}, "formItems": [{"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 6, "maxLength": 18, "showWordLimit": true, "ruleId": "RULE_101"}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "required": true}, {"prop": "revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "required": true}, {"prop": "versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "required": true}, {"prop": "description", "fieldName": "备注", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, "elements": [], "events": [{"nickName": "行点击事件", "eventName": "onRefresh", "actions": [{"id": "start", "type": "start", "title": "开始"}, {"id": "56132221", "type": "normal", "title": "节点7690", "content": "打开弹框", "config": {"actionType": "refresh", "actionName": "打开drawer", "target": "odata-table"}}, {"id": "end", "type": "end", "title": "结束"}]}]}, {"id": "DialogForm_edit_er4mn6jbtk1234567", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "修订设计零部件", "width": "520px", "height": "578px", "labelWidth": "100", "formWidth": "420", "layout": "horizontal", "submitText": "确定", "cancelText": "取消", "showSubmit": true, "showCancel": true, "showReset": false, "buttonAlign": "right", "refreshAfterSave": true, "completeMessage": "修订成功"}, "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "required": true, "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 1, "maxLength": 32, "showWordLimit": true}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "disabled": true, "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "required": true, "disabled": true}, {"prop": "mass", "fieldName": "质量", "fieldType": "text", "disabled": true}, {"prop": "material", "fieldName": "材料", "fieldType": "text", "disabled": true}, {"prop": "gravityCenter", "fieldName": "重心", "fieldType": "text", "disabled": true}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, "elements": [], "events": []}, {"id": "DialogForm_relation_er4mn6jbtk123456", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "关系属性编辑", "width": "520px", "height": "378px", "labelWidth": "100", "formWidth": "420", "showLabel": true, "showSubmit": true, "showCancel": true, "noRequest": true}, "formItems": [{"prop": "quantity", "fieldName": "数量", "fieldType": "number", "required": true, "min": 1, "max": 1000000, "step": 1, "minLength": 1, "maxLength": 8}]}, "elements": [], "events": [{"nickName": "行点击事件", "eventName": "onRefresh", "actions": [{"id": "start", "type": "start", "title": "开始"}, {"id": "56132221", "type": "normal", "title": "节点7690", "content": "关系属性编辑", "config": {"actionType": "refresh", "actionName": "打开drawer", "target": "bom-tree-table"}}, {"id": "end", "type": "end", "title": "结束"}]}]}, {"id": "bom-dialog_er4mn6jbtk123456", "type": "wg-bom-dialog", "name": "弹框", "props": {"request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"filter": {"version/isLatestVersion": {"eq": true}, "version/revision/isLatestRevision": {"eq": true}}, "expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision)"}}, "submit": {"url": "/modeling/202512/OdataService/RelCadBomWithTypes", "method": "post"}}, "events": [{"nickName": "行点击事件", "eventName": "onRefresh", "actions": [{"id": "start", "type": "start", "title": "开始"}, {"id": "56132221", "type": "normal", "title": "节点7690", "content": "", "config": {"actionType": "refresh", "target": "bom-tree-table"}}, {"id": "end", "type": "end", "title": "结束"}]}]}]}