{"config": {"apiBaseConfig": {"baseUrl": ""}}, "apis": {"submit": {"url": "/cdp/202512/OdataService/CadFile", "method": "post"}, "request": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "get", "query": {"expand": "owner,modifiedBy,lifecycleStatus,thumbnail,version($expand=revision)"}}, "metadata": {"url": "/modeling/202512/OdataService/$metadata", "method": "get"}, "panelFormEditRequestAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "get", "params": {"$expand": "version($expand=revision),thumbnail,lifecycleStatus"}}, "panelFormEditRequestPart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "get", "params": {"$expand": "version($expand=revision),thumbnail,lifecycleStatus"}}, "panelFormUpdateAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "patch"}, "panelFormUpdatePart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "patch"}, "dialogFormEditRequestAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms({{ncid}})", "method": "get"}, "dialogFormEditRequestPart": {"url": "/modeling/202512/OdataService/NeueCadParts({{ncid}})", "method": "get"}, "dialogFormUpdateAsm": {"url": "/modeling/202512/OdataService/NeueCadAsms", "method": "post"}, "dialogFormUpdatePart": {"url": "/modeling/202512/OdataService/NeueCadParts", "method": "post"}, "dialogFormSubmitRelation": {"url": "/modeling/202512/OdataService/RelCadBomRelations({{ncid}})", "method": "patch"}, "businessTypeRequest": {"url": "/modeling/202512/OdataService/EntityTypes", "method": "get"}}, "elements": [{"id": "pro-tab", "type": "pro-tab", "name": "OData表格", "props": {"tabProps": {"tabPosition": "left", "style": "width:100%;padding:16px;box-sizing:border-box"}, "toolbar": {"actions": {"header": [{"text": "锁定", "id": "suoding", "props": {"type": "primary"}, "config": {"actionType": "setEditButton", "params": {"isDisabled": true}, "target": "DialogForm_er4mn6jbtk"}}, {"text": "解锁", "props": {}, "id": "jiesuo", "config": {"actionType": "setEditButton", "params": {"isDisabled": false}, "target": "Drawer_er4mn6jbtk"}}, {"text": "修订", "props": {}, "id": "xiuding", "config": {"actionType": "open", "target": "DialogForm_edit_er4mn6jbtk1234567"}}, {"text": "删除", "id": "del", "props": {"type": "danger"}, "config": {"actionType": "open", "target": "Drawer_er4mn6jbtk"}}]}}, "tabs": [{"name": "base-info", "label": "基本信息", "elements": [{"id": "PanelForm_er4mn6jbtk1234562", "type": "wg-panel-form", "name": "面板表单", "props": {"config": {"span": 12, "gutter": 12, "labelWidth": "100px", "title": "零部件属性信息", "showSubmit": true}, "formItems": [{"name": "基本属性", "key": "base", "viewImg": "https://example.com/image1.png", "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32, "ruleId": "RULE_101"}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "disabled": false, "minLength": 6, "maxLength": 18, "showWordLimit": true, "ruleId": "RULE_101"}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true}, {"prop": "owner", "fieldName": "责任人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "lifecycleStatus.name", "fieldName": "状态", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "locked<PERSON>y", "fieldName": "锁定人", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "lockedAt", "fieldName": "锁定时间", "fieldType": "text", "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, {"name": "设计属性", "key": "design", "formItems": [{"prop": "solidSurfaceArea", "fieldName": "实体曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "openSurfaceArea", "fieldName": "开放曲面面积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "volume", "fieldName": "体积", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"label": "质量", "prop": "mass", "fieldName": "质量", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "material", "fieldName": "材料", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}, {"prop": "centerOfGravity", "fieldName": "重心", "fieldType": "text", "minLength": 1, "maxLength": 32, "disabled": true}]}]}, "elements": [], "events": []}]}, {"name": "3D-file", "label": "3D文件", "elements": [{"id": "3d-card", "type": "card", "name": "OData表格", "slots": {"header": "3D文件"}, "elements": [{"id": "3d-file-table", "type": "pro-table", "name": "3d文件", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "to/name", "label": "文件名"}, {"prop": "to/storageItem/schemaVersion", "label": "格式"}, {"prop": "to/softwareRev", "label": "软件版本"}, {"prop": "usageType", "label": "用途"}, {"prop": "createdAt", "label": "创建时间 ", "valueType": "time"}, {"prop": "modifiedAt", "label": "更新时间", "valueType": "time"}, {"prop": "operation", "label": "操作", "minWidth": 60, "actions": [{"text": "下载", "id": "download", "props": {"link": true, "type": "primary"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "download"}}, {"id": "download", "type": "normal", "config": {"actionType": "handleFileDown", "apiList": [{"url": "/modeling/202512/OdataService/BatchGetFileSignatureUrl", "method": "post", "data": {"fileIds": ["{{to.ncid}}"], "actionType": "DOWNLOAD"}}], "branches": {"success": "end", "fail": "end"}}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "request": {"url": "/modeling/202512/OdataService/RelCadFileFileWithTypes", "method": "get", "query": {"expand": "to,createdBy,to($expand=storageItem),from"}}}, "events": []}]}]}, {"name": "2D-drawing", "label": "2D工程图", "elements": [{"id": "odata-card", "type": "card", "name": "OData表格", "slots": {"header": "2D工程图"}, "elements": [{"id": "2d-file-table", "type": "pro-table", "name": "2D工程图", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "to/code", "label": "编号"}, {"prop": "to/name", "label": "名称"}, {"prop": "version/versionNumber", "label": "版本号", "render": "({ row }) => 'v'+row.version.versionNumber", "valueType": "digit"}, {"prop": "lifecycleStatus/lifecycleState", "label": "描述"}, {"prop": "createdBy/name", "label": "创建人 ", "valueType": "avatar"}, {"prop": "modifiedAt", "label": "创建时间", "valueType": "time"}, {"prop": "owner/name", "label": "责任人", "valueType": "avatar"}, {"prop": "operation", "label": "操作", "actions": [{"text": "下载", "id": "2dFile_download", "props": {"link": true, "type": "primary"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "getSignatureUrl"}}, {"id": "getSignatureUrl", "type": "normal", "title": "获取file id", "config": {"request": {"url": "/modeling/202512/OdataService/RelCadFileFileRelations", "method": "get", "query": {"expand": "to,created<PERSON>y", "filter": {"from/ncid": {"eq": "{{ncid}}"}}}}, "branches": {"true": "file", "false": "doNo"}}}, {"id": "end", "type": "end", "title": "结束"}]}, {"text": "移除", "id": "2dFile_delete", "props": {"link": true, "type": "danger"}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "删除2D工程图", "content": "是否确认删除？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "afterConfirm", "cancel": "end"}}}, {"id": "afterConfirm", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/RelCadPartTwoDimensionDrawingRelations('{{ncid}}')", "method": "delete"}, "branches": {"success": "end", "fail": "end"}}}, {"id": "end", "type": "end", "title": "结束"}]}]}], "request": {"url": "/modeling/202512/OdataService/RelCadPartTwoDimensionDrawingRelations", "method": "get", "query": {"expand": "to,created<PERSON>y", "filter": {"from/ncid": {"eq": "{{ncid}}"}}}}}, "events": [{"nickName": "行点击事件", "eventName": "onRowClick", "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "56132221", "type": "href", "title": "节点7690", "content": "行点击事件", "config": {"href": "https://cdp-cn-hangzhou-2.cloud.neuetech.cn/#/new/part-file-props?ncid={{ncid}}&odataType={{@odata.type}}"}}, {"id": "end", "type": "end", "title": "结束"}]}]}]}]}, {"name": "cad-bom", "label": "CADBOM", "elements": [{"id": "bom-card", "type": "card", "name": "bom表格", "slots": {"header": "CADBOM"}, "elements": [{"id": "bom-table", "type": "tree-table", "name": "bom表格", "props": {"searchConfig": false, "entityType": "NeueCadAsm", "rowKey": "ncid", "columns": [{"prop": "code", "label": "编码"}, {"prop": "name", "label": "名称"}, {"prop": "version/schemaVersion", "label": "版本"}, {"prop": "version/schemaVersion", "label": "版次"}, {"prop": "quantity", "label": "数量"}, {"prop": "mass", "label": "质量"}, {"prop": "transformationMatrix", "label": "重心"}, {"prop": "owner/ncid", "label": "责任创建人", "valueType": "avatar"}, {"prop": "createdBy/ncid", "label": "创建人", "valueType": "avatar"}, {"prop": "createdAt", "label": "创建时间", "valueType": "time"}, {"prop": "description", "label": "描述"}, {"prop": "operation", "label": "操作", "minWidth": 160, "actions": [{"icon": "icon-cax-newpart", "id": "add", "props": {"tooltip": {"content": "新建子件"}}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk123456"}}, {"icon": "icon-BOM-search", "id": "find", "props": {"tooltip": {"content": "查询添加"}}, "config": {"actionType": "open", "target": "DialogForm_er4mn6jbtk123456"}}, {"icon": "icon-BOM-rmove", "id": "delete", "props": {"tooltip": {"content": "移除"}}, "actions": [{"id": "start", "type": "start", "title": "开始", "config": {"next": "confirm"}}, {"id": "confirm", "type": "normal", "title": "删除BOM", "content": "是否确认删除？", "config": {"actionType": "handleConfirm", "branches": {"confirm": "deleteRequest", "cancel": "end"}}}, {"id": "deleteRequest", "type": "normal", "title": "发送删除请求", "config": {"request": {"url": "/modeling/202512/OdataService/GetRelationTreeByNcid('{{ncid}}')", "method": "delete"}, "branches": {"success": "end", "fail": "end"}}}, {"id": "end", "type": "end", "title": "结束"}]}, {"icon": "icon-cax-newnode", "id": "edit", "props": {"tooltip": {"content": "编辑子件关系"}}, "config": {"actionType": "open", "params": {"odataType": "bomRelation"}, "target": "DialogForm_relation_er4mn6jbtk123456"}}]}], "request": {"url": "/modeling/202512/OdataService/GetRelationTreeByNcid(typeNcid='ncid1.plt0entitytype.local..cadbom001',ncid='{{ncid}}',isTree=true,isLatest=true)", "method": "get"}}, "events": []}]}]}, {"name": "version-number", "label": "版本版次", "elements": []}]}, "elements": []}, {"id": "DialogForm_er4mn6jbtk123456", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "新建设计零部件", "width": "520px", "height": "578px", "labelWidth": "100", "formWidth": "420", "layout": "horizontal", "submitText": "保存", "resetText": "重置2", "cancelText": "取消", "showLabel": true, "showSubmit": true, "showReset": false, "showCancel": true, "buttonAlign": "right"}, "formItems": [{"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 6, "maxLength": 18, "showWordLimit": true, "ruleId": "RULE_101"}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "required": true}, {"prop": "revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "required": true}, {"prop": "versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "required": true}, {"prop": "description", "fieldName": "备注", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, "elements": [], "events": [{"nickName": "行点击事件", "eventName": "onRefresh", "actions": [{"id": "start", "type": "start", "title": "开始"}, {"id": "56132221", "type": "normal", "title": "节点7690", "content": "打开弹框", "config": {"actionType": "refresh", "actionName": "打开drawer", "target": "odata-table"}, "children": []}, {"id": "end", "type": "end", "title": "结束"}]}]}, {"id": "DialogForm_edit_er4mn6jbtk1234567", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "修订设计零部件", "width": "520px", "height": "578px", "labelWidth": "100", "formWidth": "420", "layout": "horizontal", "submitText": "确定", "cancelText": "取消", "showSubmit": true, "showCancel": true, "showReset": false, "buttonAlign": "right"}, "formItems": [{"prop": "code", "fieldName": "编码", "fieldType": "text", "required": true, "disabled": true, "minLength": 1, "maxLength": 32}, {"prop": "name", "fieldName": "名称", "fieldType": "text", "required": true, "minLength": 6, "maxLength": 18, "showWordLimit": true}, {"prop": "partType", "fieldName": "子类别", "fieldType": "select", "disabled": true, "required": true, "options": [{"label": "NEUE_PRT", "value": "NEUE_PRT"}, {"label": "NEUE_ASM", "value": "NEUE_ASM"}]}, {"prop": "businessType", "fieldName": "类型", "fieldType": "businessType", "required": true, "disabled": true}, {"prop": "version.revision.revisionCode", "fieldName": "版本", "fieldType": "text", "disabled": true, "required": true}, {"prop": "version.versionNumber", "fieldName": "版次", "fieldType": "text", "disabled": true, "required": true}, {"prop": "mass", "fieldName": "质量", "fieldType": "text", "disabled": true}, {"prop": "meterial", "fieldName": "材料", "fieldType": "text", "disabled": true}, {"prop": "gravityCenter", "fieldName": "重心", "fieldType": "text", "disabled": true}, {"prop": "description", "fieldName": "描述", "fieldType": "textarea", "showWordLimit": true, "maxLength": 255}]}, "elements": [], "events": []}, {"id": "DialogForm_relation_er4mn6jbtk123456", "type": "wg-dialog-form", "name": "弹框", "props": {"config": {"title": "关系属性编辑", "width": "520px", "height": "378px", "labelWidth": "100", "formWidth": "420", "showLabel": true, "showSubmit": true, "showCancel": true, "noReqest": true}, "formItems": [{"prop": "quantity", "fieldName": "数量", "fieldType": "number", "required": true, "minLength": 1, "maxLength": 32}]}, "elements": [], "events": []}]}