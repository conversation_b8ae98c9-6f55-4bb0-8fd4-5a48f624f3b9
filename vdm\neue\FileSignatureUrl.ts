/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  CollectionField,
  ComplexTypeField,
  ConstructorOrField,
  DeSerializers,
  DefaultDeSerializers,
  DeserializedType,
  EdmTypeField,
  Entity,
  EnumField,
  FieldBuilder,
  FieldOptions,
  OrderableEdmTypeField,
  PropertyMetadata
} from '@sap-cloud-sdk/odata-v4';

/**
 * FileSignatureUrl
 */
export interface FileSignatureUrl<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> {
  /**
   * Storage Item Id.
   */
  storageItemId: DeserializedType<DeSerializersT, 'Edm.String'>;
  /**
   * File Id.
   */
  fileId: DeserializedType<DeSerializersT, 'Edm.String'>;
  /**
   * Signature Url.
   */
  signatureUrl: DeserializedType<DeSerializersT, 'Edm.String'>;
}

/**
 * FileSignatureUrlField
 * @typeParam EntityT - Type of the entity the complex type field belongs to.
 */
export class FileSignatureUrlField<
  EntityT extends Entity,
  DeSerializersT extends DeSerializers = DefaultDeSerializers,
  NullableT extends boolean = false,
  SelectableT extends boolean = false
> extends ComplexTypeField<
  EntityT,
  DeSerializersT,
  FileSignatureUrl,
  NullableT,
  SelectableT
> {
  private _fieldBuilder: FieldBuilder<this, DeSerializersT> = new FieldBuilder(
    this,
    this.deSerializers
  );
  /**
   * Representation of the {@link FileSignatureUrl.storageItemId} property for query construction.
   * Use to reference this property in query operations such as 'filter' in the fluent request API.
   */
  storageItemId: OrderableEdmTypeField<
    EntityT,
    DeSerializersT,
    'Edm.String',
    false,
    false
  > = this._fieldBuilder.buildEdmTypeField(
    'storageItemId',
    'Edm.String',
    false
  );
  /**
   * Representation of the {@link FileSignatureUrl.fileId} property for query construction.
   * Use to reference this property in query operations such as 'filter' in the fluent request API.
   */
  fileId: OrderableEdmTypeField<
    EntityT,
    DeSerializersT,
    'Edm.String',
    false,
    false
  > = this._fieldBuilder.buildEdmTypeField('fileId', 'Edm.String', false);
  /**
   * Representation of the {@link FileSignatureUrl.signatureUrl} property for query construction.
   * Use to reference this property in query operations such as 'filter' in the fluent request API.
   */
  signatureUrl: OrderableEdmTypeField<
    EntityT,
    DeSerializersT,
    'Edm.String',
    false,
    false
  > = this._fieldBuilder.buildEdmTypeField('signatureUrl', 'Edm.String', false);

  /**
   * Creates an instance of FileSignatureUrlField.
   * @param fieldName - Actual name of the field as used in the OData request.
   * @param fieldOf - Either the parent entity constructor of the parent complex type this field belongs to.
   */
  constructor(
    fieldName: string,
    fieldOf: ConstructorOrField<EntityT>,
    deSerializers: DeSerializersT,
    fieldOptions?: FieldOptions<NullableT, SelectableT>
  ) {
    super(fieldName, fieldOf, deSerializers, FileSignatureUrl, fieldOptions);
  }
}

export namespace FileSignatureUrl {
  /**
   * Metadata information on all properties of the `FileSignatureUrl` complex type.
   */
  export const _propertyMetadata: PropertyMetadata<FileSignatureUrl>[] = [
    {
      originalName: 'storageItemId',
      name: 'storageItemId',
      type: 'Edm.String',
      isCollection: false
    },
    {
      originalName: 'fileId',
      name: 'fileId',
      type: 'Edm.String',
      isCollection: false
    },
    {
      originalName: 'signatureUrl',
      name: 'signatureUrl',
      type: 'Edm.String',
      isCollection: false
    }
  ];
}
