{"name": "", "version": "", "private": true, "packageManager": "pnpm@9.5.0", "workspaces": ["packages/*", "play", "docs"], "publishConfig": {"registry": "http://*************:8081/repository/npm-hosted/"}, "scripts": {"cz": "czg", "test": "vitest", "test:coverage": "vitest --coverage", "test:ssr": "vitest --config ./ssr-testing/vitest.config.ts", "prepare:e2e": "if [ ! -d \"docs/.vitepress/dist\" ]; then pnpm run docs:build; fi;", "dev": "pnpm -C play dev", "gen:version": "tsx scripts/gen-version.ts", "diff:table": "tsx scripts/build-table.ts", "update:version": "tsx scripts/update-version.ts", "clean": "pnpm run clean:dist && pnpm run -r --parallel clean", "clean:dist": "<PERSON><PERSON><PERSON> dist", "build": "pnpm run -C internal/build start", "build:theme": "pnpm run -C packages/theme-chalk build", "format": "prettier --write --cache .", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx,.md,.json --max-warnings 0 --cache", "lint:fix": "pnpm run lint --fix", "lint:commit": "commitlint", "typecheck": "pnpm run \"/^typecheck:/\"", "typecheck:web": "vue-tsc -p tsconfig.web.json --composite false --noEmit", "typecheck:play": "vue-tsc -p tsconfig.play.json --composite false --noEmit", "typecheck:node": "tsc -p tsconfig.node.json --noEmit", "typecheck:vite-config": "vue-tsc -p tsconfig.vite-config.json --composite false --noEmit", "typecheck:vitest": "vue-tsc -p tsconfig.vitest.json --composite false --noEmit", "stub": "pnpm run -r --parallel stub", "prepare": "husky", "postinstall": "pnpm stub && concurrently \"pnpm gen:version\" ", "gen-odata": "odata2ts ", "build-esm": "node scripts/build-esm.js", "build:odata": "npm run gen-odata && npm run build-esm", "gen:odata": "node scripts/gen-odata.js", "mis": "node scripts/find-missing-return-types.js"}, "peerDependencies": {"axios": "^1.6.0", "vue": "^3.2.0"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@neue-plus/components": "workspace:*", "@neue-plus/constants": "workspace:*", "@neue-plus/directives": "workspace:*", "@neue-plus/hooks": "workspace:*", "@neue-plus/locale": "workspace:*", "@neue-plus/resolver": "workspace:*", "@neue-plus/theme-chalk": "workspace:*", "@neue-plus/utils": "workspace:*", "@neue-plus/widgets": "workspace:*", "@odata/client": "^2.21.10", "@odata2ts/http-client-axios": "^0.12.0", "@odata2ts/http-client-fetch": "^0.9.0", "@odata2ts/odata-query-builder": "^0.18.2", "@odata2ts/odata-service": "^0.22.1", "@odata2ts/odata-uri-builder": "^0.14.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@vueuse/core": "^10.11.0", "async-validator": "^4.2.5", "dayjs": "^1.11.13", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "neue-plus": "workspace:*", "normalize-wheel-es": "^1.2.0", "odata-query": "^8.0.3", "vue-request": "^2.0.4", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^19.0.0", "@commitlint/config-conventional": "^19.0.0", "@neue-plus/build": "workspace:^0.0.1", "@neue-plus/build-utils": "workspace:^0.0.1", "@neue-plus/eslint-config": "workspace:*", "@odata2ts/odata2ts": "^0.39.1", "@pnpm/find-workspace-packages": "^4.0.16", "@pnpm/logger": "^4.0.0", "@pnpm/types": "^8.4.0", "@rollup/plugin-url": "^8.0.2", "@sap-cloud-sdk/generator": "^4.1.1", "@sap-cloud-sdk/odata-v4": "^4.1.1", "@types/fs-extra": "^9.0.13", "@types/gulp": "^4.0.9", "@types/jsdom": "^16.2.14", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^2.3.3", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "@vue/test-utils": "^2.0.0", "@vue/tsconfig": "^0.1.3", "c8": "^7.11.3", "chalk": "^5.0.1", "concurrently": "^7.2.2", "consola": "^2.15.3", "csstype": "^2.6.20", "cz-git": "^1.11.1", "czg": "^1.11.1", "eslint": "^8.57.1", "eslint-define-config": "^1.5.1", "expect-type": "^0.13.0", "fast-glob": "^3.2.11", "fast-xml-parser": "^5.2.5", "happy-dom": "^17.4.4", "husky": "^9.0.11", "jsdom": "16.4.0", "lint-staged": "^15.5.1", "prettier": "^2.7.1", "puppeteer": "^17.1.3", "resize-observer-polyfill": "^1.5.1", "rimraf": "^3.0.2", "rollup-plugin-postcss": "^4.0.2", "sass": "^1.79.3", "tsx": "^4.19.3", "type-fest": "^2.14.0", "typescript": "~5.5.4", "unplugin-vue-macros": "^2.7.11", "vitest": "^2.0.5", "vue": "^3.2.37", "vue-router": "^4.0.16", "vue-tsc": "^2.0.28"}, "engines": {"node": ">= 20"}, "config": {"commitizen": {"path": "./node_modules/cz-git"}}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["vite", "react", "react-dom"]}, "overrides": {"typescript": "$typescript", "jwa": "^1.4.2"}}, "lint-staged": {"*.{vue,js,ts,jsx,tsx,md,json}": ["eslint --fix", "prettier --write"]}}