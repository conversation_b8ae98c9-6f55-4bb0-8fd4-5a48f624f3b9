﻿/** @module odatajs/xml */

export const http = 'http://'
export const w3org = `${http}www.w3.org/`
export const xhtmlNS = `${w3org}1999/xhtml`
export const xmlnsNS = `${w3org}2000/xmlns/`
export const xmlNS = `${w3org}XML/1998/namespace`
export const mozillaParserErroNS = `${http}www.mozilla.org/newlayout/xml/parsererror.xml`

/**
 * 判断字符串是否有首尾空白
 */
export function hasLeadingOrTrailingWhitespace(text: string): boolean {
  const re = /(^\s)|(\s$)/
  return re.test(text)
}

/**
 * 判断是否为空白字符串或 null
 */
export function isWhitespace(text: string | null): boolean {
  const ws = /^\s*$/
  return text === null || ws.test(text)
}

/**
 * 判断元素及父元素中是否存在 xml:space="preserve"
 */
export function isWhitespacePreserveContext(
  domElement: Element | null
): boolean {
  while (domElement && domElement.nodeType === 1) {
    const val = domElement.getAttributeNS(xmlNS, 'space')
    if (val === 'preserve') return true
    else if (val === 'default') break
    else domElement = domElement.parentElement
  }
  return false
}

/**
 * 判断属性是否为 xmlns 声明
 */
export function isXmlNSDeclaration(domAttribute: Attr): boolean {
  const nodeName = domAttribute.nodeName
  return nodeName === 'xmlns' || nodeName.startsWith('xmlns:')
}

/**
 * 抛出 XML 解析错误
 */
export function xmlThrowParserError(
  exceptionOrReason: any,
  srcText?: string,
  errorXmlText?: string
): never {
  if (typeof exceptionOrReason === 'string') {
    exceptionOrReason = { message: exceptionOrReason }
  }
  throw {
    ...exceptionOrReason,
    srcText: srcText || '',
    errorXmlText: errorXmlText || '',
  }
}

/**
 * 获取元素本地名称
 */
export function xmlLocalName(elem: Element): string {
  return elem.localName || elem.nodeName
}

/**
 * 获取第一个子元素
 */
export function xmlFirstChildElement(
  elem: Element,
  ns?: string,
  localName?: string
): Element | null {
  if (!elem) return null
  let child = elem.firstElementChild
  while (child) {
    if (
      (!ns || child.namespaceURI === ns) &&
      (!localName || xmlLocalName(child) === localName)
    ) {
      return child
    }
    child = child.nextElementSibling
  }
  return null
}

/**
 * 获取元素第一个后代元素
 */
export function xmlFirstDescendantElement(
  elem: Element,
  ns?: string,
  localName?: string
): Element | null {
  const children = elem.children
  for (const child of children) {
    if (
      (!ns || child.namespaceURI === ns) &&
      (!localName || xmlLocalName(child) === localName)
    ) {
      return child
    }
    const found = xmlFirstDescendantElement(child, ns, localName)
    if (found) return found
  }
  return null
}

/**
 * 获取元素文本内容
 */
export function xmlNodeValue(node: Node): string {
  return node.nodeValue || ''
}

/**
 * 获取元素内部文本
 */
export function xmlInnerText(elem: Element): string {
  return elem.textContent || ''
}

/**
 * 浏览器端解析 XML
 */
export function xmlParse(text: string): Document {
  if (!window.DOMParser) {
    xmlThrowParserError('DOMParser is not supported in this browser.')
  }
  let dom: Document
  try {
    dom = new window.DOMParser().parseFromString(text, 'text/xml')
  } catch (e) {
    xmlThrowParserError(e, '', text)
  }

  const element = dom.documentElement
  const nsURI = element.namespaceURI
  const localName = xmlLocalName(element)

  if (localName === 'parsererror' && nsURI === mozillaParserErroNS) {
    const srcTextElement = xmlFirstChildElement(
      element,
      mozillaParserErroNS,
      'sourcetext'
    )
    const srcText = srcTextElement ? xmlNodeValue(srcTextElement) : ''
    xmlThrowParserError(xmlInnerText(element) || '', srcText, text)
  }

  return dom
}
