﻿<!--
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 -->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>OData Fuzzing Tests</title>
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="expires" content="-1" />
    <script type="text/javascript" src="http://cdnjs.cloudflare.com/ajax/libs/json2/20110223/json2.js"></script>
    <script type="text/javascript" src="http://code.jquery.com/jquery-1.4.4.js"></script>
    <script type="text/javascript" src="../_build/lib/odatajs-latest.js"></script>
    <script type="text/javascript" src="common/common.js"></script>
    <script type="text/javascript">

        var testCsdl = '' +
        '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\r\n' +
        '<edmx:Edmx Version="1.0" xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx">\r\n' +
        '  <edmx:DataServices xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" m:DataServiceVersion="2.0">\r\n' +
        '    <Schema Namespace="TestCatalog.Model" xmlns:d="http://schemas.microsoft.com/ado/2007/08/dataservices" xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns="http://schemas.microsoft.com/ado/2008/09/edm">\r\n' +
        '      <EntityType Name="Genre">\r\n' +
        '        <Key><PropertyRef Name="Name" /></Key>\r\n' +
        '        <Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" m:FC_TargetPath="SyndicationTitle" />\r\n' +
        '        <NavigationProperty Name="Titles" Relationship="TestCatalog.Model.TitleGenres" FromRole="Genres" ToRole="Titles" />\r\n' +
        '      </EntityType>\r\n' +
        '      <EntityType Name="Language">\r\n' +
        '        <Key><PropertyRef Name="Name" /></Key>\r\n' +
        '        <Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="80" Unicode="false" FixedLength="false" m:FC_TargetPath="SyndicationTitle" />\r\n' +
        '        <NavigationProperty Name="Titles" Relationship="TestCatalog.Model.TitleLanguages" FromRole="Language" ToRole="Titles" />\r\n' +
        '      </EntityType>\r\n' +
        '      <EntityType Name="Person">\r\n' +
        '        <Key><PropertyRef Name="Id" /></Key>\r\n' +
        '        <Property Name="Id" Type="Edm.Int32" Nullable="false" />\r\n' +
        '        <Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="80" Unicode="true" FixedLength="false" m:FC_TargetPath="SyndicationTitle" />\r\n' +
        '        <NavigationProperty Name="Awards" Relationship="TestCatalog.Model.FK_TitleAward_Person" FromRole="People" ToRole="TitleAwards" />\r\n' +
        '        <NavigationProperty Name="TitlesActedIn" Relationship="TestCatalog.Model.TitleActors" FromRole="People" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="TitlesDirected" Relationship="TestCatalog.Model.TitleDirectors" FromRole="People" ToRole="Titles" />\r\n' +
        '      </EntityType>\r\n' +
        '      <EntityType Name="TitleAudioFormat">\r\n' +
        '        <Key><PropertyRef Name="TitleId" /><PropertyRef Name="DeliveryFormat" /><PropertyRef Name="Language" /><PropertyRef Name="Format" /></Key>\r\n' +
        '        <Property Name="TitleId" Type="Edm.String" Nullable="false" MaxLength="30" FixedLength="false" />\r\n' +
        '        <Property Name="DeliveryFormat" Type="Edm.String" Nullable="false" MaxLength="10" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Language" Type="Edm.String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Format" Type="Edm.String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />\r\n' +
        '        <NavigationProperty Name="Title" Relationship="TestCatalog.Model.FK_TitleAudioFormat_Title" FromRole="TitleAudioFormats" ToRole="Titles" />\r\n' +
        '      </EntityType>\r\n' +
        '      <EntityType Name="TitleAward">\r\n' +
        '        <Key><PropertyRef Name="Id" /></Key>\r\n' +
        '        <Property Name="Id" Type="Edm.Guid" Nullable="false" />\r\n' +
        '        <Property Name="Type" Type="Edm.String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Category" Type="Edm.String" Nullable="false" MaxLength="60" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Year" Type="Edm.Int32" Nullable="true" />\r\n' +
        '        <Property Name="Won" Type="Edm.Boolean" Nullable="false" />\r\n' +
        '        <NavigationProperty Name="Title" Relationship="TestCatalog.Model.FK_TitleAward_Title" FromRole="TitleAwards" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="Person" Relationship="TestCatalog.Model.FK_TitleAward_Person" FromRole="TitleAwards" ToRole="People" />\r\n' +
        '      </EntityType>\r\n' +
        '      <EntityType Name="Title" m:HasStream="true">\r\n' +
        '        <Key><PropertyRef Name="Id" /></Key>\r\n' +
        '        <Property Name="Id" Type="Edm.String" Nullable="false" MaxLength="30" FixedLength="false" />\r\n' +
        '        <Property Name="Synopsis" Type="Edm.String" Nullable="true" MaxLength="Max" Unicode="false" FixedLength="false" m:FC_TargetPath="SyndicationSummary" m:FC_ContentKind="html" />\r\n' +
        '        <Property Name="ShortSynopsis" Type="Edm.String" Nullable="true" MaxLength="Max" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="AverageRating" Type="Edm.Double" Nullable="true" />\r\n' +
        '        <Property Name="ReleaseYear" Type="Edm.Int32" Nullable="true" />\r\n' +
        '        <Property Name="Url" Type="Edm.String" Nullable="true" MaxLength="200" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Runtime" Type="Edm.Int32" Nullable="true" />\r\n' +
        '        <Property Name="Rating" Type="Edm.String" Nullable="true" MaxLength="10" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="DateModified" Type="Edm.DateTime" Nullable="false" ConcurrencyMode="Fixed" m:FC_TargetPath="SyndicationUpdated" m:FC_KeepInContent="false" />\r\n' +
        '        <Property Name="Type" Type="Edm.String" Nullable="false" MaxLength="8" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="BoxArt" Type="TestCatalog.Model.BoxArt" Nullable="false" />\r\n' +
        '        <Property Name="ShortName" Type="Edm.String" Nullable="false" MaxLength="200" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="200" Unicode="false" FixedLength="false" m:FC_TargetPath="SyndicationTitle" />\r\n' +
        '        <Property Name="Instant" Type="TestCatalog.Model.InstantAvailability" Nullable="false" />\r\n' +
        '        <Property Name="Dvd" Type="TestCatalog.Model.DeliveryFormatAvailability" Nullable="false" />\r\n' +
        '        <Property Name="BluRay" Type="TestCatalog.Model.DeliveryFormatAvailability" Nullable="false" />\r\n' +
        '        <Property Name="TinyUrl" Type="Edm.String" Nullable="false" MaxLength="30" />\r\n' +
        '        <Property Name="WebsiteUrl" Type="Edm.String" Nullable="true" MaxLength="200" />\r\n' +
        '        <Property Name="TestApiId" Type="Edm.String" Nullable="false" MaxLength="200" />\r\n' +
        '        <NavigationProperty Name="AudioFormats" Relationship="TestCatalog.Model.FK_TitleAudioFormat_Title" FromRole="Titles" ToRole="TitleAudioFormats" />\r\n' +
        '        <NavigationProperty Name="Awards" Relationship="TestCatalog.Model.FK_TitleAward_Title" FromRole="Titles" ToRole="TitleAwards" />\r\n' +
        '        <NavigationProperty Name="Disc" Relationship="TestCatalog.Model.FK_Title_Disc" FromRole="Titles1" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="Movie" Relationship="TestCatalog.Model.FK_Title_Movie" FromRole="Titles1" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="Season" Relationship="TestCatalog.Model.FK_Title_Season" FromRole="Titles1" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="Series" Relationship="TestCatalog.Model.FK_Title_Series" FromRole="Titles1" ToRole="Titles" />\r\n' +
        '        <NavigationProperty Name="ScreenFormats" Relationship="TestCatalog.Model.FK_TitleScreenFormat_Title" FromRole="Titles" ToRole="TitleScreenFormats" />\r\n' +
        '        <NavigationProperty Name="Cast" Relationship="TestCatalog.Model.TitleActors" FromRole="Titles" ToRole="People" />\r\n' +
        '        <NavigationProperty Name="Languages" Relationship="TestCatalog.Model.TitleLanguages" FromRole="Titles" ToRole="Language" />\r\n' +
        '        <NavigationProperty Name="Directors" Relationship="TestCatalog.Model.TitleDirectors" FromRole="Titles" ToRole="People" />\r\n' +
        '        <NavigationProperty Name="Genres" Relationship="TestCatalog.Model.TitleGenres" FromRole="Titles" ToRole="Genres" />\r\n' +
        '      </EntityType>\r\n' +
        '      <ComplexType Name="BoxArt">\r\n' +
        '        <Property Name="SmallUrl" Type="Edm.String" Nullable="true" MaxLength="80" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="MediumUrl" Type="Edm.String" Nullable="true" MaxLength="80" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="LargeUrl" Type="Edm.String" Nullable="true" MaxLength="80" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="HighDefinitionUrl" Type="Edm.String" Nullable="true" MaxLength="80" Unicode="false" FixedLength="false" />\r\n' +
        '      </ComplexType>\r\n' +
        '      <ComplexType Name="InstantAvailability">\r\n' +
        '        <Property Name="Available" Type="Edm.Boolean" Nullable="false" />\r\n' +
        '        <Property Name="AvailableFrom" Type="Edm.DateTime" Nullable="true" />\r\n' +
        '        <Property Name="AvailableTo" Type="Edm.DateTime" Nullable="true" />\r\n' +
        '        <Property Name="HighDefinitionAvailable" Type="Edm.Boolean" Nullable="false" />\r\n' +
        '        <Property Name="Runtime" Type="Edm.Int32" Nullable="true" />\r\n' +
        '        <Property Name="Rating" Type="Edm.String" Nullable="true" MaxLength="10" Unicode="false" FixedLength="false" />\r\n' +
        '      </ComplexType>\r\n' +
        '      <ComplexType Name="DeliveryFormatAvailability">\r\n' +
        '        <Property Name="Available" Type="Edm.Boolean" Nullable="false" />\r\n' +
        '        <Property Name="AvailableFrom" Type="Edm.DateTime" Nullable="true" />\r\n' +
        '        <Property Name="AvailableTo" Type="Edm.DateTime" Nullable="true" />\r\n' +
        '        <Property Name="Runtime" Type="Edm.Int32" Nullable="true" />\r\n' +
        '        <Property Name="Rating" Type="Edm.String" Nullable="true" MaxLength="10" Unicode="false" FixedLength="false" />\r\n' +
        '      </ComplexType>\r\n' +
        '      <EntityType Name="TitleScreenFormat">\r\n' +
        '        <Key><PropertyRef Name="TitleId" /><PropertyRef Name="DeliveryFormat" /><PropertyRef Name="Format" /></Key>\r\n' +
        '        <Property Name="TitleId" Type="Edm.String" Nullable="false" MaxLength="30" FixedLength="false" />\r\n' +
        '        <Property Name="DeliveryFormat" Type="Edm.String" Nullable="false" MaxLength="10" Unicode="false" FixedLength="false" />\r\n' +
        '        <Property Name="Format" Type="Edm.String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />\r\n' +
        '        <NavigationProperty Name="Title" Relationship="TestCatalog.Model.FK_TitleScreenFormat_Title" FromRole="TitleScreenFormats" ToRole="Titles" />\r\n' +
        '      </EntityType>\r\n' +
        '      <Association Name="FK_TitleAudioFormat_Title">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="1" />\r\n' +
        '        <End Role="TitleAudioFormats" Type="TestCatalog.Model.TitleAudioFormat" Multiplicity="*" />\r\n' +
        '        <ReferentialConstraint>\r\n' +
        '          <Principal Role="Titles"><PropertyRef Name="Id" /></Principal>\r\n' +
        '          <Dependent Role="TitleAudioFormats"><PropertyRef Name="TitleId" /></Dependent>\r\n' +
        '        </ReferentialConstraint>\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_TitleAward_Title">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="1" />\r\n' +
        '        <End Role="TitleAwards" Type="TestCatalog.Model.TitleAward" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_TitleAward_Person">\r\n' +
        '        <End Role="People" Type="TestCatalog.Model.Person" Multiplicity="0..1" />\r\n' +
        '        <End Role="TitleAwards" Type="TestCatalog.Model.TitleAward" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_Title_Disc">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="0..1" />\r\n' +
        '        <End Role="Titles1" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_Title_Movie">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="0..1" />\r\n' +
        '        <End Role="Titles1" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_Title_Season">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="0..1" />\r\n' +
        '        <End Role="Titles1" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_Title_Series">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="0..1" />\r\n' +
        '        <End Role="Titles1" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="FK_TitleScreenFormat_Title">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="1" />\r\n' +
        '        <End Role="TitleScreenFormats" Type="TestCatalog.Model.TitleScreenFormat" Multiplicity="*" />\r\n' +
        '        <ReferentialConstraint>\r\n' +
        '          <Principal Role="Titles"><PropertyRef Name="Id" /></Principal>\r\n' +
        '          <Dependent Role="TitleScreenFormats"><PropertyRef Name="TitleId" /></Dependent>\r\n' +
        '        </ReferentialConstraint>\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="TitleActors">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '        <End Role="People" Type="TestCatalog.Model.Person" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="TitleLanguages">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '        <End Role="Language" Type="TestCatalog.Model.Language" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="TitleDirectors">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '        <End Role="People" Type="TestCatalog.Model.Person" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '      <Association Name="TitleGenres">\r\n' +
        '        <End Role="Titles" Type="TestCatalog.Model.Title" Multiplicity="*" />\r\n' +
        '        <End Role="Genres" Type="TestCatalog.Model.Genre" Multiplicity="*" />\r\n' +
        '      </Association>\r\n' +
        '    </Schema>\r\n' +
        '    <Schema Namespace="Test.Catalog" xmlns:d="http://schemas.microsoft.com/ado/2007/08/dataservices" xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns="http://schemas.microsoft.com/ado/2008/09/edm">\r\n' +
        '      <EntityContainer Name="TestCatalog" m:IsDefaultEntityContainer="true">\r\n' +
        '        <FunctionImport Name="Movies" EntitySet="Titles" ReturnType="Collection(TestCatalog.Model.Title)" m:HttpMethod="GET" />\r\n' +
        '        <FunctionImport Name="Series" EntitySet="Titles" ReturnType="Collection(TestCatalog.Model.Title)" m:HttpMethod="GET" />\r\n' +
        '        <FunctionImport Name="Seasons" EntitySet="Titles" ReturnType="Collection(TestCatalog.Model.Title)" m:HttpMethod="GET" />\r\n' +
        '        <FunctionImport Name="Discs" EntitySet="Titles" ReturnType="Collection(TestCatalog.Model.Title)" m:HttpMethod="GET" />\r\n' +
        '        <FunctionImport Name="Episodes" EntitySet="Titles" ReturnType="Collection(TestCatalog.Model.Title)" m:HttpMethod="GET" />\r\n' +
        '        <EntitySet Name="Genres" EntityType="TestCatalog.Model.Genre" />\r\n' +
        '        <EntitySet Name="Languages" EntityType="TestCatalog.Model.Language" />\r\n' +
        '        <EntitySet Name="People" EntityType="TestCatalog.Model.Person" />\r\n' +
        '        <EntitySet Name="TitleAudioFormats" EntityType="TestCatalog.Model.TitleAudioFormat" />\r\n' +
        '        <EntitySet Name="TitleAwards" EntityType="TestCatalog.Model.TitleAward" />\r\n' +
        '        <EntitySet Name="Titles" EntityType="TestCatalog.Model.Title" />\r\n' +
        '        <EntitySet Name="TitleScreenFormats" EntityType="TestCatalog.Model.TitleScreenFormat" />\r\n' +
        '        <AssociationSet Name="FK_TitleAudioFormat_Title" Association="TestCatalog.Model.FK_TitleAudioFormat_Title">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="TitleAudioFormats" EntitySet="TitleAudioFormats" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_TitleAward_Title" Association="TestCatalog.Model.FK_TitleAward_Title">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="TitleAwards" EntitySet="TitleAwards" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_TitleAward_Person" Association="TestCatalog.Model.FK_TitleAward_Person">\r\n' +
        '          <End Role="People" EntitySet="People" />\r\n' +
        '          <End Role="TitleAwards" EntitySet="TitleAwards" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_Title_Disc" Association="TestCatalog.Model.FK_Title_Disc">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Titles1" EntitySet="Titles" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_Title_Movie" Association="TestCatalog.Model.FK_Title_Movie">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Titles1" EntitySet="Titles" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_Title_Season" Association="TestCatalog.Model.FK_Title_Season">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Titles1" EntitySet="Titles" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_Title_Series" Association="TestCatalog.Model.FK_Title_Series">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Titles1" EntitySet="Titles" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="FK_TitleScreenFormat_Title" Association="TestCatalog.Model.FK_TitleScreenFormat_Title">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="TitleScreenFormats" EntitySet="TitleScreenFormats" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="TitleActors" Association="TestCatalog.Model.TitleActors">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="People" EntitySet="People" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="TitleLanguages" Association="TestCatalog.Model.TitleLanguages">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Language" EntitySet="Languages" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="TitleDirectors" Association="TestCatalog.Model.TitleDirectors">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="People" EntitySet="People" />\r\n' +
        '        </AssociationSet>\r\n' +
        '        <AssociationSet Name="TitleGenres" Association="TestCatalog.Model.TitleGenres">\r\n' +
        '          <End Role="Titles" EntitySet="Titles" />\r\n' +
        '          <End Role="Genres" EntitySet="Genres" />\r\n' +
        '        </AssociationSet>\r\n' +
        '      </EntityContainer>\r\n' +
        '    </Schema>\r\n' +
        '  </edmx:DataServices>\r\n' +
        '</edmx:Edmx>\r\n' +
        '';


        var iterationsRemaining = 0;
        var iterationsPerRun = 100000;
        var failureCount = 0;
        var canary = false;

        function canaryFn() {
            canary = true;
        }

        var buildSumAndNormalize = function (table) {
            // Sum all weights.
            var total = 0;
            for (var i = 0; i < table.length; i++) {
                total += table[i].w;
            }

            // Record a normalized weight (nw) and the step-ladder threshold.
            var last = 0;
            for (var i = 0; i < table.length; i++) {
                table[i].nw = (table[i].w / total);

                last += table[i].nw;
                table[i].sl = last;
            }

            table.total = total;
        };

        var randomFromWeighted = function (table) {
            if (table.total === undefined) {
                buildSumAndNormalize(table);
            }

            var number = Math.random();
            for (var i = 0; i < table.length; i++) {
                if (number < table[i].sl) {
                    return table[i].v;
                }
            }

            return table[table.length - 1].v;
        };

        // Create a table with relative weight w and value v for text generation.
        var fuzzingTextTable = [
            { w: 3, v: "canary=true" },
            { w: 2, v: "canary=true;" },
            { w: 3, v: "canary=1" },
            { w: 1, v: "window.canary=1" },
            { w: 2, v: "canary=[]" },
            { w: 2, v: "canary={}" },
            { w: 1, v: "canaryFn" },
            { w: 2, v: "canaryFn()" }
        ];

        var fuzzingTextOpTable = [
            { w: 1, v: "replace" },
            { w: 3, v: "insert" }
        ];

        /** Fuzzes the specified string.
         * @param {String} text - Text to fuzz.
         * @returns {String} The fuzzes text.
         */
        var fuzzText = function (text) {

            var location = Math.round(Math.random() * text.length - 1);
            var content = randomFromWeighted(fuzzingTextTable);
            var op = randomFromWeighted(fuzzingTextOpTable);
            if (op === "replace") {
                text = text.substr(0, location) + content + text.substr(location + content.length);
            } else if (op === "insert") {
                text = text.substr(0, location) + content + text.substr(location);
            }

            return text;
        };

        var rarely = function (rare, common) {
            if (Math.random() < 0.1) {
                return rare;
            } else {
                return common;
            }
        };

        var gibberish = function () {
            var len = Math.random() * 100;
            var result = "";
            for (var i = 0; i < len; i++) {
                result += String.fromCharCode(Math.round(Math.random() * 65000));
            }

            return result;
        };

        var generateResponse = function (suggestedContentType, body) {
            var url = rarely(null, "http://foo/bar");
            var statusCode = rarely((Math.random() * 1000).toString().substr(0, 3), 200);
            var statusText = rarely(gibberish(), "OK");
            var headers = [];
            headers["Content-Length"] = rarely(Math.random() * 200, undefined);
            headers["Content-Type"] = rarely("foo bar baz", suggestedContentType);


            return { requestUri: url, statusCode: statusCode, statusText: statusText, headers: headers, body: body };
        };

        var generateRandom = function () {
            var body = gibberish();
            return generateResponse("application/octet-stream", body);
        };

        var generateServiceDoc = function() {
            var body = '<?xml version="1.0" encoding="utf-8" standalone="yes" ?>\r\n' +
                        '<service xml:base="http://services.odata.org/OData/OData.svc/"  xmlns:atom="http://www.w3.org/2005/Atom" ' +
                        ' xmlns:app="http://www.w3.org/2007/app" xmlns="http://www.w3.org/2007/app">' +
                        '<workspace><atom:title>Default</atom:title> <collection href="Products"><atom:title>Products</atom:title></collection>' +
                        '<collection href="Categories"><atom:title>Categories</atom:title> </collection><collection href="Suppliers"><atom:title>Suppliers</atom:title> </collection></workspace></service>';
            body = fuzzText(body);
            return generateResponse("application/atomsvc+xml", body);
        };

        var generateMetadata = function () {
            var body = testCsdl;
            body = fuzzText(body);
            return generateResponse("application/xml", body);
        };

        var generateError = function () {
            var body = testCsdl;
            body = fuzzText(body);
            return generateResponse("application/xml", body);
        };

        var generatePlainText = function () {
            var body = gibberish();
            return generateResponse("plain/text", body);
        };

        var generateBatch = function () {
            var body = gibberish();
            return generateResponse("multipart/mixed; boundary=batch(123)", body);
        };

        var generateXml = function () {
            // TODO: consider doing something about an XHTML section here
            var body = '' +
                '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\r\n' +
                '<feed xml:base=http://services.odata.org/OData/OData.svc/' +
                '     xmlns:d=http://schemas.microsoft.com/ado/2007/08/dataservices' +
                '     xmlns:m=http://schemas.microsoft.com/ado/2007/08/dataservices/metadata' +
                '     xmlns="http://www.w3.org/2005/Atom">' +
                '  <title type="text">Categories</title>' +
                '  <id>http://services.odata.org/OData/OData.svc/Categories</id>' +
                '  <updated>2010-03-10T08:38:14Z</updated>' +
                '  <link rel="self" title="Categories" href="Categories" />' +
                '  <entry>' +
                '    <id>http://services.odata.org/OData/OData.svc/Categories(0)</id>' +
                '    <title type="text">Food</title>' +
                '    <updated>2010-03-10T08:38:14Z</updated>' +
                '    <author><name /></author>' +
                '    <link rel="edit" title="Category" href="Categories(0)" />' +
                '    <link rel="http://schemas.microsoft.com/ado/2007/08/dataservices/related/Products"' +
                '            type="application/atom+xml;type=feed"' +
                '            title="Products" href="Categories(0)/Products" />' +
                '    <category term="ODataDemo.Category"' +
                '            scheme="http://schemas.microsoft.com/ado/2007/08/dataservices/scheme" />' +
                '    <content type="application/xml">' +
                '      <m:properties>' +
                '        <d:ID m:type="Edm.Int32">0</d:ID>' +
                '        <d:Name>Food</d:Name>' +
                '        <d:AverageRating m:type="Edm.Double">3.6</d:AverageRating>' +
                '        <d:AvailableFrom m:type="Edm.DateTime">1999-03-23T00:00:00</d:AvailableFrom>' +
                '        <d:Name>Food</d:Name>' +
                '      </m:properties>' +
                '    </content>' +
                '  </entry>' +
                '  <!-- <entry> elements representing additional Categories go here -->' +
                '</feed>';
            return generateResponse("application/atom+xml", body);
        };

        var generateJson = function () {
            var body =  '{ "d" : { ' +
                        '"results": [ ' +
                        '{ ' +
                        '"__metadata": {  ' +
                        '"uri": "http://services.odata.org/OData/OData.svc/Categories(0)",  ' +
                        '"type": "DataServiceProviderDemo.Category" ' +
                        '}, ' +
                        '"ID": 0, ' +
                        '"Name": "Food", ' +
                        '"SomeDate": "Date(123)", ' +
                        '"Products": { ' +
                        '"__deferred": { ' +
                        '"uri": "http://services.odata.org/OData/OData.svc/Categories(0)/Products" ' +
                        '} } } ], ' +
                        '"__count": "3", ' +
                        '"__next": "http://services.odata.org/OData/OData.svc$skiptoken=12" ' +
                        '} } ' +
                        '';
            return generateResponse("application/json", body);
        };

        // Create a table with relative weight w and value v for response generation.
        var formatTable = [
            { w:  1, v: generateRandom },
            { w:  2, v: generateError },
            { w:  5, v: generatePlainText },
            { w:  5, v: generateServiceDoc },
            { w: 10, v: generateBatch },
            { w: 10, v: generateMetadata },
            { w: 40, v: generateXml },
            { w: 80, v: generateJson }
        ];

        var fakeHttpClient = {
            /** Performs a fake network request.
             * @param {Function} request - Request
             * @param {Function} success - Success callback with the response object.
             * @param {Function} error - Error callback with an error object.
             * @returns {Object} Object with an 'abort' method for the operation.
             */
            request: function (request, success, error) {
                var shouldError = rarely(true, false);
                var format = randomFromWeighted(formatTable);
                var response = format();

                window.setTimeout(function () {
                    if (shouldError) {
                        error(response);
                    } else {
                        success(response);
                    }
                }, 1);

                return {};
            }
        };

        var startTimeMs = 0;

        function startTest() {
            failureCount = 0;
            startTimeMs = new Date().getTime();
            iterationsRemaining = iterationsPerRun;
            nextIteration();
        }

        function readCallback() {
            // Success or failure don't actually matter.
            if (canary === true) {
                canary = false;
                failureCount++;
            }

            iterationsRemaining--;
            nextIteration();
        }

        function nextIteration() {
            if (iterationsRemaining === 0) {
                $("#fuzz-status").text("Tests complete. Failures: #" + failureCount);
                return;
            }

            if (iterationsRemaining % 50 === 0) {
                var text = "Running tests (pending=" + iterationsRemaining + " of " + iterationsPerRun;
                if (iterationsRemaining !== iterationsPerRun) {
                    var currentTimeMs = new Date().getTime();
                    var averageTestTimeMs = (iterationsPerRun - iterationsRemaining) / (currentTimeMs - startTimeMs);
                    var remainingTimeMs = iterationsRemaining * averageTestTimeMs;
                    text += ", ETA: " + remainingTimeMs + "ms, avg " + averageTestTimeMs + "ms";
                }

                text += "). Failures: #" + failureCount;
                $("#fuzz-status").text(text);
            }

            odatajs.oData.read("url", readCallback, readCallback, undefined, fakeHttpClient);
        }

        $(document).ready(function () {
            $("#start-button").click(startTest);
        });
    </script>

</head>
<body>
<h1>OData Fuzzing Tests</h1>
<p>
This page fuzzes the OData parsers in the odatajs library.
</p>
<button id='start-button'>Start</button>
<p id='fuzz-status'>&nbsp;</p>
</body>
</html>
