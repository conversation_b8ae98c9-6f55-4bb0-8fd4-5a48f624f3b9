// composables/useRequest.ts
import { ref } from 'vue'
import axios from './axios'
import type { AxiosRequestConfig } from 'axios'

export function useRequest<T = any>(config: AxiosRequestConfig) {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const run = async (overrideConfig?: AxiosRequestConfig) => {
    loading.value = true
    error.value = null
    try {
      const response = await axios.request<T>({ ...config, ...overrideConfig })
      data.value = response.data as any
    } catch (err: any) {
      error.value = err
    } finally {
      loading.value = false
    }
  }

  return { data, loading, error, run }
}
