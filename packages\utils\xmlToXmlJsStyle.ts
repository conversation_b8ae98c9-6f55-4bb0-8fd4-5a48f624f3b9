import { XMLParser } from 'fast-xml-parser'

/**
 * 将 XML 转换为 xml-js 风格的 JSON
 */
export function xmlToXmlJsStyle(xml: string) {
  const parser = new XMLParser({
    ignoreAttributes: false, // 保留属性
    attributeNamePrefix: '@', // 属性加 @ 前缀，方便统一处理
    parseAttributeValue: true, // 自动转换数字和布尔
    processEntities: true,
    ignoreDeclaration: false, // 保留 <?xml ... ?>
  })

  const json = parser.parse(xml)

  // 处理 ?xml → _declaration
  if (json['?xml']) {
    json._declaration = json['?xml']
    delete json['?xml']
  }

  // 递归处理节点，把属性放到 _attributes
  function convertAttributes(obj: any) {
    if (obj && typeof obj === 'object') {
      for (const key of Object.keys(obj)) {
        const val = obj[key]
        if (val && typeof val === 'object') {
          const attributes: Record<string, any> = {}
          for (const k of Object.keys(val)) {
            if (k.startsWith('@')) {
              attributes[k.slice(1)] = val[k] // 去掉 @ 前缀
              delete val[k]
            }
          }
          if (Object.keys(attributes).length) {
            val._attributes = attributes
          }
          convertAttributes(val) // 递归处理子节点
        }
      }
    }
  }

  convertAttributes(json)

  return json
}
