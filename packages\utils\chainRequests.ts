type RequestConfig<T = any, R = any> = {
  api: (params: T) => Promise<R>
  params?: T | ((prevResult: any) => T) // 可以传对象或函数
}

export async function chainRequests(configs: RequestConfig[]) {
  const results: any[] = []
  let prev: any = null

  for (const { api, params } of configs) {
    const realParams = typeof params === 'function' ? params(prev) : params

    const res = await api(realParams as any)
    results.push(res)
    prev = res
  }

  return results
}
