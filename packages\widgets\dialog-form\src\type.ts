import { dialogProps, formProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const widgetDialogFormProps = buildProps({
  ...dialogProps,
  ...formProps,
  formItems: {
    type: Array as () => Array<any>,
    default: () => [],
  },
  config: {
    type: Object as () => Record<string, any>,
    default: () => ({}),
  },
} as const)

export type WidgetDialogFormProps = ExtractPropTypes<
  typeof widgetDialogFormProps
>

// 事件类型
export type WidgetDialogFormEmits = {
  (e: 'beforeRender', node: any): void
  (e: 'afterRender', vnode: any, node: any): void
}

// import { buildProps } from '@neue-plus/utils'
// import type { ExtractPropTypes } from 'vue'

// export const widgetDialogFormProps = buildProps({} as const)

// export type WidgetDialogFormProps = ExtractPropTypes<
//   typeof widgetDialogFormProps
// >

// // 事件类型
// export type WidgetDialogFormEmits = {
//   (e: 'beforeRender', node: any): void
//   (e: 'afterRender', vnode: any, node: any): void
// }
