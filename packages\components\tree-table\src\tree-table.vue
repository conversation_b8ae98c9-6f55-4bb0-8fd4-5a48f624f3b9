<template>
  <el-table
    v-bind="{ ...tableProps, data: dataSource }"
    ref="table"
    v-loading="loading"
    :tree-props="treeProps"
    :row-key="rowKey"
    :default-expand-all="defaultExpandAll"
    :lazy="lazy"
    :load="load"
    :indent="indent"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
    @expand-change="handleExpandChange"
  >
    <ne-table-column
      v-for="column in visibleColumns"
      v-bind="column"
      :key="column.prop || column.label"
    />
    <el-table-column
      v-if="operationColumn"
      v-bind="operationColumn"
      label="操作"
    >
      <template #default="scope">
        <pro-button :record="scope" :items="operationColumn.actions" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { computed, defineExpose, onMounted, ref, watch } from 'vue'
import { useRequest } from 'vue-request'
// import { merge } from 'lodash-unified'
// import { useRequestQuery } from '@neue-plus/hooks'
// import { replaceTemplate } from '@neue-plus/utils'
import proButton from '../../pro-button/src/pro-button.vue'
import NeTableColumn from '../../table/src/table-column/index.vue'
import {
  useApisRef,
  useSysParamsRef,
} from '../../material-render/src/context/event-flow'
import { request } from '../../material-render/src/handlers/handleRequest'
import { neTreeTableProps } from './types'

defineOptions({
  name: 'NeTreeTable',
  inheritAttrs: false,
})

const props = defineProps(neTreeTableProps)
const table = ref<any>()

// 数据源
const dataSource = ref<any[]>()
const expand = ref(true)
const apisRef = useApisRef()
const sysParamsRef = useSysParamsRef()
const { data, loading, run } = useRequest(fetchData, {
  // refreshOnWindowFocus: true,
  // refocusTimespan: 1000, // 请求间隔时间
})
const tableProps = computed(() => {
  const { data, loading, bordered } = props
  return { data, loading, bordered }
})
// 可见列
const visibleColumns = computed(() => {
  return props.columns
    ?.filter(
      (column) => column.hideInTable !== true && column.prop !== 'operation'
    )
    .map((column) => ({
      ...column,
    }))
})

// 操作列
const operationColumn = computed(() => {
  const operation = props.columns.find((column) => column.prop === 'operation')
  return operation
})
function flattenRelations(list: any[]): any[] {
  return list.map((item) => {
    if (!item.to) return item
    const { to, ...rest } = item
    const { toRelations = [], ...toRest } = to
    return {
      ...toRest, // 把 name2 等字段提上来
      ...rest,
      toRelations: flattenRelations(toRelations), // 递归处理
    }
  })
}

watch(
  () => data.value,
  (result) => {
    dataSource.value = flattenRelations(result?.data?.toRelations || [])
    console.log(dataSource.value)
  }
)
// const { getRequestQuery } = useRequestQuery(visibleColumns.value)
// const filterFormat = () => {
//   const { filter = {} } = props.request.query || {}
//   const result = replaceTemplate(filter, merge({}, sysParamsRef))
//   return merge({}, result)
// }
async function fetchData() {
  // const queryFormat = filterFormat()
  if (props.request) {
    // const { expand = {} } = props.request.query || {}
    // const query = getRequestQuery(queryFormat)
    return request({
      ...props.request,
      replaceData: sysParamsRef.value,
    })
  } else if (typeof apisRef['request'] === 'function') {
    return apisRef['request']({
      replaceData: sysParamsRef.value,
      // query: getRequestQuery(queryFormat),
    })
  } else {
    dataSource.value = props.data
  }
}
// 切换展开/收起所有行
const toggleExpandAll = (collapse: boolean = expand.value) => {
  expand.value = !collapse

  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      table.value.toggleRowExpansion(node, collapse)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  dataSource.value && expandRecursive(dataSource.value)
}

// 刷新表格
const refresh = () => {
  run()
}
onMounted(() => {
  run()
})
// 暴露方法
defineExpose({
  toggleExpandAll,
  refresh,
  table,
})

// 监听数据变化
watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)

// 事件定义
const emit = defineEmits([
  'sort-change',
  'selection-change',
  'row-click',
  'expand-change',
])

// 事件处理
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  emit('sort-change', data)
}

const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

const handleRowClick = (row: any, column: any, event: Event) => {
  emit('row-click', row, column, event)
}

const handleExpandChange = (row: any, expandedRows: any[]) => {
  emit('expand-change', row, expandedRows)
}
</script>
