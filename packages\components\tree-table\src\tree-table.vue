<template>
  <el-table
    v-bind="{ ...tableProps, data: dataSource }"
    ref="table"
    v-loading="loading"
    :tree-props="treeProps"
    row-key="idPath"
    :default-expand-all="defaultExpandAll"
    :indent="indent"
    :max-height="'100vh'"
    :expand-row-keys="expandRowKeys"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
    @expand-change="handleExpandChange"
  >
    <el-table-column
      v-for="column in visibleColumns.custom"
      v-bind="column"
      :key="column.prop || column.label"
    >
      <template #default="scope">
        <component
          :is="NeRenderCore"
          v-for="element in column.elements"
          v-bind="getProps(element, scope)"
          :key="element.id"
        />
      </template>
    </el-table-column>
    <ne-table-column
      v-for="column in visibleColumns.normal"
      v-bind="column"
      :key="column.prop || column.label"
    />
    <el-table-column
      v-if="operationColumn"
      v-bind="operationColumn"
      label="操作"
      fixed="right"
    >
      <template #default="scope">
        <pro-button :record="scope" :items="operationColumn.actions" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { computed, markRaw, onMounted, ref, shallowRef, watch } from 'vue'
import { useRequest } from 'vue-request'
// import { merge } from 'lodash-unified'
// import { useRequestQuery } from '@neue-plus/hooks'
// import { replaceTemplate } from '@neue-plus/utils'
import { isUndefined, merge } from 'lodash-unified'
import { replaceTemplate } from '@neue-plus/utils'
import proButton from '../../pro-button/src/pro-button.vue'
import NeTableColumn from '../../table/src/table-column/index.vue'
import {
  useApisRef,
  useSysParamsRef,
} from '../../material-render/src/context/event-flow'
import { request } from '../../material-render/src/handlers/handleRequest'
import { neTreeTableProps } from './types'

defineOptions({
  name: 'NeTreeTable',
  inheritAttrs: false,
})

const props = defineProps(neTreeTableProps)
const table = ref<any>()
const expandRowKeys = ref<any[]>([])

// 数据源
const dataSource = ref<any[]>()
const expand = ref(true)
const apisRef = useApisRef()
const sysParamsRef = useSysParamsRef()
const { data, loading, run } = useRequest(fetchData, {
  // refreshOnWindowFocus: true,
  // refocusTimespan: 1000, // 请求间隔时间
})
const tableProps = computed(() => {
  const { data, loading, bordered } = props
  return { data, loading, bordered }
})
const getProps = (element: any, scope: any) => {
  const { row } = scope
  const obj = replaceTemplate(element, row)
  return merge({}, obj, {
    props: { type: row['@odata.type'].split('.').pop() },
  })
}

// 可见列
const visibleColumns = computed(() => {
  const arr = props.columns
    ?.filter(
      (column) => column.hideInTable !== true && column.prop !== 'operation'
    )
    .map((column) => ({
      ...column,
    }))
  return {
    custom: arr.filter((item: any) => !isUndefined(item.elements)),
    normal: arr.filter((item: any) => isUndefined(item.elements)),
  }
})

// 操作列
const operationColumn = computed(() => {
  const operation = props.columns.find((column) => column.prop === 'operation')
  return operation
})
function flattenRelations(list: any[], parentId: string): any[] {
  return list.map((item, index) => {
    if (!item.to) return item
    const { to, ...rest } = item
    const { toRelations = [], ...toRest } = to
    const idPath = `${parentId}/${index}`
    return {
      ...rest,
      ...toRest, // 把 name2 等字段提上来
      ncid: rest.ncid || toRest.ncid,
      newNcid: toRest.ncid,
      idPath,
      toRelations: flattenRelations(toRelations, idPath), // 递归处理
    }
  })
}

watch(
  () => data.value,
  (result) => {
    dataSource.value = flattenRelations(
      [{ to: merge({}, result?.data, { isRoot: true }) }],
      '0'
    )
    console.log(dataSource.value, 'dataSource.value ')
    if (!expandRowKeys.value.length) {
      expandRowKeys.value = dataSource.value.map((item) => item.idPath)
    }
  }
)

async function fetchData() {
  if (props.request) {
    return request({
      ...props.request,
      replaceData: sysParamsRef.value,
    })
  } else if (typeof apisRef['request'] === 'function') {
    return apisRef['request']({
      replaceData: sysParamsRef.value,
    })
  } else {
    dataSource.value = props.data
  }
}
// 切换展开/收起所有行
const toggleExpandAll = (collapse: boolean = expand.value) => {
  expand.value = !collapse

  const expandRecursive = (nodes: any[]) => {
    const children = props.treeProps.children || 'children'
    nodes?.forEach((node) => {
      table.value.toggleRowExpansion(node, collapse)
      if (node[children] && node[children].length) {
        expandRecursive(node[children])
      }
    })
  }
  dataSource.value && expandRecursive(dataSource.value)
}

// 刷新表格
const refresh = () => {
  dataSource.value = []
  run()
}

// 暴露方法
defineExpose({
  toggleExpandAll,
  refresh,
  table,
})

// 监听数据变化
watch(
  () => props.data,
  (val) => {
    dataSource.value = val
  }
)

// 事件定义
const emit = defineEmits([
  'sort-change',
  'selection-change',
  'row-click',
  'expand-change',
])

// 事件处理
const handleSortChange = (data: { column: any; prop: string; order: any }) => {
  emit('sort-change', data)
}

const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

const handleRowClick = (row: any, column: any, event: Event) => {
  emit('row-click', row, column, event)
}

const handleExpandChange = (row: any, expandedRows: any[]) => {
  emit('expand-change', row, expandedRows)
  const key = row.idPath
  const index = expandRowKeys.value.indexOf(key)
  if (index === -1) {
    expandRowKeys.value.push(key)
  } else {
    expandRowKeys.value.splice(index, 1)
  }
}
const NeRenderCore = shallowRef<any>(null)
onMounted(async () => {
  const module = await import('../../material-render')
  NeRenderCore.value = markRaw(module.NeRenderCore)
})
</script>
