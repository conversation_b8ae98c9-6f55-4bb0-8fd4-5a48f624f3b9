/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import { IdiFiles } from './IdiFiles';
import { IdiFilesRequestBuilder } from './IdiFilesRequestBuilder';
import { IdiConvertState } from './IdiConvertState';
import { LifecycleState } from './LifecycleState';
import {
  CustomField,
  defaultDeSerializers,
  DefaultDeSerializers,
  DeSerializers,
  AllFields,
  entityBuilder,
  EntityBuilderType,
  EntityApi,
  FieldBuilder,
  OrderableEdmTypeField,
  EnumField
} from '@sap-cloud-sdk/odata-v4';
export class IdiFilesApi<
  DeSerializersT extends DeSerializers = DefaultDeSerializers
> implements EntityApi<IdiFiles<DeSerializersT>, DeSerializersT>
{
  public deSerializers: DeSerializersT;

  private constructor(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ) {
    this.deSerializers = deSerializers;
  }

  /**
   * Do not use this method or the constructor directly.
   * Use the service function as described in the documentation to get an API instance.
   */
  public static _privateFactory<
    DeSerializersT extends DeSerializers = DefaultDeSerializers
  >(
    deSerializers: DeSerializersT = defaultDeSerializers as any
  ): IdiFilesApi<DeSerializersT> {
    return new IdiFilesApi(deSerializers);
  }

  private navigationPropertyFields!: {};

  _addNavigationProperties(linkedApis: []): this {
    this.navigationPropertyFields = {};
    return this;
  }

  entityConstructor = IdiFiles;

  requestBuilder(): IdiFilesRequestBuilder<DeSerializersT> {
    return new IdiFilesRequestBuilder<DeSerializersT>(this);
  }

  entityBuilder(): EntityBuilderType<IdiFiles<DeSerializersT>, DeSerializersT> {
    return entityBuilder<IdiFiles<DeSerializersT>, DeSerializersT>(this);
  }

  customField<NullableT extends boolean = false>(
    fieldName: string,
    isNullable: NullableT = false as NullableT
  ): CustomField<IdiFiles<DeSerializersT>, DeSerializersT, NullableT> {
    return new CustomField(
      fieldName,
      this.entityConstructor,
      this.deSerializers,
      isNullable
    ) as any;
  }

  private _fieldBuilder?: FieldBuilder<typeof IdiFiles, DeSerializersT>;
  get fieldBuilder() {
    if (!this._fieldBuilder) {
      this._fieldBuilder = new FieldBuilder(IdiFiles, this.deSerializers);
    }
    return this._fieldBuilder;
  }

  private _schema?: {
    IDI_LIGHT_MODEL_ID: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    CONVERT_STATE: EnumField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      IdiConvertState,
      true,
      true
    >;
    TRIGGER_TIME: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      true,
      true
    >;
    NAME: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    DESCRIPTION: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    SOFTWARE_REV: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    NCID: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    CREATED_AT: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    MODIFIED_AT: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.DateTimeOffset',
      false,
      true
    >;
    SCHEMA_VERSION: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      false,
      true
    >;
    LIFECYCLE_STATE: EnumField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      LifecycleState,
      false,
      true
    >;
    LIFECYCLE_NOTE: OrderableEdmTypeField<
      IdiFiles<DeSerializers>,
      DeSerializersT,
      'Edm.String',
      true,
      true
    >;
    ALL_FIELDS: AllFields<IdiFiles<DeSerializers>>;
  };

  get schema() {
    if (!this._schema) {
      const fieldBuilder = this.fieldBuilder;
      this._schema = {
        /**
         * Static representation of the {@link idiLightModelId} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        IDI_LIGHT_MODEL_ID: fieldBuilder.buildEdmTypeField(
          'idiLightModelId',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link convertState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CONVERT_STATE: fieldBuilder.buildEnumField(
          'convertState',
          IdiConvertState,
          true
        ),
        /**
         * Static representation of the {@link triggerTime} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        TRIGGER_TIME: fieldBuilder.buildEdmTypeField(
          'triggerTime',
          'Edm.DateTimeOffset',
          true
        ),
        /**
         * Static representation of the {@link name} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NAME: fieldBuilder.buildEdmTypeField('name', 'Edm.String', true),
        /**
         * Static representation of the {@link description} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        DESCRIPTION: fieldBuilder.buildEdmTypeField(
          'description',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link softwareRev} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SOFTWARE_REV: fieldBuilder.buildEdmTypeField(
          'softwareRev',
          'Edm.String',
          true
        ),
        /**
         * Static representation of the {@link ncid} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        NCID: fieldBuilder.buildEdmTypeField('ncid', 'Edm.String', false),
        /**
         * Static representation of the {@link createdAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        CREATED_AT: fieldBuilder.buildEdmTypeField(
          'createdAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link modifiedAt} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        MODIFIED_AT: fieldBuilder.buildEdmTypeField(
          'modifiedAt',
          'Edm.DateTimeOffset',
          false
        ),
        /**
         * Static representation of the {@link schemaVersion} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        SCHEMA_VERSION: fieldBuilder.buildEdmTypeField(
          'schemaVersion',
          'Edm.String',
          false
        ),
        /**
         * Static representation of the {@link lifecycleState} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_STATE: fieldBuilder.buildEnumField(
          'lifecycleState',
          LifecycleState,
          false
        ),
        /**
         * Static representation of the {@link lifecycleNote} property for query construction.
         * Use to reference this property in query operations such as 'select' in the fluent request API.
         */
        LIFECYCLE_NOTE: fieldBuilder.buildEdmTypeField(
          'lifecycleNote',
          'Edm.String',
          true
        ),
        ...this.navigationPropertyFields,
        /**
         *
         * All fields selector.
         */
        ALL_FIELDS: new AllFields('*', IdiFiles)
      };
    }

    return this._schema;
  }
}
