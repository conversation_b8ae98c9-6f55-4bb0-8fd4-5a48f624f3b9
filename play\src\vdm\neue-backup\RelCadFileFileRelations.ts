/*
 * Copyright (c) 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * This is a generated file powered by the SAP Cloud SDK for JavaScript.
 */
import {
  Entity,
  DefaultDeSerializers,
  DeSerializers,
  DeserializedType
} from '@sap-cloud-sdk/odata-v4';
import type { RelCadFileFileRelationsApi } from './RelCadFileFileRelationsApi';
import { RelCadFileFileUsageTypeEnum } from './RelCadFileFileUsageTypeEnum';
import { RelCadFileFileLinkTypeEnum } from './RelCadFileFileLinkTypeEnum';
import { LifecycleState } from './LifecycleState';

/**
 * This class represents the entity "RelCadFileFileRelations" of service "neue".
 */
export class RelCadFileFileRelations<
    T extends DeSerializers = DefaultDeSerializers
  >
  extends Entity
  implements RelCadFileFileRelationsType<T>
{
  /**
   * Technical entity name for RelCadFileFileRelations.
   */
  static override _entityName = 'RelCadFileFileRelations';
  /**
   * Default url path for the according service.
   */
  static override _defaultBasePath = '/';
  /**
   * All key fields of the RelCadFileFileRelations entity.
   */
  static _keys = ['ncid'];
  /**
   * Usage Type.
   * Maximum length: 64.
   */
  declare usageType: RelCadFileFileUsageTypeEnum;
  /**
   * Link Type.
   * Maximum length: 64.
   */
  declare linkType: RelCadFileFileLinkTypeEnum;
  /**
   * Ncid.
   * Maximum length: 255.
   */
  declare ncid: DeserializedType<T, 'Edm.String'>;
  /**
   * Created At.
   */
  declare createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Modified At.
   */
  declare modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  /**
   * Schema Version.
   */
  declare schemaVersion: DeserializedType<T, 'Edm.String'>;
  /**
   * Lifecycle State.
   */
  declare lifecycleState: LifecycleState;
  /**
   * Lifecycle Note.
   * Maximum length: 255.
   * @nullable
   */
  declare lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;

  constructor(_entityApi: RelCadFileFileRelationsApi<T>) {
    super(_entityApi);
  }
}

export interface RelCadFileFileRelationsType<
  T extends DeSerializers = DefaultDeSerializers
> {
  usageType: RelCadFileFileUsageTypeEnum;
  linkType: RelCadFileFileLinkTypeEnum;
  ncid: DeserializedType<T, 'Edm.String'>;
  createdAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  modifiedAt: DeserializedType<T, 'Edm.DateTimeOffset'>;
  schemaVersion: DeserializedType<T, 'Edm.String'>;
  lifecycleState: LifecycleState;
  lifecycleNote?: DeserializedType<T, 'Edm.String'> | null;
}
